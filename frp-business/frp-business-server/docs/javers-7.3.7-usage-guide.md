# Javers 7.3.7 正确用法指南

## 问题说明

在 Javers 7.3.7 版本中，`registerEntity(Class, String)` 方法签名已不被支持，导致以下错误：

```
无法解析方法 'registerEntity(Class<capture of ?>, String)'
```

## 解决方案

### 1. 修复后的自动配置方式

现在 `JaversAutoConfig.java` 已经修复，使用正确的 API：

```java
// 修复前（错误）
builder.registerEntity(clazz, annotation.idField());

// 修复后（正确）
ensureEntityHasIdAnnotation(clazz, annotation.idField());
builder.registerEntity(clazz);
```

### 2. 实体类正确的注解方式

对于使用注解的实体类，需要在 ID 字段上添加 `@Id` 注解：

```java
import org.javers.core.metamodel.annotation.Id;
import cn.need.cloud.biz.annotation.JaversComparable;

@JaversComparable(value = JaversComparable.Type.ENTITY, idField = "id")
public class OcoRequestVO {
    @Id  // 必须添加这个注解
    private Long id;
    
    private String note;
    private String channel;
    // ... 其他字段
}
```

或者使用 JPA 注解：

```java
import jakarta.persistence.Id;
import cn.need.cloud.biz.annotation.JaversComparable;

@JaversComparable(value = JaversComparable.Type.ENTITY, idField = "id")
public class OcoRequestVO {
    @Id  // JPA 注解也可以
    private Long id;
    
    private String note;
    private String channel;
    // ... 其他字段
}
```

### 3. 值对象的注解方式

值对象不需要 ID 字段：

```java
import cn.need.cloud.biz.annotation.JaversComparable;

@JaversComparable(JaversComparable.Type.VALUE_OBJECT)
public class OcoRequestUpdateParam {
    private Long id;
    private String note;
    private String channel;
    // ... 其他字段
}
```

### 4. 手动注册方式（推荐）

在 `JaversConfig.java` 中的手动注册方式是正确的，无需修改：

```java
private void registerManually(JaversBuilder builder) {
    // 注册 OCO 请求相关的实体和值对象
    builder.registerEntity(OcoRequest.class)
            .registerValueObject(OcoRequestUpdateParam.class);
    
    // 注册 OTC 请求相关的实体和值对象
    builder.registerEntity(OtcRequest.class)
            .registerValueObject(OtcRequestUpdateParam.class);
    
    // 注册 OTB 请求相关的实体和值对象
    builder.registerEntity(OtbRequest.class)
            .registerValueObject(OtbRequestUpdateParam.class);
}
```

### 5. 启用自动扫描配置

在 `application.yml` 中启用自动扫描：

```yaml
javers:
  auto-scan:
    enabled: true
```

## 使用建议

### 对于新项目
1. 使用注解方式，在实体类上添加 `@JaversComparable` 注解
2. 在 ID 字段上添加 `@Id` 注解
3. 在配置文件中启用自动扫描

### 对于现有项目
1. 继续使用手动注册方式（性能更好）
2. 在 `JaversConfig.registerManually()` 方法中添加新的类型

## 注意事项

1. **ID 字段注解**：使用自动扫描时，实体类的 ID 字段必须有 `@Id` 注解
2. **性能考虑**：手动注册方式性能更好，自动扫描方式更灵活
3. **兼容性**：修复后的代码兼容 Javers 7.3.7 版本
4. **警告信息**：如果 ID 字段缺少 `@Id` 注解，系统会记录警告日志

## 验证方法

启动应用后，查看日志中的 Javers 注册信息：

```
INFO  cn.need.cloud.biz.config.JaversAutoConfig - Javers 自动扫描完成：3 个实体，3 个值对象
```

如果看到警告信息，请检查对应实体类的 ID 字段是否有正确的 `@Id` 注解。
