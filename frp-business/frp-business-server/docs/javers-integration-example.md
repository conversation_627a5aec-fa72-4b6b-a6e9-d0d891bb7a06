# Javers 集成使用示例

## 概述

本项目已集成 Javers 对象比较框架，用于在 OCO/OTC 请求更新时自动记录用户的变更操作到审计日志中。

## 功能特性

- ✅ 自动检测对象字段变更
- ✅ 生成 JSON 格式的详细变更信息
- ✅ 支持多种数据类型（字符串、数字、日期等）
- ✅ 处理 null 值变更（从无到有、从有到无）
- ✅ 集成到审计日志系统
- ✅ 支持 OCO 和 OTC 请求对象比较

## 使用场景

### 1. OCO/OTC 请求更新记录

当用户更新 OCO 或 OTC 请求时，系统会自动：

1. 比较更新前后的数据
2. 生成 JSON 格式的变更信息
3. 记录到 `auditshowlog` 表的 `description` 字段

### 2. 变更描述示例

**单字段变更 JSON 格式：**

```json
{
  "changes": [
    {
      "changeType": "ValueChange",
      "globalId": {
        "entity": "OcoRequestVO",
        "cdoId": 1
      },
      "property": "note",
      "left": "原始备注",
      "right": "修改后的备注"
    }
  ]
}
```

**多字段变更 JSON 格式：**

```json
{
  "changes": [
    {
      "changeType": "ValueChange",
      "globalId": {
        "entity": "OcoRequestVO",
        "cdoId": 1
      },
      "property": "note",
      "left": "原始备注",
      "right": "修改后的备注"
    },
    {
      "changeType": "ValueChange",
      "globalId": {
        "entity": "OcoRequestVO",
        "cdoId": 1
      },
      "property": "channel",
      "left": "原始渠道",
      "right": "修改后的渠道"
    }
  ]
}
```

## 代码实现

### 1. 配置类 (JaversConfig.java)

```java
@Configuration
public class JaversConfig {
    @Bean
    public Javers javers() {
        return JaversBuilder.javers()
                .registerEntity(OcoRequestVO.class, "id")
                .registerValueObject(OcoRequestUpdateParam.class)
                .registerEntity(OtcRequestVO.class, "id")
                .registerValueObject(OtcRequestUpdateParam.class)
                .withMappingStyle(MappingStyle.FIELD)
                .build();
    }
}
```

### 2. 工具类 (ObjectCompareUtil.java)

```java
@Component
public class ObjectCompareUtil {
    @Autowired
    private Javers javers;

    public String compareAndGenerateDescription(Object oldObject, Object newObject) {
        // 比较对象并生成 JSON 格式的变更信息
    }

    public String compareAndGenerateJson(Object oldObject, Object newObject) {
        // 返回 JSON 格式的详细变更信息
    }
}
```

### 3. 服务层集成 (OcoRequestServiceImpl.java)

```java
@Service
public class OcoRequestServiceImpl {
    @Resource
    private ObjectCompareUtil objectCompareUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OcoRequest updateByParam(OcoRequestUpdateParam updateParam) {
        // 获取更新前的数据
        OcoRequestVO oldVo = detailById(updateParam.getId());
        
        // 执行更新操作
        // ...
        
        // 生成变更描述
        String changeDescription = objectCompareUtil.compareAndGenerateDescription(oldVo, updateParam);
        
        // 记录审计日志
        if (StringUtil.isNotBlank(changeDescription)) {
            OcoRequestAuditLogHelper.recordLog(entity, "UPDATE", null, changeDescription);
        }
        
        return entity;
    }
}
```

## 序列化结果示例

### 1. 用户友好描述格式

```
用户更改了：仓库从"仓库A"改为"仓库B"；备注从"原始备注"改为"修改后的备注"；发货窗口开始时间从"2024-01-01T10:00"改为"2024-01-02T09:00"
```

### 2. JSON 详细格式

```json
{
  "changes": [
    {
      "changeType": "ValueChange",
      "globalId": {
        "entity": "OcoRequestVO",
        "cdoId": 1
      },
      "property": "warehouseId",
      "left": 100,
      "right": 200
    },
    {
      "changeType": "ValueChange",
      "globalId": {
        "entity": "OcoRequestVO", 
        "cdoId": 1
      },
      "property": "note",
      "left": "原始备注",
      "right": "修改后的备注"
    }
  ]
}
```

## 字段映射配置

系统支持将英文字段名自动转换为中文描述：

| 字段名 | 中文描述 |
|--------|----------|
| warehouseId | 仓库 |
| transactionPartnerId | 交易伙伴 |
| note | 备注 |
| channel | 渠道 |
| shipWindowStart | 发货窗口开始时间 |
| shipWindowEnd | 发货窗口结束时间 |
| orderNum | 订单号 |
| shipToCompanyName | 收货公司名称 |
| shipToContactName | 收货联系人 |
| ... | ... |

## 扩展其他实体

如需为其他实体添加变更记录功能：

### 1. 在 JaversConfig 中注册实体

```java
@Bean
public Javers javers() {
    return JaversBuilder.javers()
            .registerEntity(OcoRequestVO.class, "id")
            .registerEntity(OtherEntityVO.class, "id")  // 新增
            .registerValueObject(OcoRequestUpdateParam.class)
            .registerValueObject(OtherUpdateParam.class)  // 新增
            .withMappingStyle(MappingStyle.FIELD)
            .build();
}
```

### 2. 在 ObjectCompareUtil 中添加字段映射

```java
private String getFieldDescription(String fieldName) {
    switch (fieldName) {
        // 现有映射...
        case "newField":
            return "新字段描述";
        // ...
    }
}
```

### 3. 在对应的 Service 中使用

```java
String changeDescription = objectCompareUtil.compareAndGenerateDescription(oldObject, newObject);
if (StringUtil.isNotBlank(changeDescription)) {
    // 记录到对应的审计日志
}
```

## 测试

运行测试用例验证功能：

```bash
mvn test -Dtest=ObjectCompareUtilTest
```

测试覆盖场景：
- 无变更
- 单字段变更
- 多字段变更
- 日期时间变更
- 空值变更
- JSON 格式输出

## 注意事项

1. **性能考虑**：Javers 使用反射进行对象比较，对于大对象可能有性能影响
2. **字段过滤**：可以通过配置忽略不需要比较的字段
3. **循环引用**：Javers 能够处理对象间的循环引用
4. **版本兼容**：当前使用 Javers 7.3.7 版本，升级时需注意兼容性

## 相关文档

- [Javers 官方文档](https://javers.org/documentation/)
- [Spring Boot 集成指南](https://javers.org/documentation/spring-integration/)
