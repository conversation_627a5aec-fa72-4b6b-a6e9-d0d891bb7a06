# Javers 使用方式说明

## 三种配置方式

### 方式1：手动注册（推荐，性能最好）

在 `JaversConfig.java` 中手动注册需要比较的类：

```java
private void registerManually(JaversBuilder builder) {
    // 注册 OCO 请求相关的实体和值对象
    builder.registerEntity(OcoRequestVO.class, "id")
           .registerValueObject(OcoRequestUpdateParam.class);
    
    // 注册 OTC 请求相关的实体和值对象
    builder.registerEntity(OtcRequestVO.class, "id")
           .registerValueObject(OtcRequestUpdateParam.class);
    
    // 注册 OTB 请求相关的实体和值对象
    builder.registerEntity(OtbRequestVO.class, "id")
           .registerValueObject(OtbRequestUpdateParam.class);
    
    // 添加新的类型只需要在这里添加
    builder.registerEntity(NewRequestVO.class, "id")
           .registerValueObject(NewRequestUpdateParam.class);
}
```

**优点：**
- 性能最好，启动速度快
- 明确知道注册了哪些类
- 不会意外注册不需要的类

**缺点：**
- 每次添加新类型都需要修改配置

### 方式2：注解方式（推荐，最灵活）

#### 2.1 在类上添加注解

```java
// VO 类（实体对象）
@JaversComparable(value = JaversComparable.Type.ENTITY, idField = "id")
public class OcoRequestVO {
    private Long id;
    // ... 其他字段
}

// UpdateParam 类（值对象）
@JaversComparable(JaversComparable.Type.VALUE_OBJECT)
public class OcoRequestUpdateParam {
    private Long id;
    // ... 其他字段
}
```

#### 2.2 启用自动扫描

在 `application.yml` 中配置：

```yaml
javers:
  auto-scan:
    enabled: true
```

**优点：**
- 非常灵活，添加新类型只需要加注解
- 不需要修改配置类
- 支持不同的 idField

**缺点：**
- 启动时需要扫描类，性能稍差
- 可能会意外注册不需要的类

### 方式3：自动扫描（不推荐）

自动扫描所有符合命名规则的类：

```java
// 在 JaversConfig 中启用
private void registerAutomatically(JaversBuilder builder) {
    // 自动扫描 VO 和 UpdateParam 类
}
```

**优点：**
- 完全自动化

**缺点：**
- 性能最差
- 可能注册不需要的类
- 不够灵活

## 推荐使用方案

### 对于新项目：使用注解方式

1. 在需要比较的 VO 类上添加注解：
```java
@JaversComparable(value = JaversComparable.Type.ENTITY, idField = "id")
public class YourRequestVO {
    // ...
}
```

2. 在对应的 UpdateParam 类上添加注解：
```java
@JaversComparable(JaversComparable.Type.VALUE_OBJECT)
public class YourRequestUpdateParam {
    // ...
}
```

3. 在配置文件中启用自动扫描：
```yaml
javers:
  auto-scan:
    enabled: true
```

### 对于现有项目：使用手动注册

如果你只有少量类需要比较，继续使用手动注册方式，在 `JaversConfig.registerManually()` 方法中添加新的类型。

## 使用示例

### 添加 OTB 请求比较支持

#### 方式1：手动注册
```java
// 在 JaversConfig.registerManually() 中添加
builder.registerEntity(OtbRequestVO.class, "id")
       .registerValueObject(OtbRequestUpdateParam.class);
```

#### 方式2：注解方式
```java
@JaversComparable(value = JaversComparable.Type.ENTITY, idField = "id")
public class OtbRequestVO {
    // ...
}

@JaversComparable(JaversComparable.Type.VALUE_OBJECT)
public class OtbRequestUpdateParam {
    // ...
}
```

### 在 Service 中使用

```java
@Service
public class OtbRequestServiceImpl {
    @Resource
    private ObjectCompareUtil objectCompareUtil;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public OtbRequest updateByParam(OtbRequestUpdateParam updateParam) {
        // 获取更新前的数据
        OtbRequestVO oldVo = detailById(updateParam.getId());
        
        // 执行更新操作
        // ...
        
        // 生成变更描述（JSON 格式）
        String changeDescription = objectCompareUtil.compareAndGenerateDescription(oldVo, updateParam);
        
        // 记录日志
        if (StringUtil.isNotBlank(changeDescription) && !changeDescription.equals("{}")) {
            OtbRequestAuditLogHelper.recordLog(entity, "UPDATE", null, changeDescription);
        }
        
        return entity;
    }
}
```

## 性能对比

| 方式 | 启动时间 | 内存占用 | 灵活性 | 维护成本 |
|------|----------|----------|--------|----------|
| 手动注册 | 最快 | 最少 | 中等 | 中等 |
| 注解方式 | 中等 | 中等 | 最高 | 最低 |
| 自动扫描 | 最慢 | 最多 | 最低 | 最高 |

## 建议

1. **新项目**：使用注解方式，灵活性最好
2. **现有项目**：如果类不多，继续使用手动注册；如果类很多，考虑迁移到注解方式
3. **性能敏感**：使用手动注册方式
4. **快速开发**：使用注解方式
