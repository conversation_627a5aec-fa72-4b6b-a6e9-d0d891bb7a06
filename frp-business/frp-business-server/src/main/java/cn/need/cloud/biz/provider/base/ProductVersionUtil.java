package cn.need.cloud.biz.provider.base;

import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.dto.base.BaseProductVersionDTO;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.info.ProductVersionReqDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.model.query.product.ProductVersionQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVersionVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 版本产品填充 util
 *
 * <AUTHOR>
 */
public class ProductVersionUtil {

    private ProductVersionUtil() {
    }

    public static void fillProductVersionId(List<ProductVersionReqDTO> productVersionReqList) {
        //获取产品版本
        ProductVersionService versionService = SpringUtil.getBean(ProductVersionService.class);

        //填充供应商信息
        List<TenantReqDTO> tenantReqList = productVersionReqList.stream().map(ProductVersionReqDTO::getTransactionPartner).toList();

        //填充供应商id
        TenantUtil.fillTenant(tenantReqList);

        //获取产品refNum
        List<String> refNum = productVersionReqList.stream()
                .map(ProductVersionReqDTO::getRefNum)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();

        //获取产品supplierSku
        List<String> supplierSkuList = productVersionReqList.stream()
                .map(ProductVersionReqDTO::getSupplierSku)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();

        //获取供应商id
        List<Long> tenantIdList = tenantReqList.stream()
                .map(TenantReqDTO::getTenantId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();

        //获取产品版本号
        List<Integer> versionIntList = productVersionReqList.stream()
                .map(ProductVersionReqDTO::getVersionRefNum)
                .filter(ObjectUtil::isNotEmpty)
                .map(Integer::valueOf)
                .distinct()
                .toList();

        //构建查询对象
        ProductVersionQuery productVersionQuery = new ProductVersionQuery();
        productVersionQuery.setRefNumList(refNum);
        productVersionQuery.setSupplierSkuList(supplierSkuList);
        productVersionQuery.setTransactionPartnerIdList(tenantIdList);
        productVersionQuery.setProductVersionIntList(versionIntList);


        //查询产品版本
        List<ProductVersionVO> productVersionList = Objects.requireNonNull(versionService).listByQuery(productVersionQuery);

        //根据供应商id映射产品版本
        Map<Long, List<ProductVersionVO>> productVersionMap = productVersionList.stream().collect(Collectors.groupingBy(ProductVersionVO::getTransactionPartnerId));

        //遍历版本产品
        productVersionReqList.forEach(item -> {
            //获取当前供应商下产品
            List<ProductVersionVO> versionList = productVersionMap.get(item.getTransactionPartnerId());
            //根据产品refNum映射产品版本
            Map<String, ProductVersionVO> versionRefNumMap = ObjectUtil.toMap(versionList, ProductVersionVO::getRefNum);

            //根据产品supplierSku映射产品版本
            Map<String, ProductVersionVO> versionSupplierSkuMap = ObjectUtil.toMap(versionList, ProductVersionVO::getSupplierSku);

            //获取当前版本id
            ProductVersionVO productVersionVO = versionRefNumMap.get(item.getRefNum());
            if (ObjectUtil.isNotEmpty(productVersionVO)) {
                item.setProductVersionId(productVersionVO.getId());
                item.setProductId(productVersionVO.getProductId());
                return;
            }
            productVersionVO = versionSupplierSkuMap.get(item.getSupplierSku());

            if (ObjectUtil.isNotEmpty(productVersionVO)) {
                item.setProductVersionId(productVersionVO.getId());
                item.setProductId(productVersionVO.getProductId());
                return;
            }

            throw new BusinessException(
                    StringUtil.format(
                            "The product SKU:{} or product refNum:{} is incorrect",
                            item.getSupplierSku(),
                            item.getRefNum()
                    )
            );
        });
    }

    public static Map<Long, BaseProductVersionDTO> listByProductVersionId(Collection<Long> productVersionIdList) {
        //获取产品缓存
        List<ProductVersionCache> productVersionCacheList = ProductVersionCacheUtil.listByIds(productVersionIdList);
        //获取租户id
        List<Long> tenantIdList = productVersionCacheList.stream()
                .map(ProductVersionCache::getTransactionPartnerId)
                .distinct()
                .toList();
        //获取租户信息
        List<TenantCache> tenantCacheList = TenantCacheUtil.listByIds(tenantIdList);
        //根据租户id映射租户信息
        Map<Long, TenantCache> tenantCacheMap = ObjectUtil.toMap(tenantCacheList, TenantCache::getId);
        //根据产品id获取产品缓存
        List<BaseProductVersionDTO> list = productVersionCacheList.stream().map(item -> {
            BaseProductVersionDTO baseProductVersionDTO = BeanUtil.copyNew(item, BaseProductVersionDTO.class);
            TenantCache tenantCache = tenantCacheMap.get(item.getTransactionPartnerId());
            baseProductVersionDTO.setVersionRefNum(StringUtil.toString(item.getProductVersionInt()));
            baseProductVersionDTO.setProduct(BeanUtil.copyNew(item, ProductReqDTO.class));
            TenantReqDTO tenantReqDTO = BeanUtil.copyNew(tenantCache, TenantReqDTO.class);
            tenantReqDTO.setRefNum(tenantCache.getTenantCode());
            baseProductVersionDTO.setTransactionPartner(tenantReqDTO);
            return baseProductVersionDTO;
        }).toList();
        //返回结果
        return ObjectUtil.toMap(list, BaseProductVersionDTO::getId);
    }


    public static BaseProductVersionDTO convert(BaseProductVersionVO baseProductVersionVO) {
        BaseProductVersionDTO baseProductVersionDTO = BeanUtil.copyNew(baseProductVersionVO, BaseProductVersionDTO.class);
        baseProductVersionDTO.setProduct(BeanUtil.copyNew(baseProductVersionVO, ProductReqDTO.class));
        TenantCache tenantCache = TenantCacheUtil.getById(baseProductVersionVO.getTransactionPartnerId());
        TenantReqDTO tenantReqDTO = BeanUtil.copyNew(tenantCache, TenantReqDTO.class);
        tenantReqDTO.setRefNum(tenantCache.getTenantCode());
        baseProductVersionDTO.setTransactionPartner(tenantReqDTO);
        return baseProductVersionDTO;
    }
}
