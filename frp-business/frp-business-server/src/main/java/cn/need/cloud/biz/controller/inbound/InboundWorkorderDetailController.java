package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundWorkorderDetailConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail;
import cn.need.cloud.biz.model.query.inbound.InboundWorkorderDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundAdjustReceiveQtyVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundAdjustVersionVO;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundWorkorderDetailPageVO;
import cn.need.cloud.biz.service.inbound.InboundWorkorderDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-workorder-detail")
@Tag(name = "入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致")
public class InboundWorkorderDetailController extends AbstractRestController<InboundWorkorderDetailService, InboundWorkorderDetail, InboundWorkorderDetailConverter, InboundWorkorderDetailVO> {

    @Operation(summary = "根据id获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致详情", description = "根据数据主键id，从数据库中获取其对应的入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundWorkorderDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致详情
        InboundWorkorderDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundWorkorderDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundWorkorderDetailQuery> search) {

        // 获取入库工单详情 FinishQty可能和Qty不一致，这个是有仓库人员判定(外界发的货物可能不一致) 仓库内部的操作是一致的，外界可以不一致分页
        PageData<InboundWorkorderDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "修改收货数量", description = "修改收货数量")
    @PostMapping(value = "/adjust-receive-qty")
    public Result<Integer> adjustReceiveQty(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundAdjustReceiveQtyVO inboundAdjustReceiveQtyVO) {

        service.adjustReceiveQty(inboundAdjustReceiveQtyVO);
        return success();
    }

    @Operation(summary = "调整产品版本", description = "调整产品版本")
    @PostMapping(value = "/adjust-version")
    public Result<Integer> adjustVersion(@RequestBody InboundAdjustVersionVO inboundAdjustVersionVO) {

        // 调整产品版本
        service.adjustVersion(inboundAdjustVersionVO);
        // 返回结果
        return success();
    }
}
