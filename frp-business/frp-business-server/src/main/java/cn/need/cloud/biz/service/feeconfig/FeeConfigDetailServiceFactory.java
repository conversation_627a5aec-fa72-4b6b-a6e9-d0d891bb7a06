package cn.need.cloud.biz.service.feeconfig;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@AllArgsConstructor
public class FeeConfigDetailServiceFactory {

    private final Map<String, FeeConfigDetailService> serviceMap;

    public FeeConfigDetailService getBuilder(FeeModelTypeEnum type) {
        FeeConfigDetailService service = serviceMap.get(StringUtil.format("feeConfigDetail{}", type.getCode()));
        if (service == null) {
            throw new BusinessException("Unknown FeeConfigDetailService build type: " + type);
        }
        return service;
    }
}