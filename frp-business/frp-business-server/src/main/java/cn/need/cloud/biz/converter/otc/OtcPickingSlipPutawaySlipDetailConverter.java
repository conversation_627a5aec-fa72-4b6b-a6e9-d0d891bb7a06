package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPickingSlipPutawaySlipDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlipPutawaySlipDetail;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPickingSlipPutawaySlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 拣货单上架详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public class OtcPickingSlipPutawaySlipDetailConverter extends AbstractModelConverter<OtcPickingSlipPutawaySlipDetail, OtcPickingSlipPutawaySlipDetailVO, OtcPickingSlipPutawaySlipDetailDTO> {

} 