package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.SupplierQuoteConverter;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierQuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierQuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierQuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierQuotePageVO;
import cn.need.cloud.biz.service.feeconfig.SupplierQuoteService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 供应商-仓库报价 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/supplier-quote")
@Tag(name = "供应商-仓库报价")
public class SupplierQuoteController extends AbstractRestController<SupplierQuoteService, SupplierQuote, SupplierQuoteConverter, SupplierQuoteVO> {

    @Operation(summary = "新增供应商-仓库报价", description = "接收供应商-仓库报价的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SupplierQuoteCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改供应商-仓库报价", description = "接收供应商-仓库报价的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SupplierQuoteUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除供应商-仓库报价", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取供应商-仓库报价详情", description = "根据数据主键id，从数据库中获取其对应的供应商-仓库报价详情")
    @GetMapping(value = "/detail/{id}")
    public Result<SupplierQuoteVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取供应商-仓库报价分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的供应商-仓库报价列表")
    @PostMapping(value = "/list")
    public Result<PageData<SupplierQuotePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<SupplierQuoteQuery> search) {

        // 获取供应商-仓库报价分页
        PageData<SupplierQuotePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "启用禁用", description = "启用禁用")
    @PostMapping(value = "/switch-active")
    public Result<Integer> switchActive(@RequestBody @Parameter(description = "有效无效 vo对象", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        // 返回结果
        return success(service.switchActive(id.getId()));
    }
}
