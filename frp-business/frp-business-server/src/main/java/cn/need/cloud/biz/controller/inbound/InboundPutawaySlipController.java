package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundPutawaySlipConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutAwayInfoVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipByPalletVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.cloud.biz.model.vo.page.InboundPutawaySlipPageVO;
import cn.need.cloud.biz.service.inbound.InboundPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 上架 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-putaway-slip")
@Tag(name = "上架")
public class InboundPutawaySlipController extends AbstractRestController<InboundPutawaySlipService, InboundPutawaySlip, InboundPutawaySlipConverter, InboundPutawaySlipVO> {

    @Operation(summary = "根据id获取上架详情", description = "根据数据主键id，从数据库中获取其对应的上架详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundPutawaySlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取上架详情
        InboundPutawaySlipVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取上架详情", description = "根据数据RefNum，从数据库中获取其对应的上架详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InboundPutawaySlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取上架详情
        InboundPutawaySlipVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取上架分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的上架列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundPutawaySlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundPutawaySlipQuery> search) {

        // 获取上架分页
        PageData<InboundPutawaySlipPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    /**
     * <p>
     * 单独上架
     * </p>
     *
     * @param putawayInfoVO 上架信息
     * @return 上架单详情
     */
    @Operation(summary = "单独上架", description = "单独上架")
    @PostMapping(value = "/putAway")
    public Result<Integer> putAway(@RequestBody @Parameter(description = "上架信息", required = true) InboundPutAwayInfoVO putawayInfoVO) {

        service.putAway(putawayInfoVO);
        return success();
    }

    @Operation(summary = "打托上架", description = "打托上架")
    @PostMapping(value = "/putAway-by-pallet")
    public Result<Integer> putAwayByPallet(@RequestBody @Parameter(description = "打托上架对象", required = true) InboundPutawaySlipByPalletVO inboundPutawaySlipByPalletVO) {

        service.putAwayByPallet(inboundPutawaySlipByPalletVO);
        return success();
    }

    @Operation(summary = "打印入库上架单更新状态", description = "打印入库上架单更新状态")
    @PostMapping(value = "/mark-printed")
    public Result<Integer> markPrinted(@RequestBody PrintQuery printQuery) {

        service.markPrinted(printQuery);
        return success();
    }
}
