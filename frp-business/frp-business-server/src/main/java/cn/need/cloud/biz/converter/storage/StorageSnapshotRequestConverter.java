package cn.need.cloud.biz.converter.storage;

import cn.need.cloud.biz.client.dto.storage.StorageSnapshotRequestDTO;
import cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequest;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓储快照请求 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public class StorageSnapshotRequestConverter extends AbstractModelConverter<StorageSnapshotRequest, StorageSnapshotRequestVO, StorageSnapshotRequestDTO> {

}
