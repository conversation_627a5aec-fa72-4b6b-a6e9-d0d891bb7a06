package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPalletConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.param.otb.create.pallet.OtbPalletCreateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.pallet.OtbPalletQuery;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbBuildPalletVO;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.cloud.biz.model.vo.page.OtbPalletPageVO;
import cn.need.cloud.biz.service.otb.pallet.OtbBuildPalletService;
import cn.need.cloud.biz.service.otb.pallet.OtbPalletService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * OTB托盘 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-pallet")
@Tag(name = "OTB托盘")
public class OtbPalletController extends AbstractRestController<OtbPalletService, OtbPallet, OtbPalletConverter, OtbPalletVO> {

    @Resource
    private OtbBuildPalletService otbBuildPalletService;

    @Operation(summary = "构建OTB托盘", description = "接收OTB托盘的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/build")
    public Result<OtbBuildPalletVO> build(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbPalletCreateParam insertParam) {

        // 返回结果
        return success(otbBuildPalletService.build(insertParam));
    }

    @Operation(summary = "根据id获取OTB托盘详情", description = "根据数据主键id，从数据库中获取其对应的OTB托盘详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPalletVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB托盘详情
        OtbPalletVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB托盘详情", description = "根据数据RefNum，从数据库中获取其对应的OTB托盘详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPalletVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB托盘详情
        OtbPalletVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据SsccNum获取OTB托盘详情", description = "根据数据SsccNum，从数据库中获取其对应的OTB托盘详情")
    @GetMapping(value = "/detail-by-sscc-num/{palletSsccNum}")
    public Result<OtbPalletVO> detailBySsccNum(@PathVariable("palletSsccNum") @Parameter(description = "打托单ssccNum", required = true) String palletSsccNum) {

        // 获取OTB托盘详情
        OtbPalletVO detailVo = service.detailBySsccNum(palletSsccNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTB托盘分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB托盘列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPalletPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPalletQuery> search) {

        // 获取OTB托盘分页
        PageData<OtbPalletPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "打印托盘label", description = "打印托盘label")
    @PostMapping(value = "/markPrinted")
    public Result<Boolean> markPrinted(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        service.markPrinted(printQuery);
        // 返回结果
        return success(Boolean.TRUE);
    }
}
