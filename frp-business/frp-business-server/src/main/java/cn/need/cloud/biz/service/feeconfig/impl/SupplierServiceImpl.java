package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.SupplierConverter;
import cn.need.cloud.biz.mapper.feeconfig.SupplierMapper;
import cn.need.cloud.biz.model.entity.feeconfig.Supplier;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuery;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierPageVO;
import cn.need.cloud.biz.service.feeconfig.SupplierService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 供应商信息 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service
public class SupplierServiceImpl extends SuperServiceImpl<SupplierMapper, Supplier> implements SupplierService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(SupplierCreateParam createParam) {
        // 检查传入供应商信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        if (ObjectUtil.isEmpty(createParam.getTransactionPartnerId())) {
            throw new BusinessException("TransactionPartnerId cannot be empty");
        }

        checkExistTransaction(createParam.getTransactionPartnerId());

        // 获取供应商信息转换器实例，用于将供应商信息参数对象转换为实体对象
        SupplierConverter converter = Converters.get(SupplierConverter.class);

        // 将供应商信息参数对象转换为实体对象并初始化
        Supplier entity = initSupplier(converter.toEntity(createParam));

        // 插入供应商信息实体对象到数据库
        super.insert(entity);

        // 返回供应商信息ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(SupplierUpdateParam updateParam) {
        // 检查传入供应商信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        final SupplierVO supplierVO = detailById(updateParam.getId());

        if (!updateParam.getTransactionPartnerId().equals(supplierVO.getTransactionPartnerId())) {
            checkExistTransaction(updateParam.getTransactionPartnerId());
        }

        // 获取供应商信息转换器实例，用于将供应商信息参数对象转换为实体对象
        SupplierConverter converter = Converters.get(SupplierConverter.class);

        // 将供应商信息参数对象转换为实体对象
        Supplier entity = converter.toEntity(updateParam);

        // 执行更新供应商信息操作
        return super.update(entity);

    }

    @Override
    public List<SupplierPageVO> listByQuery(SupplierQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<SupplierPageVO> pageByQuery(PageSearch<SupplierQuery> search) {
        Page<Supplier> page = Conditions.page(search, entityClass);
        List<SupplierPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public SupplierVO detailById(Long id) {
        Supplier entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Supplier", id));
        }
        return buildSupplierVO(entity);
    }

    @Override
    public SupplierVO getByTransactionPartnerId(Long transactionPartnerId) {
        // 检查传入供应商信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(transactionPartnerId)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 根据供应商信息参数获取供应商信息对象
        Supplier entity = lambdaQuery().eq(Supplier::getTransactionPartnerId, transactionPartnerId).one();

        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        return buildSupplierVO(entity);
    }

    private void checkExistTransaction(Long transactionPartnerId) {
        SupplierVO supplier = getByTransactionPartnerId(transactionPartnerId);
        if (ObjectUtil.isNotEmpty(supplier)) {
            throw new BusinessException(StringUtil.format("{} alReady exist", supplier.getTransactionPartnerVO().toString()));
        }
    }

    @Override
    public List<Long> getActiveTransactionPartnerIds() {
        // 查询所有有效的供应商
        List<Supplier> activeSuppliers = lambdaQuery()
                .eq(Supplier::getActiveFlag, true)
                .isNotNull(Supplier::getTransactionPartnerId)
                .list();

        // 提取并返回TransactionPartnerId列表
        return activeSuppliers.stream()
                .map(Supplier::getTransactionPartnerId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 初始化供应商信息对象
     * 此方法用于设置供应商信息对象的必要参数，确保其处于有效状态
     *
     * @param entity 供应商信息对象，不应为空
     * @return 返回初始化后的供应商信息
     * @throws BusinessException 如果传入的供应商信息为空，则抛出此异常
     */
    private Supplier initSupplier(Supplier entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Supplier cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建供应商信息VO对象
     *
     * @param entity 供应商信息对象
     * @return 返回包含详细信息的供应商信息VO对象
     */
    private SupplierVO buildSupplierVO(Supplier entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的供应商信息VO对象
        return Converters.get(SupplierConverter.class).toVO(entity);
    }

}
