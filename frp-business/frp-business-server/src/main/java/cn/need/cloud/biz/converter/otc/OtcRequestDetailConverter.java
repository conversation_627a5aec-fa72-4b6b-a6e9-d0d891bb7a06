package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcRequestDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcRequestDetail;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC请求详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcRequestDetailConverter extends AbstractModelConverter<OtcRequestDetail, OtcRequestDetailVO, OtcRequestDetailDTO> {

}
