package cn.need.cloud.biz.controller.storage;

import cn.need.cloud.biz.converter.storage.StorageSnapshotRequestDetailConverter;
import cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequestDetail;
import cn.need.cloud.biz.model.param.storage.create.StorageSnapshotRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.storage.update.StorageSnapshotRequestDetailUpdateParam;
import cn.need.cloud.biz.model.query.storage.StorageSnapshotRequestDetailQuery;
import cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestDetailPageVO;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestDetailVO;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestDetailService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 仓储快照请求详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@RestController
@RequestMapping("/api/biz/storage-snapshot-request-detail")
@Tag(name = "仓储快照请求详情")
public class StorageSnapshotRequestDetailController extends AbstractRestController<StorageSnapshotRequestDetailService, StorageSnapshotRequestDetail, StorageSnapshotRequestDetailConverter, StorageSnapshotRequestDetailVO> {

    @Operation(summary = "新增仓储快照请求详情", description = "接收仓储快照请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) StorageSnapshotRequestDetailCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改仓储快照请求详情", description = "接收仓储快照请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) StorageSnapshotRequestDetailUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除仓储快照请求详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取仓储快照请求详情详情", description = "根据数据主键id，从数据库中获取其对应的仓储快照请求详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<StorageSnapshotRequestDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓储快照请求详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓储快照请求详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<StorageSnapshotRequestDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<StorageSnapshotRequestDetailQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
