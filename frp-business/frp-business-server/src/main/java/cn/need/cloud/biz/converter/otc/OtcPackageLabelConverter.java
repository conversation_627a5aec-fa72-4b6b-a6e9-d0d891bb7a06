package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPackageLabelDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPackageLabel;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC包裹标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPackageLabelConverter extends AbstractModelConverter<OtcPackageLabel, OtcPackageLabelVO, OtcPackageLabelDTO> {

}
