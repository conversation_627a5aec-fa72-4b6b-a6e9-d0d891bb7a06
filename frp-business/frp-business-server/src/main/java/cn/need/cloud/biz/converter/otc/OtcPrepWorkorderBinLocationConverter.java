package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepWorkorderBinLocationDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderBinLocationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预提工单仓储位置 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPrepWorkorderBinLocationConverter extends AbstractModelConverter<OtcPrepWorkorderBinLocation, OtcPrepWorkorderBinLocationVO, OtcPrepWorkorderBinLocationDTO> {

}
