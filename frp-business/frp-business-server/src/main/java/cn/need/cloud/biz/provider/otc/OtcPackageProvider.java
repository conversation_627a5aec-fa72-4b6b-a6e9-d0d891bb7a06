package cn.need.cloud.biz.provider.otc;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.api.otc.OtcPackageClient;
import cn.need.cloud.biz.client.api.path.OtcPackagePath;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestWithWarehouseReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.client.dto.req.otc.OtcPackageDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.otc.OtcPackageListQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.otc.*;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorderDetail;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageQuery;
import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageWorkOrderQuery;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.model.vo.page.OtcPackagePageVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Stream;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(OtcPackagePath.PREFIX)
public class OtcPackageProvider implements OtcPackageClient {
    @Resource
    private OtcPackageService otcPackageService;
    @Resource
    private OtcRequestService otcRequestService;
    @Resource
    private OtcWorkorderService otcWorkorderService;
    @Resource
    private OtcWorkorderDetailService otcWorkorderDetailService;
    @Resource
    private OtcPrepWorkorderService otcPrepWorkorderService;
    @Resource
    private OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;

    /**
     * 构建实际产品信息
     *
     * @param otcWorkorderDetailList otc工单详情列表
     * @param warehouseCache         仓库缓存
     * @return 实际产品信息列表
     */
    private static @NotNull Stream<RequestProductRespDTO> buildRespDTOStream(List<OtcWorkorderDetail> otcWorkorderDetailList, WarehouseCache warehouseCache) {
        //获取产品id
        List<Long> productIdList = otcWorkorderDetailList.stream()
                .map(OtcWorkorderDetail::getProductId)
                .toList();
        //根据产品id映射产品信息
        Map<Long, BaseProductDTO> productDTOMap = ProductUtil.getByProductId(productIdList);

        return otcWorkorderDetailList.stream()
                .map(item -> {
                    RequestProductRespDTO respDTO = new RequestProductRespDTO();
                    //填充工单详情ID
                    respDTO.setId(item.getId());
                    //填充产品信息

                    respDTO.setBaseProductDTO(
                            BeanUtil.copyNew(
                                    productDTOMap.get(item.getProductId()),
                                    BaseProductDTO.class
                            )
                    );
                    //填充仓库信息
                    respDTO.setBaseWarehouseDTO(
                            BeanUtil.copyNew(
                                    warehouseCache,
                                    BaseWarehouseDTO.class
                            )
                    );
                    //填充prepWorkOrder类型
                    respDTO.setPrepWorkorderType(PrepWorkOrderTypeEnum.NONE.getStatus());

                    respDTO.setQty(item.getQty());


                    //返回结果
                    return respDTO;
                });
    }

    /**
     * 构建实际产品信息
     *
     * @param otcPrepWorkorderDetailList otc预处理工单详情列表
     * @param warehouseCache             仓库缓存
     * @return 实际产品信息列表
     */
    private static @NotNull Stream<RequestProductRespDTO> buildRespByPrepWorderDetailStream(
            List<OtcPrepWorkorderDetail> otcPrepWorkorderDetailList,
            WarehouseCache warehouseCache,
            List<OtcPrepWorkorder> otcPrepWorkorderList) {
        //根据预处理工单产品id映射预处理工单类
        Map<Long, OtcPrepWorkorder> prepWorkOrderMap = ObjectUtil.toMap(otcPrepWorkorderList, OtcPrepWorkorder::getId);
        //获取预处理工单产品id，预处理工单详情产品id
        List<Long> productIdList = getProductIdList(otcPrepWorkorderDetailList, otcPrepWorkorderList);
        //根据产品id映射产品信息
        Map<Long, BaseProductDTO> productMap = ProductUtil.getByProductId(productIdList);
        //获取预处理工单id，预处理工单详情映射
        Map<Long, List<OtcPrepWorkorderDetail>> detailMap = ObjectUtil.toMapList(otcPrepWorkorderDetailList, OtcPrepWorkorderDetail::getOtcPrepWorkorderId);

        return prepWorkOrderMap.values()
                .stream()
                .map(item -> buildRequestProductRespDTO(warehouseCache, item, productMap, detailMap));
    }

    /**
     * 构建返回对象
     *
     * @param warehouseCache   仓库缓存
     * @param otcPrepWorkorder 预处理工单
     * @param productMap       产品信息
     * @param detailMap        预处理工单详情映射
     * @return 返回对象
     */
    private static @NotNull RequestProductRespDTO buildRequestProductRespDTO(
            WarehouseCache warehouseCache,
            OtcPrepWorkorder otcPrepWorkorder,
            Map<Long, BaseProductDTO> productMap,
            Map<Long, List<OtcPrepWorkorderDetail>> detailMap) {
        //构建返回对象
        RequestProductRespDTO respDTO = new RequestProductRespDTO();
        //填充工单详情ID
        respDTO.setId(otcPrepWorkorder.getOtcWorkorderDetailId());
        //填充产品信息
        Long productId = Objects.requireNonNull(otcPrepWorkorder).getProductId();
        respDTO.setBaseProductDTO(productMap.get(productId));
        //填充仓库信息
        BaseWarehouseDTO baseWarehouseDTO = BeanUtil.copyNew(warehouseCache, BaseWarehouseDTO.class);
        respDTO.setBaseWarehouseDTO(baseWarehouseDTO);
        //统计产品数量
        respDTO.setQty(otcPrepWorkorder.getQty());
        //填充prepWorkOrder类型
        respDTO.setPrepWorkorderType(otcPrepWorkorder.getPrepWorkorderType());

        //构建返回详情
        //遍历预处理工单
        List<OtcPrepWorkorderDetail> list = detailMap.get(otcPrepWorkorder.getId());
        //添加
        //获取租户id
        List<RequestActualProductRespDTO> actualProductRespList = list.stream()
                .map(item -> {
                    RequestActualProductRespDTO dto = BeanUtil.copyNew(item, RequestActualProductRespDTO.class);
                    //填充预工单详情ID
                    dto.setId(item.getId());
                    dto.setBaseProductDTO(productMap.get(item.getProductId()));

                    dto.setLineNum(String.valueOf(item.getLineNum()));
                    //处理 PartnerLineNum
                    if (ObjectUtil.isNotEmpty(item.getParentId())) {
                        //说明有 parent

                        Optional<OtcPrepWorkorderDetail> first = list.stream()
                                .filter(x -> x.getId().equals(item.getParentId()))
                                .findFirst();
                        dto.setLineNumParent(String.valueOf(first.map(OtcPrepWorkorderDetail::getLineNum).orElse(null)));
                    }

                    //返回结果
                    return dto;
                }).toList();
        respDTO.setRequestActualProductRespList(actualProductRespList);
        return respDTO;
    }

    /**
     * 预处理工单id，预处理工单类型映射
     *
     * @param otcPrepWorkorderDetailList 预处理工单详情列表
     * @param otcPrepWorkorderList       预处理工单列表
     * @return 预处理工单id，预处理工单类型映射
     */
    private static @NotNull List<Long> getProductIdList(List<OtcPrepWorkorderDetail> otcPrepWorkorderDetailList, List<OtcPrepWorkorder> otcPrepWorkorderList) {
        //获取prep 工单详情产品id
        Stream<Long> productPrepWorkOrderDetailStream = otcPrepWorkorderDetailList.stream()
                .map(OtcPrepWorkorderDetail::getProductId);
        //prep工单产品id
        Stream<Long> productPrepWorkOrderStream = otcPrepWorkorderList.stream()
                .map(OtcPrepWorkorder::getProductId);
        //合并产品id流获取产品id
        List<Long> productIdList = Stream.concat(productPrepWorkOrderDetailStream, productPrepWorkOrderStream).toList();
        return productIdList;
    }

    @NotNull
    private static OtcPackageRespDTO getOtcPackageRespDTO(OtcPackageVO otcPackageVO) {
        OtcPackageRespDTO otcPackageRespDTO = BeanUtil.copyNew(otcPackageVO, OtcPackageRespDTO.class);
        otcPackageRespDTO.setOtcPickingSlip(BeanUtil.copyNew(otcPackageVO.getOtcPickingSlip(), RefNumRespDTO.class));
        List<OtcPackageDetailRespDTO> detailList = otcPackageVO.getDetailList()
                .stream()
                .map(item -> {
                    OtcPackageDetailRespDTO detailRespDTO = BeanUtil.copyNew(item, OtcPackageDetailRespDTO.class);
                    final BaseProductVO baseProductVO = item.getBaseProductVO();

                    BaseProductDTO baseProductDTO = new BaseProductDTO();
                    baseProductDTO.setProduct(BeanUtil.copyNew(baseProductVO, ProductReqDTO.class));
                    baseProductDTO.setTransactionPartner(BeanUtil.copyNew(baseProductVO.getTransactionPartnerVO(), TenantReqDTO.class));
                    detailRespDTO.setBaseProductDTO(baseProductDTO);

                    return detailRespDTO;
                }).toList();

        otcPackageRespDTO.setDetailList(detailList);
        otcPackageRespDTO.setLabelList(BeanUtil.copyNew(otcPackageVO.getLabelList(), OtcPackageLabelRespDTO.class));

        // 处理 packageMultiboxProductId
        //todo: 临时处理 packageMultiBox
        if (ObjectUtil.isNotEmpty(otcPackageRespDTO.getPackageMultiboxProductId())) {
            BaseProductDTO baseProductDTO = ProductUtil.getByProductId(otcPackageRespDTO.getPackageMultiboxProductId());
            otcPackageRespDTO.setPackageMultiboxProduct(baseProductDTO);
        }
        return otcPackageRespDTO;
    }

    @Override
    @PostMapping(OtcPackagePath.LIST)
    @IgnoreAuth
    public Result<PageData<OtcPackagePageRespDTO>> list(@RequestBody PageSearch<OtcPackageListQueryReqDTO> search) {
        //获取查询条件
        OtcPackageListQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());


        //转换查询参数
        PageSearch<OtcPackageListQuery> pageSearch = PageUtil.convert(search, item -> {
            OtcPackageListQuery otcPackageListQuery = new OtcPackageListQuery();
            OtcPackageQuery otcPackageQuery = Optional.ofNullable(item.getOtcPackageQuery())
                    .map(obj -> BeanUtil.copyNew(obj, OtcPackageQuery.class))
                    .orElse(null);
            OtcPackageWorkOrderQuery otcPackageWorkOrderQuery = Optional.ofNullable(item.getOtcWorkorderQuery())
                    .map(obj -> BeanUtil.copyNew(obj, OtcPackageWorkOrderQuery.class))
                    .orElse(null);
            otcPackageListQuery.setOtcPackageQuery(otcPackageQuery);
            otcPackageListQuery.setOtcWorkorderQuery(otcPackageWorkOrderQuery);
            return otcPackageListQuery;
        });
        //调用列表
        PageData<OtcPackagePageVO> data = otcPackageService.pageByQuery(pageSearch);
        //转换对象
        PageData<OtcPackagePageRespDTO> pageData = PageUtil.convert(data, item -> {
            OtcPackagePageRespDTO otcPackagePageRespDTO = BeanUtil.copyNew(item, OtcPackagePageRespDTO.class);
            OtcPackagePageWorkorderRespDTO workorderRespDTO = BeanUtil.copyNew(item.getOtcWorkorder(), OtcPackagePageWorkorderRespDTO.class);
            workorderRespDTO.setTransactionPartner(BeanUtil.copyNew(item.getOtcWorkorder().getBasePartnerVO(), BasePartnerDTO.class));
            otcPackagePageRespDTO.setOtcWorkorder(workorderRespDTO);
            otcPackagePageRespDTO.setPickingSlip(BeanUtil.copyNew(item.getPickingSlip(), RefNumRespDTO.class));
            return otcPackagePageRespDTO;
        });
        //返回结果
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(OtcPackagePath.DETAIL)
    @IgnoreAuth
    public Result<OtcPackageRespDTO> detail(@RequestBody OtcPackageDetailReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //调用详情
        otcPackageService.fillPackageId(query.getOtcPackageDetailDTO());
        OtcPackageVO otcPackageVO = otcPackageService.detailById(query.getPackageId());

        final OtcPackageRespDTO otcPackageRespDTO = getOtcPackageRespDTO(otcPackageVO);

        //返回结果
        return Result.ok(otcPackageRespDTO);
    }

    @PostMapping(OtcPackagePath.LIST_DETAIL_BY_REQUEST)
    @IgnoreAuth
    @Override
    public Result<List<OtcPackageRespDTO>> listDetailByRequest(@RequestBody BaseRequestWithWarehouseReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取请求id
        otcRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));

        final List<OtcPackageVO> packages = otcPackageService.listDetailByRequestId(query.getRequestId());

        List<OtcPackageRespDTO> packageRespList = new ArrayList<>();
        for (OtcPackageVO otcPackageVO : packages) {
            final OtcPackageRespDTO otcPackageRespDTO = getOtcPackageRespDTO(otcPackageVO);

            packageRespList.add(otcPackageRespDTO);
        }

        return Result.ok(packageRespList);
    }

    @Override
    @PostMapping(OtcPackagePath.REQUEST_ACTUAL_PRODUCT)
    @IgnoreAuth
    public Result<List<RequestProductRespDTO>> requestActualProduct(@RequestBody BaseRequestWithWarehouseReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取请求id
        otcRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));

        OtcRequestVO otcRequestVO = otcRequestService.detailById(query.getRequestId());
        //如果没有都处理完，不返回结果
        if (!RequestStatusEnum.getHasShipProcessed().contains(otcRequestVO.getOtcRequestStatus())) {
            return Result.ok(Collections.emptyList());
        }

        //获取otc工单id
        List<OtcWorkorder> otcWorkorderList = otcWorkorderService.listByRequestId(query.getRequestId());
        List<Long> otcWorkOrderIdList = otcWorkorderList.stream()
                .map(OtcWorkorder::getId)
                .toList();
        //获取工单详情
        List<OtcWorkorderDetail> otcWorkorderDetailList = otcWorkorderDetailService.listByWorkOrderIds(otcWorkOrderIdList);
        //获取仓库缓存
        Long warehouseId = otcWorkorderDetailList.stream()
                .findFirst()
                .map(OtcWorkorderDetail::getWarehouseId)
                .orElse(null);
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(warehouseId);
        //获取预配工单id
        List<OtcPrepWorkorder> otcPrepWorkorderList = otcPrepWorkorderService.listByWorkOrderIdList(otcWorkOrderIdList);
        //判空
        if (ObjectUtil.isEmpty(otcPrepWorkorderList)) {
            List<RequestProductRespDTO> list = buildRespDTOStream(otcWorkorderDetailList, warehouseCache).toList();
            //返回结果
            return Result.ok(list);
        }
        List<Long> otcPrepWorkOrderIdList = otcPrepWorkorderList.stream()
                .map(OtcPrepWorkorder::getId)
                .toList();
        //获取预配工单详情
        List<OtcPrepWorkorderDetail> otcPrepWorkorderDetailList = otcPrepWorkorderDetailService.listByPrepWorkOrderIds(otcPrepWorkOrderIdList);
        //获取需要做的工单详情id
        List<Long> workOrderDetailIdList = otcPrepWorkorderList.stream()
                .map(OtcPrepWorkorder::getOtcWorkorderDetailId)
                .toList();
        //过滤需要做的工单详情
        List<OtcWorkorderDetail> workOrderDetailList = otcWorkorderDetailList.stream()
                .filter(item -> !workOrderDetailIdList.contains(item.getId()))
                .toList();
        //获取构建流
        Stream<RequestProductRespDTO> respStream = buildRespDTOStream(workOrderDetailList, warehouseCache);

        Stream<RequestProductRespDTO> stream = buildRespByPrepWorderDetailStream(otcPrepWorkorderDetailList, warehouseCache, otcPrepWorkorderList);
        List<RequestProductRespDTO> list = Stream.concat(respStream, stream).toList();
        //返回结果
        return Result.ok(list);
    }
}

