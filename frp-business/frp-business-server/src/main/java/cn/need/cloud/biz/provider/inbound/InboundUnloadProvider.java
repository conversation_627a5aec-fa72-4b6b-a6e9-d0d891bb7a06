package cn.need.cloud.biz.provider.inbound;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.api.inbound.InboundUnloadClient;
import cn.need.cloud.biz.client.api.path.InboundUnloadPath;
import cn.need.cloud.biz.client.dto.base.BaseProductVersionDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.inbound.InboundUnloadQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundUnloadRespDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundPutawaySlipDetail;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail;
import cn.need.cloud.biz.provider.base.ProductVersionUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.inbound.InboundPutawaySlipDetailService;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderDetailService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderService;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 入库卸货单feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(InboundUnloadPath.PREFIX)
public class InboundUnloadProvider implements InboundUnloadClient {
    @Resource
    private InboundRequestService inboundRequestService;
    @Resource
    private InboundPutawaySlipDetailService inboundPutawaySlipDetailService;
    @Resource
    private InboundWorkorderService inboundWorkorderService;
    @Resource
    private InboundWorkorderDetailService inboundWorkorderDetailService;

    @Override
    @PostMapping(value = InboundUnloadPath.LIST_BY_REQUEST)
    @IgnoreAuth
    public Result<List<InboundUnloadRespDTO>> listByRequest(@RequestBody InboundUnloadQueryReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        inboundRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getReqDTO()));
        //获取工单id
        InboundWorkorder workorder = inboundWorkorderService.getByRequestId(reqDTO.getRequestId());

        //说明还没开始
        if (ObjectUtil.isEmpty(workorder)) {
            return Result.ok(Lists.arrayList());
        }

        //获取上架详情
        List<InboundPutawaySlipDetail> putawaySlipDetailList = inboundPutawaySlipDetailService.listByWorkorderId(workorder.getId());
        //判空
        if (ObjectUtil.isEmpty(putawaySlipDetailList)) {
            return Result.ok(Lists.arrayList());
        }

        //收集所有上架详情的工单详情ID
        Set<Long> workorderDetailIds = putawaySlipDetailList.stream()
                .map(InboundPutawaySlipDetail::getInboundWorkorderDetailId)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toSet());

        //获取工单详情
        List<InboundWorkorderDetail> workorderDetails = inboundWorkorderDetailService.listByIds(workorderDetailIds);

        //创建工单详情ID到工单详情的映射
        Map<Long, InboundWorkorderDetail> workorderDetailMap = workorderDetails.stream()
                .collect(Collectors.toMap(InboundWorkorderDetail::getId, detail -> detail));

        //组装返回对象
        List<Long> productVersionIdList = putawaySlipDetailList.stream()
                .map(InboundPutawaySlipDetail::getProductVersionId)
                .distinct()
                .filter(ObjectUtil::isNotEmpty)
                .toList();
        //产品信息
        Map<Long, BaseProductVersionDTO> map = ProductVersionUtil.listByProductVersionId(productVersionIdList);
        //获取仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(workorder.getWarehouseId());
        //遍历上架单详情
        List<InboundUnloadRespDTO> list = putawaySlipDetailList.stream()
                .map(item -> {
                    InboundUnloadRespDTO inboundUnloadRespDTO = BeanUtil.copyNew(item, InboundUnloadRespDTO.class);
                    inboundUnloadRespDTO.setBaseProductVersionDTO(map.get(item.getProductVersionId()));
                    inboundUnloadRespDTO.setBaseWarehouseDTO(BeanUtil.copyNew(warehouseCache, BaseWarehouseDTO.class));

                    //从工单详情中获取并填充额外字段
                    InboundWorkorderDetail workorderDetail = workorderDetailMap.get(item.getInboundWorkorderDetailId());
                    if (workorderDetail != null) {
                        inboundUnloadRespDTO.setLineNum(workorderDetail.getLineNum());
                        inboundUnloadRespDTO.setDetailType(workorderDetail.getRequestDetailSnapshotDetailType());
                        inboundUnloadRespDTO.setDetailRequestRefNum(workorderDetail.getRequestDetailSnapshotDetailRequestRefNum());
                    }

                    return inboundUnloadRespDTO;
                }).toList();
        //返回结果
        return Result.ok(list);
    }

}
