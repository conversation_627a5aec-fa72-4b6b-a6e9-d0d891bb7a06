package cn.need.cloud.biz.converter.product;


import cn.need.cloud.biz.client.dto.product.ProductVersionDTO;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品版本详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductVersionConverter extends AbstractModelConverter<ProductVersion, ProductVersionVO, ProductVersionDTO> {

}
