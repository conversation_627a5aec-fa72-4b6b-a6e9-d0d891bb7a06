package cn.need.cloud.biz.controller.setting;

import cn.need.cloud.biz.converter.setting.SequenceConverter;
import cn.need.cloud.biz.model.entity.setting.Sequence;
import cn.need.cloud.biz.model.param.setting.create.SequenceCreateParam;
import cn.need.cloud.biz.model.param.setting.update.SequenceUpdateParam;
import cn.need.cloud.biz.model.query.setting.SequenceQuery;
import cn.need.cloud.biz.model.vo.page.SequencePageVO;
import cn.need.cloud.biz.model.vo.setting.SequenceVO;
import cn.need.cloud.biz.service.setting.SequenceService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 全局序列号ref 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/sequence")
@Tag(name = "全局序列号ref")
public class SequenceController extends AbstractRestController<SequenceService, Sequence, SequenceConverter, SequenceVO> {

    @Operation(summary = "新增全局序列号ref", description = "接收全局序列号ref的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SequenceCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改全局序列号ref", description = "接收全局序列号ref的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) SequenceUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除全局序列号ref", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取全局序列号ref详情", description = "根据数据主键id，从数据库中获取其对应的全局序列号ref详情")
    @GetMapping(value = "/detail/{id}")
    public Result<SequenceVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取全局序列号ref详情
        SequenceVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取全局序列号ref分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的全局序列号ref列表")
    @PostMapping(value = "/list")
    public Result<PageData<SequencePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<SequenceQuery> search) {

        // 获取全局序列号ref分页
        PageData<SequencePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
