package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoWorkorderDTO;
import cn.need.cloud.biz.model.entity.oco.OcoWorkorder;
import cn.need.cloud.biz.model.vo.oco.OcoWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO工单表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoWorkorderConverter extends AbstractModelConverter<OcoWorkorder, OcoWorkorderVO, OcoWorkorderDTO> {

}



