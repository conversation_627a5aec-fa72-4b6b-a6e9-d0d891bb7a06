package cn.need.cloud.biz.converter.otb;


import cn.need.cloud.biz.client.dto.otb.OtbPackageLabelDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPackageLabel;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB包裹标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPackageLabelConverter extends AbstractModelConverter<OtbPackageLabel, OtbPackageLabelVO, OtbPackageLabelDTO> {

}
