package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPackageDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPackageDetail;
import cn.need.cloud.biz.model.vo.otc.pkg.OtcPackageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC包裹详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPackageDetailConverter extends AbstractModelConverter<OtcPackageDetail, OtcPackageDetailVO, OtcPackageDetailDTO> {

}
