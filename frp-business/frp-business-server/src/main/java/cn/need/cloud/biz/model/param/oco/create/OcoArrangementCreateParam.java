package cn.need.cloud.biz.model.param.oco.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;


/**
 * OCO安排表 CreateParam对象
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@Schema(description = "OCO安排表 CreateParam对象")
public class OcoArrangementCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -8937018296040585659L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 集装箱编号
     */
    @Schema(description = "集装箱编号")
    @Size(max = 255, message = "containerNum cannot exceed 255 characters")
    private String containerNum;
    /**
     * 集装箱模板ID
     */
    @Schema(description = "集装箱模板ID")
    private Long containerTemplateId;

    /**
     * 发货地址1
     */
    @Schema(description = "发货地址1")
    @Size(max = 255, message = "shipFromAddressAddr1 cannot exceed 255 characters")
    private String shipFromAddressAddr1;
    /**
     * 发货地址2
     */
    @Schema(description = "发货地址2")
    @Size(max = 255, message = "shipFromAddressAddr2 cannot exceed 255 characters")
    private String shipFromAddressAddr2;
    /**
     * 发货地址3
     */
    @Schema(description = "发货地址3")
    @Size(max = 255, message = "shipFromAddressAddr3 cannot exceed 255 characters")
    private String shipFromAddressAddr3;
    /**
     * 发货地址城市
     */
    @Schema(description = "发货地址城市")
    @Size(max = 100, message = "shipFromAddressCity cannot exceed 100 characters")
    private String shipFromAddressCity;
    /**
     * 发货地址公司
     */
    @Schema(description = "发货地址公司")
    @Size(max = 255, message = "shipFromAddressCompany cannot exceed 255 characters")
    private String shipFromAddressCompany;
    /**
     * 发货地址国家
     */
    @Schema(description = "发货地址国家")
    @Size(max = 100, message = "shipFromAddressCountry cannot exceed 100 characters")
    private String shipFromAddressCountry;
    /**
     * 发货地址邮箱
     */
    @Schema(description = "发货地址邮箱")
    @Size(max = 255, message = "shipFromAddressEmail cannot exceed 255 characters")
    private String shipFromAddressEmail;

    /**
     * 发货地址是否为住宅地址
     */
    @Schema(description = "发货地址是否为住宅地址")
    private Boolean shipFromAddressIsResidential;
    /**
     * 发货地址名称
     */
    @Schema(description = "发货地址名称")
    @Size(max = 255, message = "shipFromAddressName cannot exceed 255 characters")
    private String shipFromAddressName;
    /**
     * 发货地址备注
     */
    @Schema(description = "发货地址备注")
    @Size(max = 512, message = "shipFromAddressNote cannot exceed 512 characters")
    private String shipFromAddressNote;
    /**
     * 发货地址电话
     */
    @Schema(description = "发货地址电话")
    @Size(max = 100, message = "shipFromAddressPhone cannot exceed 100 characters")
    private String shipFromAddressPhone;
    /**
     * 发货地址州/省
     */
    @Schema(description = "发货地址州/省")
    @Size(max = 100, message = "shipFromAddressState cannot exceed 100 characters")
    private String shipFromAddressState;
    /**
     * 发货地址邮编
     */
    @Schema(description = "发货地址邮编")
    @Size(max = 50, message = "shipFromAddressZipCode cannot exceed 50 characters")
    private String shipFromAddressZipCode;
    /**
     * 收货地址1
     */
    @Schema(description = "收货地址1")
    @Size(max = 255, message = "shipToAddressAddr1 cannot exceed 255 characters")
    private String shipToAddressAddr1;
    /**
     * 收货地址2
     */
    @Schema(description = "收货地址2")
    @Size(max = 255, message = "shipToAddressAddr2 cannot exceed 255 characters")
    private String shipToAddressAddr2;
    /**
     * 收货地址3
     */
    @Schema(description = "收货地址3")
    @Size(max = 255, message = "shipToAddressAddr3 cannot exceed 255 characters")
    private String shipToAddressAddr3;
    /**
     * 收货地址城市
     */
    @Schema(description = "收货地址城市")
    @Size(max = 100, message = "shipToAddressCity cannot exceed 100 characters")
    private String shipToAddressCity;
    /**
     * 收货地址公司
     */
    @Schema(description = "收货地址公司")
    @Size(max = 255, message = "shipToAddressCompany cannot exceed 255 characters")
    private String shipToAddressCompany;
    /**
     * 收货地址国家
     */
    @Schema(description = "收货地址国家")
    @Size(max = 100, message = "shipToAddressCountry cannot exceed 100 characters")
    private String shipToAddressCountry;
    /**
     * 收货地址邮箱
     */
    @Schema(description = "收货地址邮箱")
    @Size(max = 255, message = "shipToAddressEmail cannot exceed 255 characters")
    private String shipToAddressEmail;
    /**
     * 收货地址是否为住宅地址
     */
    @Schema(description = "收货地址是否为住宅地址")
    private Boolean shipToAddressIsResidential;
    /**
     * 收货地址名称
     */
    @Schema(description = "收货地址名称")
    @Size(max = 255, message = "shipToAddressName cannot exceed 255 characters")
    private String shipToAddressName;
    /**
     * 收货地址备注
     */
    @Schema(description = "收货地址备注")
    @Size(max = 512, message = "shipToAddressNote cannot exceed 512 characters")
    private String shipToAddressNote;
    /**
     * 收货地址电话
     */
    @Schema(description = "收货地址电话")
    @Size(max = 100, message = "shipToAddressPhone cannot exceed 100 characters")
    private String shipToAddressPhone;
    /**
     * 收货地址州/省
     */
    @Schema(description = "收货地址州/省")
    @Size(max = 100, message = "shipToAddressState cannot exceed 100 characters")
    private String shipToAddressState;
    /**
     * 收货地址邮编
     */
    @Schema(description = "收货地址邮编")
    @Size(max = 50, message = "shipToAddressZipCode cannot exceed 50 characters")
    private String shipToAddressZipCode;


    /**
     * 安排详情列表
     */
    @Schema(description = "安排详情列表")
    @Valid
    private List<OcoArrangementDetailCreateParam> detailList;

}

