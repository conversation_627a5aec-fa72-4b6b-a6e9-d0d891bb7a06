package cn.need.cloud.biz.service.transfer.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorConstant;
import cn.need.cloud.biz.client.constant.enums.transfer.AuditResultType;
import cn.need.cloud.biz.client.constant.enums.transfer.TransferOwnerShipRequestStatus;
import cn.need.cloud.biz.converter.TransferOwnerShipRequestConverter;
import cn.need.cloud.biz.mapper.TransferOwnerShipRequestMapper;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequestDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestCreateParam;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestDetailCreateParam;
import cn.need.cloud.biz.model.query.TransferOwnerShipRequestQuery;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.auditlog.BaseProductLogVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryInStockVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestPageVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.helper.auditshowlog.TransferOwnerShipRequestAuditLogHelper;
import cn.need.cloud.biz.service.inventory.InventoryService;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestDetailService;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 货权转移 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@Service
public class TransferOwnerShipRequestServiceImpl extends SuperServiceImpl<TransferOwnerShipRequestMapper, TransferOwnerShipRequest> implements TransferOwnerShipRequestService {

    @Resource
    private TransferOwnerShipRequestDetailService transferOwnerShipRequestDetailService;
    @Resource
    private InventoryService inventoryService;
    @Resource
    private BinLocationDetailService binLocationDetailService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOwnerShipRequest insertByParam(TransferOwnerShipRequestCreateParam createParam) {
        // 检查传入货权转移参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 校验RequestNum
        this.checkRequestNum(createParam);

        // Check 产品
        this.checkDetails(createParam);

        // 将货权转移参数对象转换为实体对象并初始化
        TransferOwnerShipRequest entity = initTransferOwnerShipRequest(createParam);

        // Details
        var details = BeanUtil.copyNew(createParam.getDetailList(), TransferOwnerShipRequestDetail.class);
        for (int idx = 0; idx < details.size(); idx++) {
            var detail = details.get(idx);
            detail.setLineNum(idx + 1);
            detail.setHeaderId(entity.getId());
        }
        transferOwnerShipRequestDetailService.insertBatch(details);

        // 插入货权转移实体对象到数据库
        super.insert(entity);

        // 记录日志
        TransferOwnerShipRequestAuditLogHelper.recordLog(entity);

        // 返回货权转移ID
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(TransferOwnerShipRequest request, AuditResultType audit) {
        Validate.isTrue(TransferOwnerShipRequestStatus.canAudit().contains(request.getRequestStatus()),
                ErrorConstant.STATUS_ERROR_FORMAT,
                request.refNumLog(), "audit", TransferOwnerShipRequestStatus.canAudit(), request.getRequestStatus()
        );

        switch (audit) {
            case REJECT:
                request.setRequestStatus(TransferOwnerShipRequestStatus.REJECTED.getStatus());
                break;
            case AGREE:
                request.setRequestStatus(TransferOwnerShipRequestStatus.APPROVED.getStatus());
                break;
            default:
                throw new IllegalArgumentException("Invalid audit type");
        }

        TransferOwnerShipRequestAuditLogHelper.recordLog(request);

        // 非拒绝
        if (!Objects.equals(request.getRequestStatus(), TransferOwnerShipRequestStatus.REJECTED.getStatus())) {
            this.processInventoryTransfer(request);
        }

        //更新数据库
        super.update(request);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransferOwnerShipRequest createWithAudit(TransferOwnerShipRequestCreateParam input, AuditResultType audit) {
        TransferOwnerShipRequest request = this.insertByParam(input);
        this.audit(request, audit);
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TransferOwnerShipRequest> batchCreateWithAudit(List<TransferOwnerShipRequestCreateParam> inputList, AuditResultType audit) {
        // 检查传入参数是否为空
        if (ObjectUtil.isEmpty(inputList)) {
            throw new BusinessException("批量创建参数列表不能为空");
        }

        List<TransferOwnerShipRequest> resultList = new ArrayList<>();

        // 批量处理，在同一个事务中执行
        for (TransferOwnerShipRequestCreateParam input : inputList) {
            TransferOwnerShipRequest request = this.insertByParam(input);
            this.audit(request, audit);
            resultList.add(request);
        }

        return resultList;
    }

    @Override
    public List<TransferOwnerShipRequestPageVO> listByQuery(TransferOwnerShipRequestQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<TransferOwnerShipRequestPageVO> pageByQuery(PageSearch<TransferOwnerShipRequestQuery> search) {
        Page<TransferOwnerShipRequest> page = Conditions.page(search, entityClass);
        List<TransferOwnerShipRequestPageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        // 填充合作伙伴信息
        fillPartnerInfoForPageVO(dataList);

        return new PageData<>(dataList, page);
    }

    @Override
    public TransferOwnerShipRequestVO detailById(Long id) {
        TransferOwnerShipRequest entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in TransferOwnerShipRequest");
        }
        return buildTransferOwnerShipRequestVO(entity);
    }

    @Override
    public TransferOwnerShipRequestVO detailByRefNum(String refNum) {
        TransferOwnerShipRequest entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in TransferOwnerShipRequest");
        }
        return buildTransferOwnerShipRequestVO(entity);
    }


    /**
     * 构建货权转移VO对象
     *
     * @param entity 货权转移对象
     * @return 返回包含详细信息的货权转移VO对象
     */
    private TransferOwnerShipRequestVO buildTransferOwnerShipRequestVO(TransferOwnerShipRequest entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的货权转移VO对象
        return Converters.get(TransferOwnerShipRequestConverter.class).toVO(entity);
    }

    /**
     * 为分页VO列表填充合作伙伴信息
     *
     * @param dataList 分页VO列表
     */
    private void fillPartnerInfoForPageVO(List<TransferOwnerShipRequestPageVO> dataList) {
        if (ObjectUtil.isEmpty(dataList)) {
            return;
        }

        // 收集所有合作伙伴ID
        Set<Long> partnerIds = new HashSet<>();
        dataList.forEach(vo -> {
            if (vo.getFromPartnerId() != null) {
                partnerIds.add(vo.getFromPartnerId());
            }
            if (vo.getToPartnerId() != null) {
                partnerIds.add(vo.getToPartnerId());
            }
        });

        if (partnerIds.isEmpty()) {
            return;
        }

        // 批量获取租户缓存信息
        Map<Long, TenantCache> tenantCacheMap = StreamUtils.toMap(
                TenantCacheUtil.listByIds(partnerIds),
                TenantCache::getId
        );

        // 填充合作伙伴信息
        dataList.forEach(vo -> {
            // 填充源合作伙伴信息
            if (vo.getFromPartnerId() != null) {
                TenantCache fromTenantCache = tenantCacheMap.get(vo.getFromPartnerId());
                if (fromTenantCache != null) {
                    vo.setFromPartnerVO(BeanUtil.copyNew(fromTenantCache, BasePartnerVO.class));
                }
            }

            // 填充目标合作伙伴信息
            if (vo.getToPartnerId() != null) {
                TenantCache toTenantCache = tenantCacheMap.get(vo.getToPartnerId());
                if (toTenantCache != null) {
                    vo.setToPartnerVO(BeanUtil.copyNew(toTenantCache, BasePartnerVO.class));
                }
            }
        });
    }


    /**
     * 检查产品
     *
     * @param createParam 创建参数
     */
    private void checkDetails(TransferOwnerShipRequestCreateParam createParam) {
        // 租户不能一样
        Validate.isTrue(!createParam.getFromPartnerId().equals(createParam.getToPartnerId()),
                "FromPartner: {} & ToPartner:{} Can Not Be Same Partner",
                createParam.getFromPartnerId(), createParam.getToPartnerId()
        );

        // // Check for duplicate products
        // Map<Long, Long> productCounts = createParam.getDetailList().stream()
        //         .collect(Collectors.groupingBy(TransferOwnerShipRequestDetailCreateParam::getFromProductId, Collectors.counting()));
        //
        // productCounts.forEach((productId, count) -> Validate.isTrue(count <= 1, "Product {} is repeated in details", productId));

        List<Long> productIds = createParam.getDetailList()
                .stream()
                .flatMap(obj -> Stream.of(obj.getFromProductId(), obj.getToProductId()))
                .toList();

        Map<Long, ProductCache> productCacheMap = StreamUtils.toMap(ProductCacheUtil.listByIds(productIds), ProductCache::getId);

        // 租户
        Map<Long, TenantCache> tenantCacheMap = StreamUtils.toMap(
                TenantCacheUtil.listByIds(Arrays.asList(createParam.getFromPartnerId(), createParam.getToPartnerId())),
                TenantCache::getId
        );
        // 租户
        var fromPartnerLog = BeanUtil.copyNew(tenantCacheMap.get(createParam.getFromPartnerId()), BasePartnerVO.class);
        var toPartnerLog = BeanUtil.copyNew(tenantCacheMap.get(createParam.getToPartnerId()), BasePartnerVO.class);

        for (TransferOwnerShipRequestDetailCreateParam detail : createParam.getDetailList()) {
            ProductCache fromProduct = productCacheMap.get(detail.getFromProductId());
            ProductCache toProduct = productCacheMap.get(detail.getToProductId());

            var fromProductLog = BeanUtil.copyNew(fromProduct, BaseProductLogVO.class).toLog();
            var toProductLog = BeanUtil.copyNew(toProduct, BaseProductLogVO.class).toLog();

            Validate.isTrue(fromProduct.getTransactionPartnerId().equals(createParam.getFromPartnerId()),
                    "{} Not Belong To {}", fromProductLog, fromPartnerLog
            );

            Validate.isTrue(toProduct.getTransactionPartnerId().equals(createParam.getToPartnerId()),
                    "{} Not Belong To {}", toProductLog, toPartnerLog
            );

            //var type = TransferOwnerShipType.typeOf(createParam.getTransferOwnerShipType());
            // Validate.notNull(type, "TransferOwnerShipType: {} is illegal type, Please enter a valid type {} ",
            //         createParam.getTransferOwnerShipType(), Arrays.stream(TransferOwnerShipType.values())
            //                 .map(TransferOwnerShipType::getType)
            //                 .collect(Collectors.joining(StringPool.COMMA))
            // );

            switch (createParam.getTransferOwnerShipType()) {
                case TRANSFER_OWNERSHIP:
                    Validate.isTrue(fromProduct.getSupplierSku().equals(toProduct.getSupplierSku()),
                            "{} And {} SupplierSku Must Same", fromProductLog, toProductLog
                    );
                    break;
                case RE_LABEL:
                    throw new BusinessException("ReLabel type is not supported");
                default:
                    throw new IllegalArgumentException("Invalid TransferOwnerShipType");
            }
        }
    }

    /**
     * 校验RequestNum
     *
     * @param createParam 创建参数
     */
    private void checkRequestNum(TransferOwnerShipRequestCreateParam createParam) {
        Long count = this.lambdaQuery()
                .eq(TransferOwnerShipRequest::getRequestRefNum, createParam.getRequestRefNum())
                .count();

        Validate.isTrue(count == 0,
                "RequestRefNum: {} TransferOwnerShipRequest is exist",
                createParam.getRequestRefNum()
        );
    }


    /**
     * 初始化货权转移对象
     * 此方法用于设置货权转移对象的必要参数，确保其处于有效状态
     *
     * @param createParam 货权转移 新增对象，不应为空
     * @throws BusinessException 如果传入的货权转移为空，则抛出此异常
     */
    private TransferOwnerShipRequest initTransferOwnerShipRequest(TransferOwnerShipRequestCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("TransferOwnerShipRequestCreateParam cannot be empty");
        }

        // 获取货权转移转换器实例，用于将货权转移参数对象转换为实体对象
        TransferOwnerShipRequestConverter converter = Converters.get(TransferOwnerShipRequestConverter.class);

        // 将货权转移参数对象转换为实体对象并初始化
        TransferOwnerShipRequest entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());

        // 生成RefNum
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.TRANSFER_OWNER_SHIP_REQUEST));

        entity.setRequestStatus("New");


        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 处理库存锁转换
     *
     * @param request 请求
     */
    private void processInventoryTransfer(TransferOwnerShipRequest request) {
        List<TransferOwnerShipRequestDetail> details = transferOwnerShipRequestDetailService.listByHeaderId(request.getId());

        var allProductIds = details.stream()
                .flatMap(obj -> Stream.of(obj.getFromProductId(), obj.getToProductId()))
                .toList();

        var productCacheMap = StreamUtils.toMap(ProductCacheUtil.listByIds(allProductIds), ProductCache::getId);

        List<Long> productLatestIds = details.stream()
                .map(TransferOwnerShipRequestDetail::getFromProductId)
                .distinct()
                .collect(Collectors.toList());

        List<InventoryInStockVO> inventories = inventoryService.getInventoryInStock(productLatestIds);

        for (TransferOwnerShipRequestDetail detail : details) {
            ProductCache fromProduct = productCacheMap.get(detail.getFromProductId());
            ProductCache toProduct = productCacheMap.get(detail.getToProductId());

            InventoryInStockVO inventory = inventories.stream()
                    .filter(i -> i.getProductId().equals(detail.getFromProductId()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException(StringUtil.format("{} Inventory not found for product {}",
                            request.refNumLog() + " " + request.getRequestRefNum(),
                            Optional.ofNullable(fromProduct)
                                    .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                                    .map(BaseProductLogVO::toLog)
                                    .orElse(StringPool.EMPTY))
                    ));

            Validate.isTrue(inventory.getInStockCanAllocateQty() >= detail.getTransferQty(),
                    "{}: {} not enough BinLocation InStockQty To Transfer, Current BinLocation All Can TransferQty is {}, But required Transfer:{}",
                    request.refNumLog() + " " + request.getRequestRefNum(),
                    Optional.ofNullable(fromProduct)
                            .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                            .map(BaseProductLogVO::toLog)
                            .orElse(StringPool.EMPTY),
                    inventory.getInStockCanAllocateQty(), detail.getTransferQty()
            );

            // 库位详情处理转移
            this.processBinLocationTransfer(detail, fromProduct, toProduct, request);
        }
    }

    /**
     * 库位详情货权转移处理
     *
     * @param detail      货权转移详情
     * @param fromProduct fromProduct
     * @param toProduct   toProduct
     * @param request     货权转移
     */
    private void processBinLocationTransfer(
            TransferOwnerShipRequestDetail detail,
            ProductCache fromProduct,
            ProductCache toProduct,
            TransferOwnerShipRequest request) {
        List<BinLocationDetail> binLocations = binLocationDetailService.listByProductId(detail.getFromProductId());

        int totalQty = detail.getTransferQty();
        var changeList = new ArrayList<BinLocationDetailChangeBO>();
        for (BinLocationDetail binLocationDetail : binLocations.stream()
                .sorted(Comparator.comparing(BinLocationDetail::getInStockQty))
                .toList()) {

            if (totalQty <= 0) {
                break;
            }
            if (binLocationDetail.getInStockQty() <= 0) {
                continue;
            }

            int currentTransferQty = Math.min(binLocationDetail.getInStockQty(), totalQty);

            // Process To BinLocation
            BinLocationDetail toBinLocationDetail = binLocationDetailService
                    .getOrGenerateBinLocationDetailByBinLocationIdAndProductId(
                            binLocationDetail.getBinLocationId(), detail.getToProductId()
                    );

            if (ObjectUtil.isEmpty(toBinLocationDetail)) {
                throw new BusinessException(StringUtil.format("{} Can not Found toBinLocationDetail", detail));
            }

            {
                // source
                var change = new BinLocationDetailChangeBO();
                change.setSource(binLocationDetail);
                change.setDest(binLocationDetail);
                change.setChangeQty(currentTransferQty);
                change.setChangeType(TransferOwnerShipRequest.class.getSimpleName());

                var refTable = new RefTableBO();
                refTable.setRefTableId(detail.getId());
                refTable.setRefTableName(detail.getClass().getSimpleName());
                refTable.setRefTableRefNum(String.valueOf(detail.getLineNum()));
                refTable.setRefTableShowName(request.getClass()
                        .getSimpleName());
                refTable.setRefTableShowRefNum(request.getRefNum());
                change.setNote(request.getNote());
                change.setRefTable(refTable);

                changeList.add(change);
            }

            {
                // dest
                var change = new BinLocationDetailChangeBO();
                change.setSource(toBinLocationDetail);
                change.setDest(toBinLocationDetail);
                change.setChangeQty(-currentTransferQty);
                change.setChangeType(TransferOwnerShipRequest.class.getSimpleName());

                var refTable = new RefTableBO();
                refTable.setRefTableId(detail.getId());
                refTable.setRefTableName(detail.getClass().getSimpleName());
                refTable.setRefTableRefNum(String.valueOf(detail.getLineNum()));
                refTable.setRefTableShowName(request.getClass()
                        .getSimpleName());
                refTable.setRefTableShowRefNum(request.getRefNum());
                change.setRefTable(refTable);
                change.setNote(request.getNote());
                changeList.add(change);
            }


            totalQty -= currentTransferQty;
        }

        Validate.isTrue(totalQty == 0, "{} TransferQty: {} {} Not All Transfer",
                request.refNumLog(),
                Optional.ofNullable(fromProduct)
                        .map(obj -> BeanUtil.copyNew(obj, BaseProductLogVO.class))
                        .map(BaseProductLogVO::toLog)
                        .orElse(StringPool.EMPTY),
                detail.getTransferQty()
        );

        binLocationDetailService.updateInStockByChange(changeList);
    }

}
