package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.converter.otc.OtcPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.pickingslip.OtcPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.vo.base.UnpickVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipSpecialService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.redis.RedissonKit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * OTC拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-picking-slip/special")
@Tag(name = "OTC拣货单-Special")
@Validated
public class OtcPickingSlipSpecialController extends AbstractRestController<OtcPickingSlipService, OtcPickingSlip, OtcPickingSlipConverter, OtcPickingSlipVO> {

    @Resource
    private OtcPickingSlipSpecialService specialService;

    @Operation(summary = "Unpick", description = "Unpick")
    @PostMapping(value = "/unpick")
    public Result<Boolean> unpick(@RequestBody @Valid OtcPickingSlipUnpickCreateParam query) {
        RedissonKit.getInstance().lock(RedisConstant.OTC_PUTAWAY_SLIP_CREATE_LOCK_PREFIX + query.getWorkorderId(), lock -> {
            specialService.unpick(query);
        });
        return success(true);
    }

    @Operation(summary = "可Unpick列表", description = "Unpick列表")
    @GetMapping(value = "/unpick/list/{workorderId}")
    public Result<UnpickVO> unpickByWorkorderId(@PathVariable(name = "workorderId") Long workorderId) {

        return success(specialService.unpickByWorkorderId(workorderId));
    }

    @Operation(summary = "Batch Cancel", description = "Batch Cancel")
    @PostMapping(value = "/batch-cancel")
    public Result<Boolean> batchCancel(@RequestBody @Valid PickingSlipCancelUpdateParam param) {
        return success(specialService.batchCancel(param));
    }
}
