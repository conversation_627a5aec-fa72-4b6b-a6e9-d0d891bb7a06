package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeOtbDTO;
import cn.need.cloud.biz.model.entity.fee.FeeOtb;
import cn.need.cloud.biz.model.vo.fee.FeeOtbVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用otb 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeOtbConverter extends AbstractModelConverter<FeeOtb, FeeOtbVO, FeeOtbDTO> {

}
