package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeOtbConverter;
import cn.need.cloud.biz.model.entity.fee.FeeOtb;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbBuildParam;
import cn.need.cloud.biz.model.param.fee.create.FeeOtbCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOtbUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOtbQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtbVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtbPageVO;
import cn.need.cloud.biz.service.fee.FeeOtbBuildService;
import cn.need.cloud.biz.service.fee.FeeOtbService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 费用otb 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-otb")
@Tag(name = "费用otb")
public class FeeOtbController extends AbstractRestController<FeeOtbService, FeeOtb, FeeOtbConverter, FeeOtbVO> {

    @Resource
    private FeeOtbBuildService feeOtbBuildService;

    @Operation(summary = "新增费用otb", description = "接收费用otb的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) FeeOtbCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改费用otb", description = "接收费用otb的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) FeeOtbUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除费用otb", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取费用otb详情", description = "根据数据主键id，从数据库中获取其对应的费用otb详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeOtbVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用otb详情", description = "根据数据RefNum，从数据库中获取其对应的费用otb详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeOtbVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用otb分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用otb列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeOtbPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeOtbQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "构建OTB费用", description = "根据费用原始数据构建OTB费用")
    @PostMapping(value = "/build")
    public Result<Void> build(@Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeOtbBuildParam buildParam) {
        feeOtbBuildService.build(buildParam);
        return success();
    }
}
