package cn.need.cloud.biz.converter.profile;

import cn.need.cloud.biz.client.dto.proflie.ProfileWarehouseDTO;
import cn.need.cloud.biz.model.entity.setting.ProfileWarehouse;
import cn.need.cloud.biz.model.vo.profile.ProfileWarehouseVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库档案 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProfileWarehouseConverter extends AbstractModelConverter<ProfileWarehouse, ProfileWarehouseVO, ProfileWarehouseDTO> {

}
