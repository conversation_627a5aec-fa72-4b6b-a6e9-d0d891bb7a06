package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPackageConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.model.param.otb.create.pkg.OtbPackageCreateParam;
import cn.need.cloud.biz.model.query.otb.pkg.OtbPackageQuery;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbBuildPackageVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.cloud.biz.model.vo.page.OtbPackagePageVO;
import cn.need.cloud.biz.service.otb.pkg.OtbBuildPackageService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * OTB包裹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-package")
@Tag(name = "OTB包裹")
public class OtbPackageController extends AbstractRestController<OtbPackageService, OtbPackage, OtbPackageConverter, OtbPackageVO> {

    @Resource
    private OtbBuildPackageService otbBuildPackageService;

    @Operation(summary = "打包装箱", description = "打包装箱")
    @PostMapping(value = "/build")
    public Result<List<OtbBuildPackageVO>> build(@RequestBody @Parameter(description = "数据主键id", required = true) OtbPackageCreateParam param) {

        // 执行删除并返回结果
        return success(otbBuildPackageService.build(param));
    }

    @Operation(summary = "根据id获取OTB包裹详情", description = "根据数据主键id，从数据库中获取其对应的OTB包裹详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPackageVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB包裹详情
        OtbPackageVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB包裹详情", description = "根据数据RefNum，从数据库中获取其对应的OTB包裹详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPackageVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB包裹详情
        OtbPackageVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据SsccNum获取OTB包裹详情", description = "根据数据SsccNum，从数据库中获取其对应的OTB包裹详情")
    @GetMapping(value = "/detail-by-sscc-num/{packageSsccNum}")
    public Result<OtbPackageVO> detailBySsccNum(@PathVariable("packageSsccNum") @Parameter(description = "包裹SsccNum", required = true) String packageSsccNum) {

        // 获取OTB包裹详情
        OtbPackageVO detailVo = service.detailBySsccNum(packageSsccNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTB包裹分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB包裹列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPackagePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPackageQuery> search) {

        // 获取OTB包裹分页
        PageData<OtbPackagePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
