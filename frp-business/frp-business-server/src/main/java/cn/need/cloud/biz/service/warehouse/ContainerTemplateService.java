package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.ContainerTemplate;
import cn.need.cloud.biz.model.param.warehouse.create.ContainerTemplateCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.ContainerTemplateUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.ContainerTemplateQuery;
import cn.need.cloud.biz.model.vo.page.ContainerTemplatePageVO;
import cn.need.cloud.biz.model.vo.warehouse.ContainerTemplateVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;


/**
 * <p>
 * 集装箱模板配置表 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public interface ContainerTemplateService extends SuperService<ContainerTemplate> {

    /**
     * 根据参数新增集装箱模板配置表
     *
     * @param createParam 请求创建参数，包含需要插入的集装箱模板配置表的相关信息
     * @return 集装箱模板配置表ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(ContainerTemplateCreateParam createParam);


    /**
     * 根据参数更新集装箱模板配置表
     *
     * @param updateParam 请求创建参数，包含需要更新的集装箱模板配置表的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(ContainerTemplateUpdateParam updateParam);

    /**
     * 根据查询条件获取集装箱模板配置表列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个集装箱模板配置表对象的列表(分页)
     */
    List<ContainerTemplatePageVO> listByQuery(ContainerTemplateQuery query);

    /**
     * 根据查询条件获取集装箱模板配置表列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个集装箱模板配置表对象的列表(分页)
     */
    PageData<ContainerTemplatePageVO> pageByQuery(PageSearch<ContainerTemplateQuery> search);

    /**
     * 根据ID获取集装箱模板配置表
     *
     * @param id 集装箱模板配置表ID
     * @return 返回集装箱模板配置表VO对象
     */
    ContainerTemplateVO detailById(Long id);

    /**
     * 根据集装箱模板配置表唯一编码获取集装箱模板配置表
     *
     * @param refNum 集装箱模板配置表唯一编码
     * @return 返回集装箱模板配置表VO对象
     */
    ContainerTemplateVO detailByRefNum(String refNum);

}
