package cn.need.cloud.biz.controller.inventory;

import cn.need.cloud.biz.model.query.inventory.InventoryQuery;
import cn.need.cloud.biz.model.query.log.BinLocationLogChainQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInStockVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogChainVO;
import cn.need.cloud.biz.service.inventory.InventoryService;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */

@RestController
@RequestMapping("/api/biz/inventory")
@Slf4j
@Tag(name = "库存展示管理")
public class InventoryController extends AbstractController {

    @Resource
    private InventoryService inventoryService;

    @Resource
    private BinLocationLogService binLocationLogService;

    @Operation(summary = "根据产品ids获取库位InStock信息", description = "根据产品ids获取库位InStock信息")
    @PostMapping(value = "/inventoryInStockByProductIds")
    public Result<List<InventoryInStockVO>> getInventoryInStock(@RequestBody @Parameter(description = "产品ids", required = true) List<Long> productIds) {

        // 返回结果
        return success(inventoryService.getInventoryInStock(productIds));
    }

    /**
     * 根据ProductId 获取产品库存
     *
     * @param productIds 产品ID列表
     * @return 库存信息
     */
    @Operation(summary = "根据产品ids获取库位信息", description = "根据产品ids获取库位InStock信息")
    @PostMapping(value = "/inventoryByProductIds")
    public Result<List<InventoryVO>> getInventory(@RequestBody @Parameter(description = "产品ids", required = true) List<Long> productIds) {

        // 返回结果
        return success(inventoryService.getInventory(productIds));
    }

    /**
     * 根据ProductId，获取所有仓库库存
     *
     * @param productId 产品ID
     * @return 库存信息
     */
    @Operation(summary = "根据ProductId，获取所有仓库库存", description = "根据ProductId，获取所有仓库库存")
    @GetMapping(value = "/listWarehouseInventoryListByProductId/{productId}")
    public Result<List<InventoryVO>> listWarehouseInventoryListByProductId(@PathVariable("productId") @Parameter(description = "产品id", required = true) Long productId) {


        List<InventoryVO> list = inventoryService.listWarehouseInventoryListByProductId(productId);

        // 返回结果
        return success(list);
    }

    /**
     * 分页查询库存
     *
     * @param search InventoryQuery
     * @return 库存
     */
    @Operation(summary = "获取库存分页列表", description = "根据InventoryQuery获取库存分页列表")
    @PostMapping(value = "/list")
    public Result<PageData<InventoryVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InventoryQuery> search) {

        PageData<InventoryVO> resultPage = inventoryService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    /**
     * 查询 BinLocationLog 链路信息
     *
     * @param query 查询条件
     * @return 链路信息列表
     */
    @Operation(summary = "查询库位日志链路信息", description = "根据最早时间和产品ID查询库位日志链路信息，支持计算当前总库存数量")
    @PostMapping(value = "/bin-location-log-chain")
    public Result<List<BinLocationLogChainVO>> getBinLocationLogChain(
            @Valid @RequestBody @Parameter(description = "查询条件", required = true) BinLocationLogChainQuery query) {

        List<BinLocationLogChainVO> resultList = binLocationLogService.listChainByQuery(query);
        // 返回结果
        return success(resultList);
    }
}
