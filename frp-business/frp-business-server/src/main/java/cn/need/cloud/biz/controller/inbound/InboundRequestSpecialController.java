package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.service.inbound.InboundRequestSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.NoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 入库请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-request/special")
@Tag(name = "unload special接口")
@Slf4j
public class InboundRequestSpecialController extends AbstractController {

    @Resource
    private InboundRequestSpecialService service;

    @Operation(summary = "取消入库请求单", description = "入库请求单id")
    @PostMapping(value = "/cancel")
    public Result<Integer> cancelRequest(@RequestBody @Parameter(description = "入库请求单id及删除备注", required = true) NoteParam cancelParam) {

        Validate.notNull(cancelParam.getId(), "The id value cannot be null.");
        return Result.ok(service.cancelRequest(cancelParam));
    }

    @Operation(summary = "根据id删除入库请求", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(service.removeRequest(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }
}
