package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPrepWorkorderDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPrepWorkorder;
import cn.need.cloud.biz.model.vo.oco.OcoPrepWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO预处理工单表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPrepWorkorderConverter extends AbstractModelConverter<OcoPrepWorkorder, OcoPrepWorkorderVO, OcoPrepWorkorderDTO> {

}



