package cn.need.cloud.biz.service.binlocation.impl;

import cn.hutool.core.date.DateUtil;
import cn.need.cloud.biz.client.dto.binlocation.BinLocationDetailEasyExcelDTO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.vo.base.BaseBinLocationVO;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseBinLocationShowVO;
import cn.need.cloud.biz.model.vo.base.aware.BaseProductShowVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationExportService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.EasyExcelUtils;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.date.pattern.DatePattern;
import cn.need.framework.common.core.lang.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 库位详情导出服务实现类
 * </p>
 * <p>
 * 该类负责处理库位详情数据的导出功能，
 * 支持将库位详情数据导出为Excel文件供用户下载。主要用于库存管理、数据分析等场景。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 根据查询条件导出库位详情数据（仅导出库存>0的数据）
 * 2. 分批处理大量数据导出
 * 3. 填充关联的产品、库位和仓库信息
 * </p>
 *
 * <AUTHOR> AI
 * @since 2025-03-07
 */
@Service
@AllArgsConstructor
@Slf4j
public class BinLocationExportServiceImpl implements BinLocationExportService {

    /**
     * 批量大小
     */
    private static final int BATCH_SIZE = 1000;

    /**
     * Excel文件名前缀
     */
    private static final String FILE_PREFIX = "BinLocationDetail_";

    /**
     * Excel工作表名称
     */
    private static final String SHEET_NAME = "Export Result";

    /**
     * 库位详情服务
     */
    private final BinLocationDetailService binLocationDetailService;

    /**
     * 仓库服务
     */
    private final WarehouseService warehouseService;

    /**
     * 库位详情锁定服务
     */
    private final BinLocationDetailLockedService binLocationDetailLockedService;

    /**
     * 导出库位详情数据到Excel文件（仅导出库存>0的数据）
     * <p>
     * 该方法根据提供的查询条件，将符合条件的库位详情数据导出为Excel文件。
     * 为了避免一次性读取大量数据导致内存溢出，采用分批读取的方式处理。
     * 同时填充关联的产品、库位和仓库信息，提供更完整的数据视图。
     * 使用缓存机制获取产品和库位信息，提高性能。
     * </p>
     * <p>
     * 主要流程：
     * 1. 设置文件名和Sheet名
     * 2. 查询所有库位详情数据（过滤库存大于0的数据）
     * 3. 使用缓存工具类获取关联的产品和库位信息
     * 4. 转换数据格式并填充关联信息，包括交易伙伴信息
     * 5. 写入Excel文件
     * </p>
     *
     * @param query    库位查询条件
     * @param response HTTP响应对象，用于写入Excel文件
     * @return 导出是否成功
     */
    @Override
    public Boolean export(BinLocationQuery query, HttpServletResponse response) {
        // 文件名格式：BinLocationDetail_24-11-07
        String fileName = FILE_PREFIX + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN);
        AtomicInteger startCurrent = new AtomicInteger(1);
        WriteSheet writeSheet = EasyExcel.writerSheet(SHEET_NAME).build();

        // 创建头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 设置头部背景色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        // 设置头部字体
        WriteFont headFont = new WriteFont();
        headFont.setFontHeightInPoints((short) 11); // 设置字体大小
        headFont.setBold(true); // 加粗
        headWriteCellStyle.setWriteFont(headFont);
        // 设置头部居中
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 设置内容字体
        WriteFont contentFont = new WriteFont();
        contentFont.setFontHeightInPoints((short) 11); // 设置字体大小与头部一致
        contentWriteCellStyle.setWriteFont(contentFont);

        // 创建样式策略
        HorizontalCellStyleStrategy styleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        EasyExcelUtils.write(response, fileName, writeBuilder -> {
            try (ExcelWriter excelWriter = writeBuilder
                    .head(BinLocationDetailEasyExcelDTO.class)
                    .registerWriteHandler(styleStrategy)
                    .build()) {

                // 获取所有库位详情数据（仅获取库存大于0的数据）
                List<BinLocationDetail> allDetails = binLocationDetailService.list()
                        .stream()
                        .filter(detail -> detail.getInStockQty() != null && detail.getInStockQty() > 0)
                        .collect(Collectors.toList());

                if (ObjectUtil.isEmpty(allDetails)) {
                    log.info("No binlocation details found with in_stock_qty > 0");
                    return;
                }

                // 收集所有需要的ID
                List<Long> warehouseIds = StreamUtils.distinctMap(allDetails, BinLocationDetail::getWarehouseId);
                Map<Long, String> warehouseCodeMap = warehouseService.codeRelationByIds(warehouseIds);

                // 收集所有库位详情ID，用于查询锁定库存
                List<Long> binLocationDetailIds = StreamUtils.distinctMap(allDetails, BinLocationDetail::getId);

                // 获取锁定库存信息
                List<BinLocationDetailLocked> lockedList = binLocationDetailLockedService.listBinDetailIds(binLocationDetailIds);

                // 按库位详情ID分组计算锁定数量
                Map<Long, Integer> lockedQtyMap = new HashMap<>();
                if (!ObjectUtil.isEmpty(lockedList)) {
                    lockedQtyMap = lockedList.stream()
                            .collect(Collectors.groupingBy(
                                    BinLocationDetailLocked::getBinLocationDetailId,
                                    Collectors.summingInt(lock -> Math.max(lock.getQty() - lock.getFinishQty(), 0))
                            ));
                }

                // 分批处理
                for (int i = 0; i < allDetails.size(); i += BATCH_SIZE) {
                    int endIndex = Math.min(i + BATCH_SIZE, allDetails.size());
                    List<BinLocationDetail> batchDetails = allDetails.subList(i, endIndex);

                    // 收集批次中唯一的产品ID和库位ID
                    List<Long> uniqueBinLocationIds = batchDetails.stream()
                            .map(BinLocationDetail::getBinLocationId)
                            .distinct()
                            .collect(Collectors.toList());

                    List<Long> uniqueProductIds = batchDetails.stream()
                            .map(BinLocationDetail::getProductId)
                            .distinct()
                            .collect(Collectors.toList());

                    // 创建ID到ShowVO对象的映射
                    Map<Long, BaseBinLocationShowVO> binLocationShowVOMap = new HashMap<>();
                    Map<Long, BaseProductShowVO> productShowVOMap = new HashMap<>();

                    // 为每个唯一ID创建一个ShowVO对象
                    for (Long binLocationId : uniqueBinLocationIds) {
                        BaseBinLocationShowVO binLocationShowVO = new BaseBinLocationShowVO();
                        binLocationShowVO.setBinLocationId(binLocationId);
                        binLocationShowVOMap.put(binLocationId, binLocationShowVO);
                    }

                    for (Long productId : uniqueProductIds) {
                        BaseProductShowVO productShowVO = new BaseProductShowVO();
                        productShowVO.setProductId(productId);
                        productShowVOMap.put(productId, productShowVO);
                    }

                    // 一次性填充所有ShowVO对象
                    BinLocationCacheUtil.filledBinLocation(binLocationShowVOMap.values());
                    ProductCacheUtil.filledProduct(productShowVOMap.values());

                    // 构建导出DTO列表
                    List<BinLocationDetailEasyExcelDTO> dataList = new ArrayList<>();
                    for (BinLocationDetail detail : batchDetails) {
                        BinLocationDetailEasyExcelDTO excelDTO = new BinLocationDetailEasyExcelDTO();
                        excelDTO.setId(detail.getId());
                        excelDTO.setInStockQty(detail.getInStockQty());

                        // 设置锁定库存数量
                        Integer lockedQty = lockedQtyMap.getOrDefault(detail.getId(), 0);
                        excelDTO.setLockedQty(lockedQty);

                        // 计算可用库存
                        excelDTO.setAvailableQty(Math.max(detail.getInStockQty() - lockedQty, 0));

                        // 设置仓库信息
                        excelDTO.setWarehouseCode(warehouseCodeMap.get(detail.getWarehouseId()));

                        // 设置库位信息 - 只保留库位名称
                        BaseBinLocationShowVO binLocationShowVO = binLocationShowVOMap.get(detail.getBinLocationId());
                        if (binLocationShowVO != null) {
                            BaseBinLocationVO binLocationVO = binLocationShowVO.getBaseBinLocationVO();
                            if (binLocationVO != null) {
                                excelDTO.setBinLocationName(binLocationVO.getLocationName());
                            }
                        }

                        // 设置产品信息 - 保留productRefNum、productSupplierSku、productUPC
                        BaseProductShowVO productShowVO = productShowVOMap.get(detail.getProductId());
                        if (productShowVO != null) {
                            BaseProductVO baseProductVO = productShowVO.getBaseProductVO();
                            if (baseProductVO != null) {
                                excelDTO.setProductRefNum(baseProductVO.getRefNum());
                                excelDTO.setProductSupplierSku(baseProductVO.getSupplierSku());
                                excelDTO.setProductUPC(baseProductVO.getUpc());

                                // 设置交易伙伴信息
                                BasePartnerVO partnerVO = baseProductVO.getTransactionPartnerVO();
                                if (partnerVO != null) {
                                    excelDTO.setTransactionPartnerId(partnerVO.getId());
                                    excelDTO.setTransactionPartnerAbbrName(partnerVO.getAbbrName());
                                }
                            }
                        }

                        dataList.add(excelDTO);
                    }

                    excelWriter.write(dataList, writeSheet);
                }

                log.info("Successfully exported {} bin location details", allDetails.size());
            } catch (Exception e) {
                log.error("Failed to export bin location details", e);
                return;
            }
        });

        return true;
    }
} 