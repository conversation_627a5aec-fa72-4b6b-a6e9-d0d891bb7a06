package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcShipPalletDTO;
import cn.need.cloud.biz.model.entity.otc.OtcShipPallet;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC运输托盘 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcShipPalletConverter extends AbstractModelConverter<OtcShipPallet, OtcShipPalletVO, OtcShipPalletDTO> {

}
