package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPrepPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.param.otc.create.pickingslip.prep.OtcPrepPickingSlipBuildPickingSlipParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.prep.*;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepPickingSlipPageVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipBuildService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipPutAwayService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTC预提货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-prep-picking-slip")
@Tag(name = "OTC预提货单")
@Validated
public class OtcPrepPickingSlipController extends AbstractRestController<OtcPrepPickingSlipService, OtcPrepPickingSlip, OtcPrepPickingSlipConverter, OtcPrepPickingSlipVO> {

    @Resource
    private OtcPrepPickingSlipBuildService buildService;
    @Resource
    private OtcPrepPickingSlipPutAwayService putAwayService;

    @Operation(summary = "根据id获取OTC预提货单详情", description = "根据数据主键id，从数据库中获取其对应的OTC预提货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPrepPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC预提货单详情
        OtcPrepPickingSlipVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC预提货单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC预提货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPrepPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC预提货单详情
        OtcPrepPickingSlipVO detailVo = service.detailByRefNum(refNum);

        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC预提货单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC预提货单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPrepPickingSlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPrepPickingSlipListQuery> search) {

        // 获取OTC预提货单分页
        PageData<OtcPrepPickingSlipPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Prep filterBuild 新增", description = "根据传入的搜索条件参数，构建Prep拣货单")
    @PostMapping(value = "/filter-build")
    public Result<List<WorkOrderNoEnoughAvailQtyVO>> filterBuild(@RequestBody @Parameter(description = "搜索条件参数", required = true)
                                                                 @Valid OtcPrepPickingSlipFilterBuildQuery query) {

        // 返回结果
        return success(buildService.filterBuild(query));
    }

    @Operation(summary = "Prep markPrinted", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }

    @Operation(summary = "Prep拣货pick", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/pick")
    public Result<Boolean> pick(@RequestBody @Valid OtcPrepPickingSlipPickQuery query) {

        // 返回结果
        return success(service.pick(query.getPickDetailList(), query.getOtcPrepPickingSlipId()));
    }

    @Operation(summary = "putAway上架", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/put-away")
    public Result<OtcPrepPickingSlipVO> putAway(@RequestBody @Valid OtcPrepPickingSlipPutAwayQuery query) {

        // 返回结果
        OtcPrepPickingSlipVO pickingSlipVO = putAwayService.putAway(query);
        return success(pickingSlipVO);
    }

    @Operation(summary = "下拉列表", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "数据主键id", required = true) OtcPrepPickingSlipQuery query) {

        return success(service.distinctValue(query));
    }

    @Operation(summary = "构建拣货单", description = "根据PrepPickingSlipId构建拣货单")
    @PostMapping(value = "/build-picking-slip")
    public Result<OtcPickingSlipVO> buildPickingSlip(
            @RequestBody @Valid @Parameter(description = "Prep拣货单id", required = true) OtcPrepPickingSlipBuildPickingSlipParam param) {

        OtcPickingSlipVO data = buildService.buildPickingSlip(param.getOtcPrepPickingSlipId());
        data.setBasePartnerVO(data.getTransactionPartnerVO());
        // 返回结果
        return success(data);
    }
}
