package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeInboundDTO;
import cn.need.cloud.biz.model.entity.fee.FeeInbound;
import cn.need.cloud.biz.model.vo.fee.FeeInboundVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用inbound 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeInboundConverter extends AbstractModelConverter<FeeInbound, FeeInboundVO, FeeInboundDTO> {

}
