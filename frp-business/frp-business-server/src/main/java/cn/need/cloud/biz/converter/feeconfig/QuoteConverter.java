package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.QuoteDTO;
import cn.need.cloud.biz.model.entity.feeconfig.Quote;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class QuoteConverter extends AbstractModelConverter<Quote, QuoteVO, QuoteDTO> {

}
