package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.WarehouseProductConverter;
import cn.need.cloud.biz.listener.warehouse.WarehouseProductImportListener;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.param.warehouse.importparam.WarehouseProductImportParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseProductCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductListVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductVO;
import cn.need.cloud.biz.service.warehouse.WarehouseProductService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 产品即发货 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Slf4j
@RestController
@RequestMapping("/api/biz/warehouse-product")
@Tag(name = "产品即发货")
public class WarehouseProductController extends AbstractRestController<WarehouseProductService, WarehouseProduct, WarehouseProductConverter, WarehouseProductVO> {


    @Operation(summary = "新增或修改产品即发货状态(MarkProductAsSlapAndGo)", description = "接收产品即发货的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/createOrUpdate")
    public Result<Integer> createOrUpdate(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseProductCreateOrUpdateParam updateParam) {

        // 返回结果
        return success(service.createOrUpdate(updateParam));

    }


    @Operation(summary = "获取产品即发货列表接口", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @GetMapping(value = "/listByProductId/{productId}")
    public Result<List<WarehouseProductListVO>> listByProductId(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        return success(service.listByProductId(productId));
    }

    @Operation(summary = "批量导入仓库产品额外包装类型", description = "通过Excel文件批量导入仓库产品的额外包装类型信息")
    @PostMapping(value = "/import")
    public Result<Integer> importExcel(@RequestParam("file") @Parameter(description = "Excel文件", required = true) MultipartFile file) {
        return super.importExcel(file, WarehouseProductImportParam.class, () -> new WarehouseProductImportListener(service));
    }
}
