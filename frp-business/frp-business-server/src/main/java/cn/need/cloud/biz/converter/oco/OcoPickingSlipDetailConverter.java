package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPickingSlipDetail;
import cn.need.cloud.biz.model.vo.oco.OcoPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO拣货单明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPickingSlipDetailConverter extends AbstractModelConverter<OcoPickingSlipDetail, OcoPickingSlipDetailVO, OcoPickingSlipDetailDTO> {

}



