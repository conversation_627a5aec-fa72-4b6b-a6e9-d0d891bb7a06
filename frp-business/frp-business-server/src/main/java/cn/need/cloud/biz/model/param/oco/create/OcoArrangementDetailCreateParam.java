package cn.need.cloud.biz.model.param.oco.create;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;


/**
 * OCO安排明细表 CreateParam对象
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
@Data
@Schema(description = "OCO安排明细表 CreateParam对象")
public class OcoArrangementDetailCreateParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -507643693930155291L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 已分配数量
     */
    @Schema(description = "已分配数量")
    @NotNull(message = "allocatedQty cannot be null")
    private Integer allocatedQty;
    /**
     * 行号
     */
    @Schema(description = "行号")
    @NotNull(message = "lineNum cannot be null")
    private Integer lineNum;
    /**
     * 备注
     */
    @Schema(description = "备注")
    @Size(max = 512, message = "note cannot exceed 512 characters")
    private String note;

    /**
     * OCO安排ID
     */
    @Schema(description = "OCO安排ID")
    //@NotNull(message = "ocoArrangementId cannot be null")
    private Long ocoArrangementId;
    /**
     * OCO请求详情ID
     */
    @Schema(description = "OCO请求详情ID")
    @NotNull(message = "ocoRequestDetailId cannot be null")
    private Long ocoRequestDetailId;
    /**
     * OCO请求ID
     */
    @Schema(description = "OCO请求ID")
    @NotNull(message = "ocoRequestId cannot be null")
    private Long ocoRequestId;
    /**
     * 产品条码
     */
    @Schema(description = "产品条码")
    @Size(max = 255, message = "productBarcode cannot exceed 255 characters")
    private String productBarcode;
    /**
     * 产品渠道SKU
     */
    @Schema(description = "产品渠道SKU")
    @Size(max = 255, message = "productChannelSku cannot exceed 255 characters")
    private String productChannelSku;
    /**
     * 产品ID
     */
    @Schema(description = "产品ID")
    @NotNull(message = "productId cannot be null")
    private Long productId;
    /**
     * 数量
     */
    @Schema(description = "数量")
    @NotNull(message = "qty cannot be null")
    private Integer qty;

}