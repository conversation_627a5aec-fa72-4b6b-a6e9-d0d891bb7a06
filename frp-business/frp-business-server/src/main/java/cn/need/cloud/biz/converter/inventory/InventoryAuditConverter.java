package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.client.dto.inventory.InventoryAuditDTO;
import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.vo.inventory.InventoryAuditVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 库存盘点 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryAuditConverter extends AbstractModelConverter<InventoryAudit, InventoryAuditVO, InventoryAuditDTO> {

}
