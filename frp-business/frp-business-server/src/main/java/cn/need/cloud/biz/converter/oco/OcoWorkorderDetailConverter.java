package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoWorkorderDetail;
import cn.need.cloud.biz.model.vo.oco.OcoWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO工单明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoWorkorderDetailConverter extends AbstractModelConverter<OcoWorkorderDetail, OcoWorkorderDetailVO, OcoWorkorderDetailDTO> {

}



