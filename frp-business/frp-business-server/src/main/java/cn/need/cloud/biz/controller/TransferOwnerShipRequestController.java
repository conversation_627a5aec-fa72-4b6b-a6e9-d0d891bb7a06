package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.converter.TransferOwnerShipRequestConverter;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestCreateParam;
import cn.need.cloud.biz.model.query.TransferOwnerShipRequestQuery;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestPageVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestVO;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 货权转移 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
@RestController
@RequestMapping("/api/biz/transfer-owner-ship-request")
@Tag(name = "货权转移")
public class TransferOwnerShipRequestController extends AbstractRestController<TransferOwnerShipRequestService, TransferOwnerShipRequest, TransferOwnerShipRequestConverter, TransferOwnerShipRequestVO> {

    @Operation(summary = "新增货权转移", description = "接收货权转移的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<TransferOwnerShipRequest> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) TransferOwnerShipRequestCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "根据id获取货权转移详情", description = "根据数据主键id，从数据库中获取其对应的货权转移详情")
    @GetMapping(value = "/detail/{id}")
    public Result<TransferOwnerShipRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取货权转移详情", description = "根据数据RefNum，从数据库中获取其对应的货权转移详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<TransferOwnerShipRequestVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取货权转移分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的货权转移列表")
    @PostMapping(value = "/list")
    public Result<PageData<TransferOwnerShipRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<TransferOwnerShipRequestQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
