package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.entity.base.BaseWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.param.inventory.create.InventoryReserveCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReserveUpdateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryReserveQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryReserveVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryReserveVO;
import cn.need.cloud.biz.model.vo.page.InventoryReservePageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 库存预留服务接口
 * </p>
 * <p>
 * 该接口提供库存预留管理的核心业务功能，包括库存预留创建、查询、更新和释放等。
 * 库存预留用于为未来到货的产品提前分配库存，确保产品到货后能够满足已知的出库需求。
 * 与库存锁定不同，预留针对的是尚未实际入库但预期将要入库的库存。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 库存预留创建与管理
 * 2. 库存预留查询（按产品、仓库等条件）
 * 3. 预留库存释放与上架处理
 * 4. 工单与库存预留的关联管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InventoryReserveService extends SuperService<InventoryReserve> {

    /**
     * 创建库存预留记录
     * <p>
     * 根据提供的参数创建库存预留记录，通常在预期有产品入库且有确定的出库需求时调用。
     * 预留记录用于标记产品入库后应直接分配给特定出库需求的数量。
     * </p>
     *
     * @param createParam 库存预留创建参数，包含产品ID、预留数量、关联请求信息等
     * @return 创建成功的库存预留ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(InventoryReserveCreateParam createParam);


    /**
     * 更新库存预留记录
     * <p>
     * 根据参数更新已有的库存预留记录，可以修改预留数量、状态等信息。
     * 在预留需求变更或部分满足时调用。
     * </p>
     *
     * @param updateParam 库存预留更新参数，包含预留ID、更新的数量或状态等信息
     * @return 更新影响的记录数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(InventoryReserveUpdateParam updateParam);

    /**
     * 根据查询条件获取库存预留记录列表
     * <p>
     * 查询符合条件的库存预留记录，不包含分页信息。
     * 可用于特定业务场景下的库存预留数据查询。
     * </p>
     *
     * @param query 查询条件对象，包含产品ID、仓库ID、状态等筛选条件
     * @return 返回符合条件的库存预留记录列表
     */
    List<InventoryReservePageVO> listByQuery(InventoryReserveQuery query);

    /**
     * 分页查询库存预留记录
     * <p>
     * 根据查询条件和分页参数查询库存预留记录。
     * 返回的结果包含分页信息和预留记录列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的库存预留记录列表
     */
    PageData<InventoryReservePageVO> pageByQuery(PageSearch<InventoryReserveQuery> search);

    /**
     * 根据ID获取库存预留详情
     * <p>
     * 查询指定ID的库存预留记录详细信息。
     * </p>
     *
     * @param id 库存预留记录ID
     * @return 返回库存预留视图对象
     */
    InventoryReserveVO detailById(Long id);

    /**
     * 根据唯一编码获取库存预留详情
     * <p>
     * 查询指定引用编号的库存预留记录详细信息。
     * </p>
     *
     * @param refNum 库存预留记录唯一编码
     * @return 返回库存预留视图对象
     */
    InventoryReserveVO detailByRefNum(String refNum);

    /**
     * 根据产品ID列表查询未完成的库存预留记录
     * <p>
     * 查询指定产品的未完成（待处理或部分处理）库存预留记录。
     * 用于了解产品当前的预留状态和预留数量。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回未完成的库存预留记录视图对象列表
     */
    List<InventoryReserveVO> listNoFinishByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询未完成的库存预留记录（包含库存信息）
     * <p>
     * 查询指定产品的未完成库存预留记录，包含库存相关信息。
     * 返回结果中包含产品库存和预留的综合信息。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回包含库存信息的预留记录视图对象列表
     */
    List<InventoryInventoryReserveVO> listInventoryNoFinishByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询未完成的库存预留实体对象
     * <p>
     * 查询指定产品的未完成库存预留记录，返回实体对象列表。
     * 主要用于需要直接操作实体对象的场景。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回库存预留实体对象列表
     */
    List<InventoryReserve> listEntityNoFinishByProductIds(List<Long> productIds);

    /**
     * 释放库存预留
     * <p>
     * 批量释放指定的库存预留记录。
     * 通常在取消出库请求或入库计划变更时调用。
     * </p>
     *
     * @param lockedInventoryList 需要释放的库存预留参数列表
     */
    void releaseReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList);

    @Transactional(rollbackFor = Exception.class)
    void cancelReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList);

    /**
     * 处理库存预留上架
     * <p>
     * 处理库存预留记录的上架操作，将预留库存转换为实际可用库存。
     * 通常在预留的产品完成入库上架后调用。
     * </p>
     *
     * @param lockedInventoryList 需要处理上架的库存预留参数列表
     */
    void putAwayReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList);

    /**
     * 根据工单信息构建库存预留对象
     * <p>
     * 基于工单详情和工单信息构建库存预留实体对象。
     * 用于将工单中的产品数量需求转换为库存预留记录。
     * </p>
     *
     * @param <D>       工单详情模型类型
     * @param <W>       工单模型类型
     * @param detail    工单详情实例，提供产品、数量等信息
     * @param workOrder 工单实例，提供仓库、请求等信息
     * @return 构建的库存预留实体对象
     */
    <D extends BaseWorkorderDetailModel, W extends BaseWorkorderModel> InventoryReserve buildInventoryReserve(D detail, W workOrder);
}