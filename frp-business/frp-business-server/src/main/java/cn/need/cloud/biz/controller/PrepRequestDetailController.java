package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.converter.PrepRequestDetailConverter;
import cn.need.cloud.biz.model.entity.PrepRequestDetail;
import cn.need.cloud.biz.model.param.otb.create.PrepRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.prep.PrepRequestDetailUpdateParam;
import cn.need.cloud.biz.model.query.PrepRequestDetailQuery;
import cn.need.cloud.biz.model.vo.PrepRequestDetailVO;
import cn.need.cloud.biz.model.vo.page.PrepRequestDetailPageVO;
import cn.need.cloud.biz.service.PrepRequestDetailService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 预请求详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/prep-request-detail")
@Tag(name = "预请求详情")
public class PrepRequestDetailController extends AbstractRestController<PrepRequestDetailService, PrepRequestDetail, PrepRequestDetailConverter, PrepRequestDetailVO> {

    @Operation(summary = "新增预请求详情", description = "接收预请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrepRequestDetailCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改预请求详情", description = "接收预请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrepRequestDetailUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除预请求详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取预请求详情详情", description = "根据数据主键id，从数据库中获取其对应的预请求详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PrepRequestDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取预请求详情详情
        PrepRequestDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取预请求详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的预请求详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<PrepRequestDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PrepRequestDetailQuery> search) {

        // 获取预请求详情分页
        PageData<PrepRequestDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
