package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductMultiboxConverter;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxWithDetailVO;
import cn.need.cloud.biz.service.product.ProductMultiboxService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 产品多箱 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product-multibox")
@Tag(name = "产品多箱")
@Validated
public class ProductMultiboxController extends AbstractRestController<ProductMultiboxService, ProductMultibox, ProductMultiboxConverter, ProductMultiboxVO> {


    @Operation(summary = "新增or修改产品多箱", description = "接收产品多箱的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/createOrUpdate")
    public Result<Integer> createOrUpdate(@Valid @RequestBody
                                          @Parameter(description = "数据对象", required = true) List<ProductMultiboxCreateOrUpdateParam> paramList) {

        // 返回结果
        return success(service.createOrUpdate(paramList));
    }

    /**
     * 根据搜索条件参数获取列表数据
     *
     * @param productId 搜索参数
     * @return 列表数据
     */
    @Operation(summary = "获取数据multibox列表接口", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @GetMapping(value = "/listByProductId/{productId}")
    public Result<List<ProductMultiboxListVO>> listByProductId(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        return success(service.listByProductId(productId));
    }


    @Operation(summary = "multiBoxByWorkOrderDetailId", description = "根据WorkOrderDetailId 获取 ExProduct 的属性")
    @GetMapping(value = "/by-work-order-detail-id/{otcWorkOrderDetailId}")
    public Result<List<ProductMultiboxWithDetailVO>> multiBoxByWorkOrderDetailId(
            @Valid @PathVariable("otcWorkOrderDetailId") @Parameter(description = "工单详情id", required = true) Long otcWorkOrderDetailId) {

        // 获取OTC工单仓储位置分页
        List<ProductMultiboxWithDetailVO> result = service.multiBoxByWorkOrderDetailId(otcWorkOrderDetailId);
        // 返回结果
        return success(result);
    }

}
