package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbRoutingInstructionPalletLabelDTO;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstructionPalletLabel;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionPalletLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb发货指南托盘标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbRoutingInstructionPalletLabelConverter extends AbstractModelConverter<OtbRoutingInstructionPalletLabel, OtbRoutingInstructionPalletLabelVO, OtbRoutingInstructionPalletLabelDTO> {

}
