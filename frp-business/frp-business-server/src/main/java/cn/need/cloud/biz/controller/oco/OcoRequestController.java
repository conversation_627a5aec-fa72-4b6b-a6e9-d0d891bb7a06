package cn.need.cloud.biz.controller.oco;

import cn.need.cloud.biz.converter.oco.OcoRequestConverter;
import cn.need.cloud.biz.model.entity.oco.OcoRequest;
import cn.need.cloud.biz.model.param.oco.create.OcoRequestCreateParam;
import cn.need.cloud.biz.model.param.oco.update.OcoRequestAuditParam;
import cn.need.cloud.biz.model.param.oco.update.OcoRequestUpdateParam;
import cn.need.cloud.biz.model.query.oco.OcoRequestQuery;
import cn.need.cloud.biz.model.vo.oco.OcoRequestVO;
import cn.need.cloud.biz.model.vo.page.OcoRequestPageVO;
import cn.need.cloud.biz.service.oco.OcoRequestService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OCO请求表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/api/biz/oco-request")
@Tag(name = "OCO请求表")
public class OcoRequestController extends AbstractRestController<OcoRequestService, OcoRequest, OcoRequestConverter, OcoRequestVO> {

    @Operation(summary = "新增OCO请求表", description = "接收OCO请求表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoRequestCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改OCO请求表", description = "接收OCO请求表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoRequestUpdateParam updateParam) {
        // 修改OCO请求
        service.updateByParam(updateParam);
        // 返回结果
        return success();
    }

    @Operation(summary = "根据id删除OCO请求表", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OCO请求表详情", description = "根据数据主键id，从数据库中获取其对应的OCO请求表详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OcoRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OCO请求表详情", description = "根据数据RefNum，从数据库中获取其对应的OCO请求表详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OcoRequestVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取OCO请求表分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OCO请求表列表")
    @PostMapping(value = "/list")
    public Result<PageData<OcoRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OcoRequestQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "获取OCO请求表分页列表（含详情）", description = "根据传入的搜索条件参数，返回每条记录包含 detailList 的分页结果，便于前端在列表中直接展示详情")
    @PostMapping(value = "/list-with-details")
    public Result<PageData<OcoRequestVO>> listWithDetails(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OcoRequestQuery> search) {
        // 复用服务层 detailById 聚合逻辑，逐条填充 detailList
        return success(service.pageWithDetails(search));
    }

    /**
     * <p>
     * 审核OCO请求
     * </p>
     *
     * @param param 需要审核请求的数据对象
     * @return 操作结果
     */
    @Operation(summary = "审核OCO请求接口", description = "接收OCO请求审核参数，进行审核操作（通过/拒绝）")
    @PostMapping(value = "/audit")
    public Result<String> audit(@Valid @RequestBody @Parameter(description = "审核参数", required = true) OcoRequestAuditParam param) {
        service.audit(param);
        return success();
    }
}



