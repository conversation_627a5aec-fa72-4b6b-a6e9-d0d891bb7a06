package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.warehouse.WarehouseProductDTO;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品即发货 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class WarehouseProductConverter extends AbstractModelConverter<WarehouseProduct, WarehouseProductVO, WarehouseProductDTO> {

}
