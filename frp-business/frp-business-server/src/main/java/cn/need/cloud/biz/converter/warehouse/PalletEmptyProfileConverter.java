package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.PalletEmptyProfileDTO;
import cn.need.cloud.biz.model.entity.warehouse.PalletEmptyProfile;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 托盘信息 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class PalletEmptyProfileConverter extends AbstractModelConverter<PalletEmptyProfile, PalletEmptyProfileVO, PalletEmptyProfileDTO> {

}
