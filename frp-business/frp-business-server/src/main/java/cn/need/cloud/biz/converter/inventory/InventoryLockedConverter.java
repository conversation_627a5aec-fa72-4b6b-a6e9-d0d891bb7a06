package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.client.dto.inventory.InventoryLockedDTO;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.vo.inventory.InventoryLockedVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 锁定库存 产品出库审核的审核的时候，通过这个表来判断库存，改为： 锁定到了 具体的 库位，就会释放这里的值，这里当成一个临时的锁定值 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryLockedConverter extends AbstractModelConverter<InventoryLocked, InventoryLockedVO, InventoryLockedDTO> {

}
