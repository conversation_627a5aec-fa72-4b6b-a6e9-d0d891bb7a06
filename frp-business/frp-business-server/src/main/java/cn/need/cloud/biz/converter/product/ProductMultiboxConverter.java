package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductMultiboxDTO;
import cn.need.cloud.biz.model.entity.product.ProductMultibox;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品多箱 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductMultiboxConverter extends AbstractModelConverter<ProductMultibox, ProductMultiboxVO, ProductMultiboxDTO> {

}
