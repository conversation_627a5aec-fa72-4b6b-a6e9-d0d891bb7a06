package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtcConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigOtcMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtc;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtcDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtcCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtcUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtcQuery;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtcPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtcDetailService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtcService;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.helper.SectionHelper;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库报价费用配置OTC服务实现类
 * </p>
 * <p>
 * 该类实现了针对入库业务(Inbound To Customer)相关费用配置的管理服务，包括费用配置的创建、
 * 更新、查询以及状态管理等功能。OTC费用配置用于定义入库业务中不同服务项目的价格标准，
 * 是仓储费用结算的重要基础数据。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 费用配置的基础CRUD操作
 * 2. 费用配置明细的关联管理
 * 3. 费用配置的启用/禁用管理
 * 4. 与报价单的关联处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigOtc")
public class FeeConfigOtcServiceImpl extends SuperServiceImpl<FeeConfigOtcMapper, FeeConfigOtc> implements FeeConfigOtcService {

    /**
     * OTC费用配置明细服务，用于管理费用配置的详细项目
     */
    @Resource
    private FeeConfigOtcDetailService feeConfigOtcDetailservice;

    /**
     * 报价服务，用于管理与费用配置关联的报价单信息
     */
    @Resource
    private QuoteService quoteService;

    /**
     * 根据参数创建OTC费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性创建。
     * 主要步骤包括：
     * 1. 验证创建参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 初始化并插入费用配置主记录
     * 4. 关联并插入费用配置明细记录
     * </p>
     *
     * @param createParam OTC费用配置创建参数
     * @return 创建成功的费用配置ID
     * @throws BusinessException 如果参数为空或明细列表校验失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigOtcCreateParam createParam) {
        // 检查传入仓库报价费用配置otc参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }


        SectionHelper.checkDetailList(createParam.getDetailList());

        // 获取仓库报价费用配置otc转换器实例，用于将仓库报价费用配置otc参数对象转换为实体对象
        FeeConfigOtcConverter converter = Converters.get(FeeConfigOtcConverter.class);

        // 将仓库报价费用配置otc参数对象转换为实体对象并初始化
        FeeConfigOtc entity = initFeeConfigOtc(converter.toEntity(createParam));

        // 插入仓库报价费用配置otc实体对象到数据库
        super.insert(entity);

        final List<FeeConfigOtcDetail> feeConfigOtcDetails = feeConfigOtcDetailservice.initFeeConfigOtcDetail(createParam.getDetailList(), item -> item.setHeaderId(entity.getId()));

        feeConfigOtcDetailservice.insertBatch(feeConfigOtcDetails);

        // 返回仓库报价费用配置otcID
        return entity.getId();
    }

    /**
     * 根据参数更新OTC费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性更新。
     * 主要步骤包括：
     * 1. 验证更新参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 检查是否有关联的报价单（有关联报价单的配置不允许修改）
     * 4. 更新费用配置明细
     * 5. 更新费用配置主记录
     * </p>
     *
     * @param updateParam OTC费用配置更新参数
     * @return 更新影响的记录数
     * @throws BusinessException 如果参数为空、ID为空、明细列表校验失败，或存在关联报价单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigOtcUpdateParam updateParam) {
        // 检查传入仓库报价费用配置otc参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(updateParam.getDetailList());

        checkHasQuote(updateParam.getId());

        // 获取仓库报价费用配置otc转换器实例，用于将仓库报价费用配置otc参数对象转换为实体对象
        FeeConfigOtcConverter converter = Converters.get(FeeConfigOtcConverter.class);
        // 将仓库报价费用配置otc参数对象转换为实体对象
        FeeConfigOtc entity = converter.toEntity(updateParam);

        feeConfigOtcDetailservice.updateByFeeConfigOtcId(updateParam.getId(), updateParam.getDetailList());

        // 执行更新仓库报价费用配置otc操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取OTC费用配置列表
     * <p>
     * 该方法根据指定的查询条件返回符合条件的OTC费用配置列表，不包含分页信息。
     * </p>
     *
     * @param query 包含查询条件的OTC费用配置查询对象
     * @return 符合条件的OTC费用配置分页视图对象列表
     */
    @Override
    public List<FeeConfigOtcPageVO> listByQuery(FeeConfigOtcQuery query) {

        return mapper.listByQuery(query);
    }

    /**
     * 分页查询OTC费用配置列表
     * <p>
     * 该方法根据查询条件和分页参数获取OTC费用配置列表，并填充关联的报价单信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的OTC费用配置分页视图对象
     */
    @Override
    public PageData<FeeConfigOtcPageVO> pageByQuery(PageSearch<FeeConfigOtcQuery> search) {
        Page<FeeConfigOtc> page = Conditions.page(search, entityClass);
        List<FeeConfigOtcPageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        //fillData
        fillData(dataList);

        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取OTC费用配置详情
     * <p>
     * 该方法获取指定ID的OTC费用配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应ID的配置，则抛出业务异常。
     * </p>
     *
     * @param id OTC费用配置ID
     * @return 包含详细信息的OTC费用配置视图对象
     * @throws BusinessException 如果找不到指定ID的配置记录
     */
    @Override
    public FeeConfigOtcVO detailById(Long id) {
        FeeConfigOtc entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigOtc", id));
        }

        return buildFeeConfigOtcVO(entity);
    }

    /**
     * 根据参考编号获取OTC费用配置详情
     * <p>
     * 该方法根据费用配置的参考编号获取配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应参考编号的配置，则抛出业务异常。
     * </p>
     *
     * @param refNum OTC费用配置参考编号
     * @return 包含详细信息的OTC费用配置视图对象
     * @throws BusinessException 如果找不到指定参考编号的配置记录
     */
    @Override
    public FeeConfigOtcVO detailByRefNum(String refNum) {
        FeeConfigOtc entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeConfigOtc", "refNum", refNum));
        }
        return buildFeeConfigOtcVO(entity);
    }

    /**
     * 根据报价ID获取OTC费用配置列表
     * <p>
     * 该方法获取指定报价ID关联的所有OTC费用配置详细信息，包括配置明细等关联信息。
     * 如果报价ID为空，则返回空列表。
     * </p>
     *
     * @param quoteId 报价ID
     * @return 包含详细信息的OTC费用配置视图对象列表
     */
    @Override
    public List<FeeConfigOtcVO> listDetailByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return java.util.Collections.emptyList();
        }
        List<FeeConfigOtc> feeConfigOtcList = lambdaQuery()
                .eq(FeeConfigOtc::getQuoteId, quoteId)
                .list();
        return buildFeeConfigOtcVOList(feeConfigOtcList);
    }

    @Override
    public void beforeSwitchActive(FeeConfigOtc entity) {
        checkHasQuote(entity);
    }

    /**
     * 删除OTC费用配置并记录备注
     * <p>
     * 该方法用于删除指定ID的OTC费用配置，并记录删除原因。
     * 删除前会检查是否有关联的报价单，如有则不允许删除。
     * </p>
     *
     * @param id   要删除的OTC费用配置ID
     * @param note 删除原因备注
     * @return 删除影响的记录数
     * @throws BusinessException 如果存在关联报价单
     */
    @Override
    public int removeAndNote(Long id, String note) {

        checkHasQuote(id);

        return super.removeAndNote(id, note);
    }

    /**
     * 初始化OTC费用配置对象
     * <p>
     * 此方法用于设置OTC费用配置对象的必要参数，包括ID和参考编号，
     * 确保其处于有效状态。
     * </p>
     *
     * @param entity OTC费用配置对象，不应为空
     * @return 返回初始化后的OTC费用配置
     * @throws BusinessException 如果传入的费用配置为空，则抛出此异常
     */
    private FeeConfigOtc initFeeConfigOtc(FeeConfigOtc entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("FeeConfigOtc cannot be empty");
        }

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_OTC));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 填充OTC费用配置视图对象列表的关联数据
     * <p>
     * 该方法为列表中的OTC费用配置视图对象填充关联的报价单信息。
     * 主要步骤包括：
     * 1. 检查数据列表是否为空
     * 2. 获取所有关联的报价单ID
     * 3. 根据报价单ID获取报价单信息（编号和名称）
     * 4. 将报价单信息设置到对应的费用配置视图对象中
     * </p>
     *
     * @param dataList OTC费用配置视图对象列表
     */
    private void fillData(List<FeeConfigOtcPageVO> dataList) {
        if (!ObjectUtil.isNotEmpty(dataList)) {
            return;
        }

        final Map<Long, RefNumWithNameVO> quoteMap = quoteService.refNumWithNameMapByIds(FeeConfigUtil.getQuoteIds(dataList));

        if (ObjectUtil.isNotEmpty(quoteMap)) {
            for (FeeConfigOtcPageVO item : dataList) {
                if (!ObjectUtil.isNotEmpty(item.getQuoteId())) {
                    continue;
                }
                item.setQuote(quoteMap.get(item.getQuoteId()));
            }
        }
    }


    /**
     * 构建OTC费用配置视图对象
     * <p>
     * 该方法将OTC费用配置实体对象转换为包含完整信息的视图对象，
     * 包括基本信息、明细列表和关联的报价单信息。
     * 主要步骤包括：
     * 1. 将实体对象转换为视图对象
     * 2. 查询并设置费用配置明细列表
     * 3. 查询并设置关联的报价单信息
     * </p>
     *
     * @param entity OTC费用配置实体对象
     * @return 包含完整信息的OTC费用配置视图对象
     */
    private FeeConfigOtcVO buildFeeConfigOtcVO(FeeConfigOtc entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        FeeConfigOtcVO result = Converters.get(FeeConfigOtcConverter.class).toVO(entity);

        //详情
        List<FeeConfigOtcDetailVO> detailList = feeConfigOtcDetailservice.listByFeeConfigOtcId(result.getId());

        result.setDetailList(detailList);

        result.setQuote(quoteService.refNumWithNameById(entity.getQuoteId()));

        return result;
    }

    /**
     * 构建OTC费用配置视图对象列表
     *
     * @param entityList OTC费用配置实体对象列表
     * @return 返回包含详细信息的OTC费用配置视图对象列表
     */
    private List<FeeConfigOtcVO> buildFeeConfigOtcVOList(List<FeeConfigOtc> entityList) {
        if (ObjectUtil.isEmpty(entityList)) {
            return java.util.Collections.emptyList();
        }

        List<FeeConfigOtcVO> resultList = Converters.get(FeeConfigOtcConverter.class).toVO(entityList);

        // 获取所有费用配置ID
        List<Long> feeConfigIds = resultList.stream().map(FeeConfigOtcVO::getId).distinct().toList();
        List<Long> quoteIds = resultList.stream()
                .map(FeeConfigOtcVO::getQuoteId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList();

        // 批量获取所有费用配置的明细
        Map<Long, List<FeeConfigOtcDetailVO>> detailListMap = feeConfigOtcDetailservice.mapByFeeConfigOtcIdList(feeConfigIds);

        // 批量获取所有报价信息
        final Map<Long, RefNumWithNameVO> refNumVOMap = quoteService.refNumWithNameMapByIds(quoteIds);

        // 组装最终结果
        for (FeeConfigOtcVO feeConfigOtcVO : resultList) {
            // 设置明细
            List<FeeConfigOtcDetailVO> detailList = detailListMap.get(feeConfigOtcVO.getId());
            feeConfigOtcVO.setDetailList(ObjectUtil.isEmpty(detailList) ? java.util.Collections.emptyList() : detailList);

            // 设置报价信息
            RefNumWithNameVO quote = refNumVOMap.get(feeConfigOtcVO.getQuoteId());
            feeConfigOtcVO.setQuote(quote);
        }

        return resultList;
    }
}
