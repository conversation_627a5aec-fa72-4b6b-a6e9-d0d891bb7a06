package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.service.ship.AmazonClientService;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletReqDTO;
import cn.need.cloud.ship.client.dto.amazon.AmazonShipPalletRespDTO;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 亚马逊物流客户端控制器
 * </p>
 * <p>
 * 该控制器提供与亚马逊物流系统集成的测试接口，主要用于测试和调试亚马逊物流功能。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@RestController
@RequestMapping("/api/biz/amazon")
@Tag(name = "亚马逊物流接口", description = "提供亚马逊物流相关操作")
public class AmazonController extends AbstractController {

    @Resource
    private AmazonClientService amazonClientService;

    /**
     * 获取亚马逊托盘发货信息
     * <p>
     * 根据提供的托盘信息向亚马逊物流系统发送托盘发货请求，
     * 获取发货确认信息和物流追踪号。
     * </p>
     *
     * @param dto 亚马逊托盘发货请求参数
     * @return 包含亚马逊托盘发货响应结果的结果对象
     */
    @Operation(summary = "获取亚马逊托盘发货信息", description = "根据提供的托盘信息向亚马逊物流系统发送托盘发货请求")
    @PostMapping("/ship-pallet")
    public Result<AmazonShipPalletRespDTO> getShipPallet(@RequestBody AmazonShipPalletReqDTO dto) {
        AmazonShipPalletRespDTO respDTO = amazonClientService.getShipPallet(dto);
        return Result.ok(respDTO);
    }

    /**
     * 获取亚马逊托盘发货信息(简化参数)
     * <p>
     * 根据基本参数构建亚马逊托盘发货请求，适用于测试场景。
     * </p>
     *
     * @param appId             应用ID
     * @param profileRefNum     配置引用编号
     * @param shipFromPartyId   发货方ID
     * @param sellingPartyId    销售方ID
     * @param vendorContainerId 供应商容器ID
     * @param trackingNumbers   追踪号列表
     * @param labelFormat       标签格式(可选，默认为BASE64_PNG)
     * @return 包含亚马逊托盘发货响应结果的结果对象
     */
    @Operation(summary = "获取亚马逊托盘发货信息(简化参数)", description = "通过简化参数构建托盘发货请求，适用于测试")
    @PostMapping("/ship-pallet-simple")
    public Result<AmazonShipPalletRespDTO> getShipPalletSimple(
            @Parameter(description = "应用ID") @RequestParam String appId,
            @Parameter(description = "配置引用编号") @RequestParam String profileRefNum,
            @Parameter(description = "发货方ID") @RequestParam String shipFromPartyId,
            @Parameter(description = "销售方ID") @RequestParam String sellingPartyId,
            @Parameter(description = "供应商容器ID") @RequestParam String vendorContainerId,
            @Parameter(description = "追踪号列表") @RequestParam List<String> trackingNumbers,
            @Parameter(description = "标签格式(可选)") @RequestParam(required = false, defaultValue = "BASE64_PNG") String labelFormat) {

        AmazonShipPalletReqDTO dto = new AmazonShipPalletReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setShipFromPartyId(shipFromPartyId);
        dto.setSellingPartyId(sellingPartyId);
        dto.setVendorContainerId(vendorContainerId);
        dto.setRequestRefNum(vendorContainerId); // 使用容器ID作为请求参考编号
        dto.setPackageTrackingNumbers(trackingNumbers);

        // 设置标签格式
        try {
            FileDataTypeEnum fileDataTypeEnum = FileDataTypeEnum.valueOf(labelFormat);
            dto.setLabelFormat(fileDataTypeEnum.getLabelFormat());
        } catch (IllegalArgumentException e) {
            // 如果指定的枚举值不存在，使用默认值
            dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        }

        AmazonShipPalletRespDTO respDTO = amazonClientService.getShipPallet(dto);
        return Result.ok(respDTO);
    }

    /**
     * 使用预设参数获取亚马逊托盘发货信息
     * <p>
     * 使用默认参数构建亚马逊托盘发货请求，仅需提供少量必要参数即可快速测试。
     * 适用于标准场景的快速测试。
     * </p>
     *
     * @param appId          应用ID
     * @param trackingNumber 追踪号（可选，默认为"TBA320111124171"）
     * @return 包含亚马逊托盘发货响应结果的结果对象
     */
    @Operation(summary = "使用预设参数获取亚马逊托盘发货信息", description = "使用默认参数构建托盘发货请求，方便快速测试")
    @PostMapping("/ship-pallet-default")
    public Result<AmazonShipPalletRespDTO> getShipPalletWithDefault(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "追踪号") @RequestParam(required = false, defaultValue = "TBA320111124171") String trackingNumber) {

        AmazonShipPalletReqDTO dto = new AmazonShipPalletReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum("AMZPROF-00002");
        dto.setShipFromPartyId("EJBK");
        dto.setSellingPartyId("IPOXQ");
        dto.setVendorContainerId("OCWPS-202200200000");
        dto.setRequestRefNum("OCWPS-202200200000"); // 使用容器ID作为请求参考编号
        dto.setPackageTrackingNumbers(Collections.singletonList(trackingNumber));

        // 设置标签格式为PNG
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());

        AmazonShipPalletRespDTO respDTO = amazonClientService.getShipPallet(dto);
        return Result.ok(respDTO);
    }
} 