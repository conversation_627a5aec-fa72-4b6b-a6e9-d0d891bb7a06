package cn.need.cloud.biz.controller.storage;

import cn.need.cloud.biz.converter.storage.StorageSnapshotRequestConverter;
import cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequest;
import cn.need.cloud.biz.model.param.storage.build.StorageSnapshotRequestBatchBuildParam;
import cn.need.cloud.biz.model.param.storage.build.StorageSnapshotRequestBuildParam;
import cn.need.cloud.biz.model.param.storage.create.StorageSnapshotRequestCreateParam;
import cn.need.cloud.biz.model.param.storage.update.StorageSnapshotRequestUpdateParam;
import cn.need.cloud.biz.model.query.storage.StorageSnapshotRequestQuery;
import cn.need.cloud.biz.model.result.storage.StorageSnapshotRequestBuildResult;
import cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestPageVO;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestVO;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestBuildService;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 仓储快照请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
@RestController
@RequestMapping("/api/biz/storage-snapshot-request")
@Tag(name = "仓储快照请求")
public class StorageSnapshotRequestController extends
        AbstractRestController<StorageSnapshotRequestService, StorageSnapshotRequest, StorageSnapshotRequestConverter, StorageSnapshotRequestVO> {

    @Resource
    private StorageSnapshotRequestBuildService storageSnapshotRequestBuildService;

    @Operation(summary = "新增仓储快照请求", description = "接收仓储快照请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<StorageSnapshotRequest> insert(
            @Valid @RequestBody @Parameter(description = "数据对象", required = true) StorageSnapshotRequestCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改仓储快照请求", description = "接收仓储快照请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(
            @Valid @RequestBody @Parameter(description = "数据对象", required = true) StorageSnapshotRequestUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除仓储快照请求", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(
            @RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取仓储快照请求详情", description = "根据数据主键id，从数据库中获取其对应的仓储快照请求详情")
    @GetMapping(value = "/detail/{id}")
    public Result<StorageSnapshotRequestVO> detail(
            @PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取仓储快照请求详情", description = "根据数据RefNum，从数据库中获取其对应的仓储快照请求详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<StorageSnapshotRequestVO> detail(
            @PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取仓储快照请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓储快照请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<StorageSnapshotRequestPageVO>> list(
            @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<StorageSnapshotRequestQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "创建存储快照请求", description = "根据传入的参数创建存储快照请求，支持为指定交易伙伴或所有交易伙伴创建快照")
    @PostMapping(value = "/build")
    public Result<StorageSnapshotRequestBuildResult> buildStorageSnapshotRequest(
            @Valid @RequestBody @Parameter(description = "构建参数对象", required = true) StorageSnapshotRequestBuildParam buildParam) {
        // 调用构建服务创建快照请求
        StorageSnapshotRequestBuildResult result = storageSnapshotRequestBuildService
                .createStorageSnapshotRequest(buildParam);
        // 返回创建结果
        return success(result);
    }

    @Operation(summary = "批量创建存储快照请求", description = "根据传入的时间范围批量创建存储快照请求，为指定时间范围内每一天的所有交易伙伴创建快照")
    @PostMapping(value = "/build-batch")
    public Result<StorageSnapshotRequestBuildResult> buildStorageSnapshotRequestBatch(
            @Valid @RequestBody @Parameter(description = "批量构建参数对象", required = true) StorageSnapshotRequestBatchBuildParam batchBuildParam) {
        // 调用构建服务批量创建快照请求
        StorageSnapshotRequestBuildResult result = storageSnapshotRequestBuildService.createStorageSnapshotRequestBatch(
                batchBuildParam.getWarehouseId(),
                batchBuildParam.getStartDate(),
                batchBuildParam.getEndDate(),
                batchBuildParam.getNote());
        // 返回批量创建结果
        return success(result);
    }
}
