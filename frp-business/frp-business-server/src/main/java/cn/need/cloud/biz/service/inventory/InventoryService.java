package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.query.inventory.InventoryQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInStockVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库存服务接口
 * </p>
 * <p>
 * 该接口提供库存管理的核心业务功能，包括库存查询、库存统计等。
 * 支持不同类型的库存查询需求，如在库库存、预期库存、全部库存等。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 查询产品在库库存
 * 2. 根据产品ID获取库存详情
 * 3. 查询产品在不同仓库的库存分布
 * 4. 分页查询库存信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
public interface InventoryService {

    /**
     * 根据产品ID列表获取在库产品库存
     * <p>
     * 查询指定产品的在库库存信息，在库库存是指已经入库且可用的库存量。
     * 不包含已锁定、已预定、待入库的库存数量。
     * </p>
     *
     * @param productIds 产品ID列表，用于指定需要查询库存的产品
     * @return 返回产品的在库库存信息列表
     */
    List<InventoryInStockVO> getInventoryInStock(List<Long> productIds);

    /**
     * 根据产品ID列表获取产品库存
     * <p>
     * 查询指定产品的库存信息，包含在库数量、锁定数量、预定数量等完整库存数据。
     * 返回的数据可用于库存管理和库存分析。
     * </p>
     *
     * @param productIds 产品ID列表，用于指定需要查询库存的产品
     * @return 返回产品的完整库存信息列表
     */
    List<InventoryVO> getInventory(List<Long> productIds);

    /**
     * 根据产品ID列表获取产品库存信息并以Map形式返回
     * <p>
     * 查询指定产品的库存信息，并将结果以Map形式返回，
     * 其中Key为产品ID，Value为对应的库存信息。
     * 这种返回格式便于快速查找特定产品的库存信息。
     * </p>
     *
     * @param productIdList 产品ID列表，用于指定需要查询库存的产品
     * @return 返回以产品ID为键，库存信息为值的Map集合
     */
    Map<Long, InventoryVO> getInventoryReturnAll(List<Long> productIdList);

    /**
     * 根据产品ID获取所有仓库的库存信息
     * <p>
     * 查询指定产品在所有仓库的库存分布情况，
     * 可用于多仓库管理和跨仓库库存分析。
     * 返回结果包含每个仓库的库存详情。
     * </p>
     *
     * @param productId 产品ID，用于指定需要查询库存的产品
     * @return 返回产品在各仓库的库存信息列表
     */
    List<InventoryVO> listWarehouseInventoryListByProductId(Long productId);

    /**
     * 分页查询库存信息
     * <p>
     * 根据查询条件和分页参数查询库存信息，
     * 支持复杂查询条件和排序规则。
     * 返回结果包含分页信息和库存数据。
     * </p>
     *
     * @param search 包含查询条件和分页信息的对象
     * @return 返回带分页信息的库存数据
     */
    PageData<InventoryVO> pageByQuery(PageSearch<InventoryQuery> search);
}