package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductScanDTO;
import cn.need.cloud.biz.model.entity.product.ProductScan;
import cn.need.cloud.biz.model.vo.product.ProductScanVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品扫描 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductScanConverter extends AbstractModelConverter<ProductScan, ProductScanVO, ProductScanDTO> {

}
