package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoRequestDTO;
import cn.need.cloud.biz.model.entity.oco.OcoRequest;
import cn.need.cloud.biz.model.vo.oco.OcoRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO请求表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoRequestConverter extends AbstractModelConverter<OcoRequest, OcoRequestVO, OcoRequestDTO> {

}



