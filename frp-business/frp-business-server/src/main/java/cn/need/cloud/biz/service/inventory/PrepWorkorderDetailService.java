package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;

/**
 * <p>
 * OTC预提工单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface PrepWorkorderDetailService<TPrepWorkorderDetail extends PrepWorkorderDetailModel, TPrepWorkorder extends PrepWorkorderModel> {

    TPrepWorkorderDetail createPrepWorkorderDetail(
            TPrepWorkorder prepWorkorder,
            InventoryVO inventoryVO,
            int qty,
            int lineNum,
            PrepWorkOrderDetailTypeEnum detailType
    );
}