package cn.need.cloud.biz.service;

import cn.need.cloud.biz.model.entity.PrepRequest;
import cn.need.cloud.biz.model.param.otb.create.PrepRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.prep.PrepRequestUpdateParam;
import cn.need.cloud.biz.model.query.PrepRequestQuery;
import cn.need.cloud.biz.model.vo.PrepRequestVO;
import cn.need.cloud.biz.model.vo.page.PrepRequestPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 预请求服务接口，定义了预请求相关的业务操作。
 * </p>
 *
 * <p>
 * 预请求（PrepRequest）是系统中用于准备和验证请求的中间状态实体，
 * 通常在正式请求处理前进行数据验证和预处理。本接口提供了预请求的
 * 创建、更新、查询等核心业务功能。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-28
 */
public interface PrepRequestService extends SuperService<PrepRequest> {

    /**
     * 根据参数新增预请求
     *
     * @param createParam 请求创建参数，包含需要插入的预请求的相关信息
     * @return 预请求ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(PrepRequestCreateParam createParam);


    /**
     * 根据参数更新预请求
     *
     * @param updateParam 请求创建参数，包含需要更新的预请求的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(PrepRequestUpdateParam updateParam);

    /**
     * 根据查询条件获取预请求列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预请求对象的列表(分页)
     */
    List<PrepRequestPageVO> listByQuery(PrepRequestQuery query);

    /**
     * 根据查询条件获取预请求列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预请求对象的列表(分页)
     */
    PageData<PrepRequestPageVO> pageByQuery(PageSearch<PrepRequestQuery> search);

    /**
     * 根据ID获取预请求
     *
     * @param id 预请求ID
     * @return 返回预请求VO对象
     */
    PrepRequestVO detailById(Long id);

    /**
     * 根据预请求唯一编码获取预请求
     *
     * @param refNum 预请求唯一编码
     * @return 返回预请求VO对象
     */
    PrepRequestVO detailByRefNum(String refNum);


}