package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigOtbDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtb;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置otb 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigOtbConverter extends AbstractModelConverter<FeeConfigOtb, FeeConfigOtbVO, FeeConfigOtbDTO> {

}
