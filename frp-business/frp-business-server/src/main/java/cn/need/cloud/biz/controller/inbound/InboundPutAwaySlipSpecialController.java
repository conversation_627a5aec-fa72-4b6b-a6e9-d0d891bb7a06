package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.model.vo.inbound.putaway.InboundRollBackVO;
import cn.need.cloud.biz.service.inbound.InboundPutAwaySlipSpecialService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 入库上架 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@RestController
@RequestMapping("/api/biz/inbound-putaway-slip/special")
@Tag(name = "PutAwaySlip special接口")
@Slf4j
public class InboundPutAwaySlipSpecialController extends AbstractController {

    @Resource
    private InboundPutAwaySlipSpecialService service;

    @Operation(summary = "常规上架单回滚", description = "常规上架单回滚")
    @PostMapping(value = "regular-rollback")
    public Result<Boolean> rollBack(@RequestBody @Parameter(description = "上架单", required = true) InboundRollBackVO inboundRollBackVO) {

        service.rollBack(inboundRollBackVO);
        return Result.ok(Boolean.TRUE);
    }

}
