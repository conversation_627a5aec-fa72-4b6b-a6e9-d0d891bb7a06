package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.service.inbound.InboundPalletSpecialService;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * 入库打托 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@RestController
@RequestMapping("/api/biz/inbound-pallet/special")
@Tag(name = "Pallet special接口")
@Slf4j
public class InboundPalletSpecialController extends AbstractController {

    @Resource
    private InboundPalletSpecialService service;

    @Operation(summary = "打托单回滚", description = "打托单回滚")
    @PostMapping(value = "/pallet-rollback")
    public Result<Integer> rollBack(@RequestBody @Parameter(description = "打托单信息", required = true) DeletedNoteParam deletedNoteParam) {

        return Result.ok(service.palletRollBack(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }
}
