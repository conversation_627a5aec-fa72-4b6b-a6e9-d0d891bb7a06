package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.converter.otc.OtcPrepPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.base.update.RollbackPutawayUnitsUpdateParam;
import cn.need.cloud.biz.model.param.otc.update.pickingslip.prep.OtcPrepPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.vo.base.PrepUnpickVO;
import cn.need.cloud.biz.model.vo.base.workorder.PrepWorkorderRollbackPutawayUnitsVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPrepPickingSlipSpecialService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.redis.RedissonKit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * OTC拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-prep-picking-slip/special")
@Tag(name = "OTC Prep拣货单-Special")
@Validated
public class OtcPrepPickingSlipSpecialController extends AbstractRestController<OtcPrepPickingSlipService, OtcPrepPickingSlip, OtcPrepPickingSlipConverter, OtcPrepPickingSlipVO> {

    @Resource
    private OtcPrepPickingSlipSpecialService specialService;

    @Operation(summary = "Unpick", description = "Unpick")
    @PostMapping(value = "/unpick")
    public Result<Boolean> unPick(@RequestBody @Valid OtcPrepPickingSlipUnpickCreateParam query) {
        RedissonKit.getInstance().lock(RedisConstant.OTC_PUTAWAY_SLIP_CREATE_LOCK_PREFIX + query.getWorkorderId(), lock -> {
            specialService.unpick(query);
        });
        return success(true);
    }

    @Operation(summary = "可Unpick列表", description = "Unpick列表")
    @GetMapping(value = "/unpick/list/{prepWorkorderId}")
    public Result<PrepUnpickVO> unpickByWorkorderId(@PathVariable(name = "prepWorkorderId") Long prepWorkorderId) {

        return success(specialService.unpickByWorkorderId(prepWorkorderId));
    }

    @Operation(summary = "Batch Cancel", description = "Batch Cancel")
    @PostMapping(value = "/batch-cancel")
    public Result<Boolean> batchCancel(@RequestBody PickingSlipCancelUpdateParam param) {
        return success(specialService.batchCancel(param));
    }

    @Operation(summary = "Rollback PutAway Units", description = "回滚上架数量")
    @PostMapping(value = "/rollback-putaway-units")
    public Result<Boolean> rollbackPutAwayUnits(@RequestBody RollbackPutawayUnitsUpdateParam param) {
        return success(specialService.rollbackPutAwayUnits(param));
    }

    @Operation(summary = "Rollback PutAway Units 列表", description = "回滚上架数量列表")
    @GetMapping(value = "/rollback-putaway-units/list/{prepWorkorderId}")
    public Result<PrepWorkorderRollbackPutawayUnitsVO> rollbackPutAwayUnitsList(@PathVariable(name = "prepWorkorderId") Long prepWorkorderId) {
        return success(specialService.rollbackPutAwayUnitsList(prepWorkorderId));
    }

}
