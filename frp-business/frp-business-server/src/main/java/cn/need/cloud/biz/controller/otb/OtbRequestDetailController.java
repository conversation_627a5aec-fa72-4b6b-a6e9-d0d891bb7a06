package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbRequestDetailConverter;
import cn.need.cloud.biz.model.entity.otb.OtbRequestDetail;
import cn.need.cloud.biz.model.param.otb.create.request.OtbRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestDetailUpdateParam;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestDetailQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestDetailPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.service.otb.request.OtbRequestDetailService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTB请求详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-request-detail")
@Tag(name = "OTB请求详情")
public class OtbRequestDetailController extends AbstractRestController<OtbRequestDetailService, OtbRequestDetail, OtbRequestDetailConverter, OtbRequestDetailVO> {

    @Operation(summary = "新增OTB请求详情", description = "接收OTB请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRequestDetailCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OTB请求详情", description = "接收OTB请求详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRequestDetailUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OTB请求详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OTB请求详情详情", description = "根据数据主键id，从数据库中获取其对应的OTB请求详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbRequestDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB请求详情详情
        OtbRequestDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTB请求详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB请求详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbRequestDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbRequestDetailQuery> search) {

        // 获取OTB请求详情分页
        PageData<OtbRequestDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
