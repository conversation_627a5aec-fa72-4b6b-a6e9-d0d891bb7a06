package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.warehouse.PalletTemplateConverter;
import cn.need.cloud.biz.mapper.warehouse.PalletTemplateMapper;
import cn.need.cloud.biz.model.entity.warehouse.PalletEmptyProfile;
import cn.need.cloud.biz.model.entity.warehouse.PalletTemplate;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletTemplateUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletTemplateCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletTemplateQuery;
import cn.need.cloud.biz.model.vo.page.PalletTemplatePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.service.warehouse.PalletEmptyProfileService;
import cn.need.cloud.biz.service.warehouse.PalletTemplateService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 打托模板 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class PalletTemplateServiceImpl extends SuperServiceImpl<PalletTemplateMapper, PalletTemplate> implements PalletTemplateService {

    @Resource
    private PalletEmptyProfileService palletEmptyProfileService;
    @Resource
    private ProductVersionService productVersionService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(PalletTemplateCreateParam createParam) {
        // 检查传入打托模板参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取打托模板转换器实例，用于将打托模板参数对象转换为实体对象
        PalletTemplateConverter converter = Converters.get(PalletTemplateConverter.class);

        // 将打托模板参数对象转换为实体对象并初始化
        PalletTemplate entity = initPalletTemplate(converter.toEntity(createParam));

        // 插入打托模板实体对象到数据库
        super.insert(entity);

        // 返回打托模板ID
        return entity.getId();
    }


    /**
     * 初始化打托模板对象
     * 此方法用于设置打托模板对象的必要参数，确保其处于有效状态
     *
     * @param entity 打托模板对象，不应为空
     * @return 返回初始化后的打托模板
     * @throws BusinessException 如果传入的打托模板为空，则抛出此异常
     */
    private PalletTemplate initPalletTemplate(PalletTemplate entity) {
        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "PalletTemplate"));
        }
        // 生成RefNum
        entity.setRefNum(FormatUtil.substringAfter(RefNumTypeEnum.PALLET_TEMPLATE.getCode(), WarehouseContextHolder.getWarehouseId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        //查看是否存在默认模板
        PalletTemplate template = getByProVersionIdAndDefaultFlag(entity.getProductVersionId());
        if (ObjectUtil.isEmpty(template)) {
            entity.setSetDefaultFlag(Boolean.TRUE);
        }
        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(PalletTemplateUpdateParam updateParam) {
        // 检查传入打托模板参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取打托模板转换器实例，用于将打托模板参数对象转换为实体对象
        PalletTemplateConverter converter = Converters.get(PalletTemplateConverter.class);

        // 将打托模板参数对象转换为实体对象
        PalletTemplate entity = converter.toEntity(updateParam);

        // 执行更新打托模板操作
        return super.update(entity);

    }

    @Override
    public List<PalletTemplatePageVO> listByQuery(PalletTemplateQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<PalletTemplatePageVO> pageByQuery(PageSearch<PalletTemplateQuery> search) {
        //获取分页参数
        Page<PalletTemplate> page = Conditions.page(search, entityClass);
        //分页参数
        PalletTemplateQuery condition = search.getCondition();
        //填充搜索id
        fillId(condition);
        //获取分页列表
        List<PalletTemplatePageVO> dataList = mapper.listByQuery(condition, page);
        //计算产品数量及箱数
        computeQty(dataList);
        //填充产品信息
        ProductVersionCacheUtil.filledProductVersion(dataList);
        //返回分页列表
        return new PageData<>(dataList, page);
    }

    @Override
    public PalletTemplateVO detailById(Long id) {
        PalletTemplate entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "PalletTemplate", id));
        }
        return buildPalletTemplateVO(entity);
    }

    @Override
    public PalletTemplateVO detailByRefNum(String refNum) {
        PalletTemplate entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "PalletTemplate", "refNum", refNum));
        }
        return buildPalletTemplateVO(entity);
    }

    /**
     * 根据产品id获取打托模板列表
     * 该方法用于获取默认打托模板，传入产品id集合
     *
     * @param productIds 产品集合
     * @return 默认打托模板列表
     */
    @Override
    public List<PalletTemplateVO> listByProductId(Collection<Long> productIds) {
        return BeanUtil.copyNew(
                super.lambdaQuery()
                        .in(PalletTemplate::getProductVersionId, productIds)
                        .eq(PalletTemplate::getSetDefaultFlag, Boolean.TRUE)
                        .list(), PalletTemplateVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer setDefault(Long palletTemplateId) {
        //获取模板信息
        PalletTemplate palletTemplate = getById(palletTemplateId);
        //设置默认
        palletTemplate.setSetDefaultFlag(Boolean.TRUE);
        //获取当前默认模板
        PalletTemplate template = getByProVersionIdAndDefaultFlag(palletTemplate.getProductVersionId());
        if (ObjectUtil.isEmpty(template)) {
            return update(palletTemplate);
        }
        template.setSetDefaultFlag(Boolean.FALSE);
        //持久化数据库
        return updateBatch(Lists.arrayList(palletTemplate, template));
    }

    @Override
    public PalletTemplate getByProVersionIdAndDefaultFlag(Long productVersionId) {
        return lambdaQuery()
                .eq(PalletTemplate::getProductVersionId, productVersionId)
                .eq(PalletTemplate::getSetDefaultFlag, Boolean.TRUE)
                .one();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(Long id, String deletedNote) {
        //获取模板
        PalletTemplate palletTemplate = super.getById(id);
        //删除模板
        super.removeAndNote(id, deletedNote);
        //判断是否为默认模板
        if (palletTemplate.getSetDefaultFlag()) {
            //获取最新添加的产品模板
            PalletTemplate latestTemplate = getLatestTemplate(palletTemplate.getProductVersionId());
            //更新模板默认状态
            latestTemplate.setSetDefaultFlag(Boolean.TRUE);
            //持久化
            mapper.updateById(latestTemplate);
        }
        return Boolean.TRUE;
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 获取最新的模板
     *
     * @param productVersionId 产品版本id
     * @return 打托模板
     */
    private PalletTemplate getLatestTemplate(Long productVersionId) {
        return mapper.getLatestTemplate(productVersionId);
    }

    /**
     * 计算箱数，产品数
     *
     * @param dataList 模板列表
     */
    private void computeQty(List<PalletTemplatePageVO> dataList) {
        //遍历模板列表
        dataList.forEach(item -> {
            //计算箱数
            item.setCartonCount(item.getCartonPerLayer() * item.getLayersCount() + item.getExtCarton());
            //计算产品数
            item.setPcsCount(item.getCartonCount() * item.getPcsPerCarton());
        });
    }

    /**
     * 填充搜索id
     * 该方法用于填充搜索id，
     * 1 由于跨多表查询，先将关联表按查询条件搜索然后获取行数据id，
     * 2 再将行数据id收集作为搜索条件查询当前表
     *
     * @param condition 查询条件
     */
    private void fillId(PalletTemplateQuery condition) {
        //版本产品表条件搜索
        Set<Long> productVersionIds = productVersionService.getProductVersionIds(condition.getProductRefNumList(), condition.getUpcList(), condition.getSupplierSkuList());
        condition.setProductVersionIdList(productVersionIds);
    }

    /**
     * 构建打托模板VO对象
     *
     * @param entity 打托模板对象
     * @return 返回包含详细信息的打托模板VO对象
     */
    private PalletTemplateVO buildPalletTemplateVO(PalletTemplate entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        //转换为vo对象
        PalletTemplateVO palletTemplateVO = Converters.get(PalletTemplateConverter.class).toVO(entity);
        //填充版本产品
        ProductVersionCacheUtil.filledProductVersion(palletTemplateVO);
        // 填充空托盘模板
        PalletEmptyProfile palletEmptyProfile = palletEmptyProfileService.getById(palletTemplateVO.getPalletEmptyProfileId());
        palletTemplateVO.setPalletEmptyProfile(BeanUtil.copyNew(palletEmptyProfile, PalletEmptyProfileVO.class));
        // 返回包含详细信息的打托模板VO对象
        return palletTemplateVO;
    }

}
