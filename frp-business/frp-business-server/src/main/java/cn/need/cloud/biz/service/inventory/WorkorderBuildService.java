package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderInputContext;
import cn.need.cloud.biz.model.entity.base.WorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;

import java.util.List;

/**
 * <p>
 * OTC预提工单详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WorkorderBuildService {

    /**
     * 为工单分配库存
     *
     * @param workorderDetailList 工单详情列表
     * @param workOrder           工单模型
     * @param context             构建工单输入上下文
     * @return 包含工单分配库存结果的列表
     */
    List<BuildWorkOrderDetailReturnContext> allocateInventoryForWorkOrder(
            List<? extends WorkorderDetailModel> workorderDetailList,
            WorkorderModel workOrder,
            BuildWorkOrderInputContext context
    );
}