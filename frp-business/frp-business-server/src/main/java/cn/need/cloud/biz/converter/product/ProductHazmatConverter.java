package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductHazmatDTO;
import cn.need.cloud.biz.model.entity.product.ProductHazmat;
import cn.need.cloud.biz.model.vo.product.ProductHazmatVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
public class ProductHazmatConverter extends AbstractModelConverter<ProductHazmat, ProductHazmatVO, ProductHazmatDTO> {

}
