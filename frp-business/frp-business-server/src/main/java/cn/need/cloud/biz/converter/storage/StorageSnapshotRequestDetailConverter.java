package cn.need.cloud.biz.converter.storage;

import cn.need.cloud.biz.client.dto.storage.StorageSnapshotRequestDetailDTO;
import cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequestDetail;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓储快照请求详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-19
 */
public class StorageSnapshotRequestDetailConverter extends AbstractModelConverter<StorageSnapshotRequestDetail, StorageSnapshotRequestDetailVO, StorageSnapshotRequestDetailDTO> {

}
