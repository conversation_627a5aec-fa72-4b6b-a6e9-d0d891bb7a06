package cn.need.cloud.biz.service.binlocation.impl;

import cn.need.cloud.biz.cache.BinLocationCacheRepertory;
import cn.need.cloud.biz.client.constant.ExceptionConstant;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.model.bo.base.putawayslip.PutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedRollbackChangeBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailPutawayBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipDetailModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.binlocation.BinLocationSpecialService;
import cn.need.cloud.biz.service.helper.PutawaySlipHelper;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.support.api.DeletedNoteParam;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Service
public class BinLocationSpecialServiceImpl implements BinLocationSpecialService {
    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    private BinLocationDetailLockedService binLocationDetailLockedService;
    @Resource
    private BinLocationCacheRepertory binLocationCacheRepertory;
    @Resource
    private BinLocationService binLocationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer removeBinLocation(DeletedNoteParam deletedNoteParam) {
        // 校验是否为默认库位
        /* 优化建议: 当前实现存在以下问题：
         * 1. 异常信息不标准，使用了硬编码的错误消息
         * 2. 没有对删除参数进行全面的验证
         * 3. 删除缓存和删除库位详情的操作可能存在原子性问题
         *
         * 优化建议：
         * 1. 使用标准化的错误消息常量，而不是硬编码的字符串
         * 2. 对删除参数进行全面的验证，包括检查ID是否为空
         * 3. 考虑使用分布式锁或事务隔离级别来确保操作的原子性
         * 4. 添加日志记录，便于跟踪删除操作
         *
         * 示例优化代码：
         * // 验证参数
         * if (deletedNoteParam == null || deletedNoteParam.getId() == null) {
         *     throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
         * }
         *
         * // 获取库位信息
         * BinLocation binLocation = binLocationService.getById(deletedNoteParam.getId());
         * if (binLocation == null) {
         *     throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocation", deletedNoteParam.getId()));
         * }
         *
         * // 检查是否为默认库位
         * if (Boolean.TRUE.equals(binLocation.getDefaultFlag())) {
         *     throw new BusinessException(ExceptionConstant.MODIFIED_DELETE);
         * }
         *
         * // 检查库位是否有产品
         * if (binLocationDetailService.exist(deletedNoteParam.getId())) {
         *     throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Cannot delete bin location with existing products"));
         * }
         *
         * // 记录删除操作日志
         * log.info("Removing bin location: {}, note: {}", deletedNoteParam.getId(), deletedNoteParam.getDeletedNote());
         *
         * try {
         *     // 删除库位详情
         *     binLocationDetailService.removeByBinLocationId(deletedNoteParam.getId());
         *     // 删除缓存
         *     binLocationCacheRepertory.delBinLocation(StringUtil.toString(deletedNoteParam.getId()));
         *     // 删除库位
         *     return binLocationService.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote());
         * } catch (Exception e) {
         *     log.error("Failed to remove bin location: {}", deletedNoteParam.getId(), e);
         *     throw e;
         * }
         */
        // 校验是否为默认库位
        BinLocation binLocation = binLocationService.getById(deletedNoteParam.getId());
        if (Boolean.TRUE.equals(binLocation.getDefaultFlag())) {
            // 多语言
            throw new BusinessException(ExceptionConstant.MODIFIED_DELETE);
        }
        //  库位有产品
        if (binLocationDetailService.exist(deletedNoteParam.getId())) {
            throw new BusinessException("exist product  cannot be delete");
        }
        // 删除库位详情
        binLocationDetailService.removeByBinLocationId(deletedNoteParam.getId());
        // 删除缓存
        binLocationCacheRepertory.delBinLocation(StringUtil.toString(deletedNoteParam.getId()));
        // 删除库位
        return binLocationService.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote());
    }

    @Override
    public <PutawaySlipDetail extends PutawaySlipDetailModel,
            Param extends PutawaySlipPutAwayDetailBO<PutawaySlipDetail>> void rollback(List<Param> paramDetailList) {

        // 库位锁
        List<Long> lockedIds = paramDetailList.stream()
                // 获取ReadyToGo、拣货库位锁id
                .flatMap(obj -> Stream.of(obj.getReadyToGoLockedId(), obj.getPickBinLocationDetailLockedId()))
                .distinct()
                .toList();

        List<BinLocationDetailLocked> lockedList = binLocationDetailLockedService.listByIds(lockedIds);

        // 初始化上架的库位
        paramDetailList.forEach(obj -> obj.setPutawayBinLocationDetailId(obj.getPickBinLocationDetailId()));
        // 获取上架后的库位详情
        List<BinLocationDetail> putawayBinLocationDetails = paramDetailList.stream()
                // 上架库位与拣货库位不同，说明是需要上架处理
                .filter(obj -> !Objects.equals(obj.getPutawayBinLocationId(), obj.getPickBinLocationId()))
                .map(obj -> {
                    PutawaySlipDetail putawaySlipDetail = obj.getPutawaySlipDetail();
                    BinLocationDetailPutawayBO putaway = new BinLocationDetailPutawayBO()
                            .setBinLocationId(obj.getPutawayBinLocationId())
                            .setProductId(putawaySlipDetail.getProductId())
                            .setProductVersionId(putawaySlipDetail.getProductVersionId())
                            .setPutawayQty(0)
                            .setChangeType(BinLocationLogEnum.OTC_PUTAWAY_SLIP_PUT_AWAY.getStatus())
                            .setRefNumModel(obj.getPutawayRefNumModel());

                    // 上架库位
                    BinLocationDetail putawayDetail = binLocationDetailService.putIfAbsent(putaway);

                    // 设置上架id
                    obj.setPutawayBinLocationDetailId(putawayDetail.getId());

                    // 上架单详情设置目标库位信息
                    putawaySlipDetail.setDestBinLocationDetailId(putawayDetail.getId());
                    putawaySlipDetail.setDestBinLocationId(putawayDetail.getBinLocationId());

                    return putawayDetail;
                })
                .toList();

        // 拣货库位详情
        List<Long> binLocationDetailIds = Stream.concat(
                        // 拣货库位
                        paramDetailList.stream()
                                .flatMap(obj -> Stream.of(obj.getPickBinLocationDetailId())),
                        // 锁ReadyToGo库位
                        lockedList.stream()
                                .map(BinLocationDetailLocked::getBinLocationDetailId))
                .distinct()
                .toList();

        // 所有库位
        List<BinLocationDetail> binLocationDetails = Stream.concat(
                        binLocationDetailService.listByIds(binLocationDetailIds).stream(),
                        putawayBinLocationDetails.stream()
                )
                .toList();

        // 库位回滚
        PutawaySlipHelper.buildBinRollbackChangeInfo(paramDetailList, lockedList, binLocationDetails);

        // 移动库位、锁释放
        List<BinLocationDetailLockedRollbackChangeBO> changeList
                = StreamUtils.distinctMap(paramDetailList, PutawaySlipPutAwayDetailBO::getChange);
        binLocationDetailLockedService.updateByChange(changeList);
        binLocationDetailService.updateInStockByChange(changeList);
    }
}
