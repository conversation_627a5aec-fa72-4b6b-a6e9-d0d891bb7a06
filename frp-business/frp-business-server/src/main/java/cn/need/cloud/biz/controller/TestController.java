package cn.need.cloud.biz.controller;

import cn.need.cloud.ship.client.api.SSCCClient;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Set;

/**
 * 测试控制器，用于系统功能测试和开发验证。
 * 提供了各种测试接口用于验证系统功能和集成测试。
 * 该控制器主要用于开发和测试环境，不应在生产环境中使用。
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-28
 */
@RestController
@RequestMapping("/api/biz/bop")
@Tag(name = "bop接口")
public class TestController extends AbstractController {
    @Resource
    private SSCCClient ssccClient;

    /**
     * 更新库存接口，用于测试库存更新功能。
     * 此方法返回当前系统中所有租户的ID列表，主要用于系统功能验证。
     *
     * @return 包含所有租户ID的结果对象
     */
    @Operation(summary = "更新库存", description = "更新库存")
    @PostMapping(value = "/update")
    public Result<Set<Long>> update() {
        Set<Long> list = TenantCacheUtil.list();
        return Result.ok(list);
    }
}
