package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundRequestDetailDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundRequestDetail;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库请求详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundRequestDetailConverter extends AbstractModelConverter<InboundRequestDetail, InboundRequestDetailVO, InboundRequestDetailDTO> {

}
