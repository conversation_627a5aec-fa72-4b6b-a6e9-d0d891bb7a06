package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.QuoteConverter;
import cn.need.cloud.biz.mapper.feeconfig.QuoteMapper;
import cn.need.cloud.biz.model.entity.base.FeeConfigModel;
import cn.need.cloud.biz.model.entity.base.SwitchActiveModel;
import cn.need.cloud.biz.model.entity.feeconfig.Quote;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.param.feeconfig.create.QuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.QuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.QuoteQuery;
import cn.need.cloud.biz.model.vo.base.FeeConfigRefNumVO;
import cn.need.cloud.biz.model.vo.base.RefNumVO;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteFullVO;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.QuotePageVO;
import cn.need.cloud.biz.service.feeconfig.*;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 仓库报价 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@Service
public class QuoteServiceImpl extends SuperServiceImpl<QuoteMapper, Quote> implements QuoteService {

    @Resource
    @Lazy
    private FeeConfigInboundService feeConfigInboundService;
    @Resource
    @Lazy
    private FeeConfigOtcService feeConfigurationOtcService;
    @Resource
    @Lazy
    private FeeConfigOtbService feeConfigurationOtbService;
    @Resource
    @Lazy
    private FeeConfigStorageService feeConfigurationStorageService;
    @Resource
    @Lazy
    private SupplierQuoteService supplierQuoteService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(QuoteCreateParam createParam) {
        // 检查传入仓库报价参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        // 将仓库报价参数对象转换为实体对象并初始化
        Quote entity = initQuote(createParam);

        updateFeeConfigQuote(createParam, entity);

        // 插入仓库报价实体对象到数据库
        super.insert(entity);

        // 返回仓库报价ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(QuoteUpdateParam updateParam) {
        // 检查传入仓库报价参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        Quote entity = initQuote(updateParam);

        checkSupplierQuote(entity);

        updateFeeConfigQuote(updateParam, entity);

        // 执行更新仓库报价操作
        return super.update(entity);

    }

    @Override
    public List<QuotePageVO> listByQuery(QuoteQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<QuotePageVO> pageByQuery(PageSearch<QuoteQuery> search) {
        Page<Quote> page = Conditions.page(search, entityClass);
        List<QuotePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public QuoteVO detailById(Long id) {
        Quote entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Quote", id));
        }
        return buildQuoteVO(entity);
    }

    @Override
    public QuoteVO detailByRefNum(String refNum) {
        Quote entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Quote", "refNum", refNum));
        }
        return buildQuoteVO(entity);
    }

    /**
     * 启用禁用
     *
     * @param id feeConfigId
     * @return 影响行数
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Integer switchActive(Long id) {
        //数据初始化
        Quote quote = super.getById(id);
        if (ObjectUtil.isEmpty(quote)) {
            throw new BusinessException(StringUtil.format("id: {} not found in FeeConfigOtb", id));
        }

        checkSupplierQuote(quote);

        //更新状态
        if (quote.getActiveFlag()) {
            quote.setActiveFlag(Boolean.FALSE);

        } else {
            quote.setActiveFlag(Boolean.TRUE);

        }
        return super.update(quote);
    }

    @Override
    public QuoteFullVO detailFullById(Long id) {
        Quote entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Quote", id));
        }
        return buildQuoteFullVO(entity);
    }


    private void updateFeeConfigQuote(QuoteCreateParam createParam, Quote entity) {
        // 1. 处理Inbound配置
        handleConfigComparison(
                feeConfigInboundService,
                feeConfigInboundService,
                createParam.getFeeConfigInboundIdList(),
                entity.getId(),
                "Inbound"
        );

        // 2. 处理OTC配置
        handleConfigComparison(
                feeConfigurationOtcService,
                feeConfigurationOtcService,
                createParam.getFeeConfigOtcIdList(),
                entity.getId(),
                "OTC"
        );

        // 3. 处理OTB配置
        handleConfigComparison(
                feeConfigurationOtbService,
                feeConfigurationOtbService,
                createParam.getFeeConfigOtbIdList(),
                entity.getId(),
                "OTB"
        );

        // 4. 处理Storage配置
        handleConfigComparison(
                feeConfigurationStorageService,
                feeConfigurationStorageService,
                createParam.getFeeConfigStorageIdList(),
                entity.getId(),
                "Storage"
        );
    }

    @Override
    public int removeAndNote(Long id, String note) {
        Quote quote = super.getById(id);
        if (ObjectUtil.isEmpty(quote)) {
            throw new BusinessException(StringUtil.format("id: {} not found in FeeConfigOtb", id));
        }

        checkSupplierQuote(quote);

        //Remove 相关联的 FeeConfig
        feeConfigInboundService.removeQuoteId(id);
        feeConfigurationOtcService.removeQuoteId(id);
        feeConfigurationOtbService.removeQuoteId(id);
        feeConfigurationStorageService.removeQuoteId(id);

        return super.removeAndNote(id, note);
    }

    private <T extends FeeConfigModel & SwitchActiveModel, TSuperService extends SuperService<T>> void handleConfigComparison(
            SuperService<T> superService,
            FeeConfigService<T, TSuperService> service,
            List<Long> paramIds,
            Long entityId,
            String configType
    ) {
        // 获取当前关联的配置（数据库已有的）
        List<FeeConfigRefNumVO> existingConfigs = Objects.requireNonNullElse(
                service.feeConfigRefNumByQuoteId(entityId), Collections.emptyList()
        );
        List<Long> existingIds = existingConfigs.stream()
                .map(FeeConfigRefNumVO::getId)
                .toList();

        // 转换为Set以提升性能
        Set<Long> existingIdSet = new HashSet<>(existingIds);
        Set<Long> paramIdSet = new HashSet<>(Objects.requireNonNullElse(paramIds, Collections.emptyList()));

        // 计算需要移除和新增的ID
        List<Long> removeIds = existingIdSet.stream()
                .filter(id -> !paramIdSet.contains(id))
                .toList();
        List<Long> addIds = paramIdSet.stream()
                .filter(id -> !existingIdSet.contains(id))
                .toList();

        // 处理移除逻辑
        if (!removeIds.isEmpty()) {
            List<T> toRemove = superService.listByIds(removeIds);
            // 使用updateForAll方法来确保null值被正确更新
            toRemove.forEach(config -> {
                config.setQuoteId(null);
                superService.updateForAll(config);
            });
        }

        // 处理新增逻辑
        if (!addIds.isEmpty()) {
            List<T> toAdd = superService.listByIds(addIds);
            toAdd.forEach(config -> {
                // 检查配置有效性
                if (ObjectUtil.isEmpty(config.getActiveFlag()) || !config.getActiveFlag()) {
                    throw new BusinessException(
                            String.format("%s is Inactive, Can Not Config Quote",
                                    config.refNumLog())
                    );
                }
                // 检查是否已被其他报价关联
                if (ObjectUtil.isNotEmpty(config.getQuoteId()) && !config.getQuoteId().equals(entityId)) {
                    RefNumVO ref = refNumById(config.getQuoteId());
                    throw new BusinessException(
                            String.format(
                                    "%s has Config %s Quote: %s, Can Not Config Other Quote",
                                    config.refNumLog(), configType, ref.getShowString()
                            )
                    );
                }
                config.setQuoteId(entityId);
            });
            superService.updateBatch(toAdd);
        }
    }

    /**
     * 初始化仓库报价对象
     * 此方法用于设置仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param createParam 仓库报价 新增对象，不应为空
     * @return 返回初始化后的仓库报价
     * @throws BusinessException 如果传入的仓库报价为空，则抛出此异常
     */
    private Quote initQuote(QuoteCreateParam createParam) {
        // 检查传入的配置对象是否为空
        checkParam(createParam);

        // 获取仓库报价转换器实例，用于将仓库报价参数对象转换为实体对象
        QuoteConverter converter = Converters.get(QuoteConverter.class);

        // 将仓库报价参数对象转换为实体对象并初始化
        Quote entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_QUOTE));

        initQuote(entity);

        // 返回初始化后的配置对象
        return entity;
    }

    private void checkSupplierQuote(Quote entity) {
        //是否配置了 supplierQuote
        List<SupplierQuote> supplierQuotes = supplierQuoteService.listByQuoteId(entity.getId());

        if (ObjectUtil.isNotEmpty(supplierQuotes)) {
            throw new BusinessException(StringUtil.format("{} Has Config SupplierQuote {}, Can Not Update",
                    entity.refNumLog(),
                    supplierQuotes.stream().map(SupplierQuote::refNumLog).collect(Collectors.joining(","))));
        }
    }

    /**
     * 初始化仓库报价对象
     * 此方法用于设置仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 仓库报价 修改对象，不应为空
     * @return 返回初始化后的仓库报价
     * @throws BusinessException 如果传入的仓库报价为空，则抛出此异常
     */
    private Quote initQuote(QuoteUpdateParam updateParam) {
        // 检查传入的配置对象是否为空
        checkParam(updateParam);

        // 获取仓库报价转换器实例，用于将仓库报价参数对象转换为实体对象
        QuoteConverter converter = Converters.get(QuoteConverter.class);

        // 将仓库报价参数对象转换为实体对象并初始化
        Quote entity = converter.toEntity(updateParam);

        initQuote(entity);

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 检查传入的参数是否为空
     *
     * @param param 传入的参数对象
     * @throws BusinessException 如果传入的参数为空，则抛出此异常
     */
    private void checkParam(Object param) {
        if (ObjectUtil.isEmpty(param)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
    }

    /**
     * 初始化仓库报价对象
     * 此方法用于设置仓库报价对象的必要参数，确保其处于有效状态
     *
     * @param entity 仓库报价对象，不应为空
     * @throws BusinessException 如果传入的仓库报价为空，则抛出此异常
     */
    private void initQuote(Quote entity) {
        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("Quote cannot be empty");
        }

        // 添加必要的初始化逻辑
        // 例如：设置默认值、校验字段等
    }

    private QuoteFullVO buildQuoteFullVO(Quote entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的仓库报价VO对象
        final QuoteFullVO quoteVO = BeanUtil.copyNew(entity, QuoteFullVO.class);

        quoteVO.setFeeConfigInboundList(feeConfigInboundService.listDetailByQuoteId(entity.getId()));
        quoteVO.setFeeConfigOtcList(feeConfigurationOtcService.listDetailByQuoteId(entity.getId()));
        quoteVO.setFeeConfigOtbList(feeConfigurationOtbService.listDetailByQuoteId(entity.getId()));
        quoteVO.setFeeConfigStorageList(feeConfigurationStorageService.listDetailByQuoteId(entity.getId()));


        return quoteVO;

    }

    /**
     * 构建仓库报价VO对象
     *
     * @param entity 仓库报价对象
     * @return 返回包含详细信息的仓库报价VO对象
     */
    private QuoteVO buildQuoteVO(Quote entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的仓库报价VO对象
        final QuoteVO quoteVO = Converters.get(QuoteConverter.class).toVO(entity);

        quoteVO.setFeeConfigInboundList(feeConfigInboundService.feeConfigRefNumByQuoteId(entity.getId()));
        quoteVO.setFeeConfigOtcList(feeConfigurationOtcService.feeConfigRefNumByQuoteId(entity.getId()));
        quoteVO.setFeeConfigOtbList(feeConfigurationOtbService.feeConfigRefNumByQuoteId(entity.getId()));
        quoteVO.setFeeConfigStorageList(feeConfigurationStorageService.feeConfigRefNumByQuoteId(entity.getId()));


        return quoteVO;
    }

}
