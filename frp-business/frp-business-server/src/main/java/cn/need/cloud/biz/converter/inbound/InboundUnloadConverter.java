package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundUnloadDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundUnloadVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库工单卸货表 根据这个来生成上架单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundUnloadConverter extends AbstractModelConverter<InboundUnload, InboundUnloadVO, InboundUnloadDTO> {

}
