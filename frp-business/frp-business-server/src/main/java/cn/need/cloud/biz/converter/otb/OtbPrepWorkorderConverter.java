package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPrepWorkorderDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB预提工单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPrepWorkorderConverter extends AbstractModelConverter<OtbPrepWorkorder, OtbPrepWorkorderVO, OtbPrepWorkorderDTO> {

}
