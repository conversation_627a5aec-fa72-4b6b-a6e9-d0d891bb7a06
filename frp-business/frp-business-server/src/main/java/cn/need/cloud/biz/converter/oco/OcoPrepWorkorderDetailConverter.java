package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPrepWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPrepWorkorderDetail;
import cn.need.cloud.biz.model.vo.oco.OcoPrepWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO预处理工单明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPrepWorkorderDetailConverter extends AbstractModelConverter<OcoPrepWorkorderDetail, OcoPrepWorkorderDetailVO, OcoPrepWorkorderDetailDTO> {

}



