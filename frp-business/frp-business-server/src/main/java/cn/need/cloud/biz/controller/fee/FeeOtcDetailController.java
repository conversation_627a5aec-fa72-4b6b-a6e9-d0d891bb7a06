package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeOtcDetailConverter;
import cn.need.cloud.biz.model.entity.fee.FeeOtcDetail;
import cn.need.cloud.biz.model.query.fee.FeeOtcDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOtcDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOtcDetailPageVO;
import cn.need.cloud.biz.service.fee.FeeOtcDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 费用详情otc 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-otc-detail")
@Tag(name = "费用详情otc")
public class FeeOtcDetailController extends AbstractRestController<FeeOtcDetailService, FeeOtcDetail, FeeOtcDetailConverter, FeeOtcDetailVO> {


    @Operation(summary = "根据id获取费用详情otc详情", description = "根据数据主键id，从数据库中获取其对应的费用详情otc详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeOtcDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用详情otc详情", description = "根据数据RefNum，从数据库中获取其对应的费用详情otc详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeOtcDetailVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用详情otc分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用详情otc列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeOtcDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeOtcDetailQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
