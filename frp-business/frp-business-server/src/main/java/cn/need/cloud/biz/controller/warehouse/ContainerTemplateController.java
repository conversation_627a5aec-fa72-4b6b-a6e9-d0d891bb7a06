package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.ContainerTemplateConverter;
import cn.need.cloud.biz.model.entity.warehouse.ContainerTemplate;
import cn.need.cloud.biz.model.vo.warehouse.ContainerTemplateVO;
import cn.need.cloud.biz.service.warehouse.ContainerTemplateService;
import cn.need.cloud.biz.model.query.warehouse.ContainerTemplateQuery;
import cn.need.cloud.biz.model.param.warehouse.create.ContainerTemplateCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.ContainerTemplateUpdateParam;
import cn.need.cloud.biz.model.vo.page.ContainerTemplatePageVO;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 集装箱模板配置表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/api/biz/container-template")
@Tag(name = "集装箱模板配置表")
public class ContainerTemplateController extends AbstractRestController<ContainerTemplateService, ContainerTemplate, ContainerTemplateConverter, ContainerTemplateVO> {

    @Operation(summary = "新增集装箱模板配置表", description = "接收集装箱模板配置表的传参对象，将该对象持久化到数据库中")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ContainerTemplateCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改集装箱模板配置表", description = "接收集装箱模板配置表的传参对象，将该对象持久化到数据库中")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ContainerTemplateUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除集装箱模板配置表", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@Valid @RequestBody @Parameter(description = "删除原因", required = true) DeletedNoteParam deletedNoteParam) {
        Validate.notNull(deletedNoteParam.getId(), "id不能为空");
        Validate.notBlank(deletedNoteParam.getDeletedNote(), "删除原因不能为空");
        // 返回结果
        return success(service.removeById(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote(), UserCacheUtil.getUserId()));
    }

    @Operation(summary = "根据id获取集装箱模板配置表详情", description = "根据数据主键id，从数据库中获取其对应的集装箱模板配置表详情")
    @GetMapping(value = "/get/{id}")
    public Result<ContainerTemplateVO> get(@PathVariable @Parameter(description = "数据主键") Long id) {
        // 返回结果
        return success(service.getVOById(id));
    }

    @Operation(summary = "根据RefNum获取集装箱模板配置表详情", description = "根据数据RefNum，从数据库中获取其对应的集装箱模板配置表详情")
    @GetMapping(value = "/getByRefNum/{refNum}")
    public Result<ContainerTemplateVO> getByRefNum(@PathVariable @Parameter(description = "RefNum") String refNum) {
        // 返回结果
        return success(service.getVOByRefNum(refNum));
    }

    @Operation(summary = "获取集装箱模板配置表分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的集装箱模板配置表列表")
    @PostMapping(value = "/page")
    public Result<PageData<ContainerTemplatePageVO>> page(@RequestBody @Parameter(description = "搜索条件") PageSearch<ContainerTemplateQuery> search) {
        log.info("获取集装箱模板配置表分页列表，搜索条件：{}", JsonUtil.toJsonString(search));
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "获取集装箱模板配置表列表", description = "根据传入的搜索条件参数，从数据库中获取集装箱模板配置表列表")
    @PostMapping(value = "/list")
    public Result<List<ContainerTemplateVO>> list(@RequestBody @Parameter(description = "搜索条件") ContainerTemplateQuery query) {
        log.info("获取集装箱模板配置表列表，搜索条件：{}", JsonUtil.toJsonString(query));
        // 返回结果
        return success(service.listByQuery(query));
    }
}
