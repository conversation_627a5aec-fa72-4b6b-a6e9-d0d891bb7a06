package cn.need.cloud.biz.provider.product;

import cn.need.cloud.biz.client.api.path.ProductHazmatPath;
import cn.need.cloud.biz.client.api.product.ProductHazmatClient;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductHazmatCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductHazmatListRespDTO;
import cn.need.cloud.biz.model.entity.product.ProductHazmat;
import cn.need.cloud.biz.model.param.product.create.ProductHazmatCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductHazmatUpdateParam;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.service.product.ProductHazmatService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 产品多箱feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(ProductHazmatPath.PREFIX)
public class ProductHazmatProvider implements ProductHazmatClient {
    @Resource
    private ProductHazmatService productHazmatService;

    @Override
    @PostMapping(ProductHazmatPath.CREATE_UPDATE)
    @IgnoreAuth
    public Result<Boolean> createOrUpdate(@RequestBody ProductHazmatCreateOrUpdateReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //获取危险品
        ProductHazmat productHazmat = productHazmatService.getByProductId(reqDTO.getProductId());

        //判空
        if (ObjectUtil.isNull(productHazmat)) {
            ProductHazmatCreateParam createParam = BeanUtil.copyNew(reqDTO, ProductHazmatCreateParam.class);
            productHazmatService.insertByParam(createParam);
        } else {
            ProductHazmatUpdateParam updateParam = BeanUtil.copyNew(reqDTO, ProductHazmatUpdateParam.class);
            productHazmatService.updateByParam(updateParam);
        }

        return Result.ok(Boolean.TRUE);
    }

    @Override
    @IgnoreAuth
    public Result<Boolean> remove(@RequestBody BaseDeleteReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //获取hazmat
        ProductHazmat productHazmat = productHazmatService.getByProductId(reqDTO.getProductId());

        //幂等处理
        if (ObjectUtil.isEmpty(productHazmat)) {
            return Result.ok();
        }

        DeletedNoteParam deletedNoteParam = new DeletedNoteParam();
        deletedNoteParam.setId(productHazmat.getId());
        deletedNoteParam.setDeletedNote(reqDTO.getDeletedNote());
        //获取危险品
        productHazmatService.removeHazmat(deletedNoteParam);
        return Result.ok();
    }

    @Override
    @PostMapping(ProductHazmatPath.LIST_BY_PRODUCT_ID)
    @IgnoreAuth
    public Result<ProductHazmatListRespDTO> listByProductId(@RequestBody BaseProductQueryReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //获取危险品
        ProductHazmat productHazmat = productHazmatService.getByProductId(reqDTO.getProductId());

        ProductHazmatListRespDTO productHazmatListRespDTO = BeanUtil.copyNew(productHazmat, ProductHazmatListRespDTO.class);

        Map<Long, BaseProductDTO> productMap = ProductUtil.getByProductId(List.of(reqDTO.getProductId()));
        productHazmatListRespDTO.setBaseProductDTO(productMap.get(reqDTO.getProductId()));

        //返回结果
        return Result.ok(productHazmatListRespDTO);
    }
}
