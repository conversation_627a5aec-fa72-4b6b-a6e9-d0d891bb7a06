package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcShipStationConfigDTO;
import cn.need.cloud.biz.model.entity.otc.OtcShipStationConfig;
import cn.need.cloud.biz.model.vo.otc.OtcShipStationConfigVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 快递公司配置 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcShipStationConfigConverter extends AbstractModelConverter<OtcShipStationConfig, OtcShipStationConfigVO, OtcShipStationConfigDTO> {

}
