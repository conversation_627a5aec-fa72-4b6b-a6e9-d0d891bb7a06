package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbRequestConverter;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.param.otb.create.request.OtbRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestAuditParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestUpdateParam;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTB请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-request")
@Tag(name = "OTB请求")
@Validated
public class OtbRequestController extends AbstractRestController<OtbRequestService, OtbRequest, OtbRequestConverter, OtbRequestVO> {

    @Operation(summary = "新增OTB请求", description = "接收OTB请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRequestCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改OTB请求", description = "接收OTB请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRequestUpdateParam updateParam) {

        service.updateByParam(updateParam);
        // 返回结果
        return success();
    }

    /**
     * <p>
     * 审核
     * </p>
     *
     * @param param 需要审核请求的数据对象
     * @return 受影响行数
     */
    @Operation(summary = "审核接口，返回受影响行数", description = "接收数据的VO对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/audit")
    public Result<String> audit(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtbRequestAuditParam param) {

        service.audit(param);
        return success();
    }

    @Operation(summary = "根据id获取OTB请求详情", description = "根据数据主键id，从数据库中获取其对应的OTB请求详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB请求详情
        OtbRequestVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB请求详情", description = "根据数据RefNum，从数据库中获取其对应的OTB请求详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbRequestVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB请求详情
        OtbRequestVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTB请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbRequestQuery> search) {

        // 获取OTB请求分页
        PageData<OtbRequestPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Dashboard CountPreDay", description = "下拉列表")
    @PostMapping(value = "/count-pre-day")
    public Result<List<DropProVO>> countPreDay(@RequestBody @Parameter(description = "查询条件", required = true) OtbRequestQuery query) {

        return success(service.countPreDay(query));
    }
}
