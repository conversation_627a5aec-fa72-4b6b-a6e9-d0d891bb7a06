/**
 * <p>
 * 业务服务层根包
 * </p>
 * <p>
 * 该包及其子包提供系统所有业务领域的服务接口定义和实现。
 * 服务层是系统核心业务逻辑的实现地，负责协调各种资源完成业务功能，
 * 包括数据处理、业务规则验证、事务控制等。
 * </p>
 * <p>
 * 服务层遵循以下设计原则：
 * <ul>
 *   <li>高内聚低耦合 - 每个服务专注于特定业务领域</li>
 *   <li>接口与实现分离 - 所有服务都通过接口定义暴露功能</li>
 *   <li>事务一致性 - 保证业务操作的原子性和一致性</li>
 *   <li>领域驱动设计 - 按业务领域划分服务边界</li>
 * </ul>
 * </p>
 * <p>
 * 主要业务领域包括：
 * <ul>
 *   <li>入库管理 (inbound) - 处理入库请求、入库工单、上架等业务</li>
 *   <li>企业出库管理 (otb) - 处理企业出库请求、拣货、装箱、发货等业务</li>
 *   <li>消费者出库管理 (otc) - 处理消费者订单出库、拣货、装箱、发货等业务</li>
 *   <li>库存管理 (inventory) - 处理库存查询、库存锁定、库存调整等业务</li>
 *   <li>仓库管理 (warehouse) - 处理仓库基础信息、仓库配置等业务</li>
 *   <li>库位管理 (binlocation) - 处理库位的创建、查询、分配等业务</li>
 *   <li>产品管理 (product) - 处理产品基础信息、产品版本、产品关系等业务</li>
 *   <li>发货管理 (ship) - 处理物流单创建、物流标签生成等业务</li>
 *   <li>费用管理 (fee) - 处理仓储费用、服务费用的计算和管理</li>
 *   <li>系统配置 (setting) - 处理系统级别的配置和参数管理</li>
 * </ul>
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-17
 */
package cn.need.cloud.biz.service;