package cn.need.cloud.biz.converter.otb;


import cn.need.cloud.biz.client.dto.otb.OtbPackageDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPackageDetail;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB包裹详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPackageDetailConverter extends AbstractModelConverter<OtbPackageDetail, OtbPackageDetailVO, OtbPackageDetailDTO> {

}
