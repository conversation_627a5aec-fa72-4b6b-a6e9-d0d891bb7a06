package cn.need.cloud.biz.converter.otb;


import cn.need.cloud.biz.client.dto.otb.OtbPackageDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPackage;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB包裹 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPackageConverter extends AbstractModelConverter<OtbPackage, OtbPackageVO, OtbPackageDTO> {

}
