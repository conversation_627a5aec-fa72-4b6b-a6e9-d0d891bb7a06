package cn.need.cloud.biz.provider.product;

import cn.need.cloud.biz.client.api.path.ProductMultiboxPath;
import cn.need.cloud.biz.client.api.product.ProductMultiboxClient;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseProductQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.product.MultiboxDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductMultiboxCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.resp.product.MultiboxDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.product.ProductMultiboxListRespDTO;
import cn.need.cloud.biz.model.param.product.update.MultiboxDetailParam;
import cn.need.cloud.biz.model.param.product.update.ProductMultiboxCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.product.MultiboxDetailVO;
import cn.need.cloud.biz.model.vo.product.ProductMultiboxListVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.service.product.ProductMultiboxService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 产品多箱feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(ProductMultiboxPath.PREFIX)
public class ProductMultiboxProvider implements ProductMultiboxClient {
    @Resource
    private ProductMultiboxService productMultiboxService;
    @Resource
    private ProductSpecialService productSpecialService;

    @Override
    @IgnoreAuth
    public Result<Integer> createOrUpdate(@RequestBody ProductMultiboxCreateOrUpdateReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //获取产品对象集合
        List<ProductReqDTO> list = reqDTO.getMultiboxList()
                .stream()
                .map(item ->
                        item.getDetailList()
                                .stream()
                                .map(MultiboxDetailReqDTO::getProduct)
                                .collect(Collectors.toList())
                )
                .flatMap(List::stream)
                .collect(Collectors.toList());
        list.add(reqDTO.getProduct());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), list);

        //构建产品多箱方法入参
        List<ProductMultiboxCreateOrUpdateParam> paramList = buildParam(reqDTO);

        //返回影响行数
        return Result.ok(productMultiboxService.createOrUpdate(paramList));
    }

    @Override
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseDeleteReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO);

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //返回影响行数
        return Result.ok(productSpecialService.removeMultibox(reqDTO.getProductId(), reqDTO.getDeletedNote()));
    }

    @Override
    @PostMapping(value = ProductMultiboxPath.LIST_BY_PRODUCT_ID)
    @IgnoreAuth
    public Result<List<ProductMultiboxListRespDTO>> listByProductId(@RequestBody BaseProductQueryReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO);

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //调用方法
        List<ProductMultiboxListVO> list = productMultiboxService.listByProductId(reqDTO.getProductId());

        //获取产品id
        Set<Long> productIdList = list.stream()
                .map(item -> {
                    Set<Long> productIds = item.getDetailList()
                            .stream()
                            .map(MultiboxDetailVO::getProductId)
                            .collect(Collectors.toSet());
                    productIds.add(item.getProductId());
                    return productIds;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());

        //根据产品id映射产品信息
        Map<Long, BaseProductDTO> productMap = ProductUtil.getByProductId(productIdList);

        //组装返回值
        List<ProductMultiboxListRespDTO> respList = list.stream().map(item -> {
            ProductMultiboxListRespDTO respDTO = BeanUtil.copyNew(item, ProductMultiboxListRespDTO.class);
            respDTO.setBaseProductDTO(productMap.get(item.getProductId()));
            //获取详情
            List<MultiboxDetailRespDTO> detailReqList = item.getDetailList().stream().map(obj -> {
                MultiboxDetailRespDTO multiboxDetailRespDTO = BeanUtil.copyNew(obj, MultiboxDetailRespDTO.class);
                multiboxDetailRespDTO.setBaseProductDTO(productMap.get(obj.getProductId()));
                return multiboxDetailRespDTO;
            }).toList();
            //填充详情
            respDTO.setDetailList(detailReqList);
            return respDTO;
        }).toList();

        //返回结果
        return Result.ok(respList);
    }

    /**
     * 构建产品多箱创建及更新入参
     *
     * @param reqDTO 请求参数
     * @return 产品多箱创建及更新入参
     */
    private List<ProductMultiboxCreateOrUpdateParam> buildParam(ProductMultiboxCreateOrUpdateReqDTO reqDTO) {
        //组装产品多箱详情
        return reqDTO.getMultiboxList()
                .stream()
                .map(item -> {
                    ProductMultiboxCreateOrUpdateParam param = BeanUtil.copyNew(item, ProductMultiboxCreateOrUpdateParam.class);
                    param.setProductId(reqDTO.getProductId());
                    //组装产品多箱详情
                    List<MultiboxDetailParam> multiboxDetailParamList = item.getDetailList()
                            .stream()
                            .map(obj ->
                                    new MultiboxDetailParam(
                                            obj.getProductId(),
                                            obj.getLineNum(),
                                            obj.getQty()
                                    )
                            ).collect(Collectors.toList());
                    //填充多箱详情
                    param.setDetailList(multiboxDetailParamList);
                    //返回多箱对象
                    return param;
                }).collect(Collectors.toList());
    }
}
