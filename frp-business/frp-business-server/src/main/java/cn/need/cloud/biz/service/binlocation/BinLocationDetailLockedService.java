package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedBatchCreateBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationDetailLockedCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationDetailLockedUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailLockedQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.page.BinLocationDetailLockedPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 锁定 库位详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface BinLocationDetailLockedService extends SuperService<BinLocationDetailLocked> {

    /**
     * 根据参数新增锁定 库位详情
     *
     * @param createParam 请求创建参数，包含需要插入的锁定 库位详情的相关信息
     * @return 锁定 库位详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(BinLocationDetailLockedCreateParam createParam);


    /**
     * 根据参数更新锁定 库位详情
     *
     * @param updateParam 请求创建参数，包含需要更新的锁定 库位详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(BinLocationDetailLockedUpdateParam updateParam);

    /**
     * 根据查询条件获取锁定 库位详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个锁定 库位详情对象的列表(分页)
     */
    List<BinLocationDetailLockedPageVO> listByQuery(BinLocationDetailLockedQuery query);

    /**
     * 根据查询条件获取锁定 库位详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个锁定 库位详情对象的列表(分页)
     */
    PageData<BinLocationDetailLockedPageVO> pageByQuery(PageSearch<BinLocationDetailLockedQuery> search);

    /**
     * 根据ID获取锁定 库位详情
     *
     * @param id 锁定 库位详情ID
     * @return 返回锁定 库位详情VO对象
     */
    BinLocationDetailLockedVO detailById(Long id);

    /**
     * 根据锁定 库位详情唯一编码获取锁定 库位详情
     *
     * @param refNum 锁定 库位详情唯一编码
     * @return 返回锁定 库位详情VO对象
     */
    BinLocationDetailLockedVO detailByRefNum(String refNum);


    /**
     * 根据产品ID和库位详情ID列表查询锁定的库位详情。
     *
     * @param productIds           产品ID列表
     * @param binLocationDetailIds 库位详情ID列表
     * @return 符合条件的锁定库位详情列表
     */
    List<InventoryBinLocationDetailLockedVO> listInventoryLockedByProductIdsAndBinLocationIds(List<Long> productIds, List<Long> binLocationDetailIds);

    /**
     * 根据产品ID和库位详情ID列表查询锁定的库位详情。
     *
     * @param productIds           产品ID列表
     * @param binLocationDetailIds 库位详情ID列表
     * @return 符合条件的锁定库位详情列表
     */
    List<BinLocationDetailLocked> listEntitiesLockedByProductIdsAndBinLocationIds(List<Long> productIds, List<Long> binLocationDetailIds);

    /**
     * 获取锁定库存
     *
     * @param binLocationDetailIds 库位id
     * @return 锁定库存
     */
    List<BinLocationDetailLocked> listBinDetailIds(List<Long> binLocationDetailIds);

    /**
     * 库位锁定
     *
     * @param lockedList 库存需要锁的集合
     */
    void lockedBinLocationInventory(List<BinLocationDetailLocked> lockedList);

    /**
     * 根据ReadyToGo库位和详情获取/创建锁信息
     */
    List<BinLocationDetailLocked> batchCreateIfAbsent(List<BinLocationDetailLockedBatchCreateBO> candidates);

    /**
     * 释放库位库存
     *
     * @param changeList 变更列表
     */
    void updateByChange(Collection<? extends BinLocationDetailChangeBO> changeList);

    /**
     * 释放所有库存
     *
     * @param ids 释放id
     */
    void releaseAll(List<Long> ids);

    /**
     * 根据库位详情id分组
     *
     * @param binLocationDetailIdList 库位详情id集合
     * @return /
     */
    Map<Long, List<BinLocationDetailLocked>> groupByBinLocationIdList(List<Long> binLocationDetailIdList);

    /**
     * 获取锁住的库位锁集合
     *
     * @param lockedIdList 锁id集合
     * @return /
     */
    List<BinLocationDetailLocked> listByLocked(Collection<Long> lockedIdList);

    /**
     * 获取锁住的库位锁集合
     *
     * @param refTableIdList 关联id集合
     * @return /
     */
    Map<Long, List<BinLocationDetailLocked>> groupByRefTableIdLocked(List<Long> refTableIdList);

    /**
     * 获取锁住的库位锁集合
     *
     * @param refTableId 关联id集合
     * @return /
     */
    List<BinLocationDetailLocked> listByRefTableIdLocked(Long refTableId);

    /**
     * 获取库位锁集合
     *
     * @param refTableId 关联id集合
     * @return /
     */
    List<BinLocationDetailLocked> listByRefTableIds(Collection<Long> refTableId);
}