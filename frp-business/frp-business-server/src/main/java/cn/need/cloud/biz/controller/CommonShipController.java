package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.model.bo.common.CommonShipRespBO;
import cn.need.cloud.biz.service.ship.CommonClientService;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.cloud.ship.client.dto.base.BaseAddressDTO;
import cn.need.cloud.ship.client.dto.base.BaseInfoDTO;
import cn.need.cloud.ship.client.dto.base.BasePackageDTO;
import cn.need.cloud.ship.client.dto.base.BaseShipSizeDTO;
import cn.need.cloud.ship.client.dto.common.CommonShipCreateReqDTO;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collections;

/**
 * <p>
 * 通用物流客户端控制器
 * </p>
 * <p>
 * 该控制器提供通用物流功能的测试接口，主要用于测试和调试物流单创建功能。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@RestController
@RequestMapping("/api/biz/ship")
@Tag(name = "通用物流接口", description = "提供通用物流相关操作")
public class CommonShipController extends AbstractController {

    @Resource
    private CommonClientService commonClientService;

    /**
     * 创建物流单
     * <p>
     * 根据提供的物流信息创建物流单，获取物流单号和相关物流信息。
     * </p>
     *
     * @param dto 物流单创建请求参数
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "创建物流单", description = "根据提供的物流信息创建物流单，获取物流单号和相关物流信息")
    @PostMapping("/create")
    public Result<CommonShipRespBO> createShipment(@RequestBody CommonShipCreateReqDTO dto) {
        // 如果appId为空，设置默认值
        if (StringUtil.isEmpty(dto.getAppId())) {
            dto.setAppId("1001");
        }
        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }

    /**
     * 使用UPS格式的地址创建物流单
     * <p>
     * 根据UPS格式的地址信息构建物流单创建请求，方便与UPS系统进行集成测试。
     * </p>
     *
     * @param appId                 应用ID
     * @param profileRefNum         配置引用编号
     * @param requestRefNum         请求参考编号
     * @param shipCarrier           物流承运商
     * @param shipMethod            物流方式
     * @param packageRefNum         包裹参考编号
     * @param shipToName            收件人姓名
     * @param shipToAttentionName   收件人联系人姓名
     * @param shipToPhone           收件人电话
     * @param shipToAddressLine     收件人地址行
     * @param shipToCity            收件人城市
     * @param shipToState           收件人州/省
     * @param shipToZipCode         收件人邮编
     * @param shipToCountry         收件人国家
     * @param shipFromName          发件人姓名
     * @param shipFromAttentionName 发件人联系人姓名
     * @param shipFromPhone         发件人电话
     * @param shipFromAddressLine   发件人地址行
     * @param shipFromCity          发件人城市
     * @param shipFromState         发件人州/省
     * @param shipFromZipCode       发件人邮编
     * @param shipFromCountry       发件人国家
     * @param packageLength         包裹长度
     * @param packageWidth          包裹宽度
     * @param packageHeight         包裹高度
     * @param packageWeight         包裹重量
     * @param referenceNumber1      参考编号1
     * @param referenceNumber2      参考编号2
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "使用UPS格式地址创建物流单", description = "使用UPS格式的地址信息创建物流单，方便与UPS系统集成测试")
    @PostMapping("/create-ups-format")
    public Result<CommonShipRespBO> createShipmentWithUpsFormat(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "配置引用编号") @RequestParam(required = false, defaultValue = "UPSPROF-00001") String profileRefNum,
            @Parameter(description = "请求参考编号") @RequestParam(required = false, defaultValue = "UPSREQ-00001") String requestRefNum,
            @Parameter(description = "物流承运商") @RequestParam(required = false, defaultValue = "UPS") String shipCarrier,
            @Parameter(description = "物流方式") @RequestParam(required = false, defaultValue = "03") String shipMethod,
            @Parameter(description = "包裹参考编号") @RequestParam(required = false, defaultValue = "UPSPKG-00001") String packageRefNum,

            @Parameter(description = "收件人姓名") @RequestParam(required = false, defaultValue = "AMAZON.COM") String shipToName,
            @Parameter(description = "收件人联系人姓名") @RequestParam(required = false, defaultValue = "SNA4") String shipToAttentionName,
            @Parameter(description = "收件人电话") @RequestParam(required = false, defaultValue = "0000000000") String shipToPhone,
            @Parameter(description = "收件人地址行") @RequestParam(required = false, defaultValue = "2496 w walnut ave") String shipToAddressLine,
            @Parameter(description = "收件人城市") @RequestParam(required = false, defaultValue = "RIALTO") String shipToCity,
            @Parameter(description = "收件人州/省") @RequestParam(required = false, defaultValue = "CA") String shipToState,
            @Parameter(description = "收件人邮编") @RequestParam(required = false, defaultValue = "92376") String shipToZipCode,
            @Parameter(description = "收件人国家") @RequestParam(required = false, defaultValue = "US") String shipToCountry,

            @Parameter(description = "发件人姓名") @RequestParam(required = false, defaultValue = "LA03") String shipFromName,
            @Parameter(description = "发件人联系人姓名") @RequestParam(required = false, defaultValue = "iPower Inc.") String shipFromAttentionName,
            @Parameter(description = "发件人电话") @RequestParam(required = false, defaultValue = "6268637344") String shipFromPhone,
            @Parameter(description = "发件人地址行") @RequestParam(required = false, defaultValue = "8798 9TH ST") String shipFromAddressLine,
            @Parameter(description = "发件人城市") @RequestParam(required = false, defaultValue = "RANCHO CUCAMONGA") String shipFromCity,
            @Parameter(description = "发件人州/省") @RequestParam(required = false, defaultValue = "CA") String shipFromState,
            @Parameter(description = "发件人邮编") @RequestParam(required = false, defaultValue = "91730") String shipFromZipCode,
            @Parameter(description = "发件人国家") @RequestParam(required = false, defaultValue = "US") String shipFromCountry,

            @Parameter(description = "包裹长度") @RequestParam(required = false, defaultValue = "18.0") BigDecimal packageLength,
            @Parameter(description = "包裹宽度") @RequestParam(required = false, defaultValue = "10.0") BigDecimal packageWidth,
            @Parameter(description = "包裹高度") @RequestParam(required = false, defaultValue = "15.0") BigDecimal packageHeight,
            @Parameter(description = "包裹重量") @RequestParam(required = false, defaultValue = "3.4") BigDecimal packageWeight,

            @Parameter(description = "参考编号1") @RequestParam(required = false) String referenceNumber1,
            @Parameter(description = "参考编号2") @RequestParam(required = false) String referenceNumber2) {

        CommonShipCreateReqDTO dto = new CommonShipCreateReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        dto.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());

        // 设置基本信息
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setRequestRefNum(requestRefNum);
        baseInfoDTO.setShipCarrier(shipCarrier);
        baseInfoDTO.setShipMethod(shipMethod);
        baseInfoDTO.setShipDate(LocalDateTime.now());

        // 设置收件地址 (ShipTo)
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setName(shipToName);
        shipToAddress.setCompany(StringUtil.isNotEmpty(shipToAttentionName) ? shipToAttentionName : shipToName);
        shipToAddress.setAddr1(shipToAddressLine);
        shipToAddress.setCity(shipToCity);
        shipToAddress.setState(shipToState);
        shipToAddress.setZipCode(shipToZipCode);
        shipToAddress.setCountry(shipToCountry);
        shipToAddress.setPhone(shipToPhone);
        baseInfoDTO.setShipToAddress(shipToAddress);

        // 设置发件地址 (ShipFrom)
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setName(shipFromName);
        shipFromAddress.setCompany(StringUtil.isNotEmpty(shipFromAttentionName) ? shipFromAttentionName : shipFromName);
        shipFromAddress.setAddr1(shipFromAddressLine);
        shipFromAddress.setCity(shipFromCity);
        shipFromAddress.setState(shipFromState);
        shipFromAddress.setZipCode(shipFromZipCode);
        shipFromAddress.setCountry(shipFromCountry);
        shipFromAddress.setPhone(shipFromPhone);
        baseInfoDTO.setShipFromAddress(shipFromAddress);

        dto.setBase(baseInfoDTO);

        // 设置包裹信息
        BasePackageDTO packageDTO = new BasePackageDTO();
        packageDTO.setPackageRefNum(packageRefNum);

        // // 添加参考编号
        // if (StringUtil.isNotEmpty(referenceNumber1)) {
        //     packageDTO.setReference1(referenceNumber1);
        // }
        // if (StringUtil.isNotEmpty(referenceNumber2)) {
        //     packageDTO.setReference2(referenceNumber2);
        // }

        // 设置包裹尺寸
        BaseShipSizeDTO sizeDTO = new BaseShipSizeDTO();
        sizeDTO.setLength(packageLength);
        sizeDTO.setWidth(packageWidth);
        sizeDTO.setHeight(packageHeight);
        sizeDTO.setWeight(packageWeight);
        sizeDTO.setDimensionUnit("IN");
        sizeDTO.setWeightUnit("LB");
        packageDTO.setShipSize(sizeDTO);

        dto.setPackages(Collections.singletonList(packageDTO));

        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }

    /**
     * 使用预设的亚马逊UPS格式创建物流单（快速测试）
     * <p>
     * 使用示例中提供的亚马逊地址信息创建UPS物流单，用于快速测试。
     * </p>
     *
     * @param appId         应用ID
     * @param profileRefNum 配置引用编号
     * @param requestRefNum 请求参考编号
     * @param packageRefNum 包裹参考编号
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "使用预设亚马逊UPS格式创建物流单", description = "使用预设的亚马逊地址创建UPS物流单，用于快速测试")
    @PostMapping("/create-amazon-ups")
    public Result<CommonShipRespBO> createAmazonUpsShipment(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "配置引用编号") @RequestParam(required = false, defaultValue = "UPSPROF-00001") String profileRefNum,
            @Parameter(description = "请求参考编号") @RequestParam(required = false, defaultValue = "UPSREQ-00001") String requestRefNum,
            @Parameter(description = "包裹参考编号") @RequestParam(required = false, defaultValue = "UPSPKG-00001") String packageRefNum) {

        CommonShipCreateReqDTO dto = new CommonShipCreateReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        dto.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());

        // 设置基本信息
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setRequestRefNum(requestRefNum);
        baseInfoDTO.setShipCarrier("UPS");  // UPS承运商
        baseInfoDTO.setShipMethod("UPS Ground");    // UPS地面服务
        baseInfoDTO.setShipDate(LocalDateTime.now());

        // 设置收件地址 (ShipTo) - 从示例中获取
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setName("AMAZON.COM");
        shipToAddress.setCompany("SNA4");
        shipToAddress.setAddr1("2496 w walnut ave");
        shipToAddress.setCity("RIALTO");
        shipToAddress.setState("CA");
        shipToAddress.setZipCode("92376");
        shipToAddress.setCountry("US");
        shipToAddress.setPhone("0000000000");
        baseInfoDTO.setShipToAddress(shipToAddress);

        // 设置发件地址 (ShipFrom) - 从示例中获取
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setName("LA03");
        shipFromAddress.setCompany("iPower Inc.");
        shipFromAddress.setAddr1("8798 9TH ST");
        shipFromAddress.setCity("RANCHO CUCAMONGA");
        shipFromAddress.setState("CA");
        shipFromAddress.setZipCode("91730");
        shipFromAddress.setCountry("US");
        shipFromAddress.setPhone("6268637344");
        baseInfoDTO.setShipFromAddress(shipFromAddress);

        dto.setBase(baseInfoDTO);

        // 设置包裹信息
        BasePackageDTO packageDTO = new BasePackageDTO();
        packageDTO.setPackageRefNum(packageRefNum);
        // packageDTO.setReference1("00008100001780277373");
        // packageDTO.setReference2("002-OBPKG-24121800CT");

        // 设置包裹尺寸 - 从示例中获取
        BaseShipSizeDTO sizeDTO = new BaseShipSizeDTO();
        sizeDTO.setLength(new BigDecimal("18.0"));
        sizeDTO.setWidth(new BigDecimal("10.0"));
        sizeDTO.setHeight(new BigDecimal("15.0"));
        sizeDTO.setWeight(new BigDecimal("3.4"));
        sizeDTO.setDimensionUnit("IN");
        sizeDTO.setWeightUnit("LB");
        packageDTO.setShipSize(sizeDTO);

        dto.setPackages(Collections.singletonList(packageDTO));

        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }

    /**
     * 使用预设的Ontranc EMS格式创建物流单（快速测试）
     * <p>
     * 使用示例中提供的Ontranc EMS地址信息创建物流单，用于快速测试。
     * </p>
     *
     * @param appId         应用ID
     * @param profileRefNum 配置引用编号 (默认使用"ONTPROF-00002")
     * @param requestRefNum 请求参考编号 (默认使用示例的请求编号)
     * @param packageRefNum 包裹参考编号 (默认使用示例的包裹编号)
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "使用预设Ontranc EMS格式创建物流单", description = "使用预设的Ontranc EMS地址创建物流单，用于快速测试")
    @PostMapping("/create-ontranc-ems")
    public Result<CommonShipRespBO> createOntrancEmsShipment(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "配置引用编号") @RequestParam(required = false, defaultValue = "ONTPROF-00002") String profileRefNum,
            @Parameter(description = "请求参考编号") @RequestParam(required = false, defaultValue = "00008100001780338975") String requestRefNum,
            @Parameter(description = "包裹参考编号") @RequestParam(required = false, defaultValue = "002-OBPKG-241219009C") String packageRefNum) {

        CommonShipCreateReqDTO dto = new CommonShipCreateReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_ZPL.getLabelFormat());
        dto.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_ZPL.getEncodeFormat());

        // 设置基本信息
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setRequestRefNum(requestRefNum);
        baseInfoDTO.setShipCarrier("OnTrac");
        baseInfoDTO.setShipMethod("OnTrac");
        baseInfoDTO.setShipDate(LocalDateTime.now());

        // 设置收件地址 (ShipTo) - 从示例中获取
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setName("AMAZON.COM");
        shipToAddress.setCompany("LAS6");
        shipToAddress.setAddr1("4550 Nexus Way");
        shipToAddress.setAddr2("");
        shipToAddress.setCity("LAS VEGAS");
        shipToAddress.setState("NV");
        shipToAddress.setZipCode("89115");
        shipToAddress.setCountry("US");
        baseInfoDTO.setShipToAddress(shipToAddress);

        // 设置发件地址 (ShipFrom) - 从示例中获取
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setName("LA03");
        shipFromAddress.setCompany("iPower Inc.");
        shipFromAddress.setAddr1("8798 9TH ST");
        shipFromAddress.setAddr2("");
        shipFromAddress.setAddr3("");
        shipFromAddress.setCity("RANCHO CUCAMONGA");
        shipFromAddress.setState("CA");
        shipFromAddress.setZipCode("91730");
        shipFromAddress.setCountry("US");
        shipFromAddress.setPhone("6268637344");
        shipFromAddress.setEmail("<EMAIL>");
        //shipFromAddress.setNote("");
        baseInfoDTO.setShipFromAddress(shipFromAddress);

        dto.setBase(baseInfoDTO);

        // 设置包裹信息
        BasePackageDTO packageDTO = new BasePackageDTO();
        packageDTO.setPackageRefNum(packageRefNum);

        // 设置包裹尺寸 - 从示例中获取
        BaseShipSizeDTO sizeDTO = new BaseShipSizeDTO();
        sizeDTO.setLength(new BigDecimal("24.094"));
        sizeDTO.setWidth(new BigDecimal("5.236"));
        sizeDTO.setHeight(new BigDecimal("15.748"));
        sizeDTO.setWeight(new BigDecimal("11.023"));
        sizeDTO.setDimensionUnit("IN");
        sizeDTO.setWeightUnit("LB");
        packageDTO.setShipSize(sizeDTO);

        dto.setPackages(Collections.singletonList(packageDTO));

        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }

    /**
     * 使用预设的Amazon ShipAPI格式创建物流单
     * <p>
     * 使用示例中提供的Amazon ShipAPI格式地址信息创建物流单，用于快速测试。
     * 此格式基于Amazon Ship API标准请求格式。
     * </p>
     *
     * @param appId         应用ID
     * @param profileRefNum 配置引用编号
     * @param requestRefNum 请求参考编号
     * @param packageRefNum 包裹参考编号 (默认使用示例的ClientReferenceId: 47782174)
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "使用Amazon ShipAPI格式创建物流单", description = "使用Amazon ShipAPI格式地址信息创建物流单，用于快速测试")
    @PostMapping("/create-amazon-ship")
    public Result<CommonShipRespBO> createAmazonShipApiShipment(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "配置引用编号") @RequestParam(required = false, defaultValue = "AMZSHIP-00001") String profileRefNum,
            @Parameter(description = "请求参考编号") @RequestParam(required = false, defaultValue = "AMZSHIP-REQ-00001") String requestRefNum,
            @Parameter(description = "包裹参考编号") @RequestParam(required = false, defaultValue = "47782174") String packageRefNum,
            @Parameter(description = "物流承运商") @RequestParam(required = false, defaultValue = "AMAZON") String shipCarrier,
            @Parameter(description = "物流方式") @RequestParam(required = false, defaultValue = "AMAZON") String shipMethod) {

        CommonShipCreateReqDTO dto = new CommonShipCreateReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        dto.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());

        // 设置基本信息
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setRequestRefNum(requestRefNum);
        baseInfoDTO.setShipCarrier(shipCarrier);
        baseInfoDTO.setShipMethod(shipMethod);
        baseInfoDTO.setShipDate(LocalDateTime.now());

        // 设置收件地址 (ShipTo) - 从示例中获取
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setName("Shannon Geyer");
        shipToAddress.setAddr1("1201 ROYAL BLVD null");
        shipToAddress.setCity("GREEN BAY");
        shipToAddress.setState("WI");
        shipToAddress.setZipCode("54303-4238");
        shipToAddress.setCountry("US");
        baseInfoDTO.setShipToAddress(shipToAddress);

        // 设置发件地址 (ShipFrom) - 从示例中获取
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setName("GUY YANG");
        shipFromAddress.setAddr1("4600 E Wall St");
        shipFromAddress.setCity("Ontario");
        shipFromAddress.setState("CA");
        shipFromAddress.setZipCode("91761");
        shipFromAddress.setCountry("US");
        baseInfoDTO.setShipFromAddress(shipFromAddress);

        dto.setBase(baseInfoDTO);

        // 设置包裹信息
        BasePackageDTO packageDTO = new BasePackageDTO();
        packageDTO.setPackageRefNum(packageRefNum);

        // 设置包裹尺寸 - 从示例中获取
        BaseShipSizeDTO sizeDTO = new BaseShipSizeDTO();
        sizeDTO.setLength(new BigDecimal("24.0"));
        sizeDTO.setWidth(new BigDecimal("13.0"));
        sizeDTO.setHeight(new BigDecimal("8.0"));
        sizeDTO.setWeight(new BigDecimal("25.99"));
        sizeDTO.setDimensionUnit("IN");
        sizeDTO.setWeightUnit("LB");
        packageDTO.setShipSize(sizeDTO);

        dto.setPackages(Collections.singletonList(packageDTO));

        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }

    /**
     * 使用预设的FedEx格式创建物流单（快速测试）
     * <p>
     * 使用示例中提供的FedEx地址信息创建物流单，用于快速测试。
     * </p>
     *
     * @param appId         应用ID
     * @param profileRefNum 配置引用编号
     * @param requestRefNum 请求参考编号
     * @param packageRefNum 包裹参考编号
     * @return 包含物流单创建响应结果的结果对象
     */
    @Operation(summary = "使用预设FedEx格式创建物流单", description = "使用预设的FedEx地址创建物流单，用于快速测试")
    @PostMapping("/create-fedex")
    public Result<CommonShipRespBO> createFedExShipment(
            @Parameter(description = "应用ID") @RequestParam(required = false, defaultValue = "1001") String appId,
            @Parameter(description = "配置引用编号") @RequestParam(required = false, defaultValue = "FEDPROF-00002") String profileRefNum,
            @Parameter(description = "请求参考编号") @RequestParam(required = false, defaultValue = "FEDEX-REQ-00001") String requestRefNum,
            @Parameter(description = "包裹参考编号") @RequestParam(required = false, defaultValue = "FEDEX-PKG-00001") String packageRefNum) {

        CommonShipCreateReqDTO dto = new CommonShipCreateReqDTO();
        dto.setAppId(appId);
        dto.setProfileRefNum(profileRefNum);
        dto.setLabelFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getLabelFormat());
        dto.setEncodeFormat(FileDataTypeEnum.BASE_64_STRING_PNG.getEncodeFormat());

        // 设置基本信息
        BaseInfoDTO baseInfoDTO = new BaseInfoDTO();
        baseInfoDTO.setRequestRefNum(requestRefNum);
        baseInfoDTO.setShipCarrier("FEDEX");       // FedEx承运商
        baseInfoDTO.setShipMethod("FEDEX_GROUND");       // FedEx地面服务
        baseInfoDTO.setShipDate(LocalDateTime.now());

        // 设置收件地址 (ShipTo)
        BaseAddressDTO shipToAddress = new BaseAddressDTO();
        shipToAddress.setName("Test Recipient");
        shipToAddress.setCompany("Recipient Company");
        shipToAddress.setAddr1("123 Recipient St");
        shipToAddress.setCity("Boston");
        shipToAddress.setState("MA");
        shipToAddress.setZipCode("02108");
        shipToAddress.setCountry("US");
        shipToAddress.setPhone("6175551234");
        shipToAddress.setEmail("<EMAIL>");
        baseInfoDTO.setShipToAddress(shipToAddress);

        // 设置发件地址 (ShipFrom)
        BaseAddressDTO shipFromAddress = new BaseAddressDTO();
        shipFromAddress.setName("Test Sender");
        shipFromAddress.setCompany("Sender Company Inc.");
        shipFromAddress.setAddr1("456 Sender Ave");
        shipFromAddress.setCity("Los Angeles");
        shipFromAddress.setState("CA");
        shipFromAddress.setZipCode("90001");
        shipFromAddress.setCountry("US");
        shipFromAddress.setPhone("2135557890");
        shipFromAddress.setEmail("<EMAIL>");
        baseInfoDTO.setShipFromAddress(shipFromAddress);

        dto.setBase(baseInfoDTO);

        // 设置包裹信息
        BasePackageDTO packageDTO = new BasePackageDTO();
        packageDTO.setPackageRefNum(packageRefNum);

        // 设置包裹尺寸
        BaseShipSizeDTO sizeDTO = new BaseShipSizeDTO();
        sizeDTO.setLength(new BigDecimal("15.0"));
        sizeDTO.setWidth(new BigDecimal("12.0"));
        sizeDTO.setHeight(new BigDecimal("10.0"));
        sizeDTO.setWeight(new BigDecimal("5.0"));
        sizeDTO.setDimensionUnit("IN");
        sizeDTO.setWeightUnit("LB");
        packageDTO.setShipSize(sizeDTO);

        dto.setPackages(Collections.singletonList(packageDTO));

        CommonShipRespBO respBO = commonClientService.createShipment(dto);
        return Result.ok(respBO);
    }
} 