package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipFilterBuildQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipPickQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipPiecePackageQuery;
import cn.need.cloud.biz.model.query.otc.pickingslip.OtcPickingSlipQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipPieceVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipPrintSummaryCountVO;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtcPickingSlipPageVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipBuildService;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTC拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-picking-slip")
@Tag(name = "OTC拣货单")
@Validated
public class OtcPickingSlipController extends AbstractRestController<OtcPickingSlipService, OtcPickingSlip, OtcPickingSlipConverter, OtcPickingSlipVO> {

    @Resource
    private OtcPickingSlipBuildService otcPickingSlipBuildService;

    @Operation(summary = "根据id获取OTC拣货单详情", description = "根据数据主键id，从数据库中获取其对应的OTC拣货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC拣货单详情
        OtcPickingSlipVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC拣货单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC拣货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC拣货单详情
        OtcPickingSlipVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC拣货单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC拣货单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPickingSlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPickingSlipQuery> search) {

        // 获取OTC拣货单分页
        PageData<OtcPickingSlipPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        // 返回结果
        service.markPrinted(query);
        return success(true);
    }

    @Operation(summary = "Summary Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/summary-print")
    public Result<OtcPickingSlipPrintSummaryCountVO> summaryPrint(@RequestBody @Valid OtcPickingSlipQuery query) {

        // 返回结果
        return success(service.summaryPrint(query));
    }

    @Operation(summary = "拣货Pick", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/pick")
    public Result<Boolean> pick(@RequestBody @Valid OtcPickingSlipPickQuery query) {

        // 返回结果
        return success(service.pick(query));
    }

    @Operation(summary = "FilterBuild 新增", description = "根据传入的搜索条件参数，构建拣货单")
    @PostMapping(value = "/filter-build")
    public Result<List<WorkOrderNoEnoughAvailQtyVO>> filterBuild(@RequestBody @Parameter(description = "搜索条件参数", required = true)
                                                                 @Valid OtcPickingSlipFilterBuildQuery query) {

        // 返回结果
        return success(otcPickingSlipBuildService.filterBuild(query));
    }


    @Operation(summary = "获取一票一件 发货包裹情况", description = "根据传入的条件参数，返回包裹情况")
    @PostMapping(value = "/single-order-single-piece-package")
    public Result<List<OtcPickingSlipPieceVO>> singleOrderSinglePiecePackage(
            @Valid @RequestBody @Parameter(description = "包裹情况条件", required = true) OtcPickingSlipPiecePackageQuery query) {

        // 返回结果
        return success(service.singleOrderSinglePiecePackage(query.getOtcPickingSlipId(), query.getProductId()));
    }

    @Operation(summary = "获取 SlapAndGo 发货包裹情况", description = "根据传入的条件参数，返回包裹情况")
    @PostMapping(value = "/slap-and-go-package")
    public Result<List<OtcPickingSlipPieceVO>> slapAndGo(
            @Valid @RequestBody @Parameter(description = "包裹情况条件", required = true) OtcPickingSlipPiecePackageQuery query) {

        // 返回结果
        return success(service.slapAndGoPackage(query.getOtcPickingSlipId(), query.getProductId()));
    }

    @Operation(summary = "获取 Multi Box 发货包裹情况", description = "根据传入的条件参数，返回包裹情况")
    @PostMapping(value = "/multi-box-package")
    public Result<List<OtcPickingSlipPieceVO>> multiBoxPackage(
            @Valid @RequestBody @Parameter(description = "包裹情况条件", required = true) OtcPickingSlipPiecePackageQuery query) {

        // 返回结果
        return success(service.multiBoxPackage(query.getOtcPickingSlipId(), query.getProductId()));
    }

    @Operation(summary = "获取一票多件 发货包裹情况", description = "根据传入的条件参数，返回包裹情况")
    @GetMapping(value = "/single-order-multiple-piece-package")
    public Result<OtcPickingSlipPieceVO> singleOrderMultiplePiecePackage(
            @RequestParam @Parameter(description = "主键id", required = true) Long otcPickingSlipId) {

        // 返回结果
        return success(service.singleOrderMultiplePiecePackage(otcPickingSlipId));
    }

    @Operation(summary = "下拉列表", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "下拉值查询条件", required = true) OtcPickingSlipQuery query) {

        return success(service.distinctValue(query));
    }
}
