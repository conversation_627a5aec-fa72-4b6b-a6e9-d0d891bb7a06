package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.SupplierQuoteDTO;
import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierQuoteVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 供应商-仓库报价 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class SupplierQuoteConverter extends AbstractModelConverter<SupplierQuote, SupplierQuoteVO, SupplierQuoteDTO> {

}
