package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPackageDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPackageDetail;
import cn.need.cloud.biz.model.vo.oco.OcoPackageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO包裹明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPackageDetailConverter extends AbstractModelConverter<OcoPackageDetail, OcoPackageDetailVO, OcoPackageDetailDTO> {

}



