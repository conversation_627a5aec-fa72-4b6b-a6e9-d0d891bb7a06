package cn.need.cloud.biz.converter;

import cn.need.cloud.biz.client.dto.PrepRequestDTO;
import cn.need.cloud.biz.model.entity.PrepRequest;
import cn.need.cloud.biz.model.vo.PrepRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 预请求对象转换器，负责在不同层级的预请求对象之间进行转换。
 * </p>
 *
 * <p>
 * 该转换器实现了实体对象(PrepRequest)、视图对象(PrepRequestVO)和
 * 数据传输对象(PrepRequestDTO)之间的互相转换，保证了各层之间数据结构的一致性。
 * 在MVC架构中，负责数据模型在不同表现形式间的映射，解耦了业务逻辑和数据展示。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-25
 */
public class PrepRequestConverter extends AbstractModelConverter<PrepRequest, PrepRequestVO, PrepRequestDTO> {

}
