package cn.need.cloud.biz.service.feeoriginalData.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.client.constant.enums.storage.StorageSnapshotRequestStatusEnum;
import cn.need.cloud.biz.model.bo.fee.storage.FodExtraDataStorageBO;
import cn.need.cloud.biz.model.bo.fee.storage.FodStorageSnapshotRequestBO;
import cn.need.cloud.biz.model.bo.fee.storage.FodStorageSnapshotRequestDetailBO;
import cn.need.cloud.biz.model.query.storage.StorageSnapshotRequestQuery;
import cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestPageVO;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestDetailVO;
import cn.need.cloud.biz.model.vo.storage.StorageSnapshotRequestVO;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataBuildService;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestDetailService;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 存储费用原始数据构建服务实现
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Service("fodBuildStorage")
@AllArgsConstructor
public class FeeOriginalDataBuildStorageServiceImpl implements FeeOriginalDataBuildService<FodExtraDataStorageBO> {

    private final StorageSnapshotRequestService storageSnapshotRequestService;
    private final StorageSnapshotRequestDetailService storageSnapshotRequestDetailService;

    @Override
    public FodExtraDataStorageBO buildFeeOriginalData() {
        PageSearch<StorageSnapshotRequestQuery> storageSnapshotRequestQueryPageSearch = new PageSearch<>();
        storageSnapshotRequestQueryPageSearch.setCurrent(1);
        storageSnapshotRequestQueryPageSearch.setSize(1);
        StorageSnapshotRequestQuery storageSnapshotRequestQuery = new StorageSnapshotRequestQuery();
        storageSnapshotRequestQuery.setRequestStatus(StorageSnapshotRequestStatusEnum.PROCESSED.getStatus());
        storageSnapshotRequestQuery.setFeeStatus(FeeStatusEnum.NEW.getStatus());
        storageSnapshotRequestQueryPageSearch.setCondition(storageSnapshotRequestQuery);

        final PageData<StorageSnapshotRequestPageVO> storageSnapshotRequestPageVOPageData = storageSnapshotRequestService
                .pageByQuery(storageSnapshotRequestQueryPageSearch);

        if (ObjectUtil.isEmpty(storageSnapshotRequestPageVOPageData.getRecords())) {
            return null;
        }
        // todo:这里使用Redis锁
        return buildFeeOriginalData(storageSnapshotRequestPageVOPageData.getRecords().get(0).getId());
    }

    @Override
    public FodExtraDataStorageBO buildFeeOriginalData(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            return null;
        }

        StorageSnapshotRequestVO storageSnapshotRequestVO = storageSnapshotRequestService.detailById(requestId);

        return buildFeeOriginalData(storageSnapshotRequestVO);
    }

    @NotNull
    private FodExtraDataStorageBO buildFeeOriginalData(StorageSnapshotRequestVO storageSnapshotRequestVO) {
        if (!StorageSnapshotRequestStatusEnum.PROCESSED.equals(storageSnapshotRequestVO.getRequestStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "PROCESSED",
                    storageSnapshotRequestVO.getRequestStatus()));
        }

        if (!FeeStatusEnum.getCanBuildStatuses().contains(storageSnapshotRequestVO.getFeeStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "New",
                    storageSnapshotRequestVO.getFeeStatus()));
        }

        // 查询存储快照请求详情
        List<StorageSnapshotRequestDetailVO> storageSnapshotRequestDetails = storageSnapshotRequestDetailService
                .listByStorageSnapshotRequestId(storageSnapshotRequestVO.getId());

        FodExtraDataStorageBO extraData = new FodExtraDataStorageBO();

        // 填充基本信息
        extraData.setSnapshotRequestId(storageSnapshotRequestVO.getId());
        extraData.setSnapshotRequestRefNum(storageSnapshotRequestVO.getRefNum());
        extraData.setSnapshotRefNum(storageSnapshotRequestVO.getRefNum());
        extraData.setTransactionPartnerId(storageSnapshotRequestVO.getTransactionPartnerId());
        extraData.setWarehouseId(storageSnapshotRequestVO.getWarehouseId());
        extraData.setRefNum(storageSnapshotRequestVO.getRefNum());
        extraData.setProcessStartTime(storageSnapshotRequestVO.getProcessStartTime());
        extraData.setProcessEndTime(storageSnapshotRequestVO.getProcessEndTime());
        extraData.setFeeCalculationTime(storageSnapshotRequestVO.getFeeCalculationTime());
        extraData.setNote(storageSnapshotRequestVO.getNote());

        // 填充存储相关数据
        extraData.setStorageSnapshotRequestList(
                List.of(BeanUtil.copyNew(storageSnapshotRequestVO, FodStorageSnapshotRequestBO.class)));
        extraData.setStorageSnapshotRequestDetailList(
                BeanUtil.copyNew(storageSnapshotRequestDetails, FodStorageSnapshotRequestDetailBO.class));

        return extraData;
    }
}
