package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductConverter extends AbstractModelConverter<Product, ProductVO, ProductReqDTO> {

}
