package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcRequestPackageDetailConverter;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageDetail;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestPackageDetailCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestPackageDetailUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageDetailQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackageDetailPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestPackageDetailService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC请求包裹详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-request-package-detail")
@Tag(name = "OTC请求包裹详情")
public class OtcRequestPackageDetailController extends AbstractRestController<OtcRequestPackageDetailService, OtcRequestPackageDetail, OtcRequestPackageDetailConverter, OtcRequestPackageDetailVO> {

    @Operation(summary = "新增OTC请求包裹详情", description = "接收OTC请求包裹详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageDetailCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OTC请求包裹详情", description = "接收OTC请求包裹详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageDetailUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OTC请求包裹详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OTC请求包裹详情详情", description = "根据数据主键id，从数据库中获取其对应的OTC请求包裹详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcRequestPackageDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC请求包裹详情详情
        OtcRequestPackageDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC请求包裹详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC请求包裹详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcRequestPackageDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcRequestPackageDetailQuery> search) {

        // 获取OTC请求包裹详情分页
        PageData<OtcRequestPackageDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
