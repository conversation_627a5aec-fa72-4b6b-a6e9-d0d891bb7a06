package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote;
import cn.need.cloud.biz.model.param.feeconfig.create.SupplierQuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.SupplierQuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.SupplierQuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.SupplierQuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.SupplierQuotePageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 供应商-仓库报价 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface SupplierQuoteService extends SuperService<SupplierQuote>,
        RefNumService<SupplierQuote, SupplierQuoteService> {

    /**
     * 根据参数新增供应商-仓库报价
     *
     * @param createParam 请求创建参数，包含需要插入的供应商-仓库报价的相关信息
     * @return 供应商-仓库报价ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(SupplierQuoteCreateParam createParam);


    /**
     * 根据参数更新供应商-仓库报价
     *
     * @param updateParam 请求创建参数，包含需要更新的供应商-仓库报价的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(SupplierQuoteUpdateParam updateParam);

    /**
     * 根据查询条件获取供应商-仓库报价列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个供应商-仓库报价对象的列表(分页)
     */
    List<SupplierQuotePageVO> listByQuery(SupplierQuoteQuery query);

    /**
     * 根据查询条件获取供应商-仓库报价列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个供应商-仓库报价对象的列表(分页)
     */
    PageData<SupplierQuotePageVO> pageByQuery(PageSearch<SupplierQuoteQuery> search);

    /**
     * 根据ID获取供应商-仓库报价
     *
     * @param id 供应商-仓库报价ID
     * @return 返回供应商-仓库报价VO对象
     */
    SupplierQuoteVO detailById(Long id);

    /**
     * 根据报价ID获取供应商-仓库报价列表
     *
     * @param quoteId 供应商-仓库报价ID
     * @return 匹配的供应商-仓库报价实体列表
     */
    List<SupplierQuote> listByQuoteId(Long quoteId);


    /**
     * 切换供应商-仓库报价的激活状态
     *
     * @param id 供应商-仓库报价ID
     * @return 更新后的状态值 (1-激活, 0-未激活)
     */
    Integer switchActive(Long id);

    /**
     * 根据交易伙伴ID和时间获取有效的供应商-仓库报价
     *
     * @param transactionPartnerId 交易伙伴ID
     * @param time                 查询时间
     * @return 有效的供应商-仓库报价实体
     */
    SupplierQuote getActiveSupplierQuote(Long transactionPartnerId, LocalDateTime time);
}