package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoArrangementDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoArrangementDetail;
import cn.need.cloud.biz.model.vo.oco.OcoArrangementDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO安排明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-04
 */
public class OcoArrangementDetailConverter extends AbstractModelConverter<OcoArrangementDetail, OcoArrangementDetailVO, OcoArrangementDetailDTO> {

}
