package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPickingSlipDetail;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预拣货单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPickingSlipDetailConverter extends AbstractModelConverter<OtcPickingSlipDetail, OtcPickingSlipDetailVO, OtcPickingSlipDetailDTO> {

}
