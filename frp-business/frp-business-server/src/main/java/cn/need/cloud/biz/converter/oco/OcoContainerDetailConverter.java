package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoContainerDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoContainerDetail;
import cn.need.cloud.biz.model.vo.oco.OcoContainerDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO集装箱明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoContainerDetailConverter extends AbstractModelConverter<OcoContainerDetail, OcoContainerDetailVO, OcoContainerDetailDTO> {

}



