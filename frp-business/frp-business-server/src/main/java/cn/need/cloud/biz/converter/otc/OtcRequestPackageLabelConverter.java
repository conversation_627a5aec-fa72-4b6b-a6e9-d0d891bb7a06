package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcRequestPackageLabelDTO;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageLabel;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC请求包裹标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcRequestPackageLabelConverter extends AbstractModelConverter<OtcRequestPackageLabel, OtcRequestPackageLabelVO, OtcRequestPackageLabelDTO> {

}
