package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.biz.model.entity.base.WorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;

/**
 * <p>
 * OTC预提工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface PrepWorkorderService<TPrepWorkorder extends PrepWorkorderModel, TWorkorder extends WorkorderModel, TWorkorderDetail extends WorkorderDetailModel> {

    TPrepWorkorder createBasePrepWorkorder(
            TWorkorder workOrder,
            TWorkorderDetail detail,
            PrepWorkOrderTypeEnum prepWorkOrderType,
            InventoryReserve inventoryReserve
    );
}