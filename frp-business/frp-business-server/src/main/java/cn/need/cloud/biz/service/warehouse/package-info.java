/**
 * <p>
 * 仓库管理服务包
 * </p>
 * <p>
 * 该包提供仓库管理的核心服务接口，包括仓库基础信息管理、仓库产品关联管理、仓库操作管理等功能。
 * 仓库是物流系统的基础设施，本包提供了对仓库全生命周期的管理，支持仓库的创建、配置、运营和监控。
 * </p>
 * <p>
 * 主要模块包括：
 * 1. 仓库基础管理 - 提供仓库的创建、更新、查询等基本功能
 * 2. 仓库产品管理 - 管理仓库与产品的关联关系，如仓库产品配置等
 * 3. 仓库操作管理 - 处理仓库的日常操作，如库存盘点、库位调整等
 * 4. 仓库序列管理 - 管理仓库中各种业务编号的生成规则和序列
 * 5. 托盘管理 - 管理仓库中的托盘资源，包括托盘模板和空托盘管理
 * 6. 仓库特殊属性 - 管理仓库的特殊配置和属性
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
package cn.need.cloud.biz.service.warehouse; 