package cn.need.cloud.biz.converter.fee;


import cn.need.cloud.biz.client.dto.fee.FeeInboundDetailDTO;
import cn.need.cloud.biz.model.entity.fee.FeeInboundDetail;
import cn.need.cloud.biz.model.vo.fee.FeeInboundDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 费用详情inbound 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeInboundDetailConverter extends AbstractModelConverter<FeeInboundDetail, FeeInboundDetailVO, FeeInboundDetailDTO> {

}
