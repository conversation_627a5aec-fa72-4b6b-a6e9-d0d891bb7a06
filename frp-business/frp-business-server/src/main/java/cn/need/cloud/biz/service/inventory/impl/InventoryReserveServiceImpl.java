package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.inventory.InventoryReserveStatusEnum;
import cn.need.cloud.biz.converter.inventory.InventoryInventoryReserveConverter;
import cn.need.cloud.biz.converter.inventory.InventoryReserveConverter;
import cn.need.cloud.biz.mapper.inventory.InventoryReserveMapper;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.param.inventory.create.InventoryReserveCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReserveUpdateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryReserveQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryReserveVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryReserveVO;
import cn.need.cloud.biz.model.vo.page.InventoryReservePageVO;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.exception.unchecked.ImpossibleException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;

/**
 * <p>
 * 库存预留服务实现类
 * </p>
 * <p>
 * 该类实现了库存预留的核心业务逻辑，负责库存预留的创建、查询、释放和管理。
 * 主要功能包括：
 * 1. 库存预留的创建和管理，包括单个预留和批量预留
 * 2. 库存预留的查询功能，支持根据ID、参考编号、产品ID等多种方式查询
 * 3. 库存预留的状态管理，包括新建、部分完成和完成
 * 4. 库存预留的释放和上架管理，包括释放预留库存和上架预留库存
 * 5. 库存预留的分页查询和详情展示
 * </p>
 * <p>
 * 库存预留是库存管理的重要组成部分，用于预先保留库存以满足未来的需求。
 * 当系统需要为某个业务操作（如订单、工单等）预留库存时，会创建库存预留记录。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class InventoryReserveServiceImpl extends SuperServiceImpl<InventoryReserveMapper, InventoryReserve> implements InventoryReserveService {

    /**
     * 编号生成客户端，用于生成库存预留的参考编号
     */
    @Resource
    private NumberGenerateClient numberGenerateClient;


    /**
     * 根据参数创建库存预留
     * <p>
     * 该方法用于根据传入的参数创建库存预留记录。
     * 主要流程包括：
     * 1. 验证参数是否为空
     * 2. 将参数转换为实体对象
     * 3. 初始化实体对象
     * 4. 将实体对象插入数据库
     * 5. 返回创建的库存预留ID
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据的一致性。
     * </p>
     *
     * @param createParam 库存预留创建参数
     * @return 创建的库存预留ID
     * @throws BusinessException 如果参数为空，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(InventoryReserveCreateParam createParam) {
        // 检查传入预留库存参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取预留库存转换器实例，用于将预留库存参数对象转换为实体对象
        InventoryReserveConverter converter = Converters.get(InventoryReserveConverter.class);

        // 将预留库存参数对象转换为实体对象并初始化
        InventoryReserve entity = initInventoryReserve(converter.toEntity(createParam));

        // 插入预留库存实体对象到数据库
        super.insert(entity);

        // 返回预留库存ID
        return entity.getId();
    }


    /**
     * 初始化库存预留对象
     * <p>
     * 该方法用于初始化库存预留对象，设置必要的参数以确保其处于有效状态。
     * 主要操作包括：
     * 1. 验证实体对象是否为空
     * 2. 设置库存预留的初始状态和必要参数
     * </p>
     * <p>
     * 库存预留对象初始化后将处于可用状态，可以被插入到数据库中。
     * </p>
     *
     * @param entity 库存预留实体对象
     * @return 初始化后的库存预留实体对象
     * @throws BusinessException 如果实体对象为空，则抛出异常
     */
    private InventoryReserve initInventoryReserve(InventoryReserve entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("InventoryReserve cannot be empty");
        }

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 根据参数更新库存预留
     * <p>
     * 该方法用于根据传入的参数更新库存预留记录。
     * 主要流程包括：
     * 1. 验证参数是否为空，以及是否包含ID
     * 2. 将参数转换为实体对象
     * 3. 执行更新操作
     * 4. 返回更新的记录数量
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据的一致性。
     * </p>
     *
     * @param updateParam 库存预留更新参数
     * @return 更新的记录数量
     * @throws BusinessException 如果参数为空或不包含ID，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(InventoryReserveUpdateParam updateParam) {
        // 检查传入预留库存参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取预留库存转换器实例，用于将预留库存参数对象转换为实体对象
        InventoryReserveConverter converter = Converters.get(InventoryReserveConverter.class);

        // 将预留库存参数对象转换为实体对象
        InventoryReserve entity = converter.toEntity(updateParam);

        // 执行更新预留库存操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取库存预留列表
     * <p>
     * 该方法用于根据指定的查询条件获取库存预留列表，不带分页。
     * 查询条件可以包括产品ID、预留状态、预留类型等多种条件。
     * </p>
     *
     * @param query 库存预留查询条件对象
     * @return 库存预留列表，包含库存预留基本信息和关联的产品信息
     */
    @Override
    public List<InventoryReservePageVO> listByQuery(InventoryReserveQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取库存预留列表
     * <p>
     * 该方法用于根据指定的查询条件和分页参数获取库存预留列表。
     * 主要流程包括：
     * 1. 根据分页参数创建分页对象
     * 2. 执行分页查询并获取数据列表
     * 3. 返回带分页信息的数据列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 带分页信息的库存预留列表，包含库存预留基本信息和关联的产品信息
     */
    @Override
    public PageData<InventoryReservePageVO> pageByQuery(PageSearch<InventoryReserveQuery> search) {
        Page<InventoryReserve> page = Conditions.page(search, entityClass);
        List<InventoryReservePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取库存预留详情
     * <p>
     * 该方法用于根据库存预留ID获取库存预留的详细信息。
     * 如果指定ID的库存预留不存在，则抛出业务异常。
     * </p>
     *
     * @param id 库存预留ID
     * @return 库存预留VO对象，包含库存预留基本信息和关联的产品信息
     * @throws BusinessException 如果库存预留不存在，则抛出业务异常
     */
    @Override
    public InventoryReserveVO detailById(Long id) {
        InventoryReserve entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in InventoryReserve");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "InventoryReserve", id));
        }
        return buildInventoryReserveVO(entity);
    }

    /**
     * 根据参考编号获取库存预留详情
     * <p>
     * 该方法用于根据库存预留的参考编号（RefNum）获取库存预留的详细信息。
     * 如果指定参考编号的库存预留不存在，则抛出业务异常。
     * </p>
     *
     * @param refNum 库存预留参考编号
     * @return 库存预留VO对象，包含库存预留基本信息和关联的产品信息
     * @throws BusinessException 如果库存预留不存在，则抛出业务异常
     */
    @Override
    public InventoryReserveVO detailByRefNum(String refNum) {
        InventoryReserve entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in InventoryReserve");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InventoryReserve", "refNum", refNum));
        }
        return buildInventoryReserveVO(entity);
    }

    /**
     * 根据产品ID列表获取未完成的库存预留列表
     * <p>
     * 该方法用于根据产品ID列表获取未完成的库存预留列表。
     * 未完成的库存预留指状态不是“完成”的预留记录，包括新建和部分完成的预留。
     * </p>
     * <p>
     * 该方法主要用于查询特定产品的未完成预留，便于库存管理和规划。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 未完成的库存预留VO对象列表，如果产品ID列表为空则返回空列表
     */
    @Override
    public List<InventoryReserveVO> listNoFinishByProductIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<InventoryReserve> inventoryReserves = listEntityNoFinishByProductIds(productIds);

        return Converters.get(InventoryReserveConverter.class).toVO(inventoryReserves);
    }

    /**
     * 根据产品ID列表获取库存系统中未完成的库存预留列表
     * <p>
     * 该方法用于根据产品ID列表获取库存系统中未完成的库存预留列表。
     * 返回的是专门为库存系统设计的VO对象，包含库存预留的基本信息和库存系统需要的特定字段。
     * </p>
     * <p>
     * 该方法主要用于库存系统的库存查询和统计功能。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 库存系统中未完成的库存预留VO对象列表，如果产品ID列表为空则返回空列表
     */
    @Override
    public List<InventoryInventoryReserveVO> listInventoryNoFinishByProductIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<InventoryReserve> inventoryReserves = listEntityNoFinishByProductIds(productIds);

        return Converters.get(InventoryInventoryReserveConverter.class).toVO(inventoryReserves);
    }

    /**
     * 根据产品ID列表获取未完成的库存预留实体列表
     * <p>
     * 该方法用于根据产品ID列表获取未完成的库存预留实体列表。
     * 与 listNoFinishByProductIds 方法类似，但返回的是实体对象而非VO对象。
     * </p>
     * <p>
     * 该方法主要用于内部处理，当需要直接操作实体对象时使用。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 未完成的库存预留实体列表，如果产品ID列表为空则返回空列表
     */
    @Override
    public List<InventoryReserve> listEntityNoFinishByProductIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<InventoryReserve> inventoryReserves = lambdaQuery()
                .in(InventoryReserve::getProductId, productIds)
                .in(InventoryReserve::getReserveStatus, InventoryReserveStatusEnum.NEW.getStatus(), InventoryReserveStatusEnum.PART_FINISH.getStatus())
                .list();

        return inventoryReserves;
    }

    /**
     * 释放库存预留
     * <p>
     * 该方法用于释放库存预留，将预留的库存标记为已完成或部分完成。
     * 主要流程包括：
     * 1. 验证预留库存列表是否为空
     * 2. 计算每个预留库存的完成数量和剩余数量
     * 3. 更新预留库存的完成数量和状态
     * </p>
     * <p>
     * 当预留库存的完成数量等于预留数量时，状态变为“完成”；
     * 当完成数量小于预留数量时，状态变为“部分完成”。
     * </p>
     *
     * @param lockedInventoryList 需要释放的库存预留列表，包含ID和释放数量
     * @throws IllegalArgumentException 如果释放数量超过剩余预留数量或预留信息不存在，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList) {
        /* 优化建议: 当前方法使用了calculateAnyQty通用方法处理库存预留的释放，这是一个好的设计。
         * 但是在calculateAnyQty方法中仍然存在一些可以优化的点：
         *
         * 1. 在查询时使用了ne(InventoryReserve::getReserveStatus, InventoryReserveStatusEnum.FINISH.getStatus())
         *    这会导致数据库无法使用索引。建议使用in操作列出所有可能的非完成状态。
         *
         * 2. 当处理大量数据时，可以考虑分批处理，避免单个事务过大。
         *
         * 3. 当前实现中使用了多次stream操作，可以合并为一次操作以提高效率。
         *
         * 4. 可以考虑使用批量更新操作而不是单条更新，减少数据库交互。
         */
        calculateAnyQty(lockedInventoryList, (param, inventoryReserve) -> {
            int remainingQty = inventoryReserve.getReserveQty() - inventoryReserve.getFinishQty() - param.getQty();

            Validate.isTrue(remainingQty >= 0,
                    "The ReserveLocked {} released quantity cannot exceed the remaining locked {} quantity",
                    inventoryReserve.getId(), remainingQty
            );
            // 完成释放数量
            inventoryReserve.setFinishQty(param.getQty() + inventoryReserve.getFinishQty());
            // 释放状态判断
            String lockedStatus = Objects.equals(inventoryReserve.getFinishQty(), inventoryReserve.getQty())
                    ? InventoryReserveStatusEnum.FINISH.getStatus()
                    : InventoryReserveStatusEnum.PART_FINISH.getStatus();
            inventoryReserve.setReserveStatus(lockedStatus);
        });
    }


    /**
     * 释放库存预留
     * <p>
     * 该方法用于释放库存预留，将预留的库存标记为已完成或部分完成。
     * 主要流程包括：
     * 1. 验证预留库存列表是否为空
     * 2. 计算每个预留库存的完成数量和剩余数量
     * 3. 更新预留库存的完成数量和状态
     * </p>
     * <p>
     * 当预留库存的完成数量等于预留数量时，状态变为“完成”；
     * 当完成数量小于预留数量时，状态变为“部分完成”。
     * </p>
     *
     * @param lockedInventoryList 需要释放的库存预留列表，包含ID和释放数量
     * @throws IllegalArgumentException 如果释放数量超过剩余预留数量或预留信息不存在，则抛出异常
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList) {
        /* 优化建议: 当前方法使用了calculateAnyQty通用方法处理库存预留的释放，这是一个好的设计。
         * 但是在calculateAnyQty方法中仍然存在一些可以优化的点：
         *
         * 1. 在查询时使用了ne(InventoryReserve::getReserveStatus, InventoryReserveStatusEnum.FINISH.getStatus())
         *    这会导致数据库无法使用索引。建议使用in操作列出所有可能的非完成状态。
         *
         * 2. 当处理大量数据时，可以考虑分批处理，避免单个事务过大。
         *
         * 3. 当前实现中使用了多次stream操作，可以合并为一次操作以提高效率。
         *
         * 4. 可以考虑使用批量更新操作而不是单条更新，减少数据库交互。
         */
        calculateAnyQty(lockedInventoryList, (param, inventoryReserve) -> {
            int remainingQty = inventoryReserve.getQty() - inventoryReserve.getFinishQty() - param.getQty();

            if (remainingQty < 0) {
                throw new ImpossibleException(StringUtil.format("The ReserveLocked {} released quantity cannot exceed the remaining locked {} quantity",
                        inventoryReserve.getId(), remainingQty));
            }

            inventoryReserve.setNote(StringUtil.format("Cancel"));

            // 完成释放数量
            inventoryReserve.setFinishQty(param.getQty() + inventoryReserve.getFinishQty());
            // 释放状态判断
            String lockedStatus = Objects.equals(inventoryReserve.getFinishQty(), inventoryReserve.getQty())
                    ? InventoryReserveStatusEnum.FINISH.getStatus()
                    : InventoryReserveStatusEnum.PART_FINISH.getStatus();
            inventoryReserve.setReserveStatus(lockedStatus);
        });
    }

    /**
     * 上架库存预留
     * <p>
     * 该方法用于上架库存预留，将库存标记为已预留。
     * 主要流程包括：
     * 1. 验证预留库存列表是否为空
     * 2. 计算每个预留库存的预留数量和剩余可预留数量
     * 3. 更新预留库存的预留数量
     * </p>
     * <p>
     * 与释放库存预留不同，上架库存预留是将库存标记为已预留，而不是标记为已完成。
     * </p>
     *
     * @param lockedInventoryList 需要上架的库存预留列表，包含ID和上架数量
     * @throws IllegalArgumentException 如果上架数量超过剩余可预留数量或预留信息不存在，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void putAwayReserveInventory(List<InventoryReleaseLockedParam> lockedInventoryList) {
        calculateAnyQty(lockedInventoryList, (param, inventoryReserve) -> {
            int remainingQty = inventoryReserve.getQty() - inventoryReserve.getReserveQty() - param.getQty();

            Validate.isTrue(remainingQty >= 0,
                    "The ReserveLocked {} released quantity cannot exceed the remaining locked {} quantity",
                    inventoryReserve.getId(), remainingQty
            );
            // 上架预定数量
            inventoryReserve.setReserveQty(param.getQty() + inventoryReserve.getReserveQty());
        });
    }


    /**
     * 计算库存预留各种数量的通用逻辑流程
     * <p>
     * 该方法是库存预留数量计算的通用实现，用于处理释放和上架等操作的数量计算。
     * 主要流程包括：
     * 1. 验证预留库存列表是否为空
     * 2. 获取预留库存实体列表，排除已完成的预留
     * 3. 验证预留库存是否存在
     * 4. 对每个预留库存进行数量计算和状态更新
     * 5. 批量更新预留库存实体
     * </p>
     * <p>
     * 该方法使用函数式编程的方式，通过传入不同的计算函数来实现不同的数量计算逻辑。
     * </p>
     *
     * @param lockedInventoryList 需要计算数量的库存预留列表
     * @param calculateConsumer   数量计算函数，接收预留参数和预留实体，负责计算和更新数量
     * @throws IllegalArgumentException 如果预留库存列表为空或预留信息不存在，则抛出异常
     */
    private void calculateAnyQty(List<InventoryReleaseLockedParam> lockedInventoryList,
                                 BiConsumer<InventoryReleaseLockedParam, InventoryReserve> calculateConsumer) {
        /* 优化建议: 该方法是一个通用的库存预留数量计算方法，使用了函数式编程的方式，设计很好。
         * 但仍然存在一些可以优化的点：
         *
         * 1. 查询条件优化：
         *    当前使用 ne(InventoryReserve::getReserveStatus, InventoryReserveStatusEnum.FINISH.getStatus())
         *    这会导致数据库无法使用索引。建议使用in操作列出所有可能的非完成状态：
         *    .in(InventoryReserve::getReserveStatus, Arrays.asList(
         *        InventoryReserveStatusEnum.NEW.getStatus(),
         *        InventoryReserveStatusEnum.PART_FINISH.getStatus(),
         *        InventoryReserveStatusEnum.PART_RELEASE.getStatus()
         *    ))
         *
         * 2. 数据分批处理：
         *    当处理大量数据时，可以将数据分批处理，每批不超过一定数量（如500条），
         *    避免单个事务过大导致数据库锁定时间过长。
         *
         * 3. 流式操作优化：
         *    当前实现中使用了多次stream操作，可以合并为一次操作以提高效率。
         *    例如，可以先将参数转换为Map，然后直接使用Map进行查找。
         *
         * 4. 错误处理增强：
         *    当前实现中使用Validate.isTrue抛出异常，可以考虑使用更特定的异常类型，
         *    并提供更详细的错误信息，便于问题跟踪和调试。
         */
        Validate.notEmpty(lockedInventoryList, "No InventoryReserveLock information obtained");

        // 获取锁实例
        List<Long> idList = StreamUtils.distinctMap(lockedInventoryList, InventoryReleaseLockedParam::getId);
        List<InventoryReserve> lockedList = lambdaQuery()
                .in(IdModel::getId, idList)
                .ne(InventoryReserve::getReserveStatus, InventoryReserveStatusEnum.FINISH.getStatus())
                .list();

        // 锁映射
        Map<Long, InventoryReserve> lockedMap = StreamUtils.toMap(lockedList, IdModel::getId);
        List<Long> notLockedList = lockedInventoryList.stream()
                .map(InventoryReleaseLockedParam::getId)
                .filter(id -> !lockedMap.containsKey(id))
                .toList();
        // 该锁不存在
        Validate.isTrue(ObjectUtil.isEmpty(notLockedList),
                "InventoryReserveLock: {} information does not exist", notLockedList
        );

        lockedInventoryList.stream()
                .filter(obj -> lockedMap.containsKey(obj.getId()))
                .forEach(param -> {
                    InventoryReserve inventoryReserve = lockedMap.get(param.getId());
                    // 校验释放数量
                    Validate.isTrue(param.getQty() > 0,
                            "The Locked:{} quantity released must be greater than 0",
                            param.getId()
                    );
                    calculateConsumer.accept(param, inventoryReserve);
                });
        // 更新状态
        Validate.isTrue(super.updateBatch(lockedList) == lockedList.size(), "Release locked inventory failed");
    }


    /**
     * 构建库存预留VO对象
     * <p>
     * 该方法用于将库存预留实体转换为库存预留VO对象。
     * 如果输入的实体为空，则返回null。
     * </p>
     * <p>
     * 该方法使用转换器将实体对象转换为VO对象，便于向前端展示库存预留信息。
     * </p>
     *
     * @param entity 库存预留实体对象
     * @return 库存预留VO对象，如果实体为空则返回null
     */
    private InventoryReserveVO buildInventoryReserveVO(InventoryReserve entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的预留库存VO对象
        return Converters.get(InventoryReserveConverter.class).toVO(entity);
    }


    /**
     * 根据工单详情和工单构建库存预留实体
     * <p>
     * 该方法用于根据工单详情和工单构建库存预留实体。
     * 主要流程包括：
     * 1. 创建新的库存预留实体
     * 2. 设置库存预留的各种属性，如完成数量、预留数量、引用表信息等
     * 3. 设置库存预留的产品ID、数量、状态等
     * </p>
     * <p>
     * 该方法使用泛型，可以处理任何继承了BaseWorkorderDetailModel和BaseWorkorderModel的工单和工单详情。
     * </p>
     *
     * @param detail    工单详情对象，必须继承BaseWorkorderDetailModel
     * @param workOrder 工单对象，必须继承BaseWorkorderModel
     * @param <D>       工单详情类型，必须继承BaseWorkorderDetailModel
     * @param <W>       工单类型，必须继承BaseWorkorderModel
     * @return 新创建的库存预留实体
     */
    @Override
    public <D extends BaseWorkorderDetailModel, W extends BaseWorkorderModel> InventoryReserve buildInventoryReserve(D detail, W workOrder) {
        InventoryReserve inventoryReserve = new InventoryReserve();

        inventoryReserve.setFinishQty(0);
        inventoryReserve.setReserveQty(0);
        inventoryReserve.setRefTableId(detail.getId());
        inventoryReserve.setRefTableName(detail.getClass().getSimpleName());
        inventoryReserve.setRefTableRefNum(detail.getLineNum().toString());
        inventoryReserve.setRefTableShowName(workOrder.getClass().getSimpleName());
        inventoryReserve.setRefTableShowRefNum(workOrder.getRefNum());
        inventoryReserve.setQty(detail.getQty());
        inventoryReserve.setProductId(detail.getProductId());
        inventoryReserve.setNote(null);
        inventoryReserve.setId(IdWorker.getId());
        inventoryReserve.setReserveType("TODO");
        inventoryReserve.setReserveStatus(InventoryReserveStatusEnum.NEW.getStatus());

        return inventoryReserve;
    }

}
