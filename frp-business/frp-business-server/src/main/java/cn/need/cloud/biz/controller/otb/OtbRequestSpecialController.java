package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestCancelParam;
import cn.need.cloud.biz.service.otb.request.OtbRequestSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * OTB请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-request/special")
@Tag(name = "OTB请求")
@Slf4j
public class OtbRequestSpecialController extends AbstractController {
    @Resource
    private OtbRequestSpecialService service;

    /**
     * <p>
     * 根据数据主键id取消请求
     * </p>
     *
     * @param param 数据主键id
     * @return 受影响行数
     */
    @Operation(summary = "取消请求接口，返回受影响行数", description = "根据数据主键id，从数据库中取消请求")
    @PostMapping(value = "/cancel")
    public Result<Integer> cancel(@RequestBody @Parameter(description = "数据主键id", required = true) OtbRequestCancelParam param) {

        Validate.notNull(param.getId(), "The id value cannot be null.");
        return Result.ok(service.cancel(param));
    }
}
