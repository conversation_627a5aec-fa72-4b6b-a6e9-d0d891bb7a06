package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcRequestDTO;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC请求 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcRequestConverter extends AbstractModelConverter<OtcRequest, OtcRequestVO, OtcRequestDTO> {

}
