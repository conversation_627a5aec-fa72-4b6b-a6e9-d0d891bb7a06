package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepPutawaySlipDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPutawaySlip;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPrepPutawaySlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC上架单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
public class OtcPrepPutawaySlipConverter extends AbstractModelConverter<OtcPrepPutawaySlip, OtcPrepPutawaySlipVO, OtcPrepPutawaySlipDTO> {

}
