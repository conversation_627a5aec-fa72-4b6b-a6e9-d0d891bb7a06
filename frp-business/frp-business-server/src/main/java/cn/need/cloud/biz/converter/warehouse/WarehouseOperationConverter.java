package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.warehouse.WarehouseOperationDTO;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseOperation;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库分配 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class WarehouseOperationConverter extends AbstractModelConverter<WarehouseOperation, WarehouseOperationVO, WarehouseOperationDTO> {

}
