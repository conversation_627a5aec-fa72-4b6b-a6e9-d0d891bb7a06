package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.config.ShipConfig;
import cn.need.cloud.biz.service.ship.SsccClientService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * SSCC编号生成控制器
 * </p>
 * <p>
 * 该控制器提供SSCC(Serial Shipping Container Code)编号生成相关的API接口，
 * 主要用于测试和调试SSCC编号生成功能。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-05
 */
@RestController
@RequestMapping("/api/biz/sscc")
@Tag(name = "SSCC编号生成接口", description = "提供SSCC编号生成相关操作")
public class SsccController extends AbstractController {

    @Resource
    private SsccClientService ssccClientService;

    @Resource
    private ShipConfig shipConfig;


    /**
     * 生成标准SSCC编号
     * <p>
     * 基于系统配置的SSCC前缀生成标准SSCC编号。
     * 如果提供了自定义前缀，则使用该前缀替代系统配置的前缀。
     * </p>
     *
     * @return 包含生成的SSCC编号的结果对象
     */
    @Operation(summary = "生成标准SSCC编号", description = "基于系统配置或自定义前缀生成标准SSCC编号")
    @GetMapping("/generate/auto")
    public Result<String> generateSsccNum() {
        String ssccNum = ssccClientService.getSsccNum(shipConfig.getSsccPrefix());
        return Result.ok(ssccNum);
    }


    /**
     * 生成标准SSCC编号
     * <p>
     * 基于系统配置的SSCC前缀生成标准SSCC编号。
     * 如果提供了自定义前缀，则使用该前缀替代系统配置的前缀。
     * </p>
     *
     * @param customPrefix 自定义前缀(可选)
     * @return 包含生成的SSCC编号的结果对象
     */
    @Operation(summary = "生成标准SSCC编号", description = "基于系统配置或自定义前缀生成标准SSCC编号")
    @GetMapping("/generate")
    public Result<String> generateSsccNum(
            @Parameter(description = "自定义前缀(可选)") @RequestParam(required = false) String customPrefix) {
        String prefix = customPrefix != null ? customPrefix : shipConfig.getSsccPrefix();
        String ssccNum = ssccClientService.getSsccNum(prefix);
        return Result.ok(ssccNum);
    }

    /**
     * 生成简短版SSCC编号
     * <p>
     * 基于系统配置的SSCC前缀生成简短版SSCC编号。
     * 如果提供了自定义前缀，则使用该前缀替代系统配置的前缀。
     * </p>
     *
     * @param customPrefix 自定义前缀(可选)
     * @return 包含生成的简短版SSCC编号的结果对象
     */
    @Operation(summary = "生成简短版SSCC编号", description = "基于系统配置或自定义前缀生成简短版SSCC编号")
    @GetMapping("/generate-short")
    public Result<String> generateShortSsccNum(
            @Parameter(description = "自定义前缀(可选)") @RequestParam(required = false) String customPrefix) {
        String prefix = customPrefix != null ? customPrefix : shipConfig.getSsccPrefix();
        String shortSsccNum = ssccClientService.getShortSsccNum(prefix);
        return Result.ok(shortSsccNum);
    }

    /**
     * 批量生成SSCC编号
     * <p>
     * 基于系统配置的SSCC前缀批量生成指定数量的SSCC编号。
     * 如果提供了自定义前缀，则使用该前缀替代系统配置的前缀。
     * </p>
     *
     * @param count        需要生成的SSCC编号数量
     * @param customPrefix 自定义前缀(可选)
     * @return 包含生成的SSCC编号列表的结果对象
     */
    @Operation(summary = "批量生成SSCC编号", description = "基于系统配置或自定义前缀批量生成SSCC编号")
    @GetMapping("/batch-generate")
    public Result<List<String>> batchGenerateSsccNum(
            @Parameter(description = "需要生成的数量") @RequestParam(defaultValue = "5") Integer count,
            @Parameter(description = "自定义前缀(可选)") @RequestParam(required = false) String customPrefix) {
        if (count <= 0 || count > 100) {
            return failed("数量必须在1-100之间");
        }

        String prefix = customPrefix != null ? customPrefix : shipConfig.getSsccPrefix();
        List<String> ssccNumList = new ArrayList<>(count);

        for (int i = 0; i < count; i++) {
            ssccNumList.add(ssccClientService.getSsccNum(prefix));
        }

        return Result.ok(ssccNumList);
    }
} 