package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderDetailTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.model.bo.inventory.BuildPrepWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderInputContext;
import cn.need.cloud.biz.model.bo.inventory.CheckInventoryProductBO;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.biz.model.entity.base.WorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.vo.base.BaseFullProductVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryVO;
import cn.need.cloud.biz.service.inventory.*;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工单服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since ...
 */
@Service
public class WorkorderBuildServiceImpl implements WorkorderBuildService {

    @Resource
    private InventoryLockedService inventoryLockedService;

    @Resource
    private InventoryReserveService inventoryReserveService;

    /**
     * 优化建议: 以下字段使用了原始类型(raw type)，存在类型安全问题。
     * 应该使用参数化类型来提高类型安全性：
     *
     * @Resource private Map<String, PrepWorkorderDetailService<?, ?>> prepWorkorderDetailServiceList;
     * @Resource private Map<String, PrepWorkorderService<?, ?, ?>> prepWorkorderServiceList;
     * @Resource private Map<String, WorkorderDetailService<?, ?>> workorderDetailServiceList;
     */
    @Resource
    private Map<String, PrepWorkorderDetailService> prepWorkorderDetailServiceList;

    @Resource
    private Map<String, PrepWorkorderService> prepWorkorderServiceList;

    @Resource
    private Map<String, WorkorderDetailService> workorderDetailServiceList;

    /**
     * 获取预处理工单明细服务
     * <p>
     * 优化建议: 当前方法使用了原始类型(raw type)，存在类型安全问题。
     * 应该使用参数化类型来提高类型安全性：
     * <p>
     * private <TPrepWorkorderDetail extends PrepWorkorderDetailModel, TPrepWorkorder extends PrepWorkorderModel>
     * PrepWorkorderDetailService<TPrepWorkorderDetail, TPrepWorkorder> getPrepWorkorderDetailService(BuildWorkOrderInputContext context) {
     * // 方法实现...
     * }
     * <p>
     * 同样，应该将类中的字段声明也修改为参数化类型：
     *
     * @param context 工单输入上下文
     * @return 预处理工单明细服务
     * @Resource private Map<String, PrepWorkorderDetailService<?, ?>> prepWorkorderDetailServiceList;
     */
    private PrepWorkorderDetailService getPrepWorkorderDetailService(BuildWorkOrderInputContext context) {
        switch (context.getWorkorderType()) {
            case OTC -> {
                return prepWorkorderDetailServiceList.get("otcPrepWorkorderDetailServiceImpl");
            }
            case OTB -> {
                return prepWorkorderDetailServiceList.get("otbPrepWorkorderDetailServiceImpl");
            }
            default -> throw new IllegalStateException("Unexpected value: " + context.getWorkorderType());
        }
    }

    /**
     * 获取工单明细服务
     * <p>
     * 优化建议: 当前方法使用了原始类型(raw type)，存在类型安全问题。
     * 应该使用参数化类型来提高类型安全性：
     * <p>
     * private <TWorkorderDetail extends WorkorderDetailModel, TWorkorder extends WorkorderModel>
     * WorkorderDetailService<TWorkorderDetail, TWorkorder> getWorkorderDetailService(BuildWorkOrderInputContext context) {
     * // 方法实现...
     * }
     * <p>
     * 同样，应该将类中的字段声明也修改为参数化类型：
     *
     * @param context 工单输入上下文
     * @return 工单明细服务
     * @Resource private Map<String, WorkorderDetailService<?, ?>> workorderDetailServiceList;
     */
    private WorkorderDetailService getWorkorderDetailService(BuildWorkOrderInputContext context) {
        switch (context.getWorkorderType()) {
            case OTC -> {
                return workorderDetailServiceList.get("otcWorkorderDetailServiceImpl");
            }
            case OTB -> {
                return workorderDetailServiceList.get("otbWorkorderDetailServiceImpl");
            }
            default -> throw new IllegalStateException("Unexpected value: " + context.getWorkorderType());
        }
    }

    /**
     * 获取预处理工单服务
     * <p>
     * 优化建议: 当前方法使用了原始类型(raw type)，存在类型安全问题。
     * 应该使用参数化类型来提高类型安全性：
     * <p>
     * private <TPrepWorkorder extends PrepWorkorderModel, TWorkorder extends WorkorderModel, TWorkorderDetail extends WorkorderDetailModel>
     * PrepWorkorderService<TPrepWorkorder, TWorkorder, TWorkorderDetail> getPrepWorkorderService(BuildWorkOrderInputContext context) {
     * // 方法实现...
     * }
     * <p>
     * 同样，应该将类中的字段声明也修改为参数化类型：
     *
     * @param context 工单输入上下文
     * @return 预处理工单服务
     * @Resource private Map<String, PrepWorkorderService<?, ?, ?>> prepWorkorderServiceList;
     */
    private PrepWorkorderService getPrepWorkorderService(BuildWorkOrderInputContext context) {
        switch (context.getWorkorderType()) {
            case OTC -> {
                return prepWorkorderServiceList.get("otcPrepWorkorderServiceImpl");
            }
            case OTB -> {
                return prepWorkorderServiceList.get("otbPrepWorkorderServiceImpl");
            }
            default -> throw new IllegalStateException("Unexpected value: " + context.getWorkorderType());
        }
    }

    /**
     * 分配库存给工单明细
     *
     * @param workorderDetailList 工单明细列表
     * @param workOrder           当前工单
     * @param context             构建工单输入上下文
     * @return 最终的工单明细上下文列表
     */
    @Override
    public List<BuildWorkOrderDetailReturnContext> allocateInventoryForWorkOrder(
            List<? extends WorkorderDetailModel> workorderDetailList,
            WorkorderModel workOrder,
            BuildWorkOrderInputContext context
    ) {

        // 初始化最终明细上下文列表
        List<BuildWorkOrderDetailReturnContext> finalDetailContextList = new ArrayList<>();
        // 初始化需要预留库存的明细列表
        List<WorkorderDetailModel> doPrepList = new ArrayList<>();

        // 1) 先处理不需要预留库存的部分
        handleNoReserveWorkorderDetail(workorderDetailList, workOrder, context, finalDetailContextList, doPrepList);

        // 2) 再处理需要预留库存的部分
        handleReserveWorkorderDetail(doPrepList, workOrder, context, finalDetailContextList);

        return finalDetailContextList;
    }

    /**
     * 处理不需要预留库存的工单明细
     *
     * @param sourceDetailList       源明细列表
     * @param workOrder              当前工单
     * @param context                构建工单输入上下文
     * @param finalDetailContextList 最终明细上下文列表
     * @param doPrepList             需要预留库存的明细列表
     */
    private void handleNoReserveWorkorderDetail(
            List<? extends WorkorderDetailModel> sourceDetailList,
            WorkorderModel workOrder,
            BuildWorkOrderInputContext context,
            List<BuildWorkOrderDetailReturnContext> finalDetailContextList,
            List<WorkorderDetailModel> doPrepList
    ) {
        //todo: 去掉 LineNum 赋值，应该在外部
        // 遍历每一个工单明细
        for (int i = 0; i < sourceDetailList.size(); i++) {

            WorkorderDetailModel detail = sourceDetailList.get(i);

            //todo: 待优化，checkInventory 里面应该要实现
            //再次 Check 库存，防止 ComboAB（A+B） 于 A 一个工单的情况

            /* 优化建议: 当前实现中存在以下问题：
             * 1. 在handleNoReserveWorkorderDetail和handleReserveWorkorderDetail方法中都有相同的库存检查逻辑，导致代码重复
             * 2. 当前实现不能正确处理ComboAB（组合产品）和A在同一工单中的情况
             *
             * 优化建议：
             * 1. 将库存检查逻辑提取到InventoryUtil.checkInventory方法中，并增强其实现
             * 2. 在checkInventory方法中增加对组合产品的特殊处理，确保在检查组合产品时考虑其组件产品的库存情况
             * 3. 实现一个全局的工单库存跟踪机制，记录当前工单中已分配的库存，避免重复分配
             *
             * 具体实现建议：
             * 1. 在InventoryUtil中增加一个新的方法：
             *    public static void checkInventoryForWorkorder(List<CheckInventoryProductBO> products,
             *                                               Map<Long, InventoryVO> inventoryMap,
             *                                               Map<Long, Integer> allocatedInventory)
             *
             * 2. 在BuildWorkOrderInputContext中增加一个字段记录已分配的库存：
             *    private Map<Long, Integer> allocatedInventory = new HashMap<>();
             */
            // 将请求明细转换为库存校验对象
            List<CheckInventoryProductBO> checkInventoryList = Collections.singletonList(BeanUtil.copyNew(detail, CheckInventoryProductBO.class));
            // 校验库存是否满足需求
            InventoryUtil.checkInventory(checkInventoryList, context.getInventoryMap());

            // 设置行号
            detail.setLineNum(i + 1);
            // 生成唯一ID
            detail.setId(IdWorker.getId());

            // 获取产品和库存信息
            BaseFullProductVO baseProduct = context.getProductMap().get(detail.getProductId());
            InventoryVO inventory = context.getInventoryMap().get(detail.getProductId());
            // 检查产品和库存是否存在
            checkNullProductOrInventory(detail.getProductId(), baseProduct, inventory);

            // 计算需要预留的部分
            int canAllocateQty = inventory.getInStockCanAllocateQty();
            int reserveQty = Math.max(0, detail.getQty() - canAllocateQty);
            int noReserveQty = detail.getQty() - reserveQty;

            // 如果需要预留库存
            if (reserveQty > 0) {
                // 复制一个新的明细用于预留
                //WorkorderDetailModel prepDetail = BeanUtil.copyNew(detail, WorkorderDetailModel.class);
                WorkorderDetailModel prepDetail = getWorkorderDetailService(context).createNewWorkOrderDetail(detail, workOrder);
                prepDetail.setQty(reserveQty);
                prepDetail.setReserveQty(reserveQty);
                doPrepList.add(prepDetail);
            }

            // 设置无需预留的数量
            detail.setQty(noReserveQty);
            if (detail.getQty() <= 0) {
                continue;
            }

            // 对不需要预留的数量进行锁定
            InventoryLocked locked = inventoryLockedService.buildInventoryLocked(detail, workOrder);
            InventoryUtil.addLockedInventories(detail.getProductId(), locked, context.getInventoryMap());

            // 设置锁定ID
            detail.setInventoryLockedId(locked.getId());
            // 添加到最终明细上下文列表
            finalDetailContextList.add(new BuildWorkOrderDetailReturnContext(detail, locked));
        }
    }

    /**
     * 处理需要预留库存的工单明细
     *
     * @param doPrepWorkorderDetailList 需要预留库存的明细列表
     * @param workOrder                 当前工单
     * @param context                   构建工单输入上下文
     * @param finalDetailContextList    最终明细上下文列表
     */
    private void handleReserveWorkorderDetail(
            List<WorkorderDetailModel> doPrepWorkorderDetailList,
            WorkorderModel workOrder,
            BuildWorkOrderInputContext context,
            List<BuildWorkOrderDetailReturnContext> finalDetailContextList
    ) {
        // 遍历每一个需要预留库存的明细
        for (WorkorderDetailModel detail : doPrepWorkorderDetailList) {

            //todo: 待优化，checkInventory 里面应该要实现
            //再次 Check 库存，防止 ComboAB（A+B） 于 A 一个工单的情况
            // 将请求明细转换为库存校验对象
            List<CheckInventoryProductBO> checkInventoryList = Collections.singletonList(BeanUtil.copyNew(detail, CheckInventoryProductBO.class));
            // 校验库存是否满足需求
            InventoryUtil.checkInventory(checkInventoryList, context.getInventoryMap());

            // 获取产品和库存信息
            BaseFullProductVO baseProduct = context.getProductMap().get(detail.getProductId());
            InventoryVO inventory = context.getInventoryMap().get(detail.getProductId());
            // 检查产品和库存是否存在
            checkNullProductOrInventory(detail.getProductId(), baseProduct, inventory);

            // noReserveQty不应该大于0
            int canAllocateQty = inventory.getInStockCanAllocateQty();
            int reserveQty = Math.max(0, detail.getQty() - canAllocateQty);
            if (reserveQty <= 0) {
                continue;
            }

            // 一旦有一条需要Reserve，则整个workOrder变为NEW
            workOrder.setWorkorderPrepStatus(WorkOrderPrepStatusEnum.NEW.getStatus());

            // 依次从group -> assemblyInStock -> convertAssemblyQty分配
            reserveQty = tryHandleInStockGroups(reserveQty, workOrder, detail, inventory, baseProduct, finalDetailContextList, context);
            reserveQty = tryHandleInStockConvertAssembly(reserveQty, workOrder, detail, inventory, baseProduct, finalDetailContextList, context);
            // 混合 Prep
            reserveQty = tryHandleConvertPack(reserveQty, workOrder, detail, inventory, baseProduct, finalDetailContextList, context);

            // 如果还有剩余未分配的预留数量，抛出异常
            if (reserveQty != 0) {
                //todo: 这里要抛出 Impossible 异常！！
                throw new BusinessException(StringUtil.format(
                        "handleReserveWorkorderDetail Error Inventory Calculate, {} allocate ReserveQty Error ",
                        baseProduct.toString()
                ));
            }
        }
    }

    /**
     * 优先从groupsInStockCanAllocateQty中分配库存 -> PREPCONVERT
     *
     * @param reserveQty             需要预留的数量
     * @param workOrder              当前工单
     * @param originalDetail         原始明细
     * @param inventory              库存信息
     * @param baseProduct            产品信息
     * @param finalDetailContextList 最终明细上下文列表
     * @param context                BuildWorkOrderInputContext
     * @return 剩余需要预留的数量
     */
    private int tryHandleInStockGroups(
            int reserveQty,
            WorkorderModel workOrder,
            WorkorderDetailModel originalDetail,
            InventoryVO inventory,
            BaseFullProductVO baseProduct,
            List<BuildWorkOrderDetailReturnContext> finalDetailContextList,
            BuildWorkOrderInputContext context
    ) {
        /* 优化建议: 该方法处理从组库存中分配库存的逻辑，存在以下可优化点：
         *
         * 1. 类型安全问题：
         *    当前使用了原始类型(raw type)的服务，如WorkorderDetailService和PrepWorkorderService，
         *    应该使用参数化类型以提高类型安全性。
         *
         * 2. 错误处理增强：
         *    当库存锁定或预留失败时，应提供更详细的错误信息，包括产品ID、数量等。
         *
         * 3. 事务处理：
         *    当前方法没有显式的事务处理，如果中间步骤失败，可能导致数据不一致。
         *    建议在调用该方法的上层方法中添加事务注解。
         *
         * 4. 库存跟踪机制：
         *    当前实现没有跟踪已分配的库存，可能导致重复分配。
         *    建议在BuildWorkOrderInputContext中添加一个字段记录已分配的库存。
         *
         * 5. 性能优化：
         *    当前实现中每次都会创建新的工单明细和锁定/预留记录，可以考虑批量创建以提高性能。
         */
        // 获取可分配的group库存数量
        int groupsCanAllocateQty = inventory.getGroupsInStockCanAllocateQty();
        if (groupsCanAllocateQty <= 0 || reserveQty <= 0) {
            return reserveQty;
        }

        // 计算此次分配的数量
        int groupReserveQty = Math.min(reserveQty, groupsCanAllocateQty);
        reserveQty -= groupReserveQty;

        // 创建新的工单明细
        WorkorderDetailModel currentDetail = getWorkorderDetailService(context).createNewWorkOrderDetail(originalDetail, workOrder);
        currentDetail.setQty(groupReserveQty);
        currentDetail.setReserveQty(groupReserveQty);

        // 锁定并预留库存
        InventoryLocked locked = inventoryLockedService.buildInventoryLocked(currentDetail, workOrder);
        InventoryUtil.addLockedInventories(currentDetail.getProductId(), locked, context.getInventoryMap());
        // 设置锁定ID
        currentDetail.setInventoryLockedId(locked.getId());

        // 创建库存预留记录
        InventoryReserve reserve = inventoryReserveService.buildInventoryReserve(currentDetail, workOrder);
        InventoryUtil.addReserveInventories(currentDetail.getProductId(), reserve, context.getInventoryMap());
        // 设置预留ID
        currentDetail.setInventoryReserveId(reserve.getId());

        // 创建明细上下文
        BuildWorkOrderDetailReturnContext detailCtx = new BuildWorkOrderDetailReturnContext(currentDetail, locked, reserve);

        // 构建预处理工单
        PrepWorkorderModel prepWorkorder = getPrepWorkorderService(context).createBasePrepWorkorder(
                workOrder,
                currentDetail,
                PrepWorkOrderTypeEnum.PREPCONVERT,
                reserve
        );
        detailCtx.setPrepWorkorder(prepWorkorder);

        // 分配group列表中的库存
        int totalDoPrepQty = prepWorkorder.getQty();
        List<BuildPrepWorkOrderDetailReturnContext> prepReturnContexts = allocateGroupInventory(
                totalDoPrepQty, prepWorkorder, inventory.getGroupList(), baseProduct, context
        );
        detailCtx.setPrepWorkOrderDetailReturnContextList(prepReturnContexts);

        // 添加到最终明细上下文列表
        finalDetailContextList.add(detailCtx);
        return reserveQty;
    }

    /**
     * 在convertAssemblyInStockCanAllocateQty场景中预留 -> PREPPACK或PREPMULTIBOX
     *
     * @param reserveQty             需要预留的数量
     * @param workOrder              当前工单
     * @param originalDetail         原始明细
     * @param inventory              库存信息
     * @param baseProduct            产品信息
     * @param finalDetailContextList 最终明细上下文列表
     * @param context                构建工单输入上下文
     * @return 剩余需要预留的数量
     */
    private int tryHandleInStockConvertAssembly(
            int reserveQty,
            WorkorderModel workOrder,
            WorkorderDetailModel originalDetail,
            InventoryVO inventory,
            BaseFullProductVO baseProduct,
            List<BuildWorkOrderDetailReturnContext> finalDetailContextList,
            BuildWorkOrderInputContext context
    ) {
        // 获取可分配的组装库存数量
        int convertAssemblyInStockCanAllocateQty = inventory.getConvertAssemblyInStockCanAllocateQty();
        if (convertAssemblyInStockCanAllocateQty <= 0 || reserveQty <= 0) {
            return reserveQty;
        }

        // 根据是否多箱选择预处理工单类型
        PrepWorkOrderTypeEnum prepType = getPackOrMultiBoxType(context.isMultibox(), true);

        // 计算此次分配的数量
        int assemblyReserveQty = Math.min(reserveQty, convertAssemblyInStockCanAllocateQty);
        reserveQty -= assemblyReserveQty;

        // 创建新的工单明细
        WorkorderDetailModel currentDetail = getWorkorderDetailService(context).createNewWorkOrderDetail(originalDetail, workOrder);
        currentDetail.setQty(assemblyReserveQty);
        currentDetail.setReserveQty(assemblyReserveQty);

        // 锁定并预留库存
        InventoryLocked locked = inventoryLockedService.buildInventoryLocked(currentDetail, workOrder);
        InventoryUtil.addLockedInventories(currentDetail.getProductId(), locked, context.getInventoryMap());
        // 设置锁定ID
        currentDetail.setInventoryLockedId(locked.getId());

        InventoryReserve reserve = inventoryReserveService.buildInventoryReserve(currentDetail, workOrder);
        InventoryUtil.addReserveInventories(currentDetail.getProductId(), reserve, context.getInventoryMap());
        // 设置预留ID
        currentDetail.setInventoryReserveId(reserve.getId());

        // 创建明细上下文
        BuildWorkOrderDetailReturnContext detailCtx = new BuildWorkOrderDetailReturnContext(currentDetail, locked, reserve);
        finalDetailContextList.add(detailCtx);

        // 构建预处理工单
        PrepWorkorderModel prepWorkorder = getPrepWorkorderService(context).createBasePrepWorkorder(workOrder, currentDetail, prepType, reserve);
        detailCtx.setPrepWorkorder(prepWorkorder);

        // 分配组件库存
        List<BuildPrepWorkOrderDetailReturnContext> prepReturnContexts = allocateComponentInventory(prepWorkorder, inventory.getComponentList(), baseProduct, context);
        detailCtx.setPrepWorkOrderDetailReturnContextList(prepReturnContexts);

        return reserveQty;
    }

    /**
     * 处理转换包装的预留逻辑 -> PREPPACK或PREPMULTIBOX
     *
     * @param reserveQty             需要预留的数量
     * @param workOrder              当前工单
     * @param originalDetail         原始明细
     * @param inventory              库存信息
     * @param baseProduct            产品信息
     * @param finalDetailContextList 最终明细上下文列表
     * @param context                构建工单输入上下文
     * @return 剩余需要预留的数量
     */
    private int tryHandleConvertPack(
            int reserveQty,
            WorkorderModel workOrder,
            WorkorderDetailModel originalDetail,
            InventoryVO inventory,
            BaseFullProductVO baseProduct,
            List<BuildWorkOrderDetailReturnContext> finalDetailContextList,
            BuildWorkOrderInputContext context
    ) {
        if (reserveQty <= 0) {
            return reserveQty;
        }

        // 获取可转换的组装数量
        int assemblyQty = inventory.getConvertAssemblyQty();
        int assemblyReserveQty = Math.min(reserveQty, assemblyQty);
        reserveQty -= assemblyReserveQty;

        // 创建新的工单明细
        WorkorderDetailModel currentDetail = getWorkorderDetailService(context).createNewWorkOrderDetail(originalDetail, workOrder);
        currentDetail.setQty(assemblyReserveQty);
        currentDetail.setReserveQty(assemblyReserveQty);

        // 锁定并预留库存
        InventoryLocked locked = inventoryLockedService.buildInventoryLocked(currentDetail, workOrder);
        InventoryUtil.addLockedInventories(currentDetail.getProductId(), locked, context.getInventoryMap());
        // 设置锁定ID
        currentDetail.setInventoryLockedId(locked.getId());

        InventoryReserve reserve = inventoryReserveService.buildInventoryReserve(currentDetail, workOrder);
        InventoryUtil.addReserveInventories(currentDetail.getProductId(), reserve, context.getInventoryMap());
        // 设置预留ID
        currentDetail.setInventoryReserveId(reserve.getId());

        // 创建明细上下文
        BuildWorkOrderDetailReturnContext detailCtx = new BuildWorkOrderDetailReturnContext(currentDetail, locked, reserve);

        // 构建预处理工单类型
        PrepWorkOrderTypeEnum prepType = getPackOrMultiBoxType(context.isMultibox(), false);
        // 创建预处理工单
        PrepWorkorderModel prepWorkorder = getPrepWorkorderService(context).createBasePrepWorkorder(workOrder, currentDetail, prepType, reserve);
        detailCtx.setPrepWorkorder(prepWorkorder);

        // 分配复杂组件库存或组装
        List<BuildPrepWorkOrderDetailReturnContext> prepReturnContexts = allocateComplexComponentInventory(prepWorkorder, inventory.getComponentList(), baseProduct, context);
        detailCtx.setPrepWorkOrderDetailReturnContextList(prepReturnContexts);

        // 添加到最终明细上下文列表
        finalDetailContextList.add(detailCtx);
        return reserveQty;
    }


    // region ============== 辅助方法：分配group组件、分配普通组件、collect数据、记录日志等 ==============

    /**
     * 分配groupList库存，生成一系列PrepWorkorderDetail及库存锁定
     *
     * @param totalQtyNeeded 需要分配的总数量
     * @param prepWorkorder  当前PrepWorkorder
     * @param groupList      group库存列表
     * @param baseProduct    产品信息
     * @return 分配后的PrepWorkOrderDetailReturnContext列表
     * <p>
     * 优化建议: 该方法存在以下可优化点：
     * <p>
     * 1. 没有在循环前进行总量预检查，可能导致部分分配后才发现库存不足。
     * <p>
     * 2. 异常信息不够具体，只提供了产品信息，没有包含具体的数量信息。
     * <p>
     * 3. 与其他库存分配方法有重复代码，如创建预处理工单明细、锁定库存等操作。
     * <p>
     * 优化建议：
     * 1. 在循环前进行总量预检查，确保所有group库存的总量足够：
     * int totalAvailableQty = groupList.stream()
     * .mapToInt(InventoryVO::getInStockCanAllocateQty)
     * .sum();
     * if (totalAvailableQty < totalQtyNeeded) {
     * throw new BusinessException(...);
     * }
     * <p>
     * 2. 提供更详细的异常信息，包含需要的数量和实际可用数量：
     * throw new BusinessException(StringUtil.format(
     * "Error Inventory Calculate, {} groupsInStockCanAllocateQty required: {}, available: {}",
     * baseFullProductStr(baseProduct), totalQtyNeeded, totalAvailableQty
     * ));
     * <p>
     * 3. 提取公共逻辑到单独的方法，如创建预处理工单明细、锁定库存等操作。
     */
    private List<BuildPrepWorkOrderDetailReturnContext> allocateGroupInventory(
            int totalQtyNeeded,
            PrepWorkorderModel prepWorkorder,
            List<InventoryVO> groupList,
            BaseFullProductVO baseProduct,
            BuildWorkOrderInputContext context
    ) {
        List<BuildPrepWorkOrderDetailReturnContext> result = new ArrayList<>();
        int remain = totalQtyNeeded;
        // 遍历每一个group库存
        for (int j = 0; j < groupList.size() && remain > 0; j++) {
            InventoryVO vo = groupList.get(j);
            if (vo.getInStockCanAllocateQty() <= 0) {
                continue;
            }
            // 计算此次分配的数量
            int currentQty = Math.min(vo.getInStockCanAllocateQty(), remain);
            remain -= currentQty;

            // 创建预处理工单明细
            PrepWorkorderDetailModel prepDetail = getPrepWorkorderDetailService(context).createPrepWorkorderDetail(
                    prepWorkorder,
                    vo,
                    currentQty,
                    j + 1,
                    PrepWorkOrderDetailTypeEnum.NONE
            );

            // 锁定库存
            InventoryLocked locked = inventoryLockedService.buildInventoryLocked(prepDetail, prepWorkorder);
            InventoryUtil.addLockedInventories(prepDetail.getProductId(), locked, context.getInventoryMap());
            prepDetail.setInventoryLockedId(locked.getId());

            // 添加到结果列表
            result.add(new BuildPrepWorkOrderDetailReturnContext(prepDetail, locked));
        }
        // 如果剩余数量不为0，说明库存不足
        if (remain != 0) {
            throw new BusinessException(StringUtil.format(
                    "Error Inventory Calculate, {} groupsInStockCanAllocateQty Can not enough to do",
                    baseFullProductStr(baseProduct)
            ));
        }
        return result;
    }

    /**
     * 分配组件库存（在有convertAssemblyInStockCanAllocateQty的场景中）
     *
     * @param prepWorkorder 当前PrepWorkorder
     * @param componentList 组件库存列表
     * @param baseProduct   产品信息
     * @return 分配后的PrepWorkOrderDetailReturnContext列表
     * <p>
     * 优化建议: 该方法与 allocateGroupInventory 方法结构非常相似，存在以下可优化点：
     * <p>
     * 1. 代码重复：与 allocateGroupInventory 方法有大量相同的逻辑，如循环遍历、创建预处理工单明细、锁定库存等。
     * <p>
     * 2. 缺少预分配检查：同样没有在循环前进行总量预检查，可能导致部分分配后才发现库存不足。
     * <p>
     * 3. 类型安全问题：使用了原始类型(raw type)的服务，如PrepWorkorderDetailService，应该使用参数化类型。
     * <p>
     * 优化建议：
     * 1. 将 allocateGroupInventory 和 allocateComponentInventory 方法的公共逻辑提取到一个通用方法中：
     * private List<BuildPrepWorkOrderDetailReturnContext> allocateInventory(
     * int totalQtyNeeded,
     * PrepWorkorderModel prepWorkorder,
     * List<InventoryVO> inventoryList,
     * BaseFullProductVO baseProduct,
     * BuildWorkOrderInputContext context,
     * String inventoryType) // "group" 或 "component"
     * <p>
     * 2. 在循环前进行总量预检查，确保所有组件库存的总量足够。
     * <p>
     * 3. 使用参数化类型替代原始类型，提高类型安全性。
     * <p>
     * 4. 创建一个工具方法来处理预处理工单明细的创建和库存锁定：
     * private BuildPrepWorkOrderDetailReturnContext createAndLockPrepDetail(
     * PrepWorkorderModel prepWorkorder,
     * InventoryVO inventory,
     * int quantity,
     * int lineNum,
     * PrepWorkOrderDetailTypeEnum type,
     * BuildWorkOrderInputContext context)
     */
    private List<BuildPrepWorkOrderDetailReturnContext> allocateComponentInventory(
            PrepWorkorderModel prepWorkorder,
            List<InventoryVO> componentList,
            BaseFullProductVO baseProduct,
            BuildWorkOrderInputContext context
    ) {
        List<BuildPrepWorkOrderDetailReturnContext> result = new ArrayList<>();
        int totalQty = prepWorkorder.getQty();
        // 遍历每一个组件库存
        for (int j = 0; j < componentList.size(); j++) {
            InventoryVO vo = componentList.get(j);
            if (vo.getInStockCanAllocateQty() <= 0) {
                throw new BusinessException(StringUtil.format(
                        "Error Inventory Calculate, {} convertAssemblyInStockCanAllocateQty Can not enough to do",
                        baseFullProductStr(baseProduct)
                ));
            }

            // 计算需要的数量
            int neededQty = totalQty * vo.getComponentQty();
            // 分配数量
            int allocated = Math.min(vo.getInStockCanAllocateQty(), neededQty);

            neededQty -= allocated;
            if (neededQty != 0) {
                throw new BusinessException(StringUtil.format(
                        "Error Inventory Calculate, {} convertAssemblyInStockCanAllocateQty Can not enough to do",
                        baseFullProductStr(baseProduct)
                ));
            }

            // 创建预处理工单明细
            PrepWorkorderDetailModel prepDetail = getPrepWorkorderDetailService(context).createPrepWorkorderDetail(
                    prepWorkorder,
                    vo,
                    allocated,
                    j + 1,
                    PrepWorkOrderDetailTypeEnum.NONE
            );
            // 锁定库存
            InventoryLocked locked = inventoryLockedService.buildInventoryLocked(prepDetail, prepWorkorder);
            InventoryUtil.addLockedInventories(prepDetail.getProductId(), locked, context.getInventoryMap());

            prepDetail.setInventoryLockedId(locked.getId());

            // 添加到结果列表
            result.add(new BuildPrepWorkOrderDetailReturnContext(prepDetail, locked));
        }
        return result;
    }

    /**
     * 剩余场景，需要从inStock + groupList中混合拣货
     *
     * @param prepWorkorder 当前PrepWorkorder
     * @param componentList 组件库存列表
     * @param baseProduct   产品信息
     * @return 分配后的PrepWorkOrderDetailReturnContext列表
     * <p>
     * 优化建议: 该方法是三个库存分配方法中最复杂的一个，存在以下可优化点：
     * <p>
     * 1. 代码重复：与 allocateGroupInventory 和 allocateComponentInventory 方法有大量相似代码
     * 如创建 PrepWorkorderDetailModel、锁定库存等操作。
     * <p>
     * 2. 异常处理重复：多处抛出相同的异常信息，可以提取为共同方法。
     * <p>
     * 3. 嵌套循环复杂度高：包含多层嵌套循环，增加了代码复杂度和维护难度。
     * <p>
     * 4. 缺少预分配检查：在分配库存前没有检查总体库存是否足够，可能导致部分分配后才发现库存不足。
     * <p>
     * 优化建议：
     * 1. 提取公共逻辑到单独的方法，如创建预处理工单明细、锁定库存等操作：
     * private BuildPrepWorkOrderDetailReturnContext createAndLockPrepDetail(PrepWorkorderModel prepWorkorder,
     * InventoryVO inventory,
     * int quantity,
     * int lineNum,
     * PrepWorkOrderDetailTypeEnum type,
     * BuildWorkOrderInputContext context)
     * <p>
     * 2. 在方法开始时进行总量预检查，确保所有组件的库存总量足够，避免部分分配后才发现库存不足。
     * <p>
     * 3. 使用更清晰的变量命名和注释，提高代码可读性。
     * <p>
     * 4. 将异常信息提取为常量，便于统一管理和国际化。
     */
    private List<BuildPrepWorkOrderDetailReturnContext> allocateComplexComponentInventory(
            PrepWorkorderModel prepWorkorder,
            List<InventoryVO> componentList,
            BaseFullProductVO baseProduct,
            BuildWorkOrderInputContext context
    ) {
        List<BuildPrepWorkOrderDetailReturnContext> result = new ArrayList<>();
        int totalQty = prepWorkorder.getQty();

        // 遍历每一个组件库存
        for (int j = 0; j < componentList.size(); j++) {
            InventoryVO vo = componentList.get(j);
            // 计算需要的数量
            int neededQty = totalQty * vo.getComponentQty();

            // 先从inStockCanAllocateQty拣货
            int fromInStock = Math.min(vo.getInStockCanAllocateQty(), neededQty);
            neededQty -= fromInStock;
            if (fromInStock > 0) {
                // 创建预处理工单明细
                PrepWorkorderDetailModel prepDetail = getPrepWorkorderDetailService(context).createPrepWorkorderDetail(
                        prepWorkorder,
                        vo,
                        fromInStock,
                        j + 1,
                        PrepWorkOrderDetailTypeEnum.NONE
                );
                // 锁定库存
                InventoryLocked locked = inventoryLockedService.buildInventoryLocked(prepDetail, prepWorkorder);
                InventoryUtil.addLockedInventories(prepDetail.getProductId(), locked, context.getInventoryMap());
                prepDetail.setInventoryLockedId(locked.getId());
                // 添加到结果列表
                result.add(new BuildPrepWorkOrderDetailReturnContext(prepDetail, locked));
            }

            // 如果还需要更多，则从groupList中拣货
            if (neededQty > 0) {
                // 计算从group中需要的数量
                int fromGroup = Math.min(vo.getGroupsInStockAvailQty(), neededQty);
                neededQty -= fromGroup;
                if (fromGroup > 0) {
                    // 创建一个带PREPCONVERT的父明细
                    PrepWorkorderDetailModel convertDetail = getPrepWorkorderDetailService(context).createPrepWorkorderDetail(
                            prepWorkorder,
                            vo,
                            fromGroup,
                            j + 1,
                            PrepWorkOrderDetailTypeEnum.PREPCONVERT
                    );
                    // 父明细本身不锁定库存，需要拆分子明细
                    result.add(new BuildPrepWorkOrderDetailReturnContext(convertDetail, null));

                    // 分配给groupVo
                    int remainGroup = fromGroup;
                    for (InventoryVO groupVo : vo.getGroupList()) {
                        if (remainGroup <= 0) {
                            break;
                        }
                        if (groupVo.getInStockCanAllocateQty() <= 0) {
                            continue;
                        }

                        // 计算此次分配的数量
                        int alloc = Math.min(groupVo.getInStockCanAllocateQty(), remainGroup);
                        remainGroup -= alloc;

                        // 创建子明细
                        PrepWorkorderDetailModel groupChildDetail = getPrepWorkorderDetailService(context).createPrepWorkorderDetail(
                                prepWorkorder,
                                groupVo,
                                alloc,
                                j + 1,
                                PrepWorkOrderDetailTypeEnum.NONE
                        );
                        // 设置父ID
                        groupChildDetail.setParentId(convertDetail.getId());

                        // 锁定库存
                        InventoryLocked groupLocked = inventoryLockedService.buildInventoryLocked(groupChildDetail, prepWorkorder);
                        InventoryUtil.addLockedInventories(groupChildDetail.getProductId(), groupLocked, context.getInventoryMap());

                        groupChildDetail.setInventoryLockedId(groupLocked.getId());
                        // 添加到结果列表
                        result.add(new BuildPrepWorkOrderDetailReturnContext(groupChildDetail, groupLocked));
                    }

                    // 如果还有剩余未分配，抛出异常
                    if (remainGroup != 0) {
                        throw new BusinessException(StringUtil.format(
                                "Error Inventory Calculate, {} groupsInStockCanAllocateQty Can not enough to do",
                                baseFullProductStr(baseProduct)
                        ));
                    }
                }
            }

            // 如果还没分配完，库存不足
            if (neededQty > 0) {
                throw new BusinessException(StringUtil.format(
                        "Error Inventory Calculate, {} convertAssembly not enough to do",
                        baseFullProductStr(baseProduct)
                ));
            }
        }

        return result;
    }


    /**
     * 检查产品和库存是否为空
     *
     * @param productId   产品ID
     * @param baseProduct 产品信息
     * @param inventory   库存信息
     */
    private void checkNullProductOrInventory(Long productId, BaseFullProductVO baseProduct, InventoryVO inventory) {
        if (baseProduct == null) {
            throw new BusinessException(StringUtil.format(
                    "product:{} Not Found In baseFullProductVO, Please contact Developer",
                    productId
            ));
        }
        if (inventory == null) {
            throw new BusinessException(StringUtil.format(
                    "product:{} Not Found In Inventory, Please contact Developer",
                    productId
            ));
        }
    }

    /**
     * 根据orderType和标记判断返回PREPPACK/PREPMULTIBOX或PREPCONVERTPACK/PREPCONVERTMULTIBOX
     *
     * @param isMultiBox 是否是Multibox
     * @param directPack 是否直接打包
     * @return PrepWorkOrderTypeEnum枚举类型
     */
    private PrepWorkOrderTypeEnum getPackOrMultiBoxType(boolean isMultiBox, boolean directPack) {
        if (directPack) {
            return isMultiBox ? PrepWorkOrderTypeEnum.PREPMULTIBOX : PrepWorkOrderTypeEnum.PREPPACK;
        } else {
            return isMultiBox ? PrepWorkOrderTypeEnum.PREPCONVERTMULTIBOX : PrepWorkOrderTypeEnum.PREPCONVERTPACK;
        }
    }

    /**
     * 获取BaseFullProductVO的字符串表示
     *
     * @param baseProduct 产品信息
     * @return 字符串表示
     */
    private String baseFullProductStr(BaseFullProductVO baseProduct) {
        return baseProduct == null ? "NULL" : baseProduct.toString();
    }


    // endregion

}
