package cn.need.cloud.biz.converter.profile;

import cn.need.cloud.biz.client.dto.proflie.ProfileSystemDTO;
import cn.need.cloud.biz.model.entity.setting.ProfileSystem;
import cn.need.cloud.biz.model.vo.profile.ProfileSystemVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
public class ProfileSystemConverter extends AbstractModelConverter<ProfileSystem, ProfileSystemVO, ProfileSystemDTO> {

}
