package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPickingSlipDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb拣货单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPickingSlipConverter extends AbstractModelConverter<OtbPickingSlip, OtbPickingSlipVO, OtbPickingSlipDTO> {

}
