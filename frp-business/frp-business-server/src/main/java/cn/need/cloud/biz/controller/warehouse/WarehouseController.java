package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.WarehouseConverter;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseQuery;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseDropVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseVO;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.service.warehouse.WarehouseSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 仓库基础信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@RestController
@RequestMapping("/api/biz/no-warehouse/warehouse")
@Tag(name = "仓库基础信息")
public class WarehouseController extends AbstractRestController<WarehouseService, Warehouse, WarehouseConverter, WarehouseVO> {
    @Resource
    private WarehouseSpecialService warehouseSpecialService;

    @Operation(summary = "新增仓库基础信息", description = "接收仓库基础信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改仓库基础信息", description = "接收仓库基础信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除仓库基础信息", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(warehouseSpecialService.removeWarehouse(deletedNoteParam));
    }

    @Operation(summary = "根据id获取仓库基础信息详情", description = "根据数据主键id，从数据库中获取其对应的仓库基础信息详情")
    @GetMapping(value = "/detail/{id}")
    public Result<WarehouseVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取仓库基础信息详情
        WarehouseVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "启用禁用", description = "启用禁用")
    @PostMapping(value = "/switch-active")
    public Result<Integer> switchActive(@RequestBody @Parameter(description = "有效无效 vo对象", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        // 返回结果
        return success(service.switchActive(id.getId()));
    }

    @Operation(summary = "根据RefNum获取仓库基础信息详情", description = "根据数据RefNum，从数据库中获取其对应的仓库基础信息详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<WarehouseVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取仓库基础信息详情
        WarehouseVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取仓库基础信息分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库基础信息列表")
    @PostMapping(value = "/list")
    public Result<PageData<WarehousePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<WarehouseQuery> search) {

        // 获取仓库基础信息分页
        PageData<WarehousePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "获取仓库下拉", description = "获取仓库下拉")
    @GetMapping(value = "/warehouse-list/{id}")
    public Result<List<WarehouseDropVO>> list(@PathVariable("id") Long id) {

        return success(service.dropList(id));
    }
}
