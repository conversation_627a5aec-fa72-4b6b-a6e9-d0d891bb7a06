package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorderDetail;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预提工单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPrepWorkorderDetailConverter extends AbstractModelConverter<OtcPrepWorkorderDetail, OtcPrepWorkorderDetailVO, OtcPrepWorkorderDetailDTO> {

}
