package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPalletDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPallet;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbPalletVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB托盘 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPalletConverter extends AbstractModelConverter<OtbPallet, OtbPalletVO, OtbPalletDTO> {

}
