package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.model.bo.base.putawayslip.PutawaySlipPutAwayDetailBO;
import cn.need.cloud.biz.model.entity.base.putawayslip.PutawaySlipDetailModel;
import cn.need.framework.common.support.api.DeletedNoteParam;

import java.util.List;

/**
 * <p>
 * 库位special service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface BinLocationSpecialService {
    /**
     * 删除库位
     *
     * @param deletedNoteParam 库位id及删除备注
     * @return 影响行数
     */
    Integer removeBinLocation(DeletedNoteParam deletedNoteParam);

    /**
     * 上架Rollback
     *
     * @param paramDetailList 上架明细
     */
    <PutawaySlipDetail extends PutawaySlipDetailModel,
            Param extends PutawaySlipPutAwayDetailBO<PutawaySlipDetail>> void rollback(List<Param> paramDetailList);
}
