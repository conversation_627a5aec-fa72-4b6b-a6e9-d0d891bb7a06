package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigOtcDetailDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtcDetail;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置otc详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigOtcDetailConverter extends AbstractModelConverter<FeeConfigOtcDetail, FeeConfigOtcDetailVO, FeeConfigOtcDetailDTO> {

}
