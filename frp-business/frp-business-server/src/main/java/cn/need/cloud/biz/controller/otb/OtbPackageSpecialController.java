package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.model.param.otb.update.pkg.OtbPackageBatchRollbackUpdateParam;
import cn.need.cloud.biz.model.query.base.PackageRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.pkg.PackageRollbackConfirmVO;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageSpecialService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * OTC包裹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-package/special")
@Tag(name = "OTC包裹-Special")
@Slf4j
@AllArgsConstructor
public class OtbPackageSpecialController extends AbstractController {

    private final OtbPackageSpecialService otbPackageSpecialService;

    @Operation(summary = "Rollback", description = "批量Rollback")
    @PostMapping(value = "/batch-rollback")
    public Result<Boolean> rollback(@RequestBody @Valid OtbPackageBatchRollbackUpdateParam param) {

        // 返回结果
        return Result.ok(otbPackageSpecialService.batchRollback(param));
    }

    @Operation(summary = "Rollback 确认列表", description = "批量Rollback确认列表")
    @PostMapping(value = "/rollback/confirm")
    public Result<PackageRollbackConfirmVO> rollbackConfirm(@RequestBody @Valid PackageRollbackListQuery param) {

        // 返回结果
        return Result.ok(otbPackageSpecialService.rollbackConfirm(param));
    }

    @Operation(summary = "Cancel", description = "批量Cancel")
    @PostMapping(value = "/batch-cancel")
    public Result<Boolean> cancel(@RequestBody @Valid OtbPackageBatchRollbackUpdateParam param) {

        // 返回结果
        return Result.ok(otbPackageSpecialService.batchCancel(param));
    }

    @Operation(summary = "Cancel 确认列表", description = "批量Cancel确认列表")
    @PostMapping(value = "/cancel/confirm")
    public Result<PackageRollbackConfirmVO> cancelConfirm(@RequestBody @Valid PackageRollbackListQuery param) {

        // 返回结果
        return Result.ok(otbPackageSpecialService.rollbackConfirm(param));
    }
}
