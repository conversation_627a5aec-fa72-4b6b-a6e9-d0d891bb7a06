package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.Quote;
import cn.need.cloud.biz.model.param.feeconfig.create.QuoteCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.QuoteUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.QuoteQuery;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteFullVO;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.QuotePageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 仓库报价 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface QuoteService extends SuperService<Quote>,
        RefNumService<Quote, QuoteService>,
        RefNumWithNameService<Quote, QuoteService> {

    /**
     * 根据参数新增仓库报价
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价的相关信息
     * @return 仓库报价ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(QuoteCreateParam createParam);


    /**
     * 根据参数更新仓库报价
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(QuoteUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库报价列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价对象的列表(分页)
     */
    List<QuotePageVO> listByQuery(QuoteQuery query);

    /**
     * 根据查询条件获取仓库报价列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价对象的列表(分页)
     */
    PageData<QuotePageVO> pageByQuery(PageSearch<QuoteQuery> search);

    /**
     * 根据ID获取仓库报价
     *
     * @param id 仓库报价ID
     * @return 返回仓库报价VO对象
     */
    QuoteVO detailById(Long id);


    QuoteVO detailByRefNum(String refNum);

    @Transactional(rollbackFor = Exception.class)
    Integer switchActive(Long id);

    QuoteFullVO detailFullById(Long id);
}