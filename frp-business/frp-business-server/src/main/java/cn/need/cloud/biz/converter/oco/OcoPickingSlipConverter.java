package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPickingSlipDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPickingSlip;
import cn.need.cloud.biz.model.vo.oco.OcoPickingSlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO拣货单表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPickingSlipConverter extends AbstractModelConverter<OcoPickingSlip, OcoPickingSlipVO, OcoPickingSlipDTO> {

}



