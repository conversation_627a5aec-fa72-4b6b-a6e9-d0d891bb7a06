package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.entity.base.BaseWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.BaseWorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.param.inventory.create.InventoryLockedCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReleaseLockedParam;
import cn.need.cloud.biz.model.query.inventory.InventoryLockedQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryInventoryLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryLockedVO;
import cn.need.cloud.biz.model.vo.page.InventoryLockedPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 库存锁定服务接口
 * </p>
 * <p>
 * 该接口提供库存锁定管理的核心业务功能，包括库存锁定创建、查询和释放等。
 * 库存锁定是出库流程中的重要环节，用于在出库请求审核通过后临时锁定库存，
 * 防止库存被其他请求重复分配，直到锁定到具体库位后释放。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 库存锁定创建与管理
 * 2. 库存锁定查询（按产品、请求等条件）
 * 3. 锁定库存释放处理
 * 4. 工单与库存锁定的关联管理
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface InventoryLockedService extends SuperService<InventoryLocked> {

    /**
     * 创建库存锁定记录
     * <p>
     * 根据提供的参数创建库存锁定记录，通常在出库请求审核通过后调用。
     * 锁定记录会临时占用相应产品的库存数量，直到锁定到具体库位后释放。
     * </p>
     *
     * @param createParam 库存锁定创建参数，包含产品ID、锁定数量、关联请求信息等
     * @return 创建成功的库存锁定实体对象
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    InventoryLocked insertByParam(InventoryLockedCreateParam createParam);


    /**
     * 根据查询条件获取库存锁定记录列表
     * <p>
     * 查询符合条件的库存锁定记录，不包含分页信息。
     * 可用于特定业务场景下的库存锁定数据查询。
     * </p>
     *
     * @param query 查询条件对象，包含产品ID、仓库ID、请求ID等筛选条件
     * @return 返回符合条件的库存锁定记录列表
     */
    List<InventoryLockedPageVO> listByQuery(InventoryLockedQuery query);

    /**
     * 分页查询库存锁定记录
     * <p>
     * 根据查询条件和分页参数查询库存锁定记录。
     * 返回的结果包含分页信息和锁定记录列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的库存锁定记录列表
     */
    PageData<InventoryLockedPageVO> pageByQuery(PageSearch<InventoryLockedQuery> search);

    /**
     * 根据ID获取库存锁定详情
     * <p>
     * 查询指定ID的库存锁定记录详细信息。
     * </p>
     *
     * @param id 库存锁定记录ID
     * @return 返回库存锁定视图对象
     */
    InventoryLockedVO detailById(Long id);

    /**
     * 根据唯一编码获取库存锁定详情
     * <p>
     * 查询指定引用编号的库存锁定记录详细信息。
     * </p>
     *
     * @param refNum 库存锁定记录唯一编码
     * @return 返回库存锁定视图对象
     */
    InventoryLockedVO detailByRefNum(String refNum);


    /**
     * 根据产品ID列表查询库存锁定记录
     * <p>
     * 查询指定产品的最新库存锁定记录。
     * 用于了解产品当前的锁定状态和锁定数量。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回查询到的库存锁定记录视图对象列表
     */
    List<InventoryLockedVO> listLockeByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询库存锁定记录（包含库存信息）
     * <p>
     * 查询指定产品的最新库存锁定记录，包含库存相关信息。
     * 返回结果中包含产品库存和锁定的综合信息。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回包含库存信息的锁定记录视图对象列表
     */
    List<InventoryInventoryLockedVO> listInventoryLockeByProductIds(List<Long> productIds);

    /**
     * 根据产品ID列表查询库存锁定实体对象
     * <p>
     * 查询指定产品的最新库存锁定记录，返回实体对象列表。
     * 主要用于需要直接操作实体对象的场景。
     * </p>
     *
     * @param productIds 产品ID列表
     * @return 返回库存锁定实体对象列表
     */
    List<InventoryLocked> listEntityLockeByProductIds(List<Long> productIds);

    /**
     * 释放锁定库存
     * <p>
     * 批量释放指定的库存锁定记录。
     * 通常在库存锁定到具体库位后调用，或在取消出库请求时调用。
     * </p>
     *
     * @param lockedInventoryList 需要释放的库存锁定参数列表
     */
    void releaseLockedInventory(List<InventoryReleaseLockedParam> lockedInventoryList);

    /**
     * 根据工单信息构建库存锁定对象
     * <p>
     * 基于工单详情和工单信息构建库存锁定实体对象。
     * 用于将工单中的产品数量转换为库存锁定记录。
     * </p>
     *
     * @param <D>       工单详情模型类型
     * @param <W>       工单模型类型
     * @param detail    工单详情实例，提供产品、数量等信息
     * @param workOrder 工单实例，提供仓库、请求等信息
     * @return 构建的库存锁定实体对象
     */
    <D extends BaseWorkorderDetailModel, W extends BaseWorkorderModel> InventoryLocked buildInventoryLocked(D detail, W workOrder);
}