package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.model.entity.inventory.InventoryAudit;
import cn.need.cloud.biz.model.param.inventory.create.InventoryAuditCreateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryAuditQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryAuditVO;
import cn.need.cloud.biz.model.vo.page.InventoryAuditPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 库存盘点服务接口
 * </p>
 * <p>
 * 该接口提供库存盘点管理的核心业务功能，包括库存盘点创建、查询、执行等。
 * 库存盘点是仓库管理中的重要环节，用于核对实际库存与系统库存的差异，
 * 确保库存数据的准确性，并及时处理差异。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 库存盘点计划创建与执行
 * 2. 锁定库存盘点处理
 * 3. 库存盘点结果查询
 * 4. 与外部系统的盘点数据同步
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
public interface InventoryAuditService extends SuperService<InventoryAudit> {

    /**
     * 执行库存盘点
     * <p>
     * 根据提供的参数执行库存盘点操作，记录实际库存与系统库存的差异。
     * 盘点结果会更新系统库存记录，并生成盘点差异记录。
     * </p>
     *
     * @param createParam 库存盘点参数，包含产品ID、仓库ID、实际库存数量等信息
     * @return 创建成功的库存盘点记录ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long inventoryAudit(InventoryAuditCreateParam createParam);

    /**
     * 执行锁定库存盘点
     * <p>
     * 对已锁定的库存执行盘点操作，验证锁定库存的实际状态。
     * 此方法不返回盘点ID，通常用于批量盘点场景中的单条处理。
     * </p>
     *
     * @param createParam 库存盘点参数，包含产品ID、锁定库存信息等
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    void lockedInventoryAudit(InventoryAuditCreateParam createParam);

    /**
     * 批量执行库存盘点
     * <p>
     * 对多个产品或库位同时执行盘点操作。
     * 内部会调用单条盘点方法处理每个盘点记录。
     * </p>
     *
     * @param createParams 库存盘点参数列表，每个参数包含一个盘点项目的信息
     * @throws IllegalArgumentException 如果传入的createParams为空或包含空元素，则抛出此异常
     */
    default void batchInventoryAudit(List<InventoryAuditCreateParam> createParams) {
        createParams.forEach(this::lockedInventoryAudit);
    }

    /**
     * 执行库存盘点并同步到BOP系统
     * <p>
     * 执行库存盘点操作，并将盘点结果同步到业务运营平台(BOP)。
     * 此方法适用于需要与外部系统协同的盘点场景。
     * </p>
     *
     * @param auditParam 库存盘点参数，包含产品ID、仓库ID、实际库存数量等信息
     * @return 创建成功的库存盘点记录ID
     */
    Long inventoryAuditWithBop(InventoryAuditCreateParam auditParam);

    /**
     * 根据查询条件获取库存盘点记录列表
     * <p>
     * 查询符合条件的库存盘点记录，不包含分页信息。
     * 可用于特定业务场景下的库存盘点数据查询。
     * </p>
     *
     * @param query 查询条件对象，包含产品ID、仓库ID、时间范围等筛选条件
     * @return 返回符合条件的库存盘点记录列表
     */
    List<InventoryAuditPageVO> listByQuery(InventoryAuditQuery query);

    /**
     * 分页查询库存盘点记录
     * <p>
     * 根据查询条件和分页参数查询库存盘点记录。
     * 返回的结果包含分页信息和盘点记录列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的库存盘点记录列表
     */
    PageData<InventoryAuditPageVO> pageByQuery(PageSearch<InventoryAuditQuery> search);

    /**
     * 根据ID获取库存盘点详情
     * <p>
     * 查询指定ID的库存盘点记录详细信息。
     * </p>
     *
     * @param id 库存盘点记录ID
     * @return 返回库存盘点视图对象
     */
    InventoryAuditVO detailById(Long id);

    /**
     * 根据唯一编码获取库存盘点详情
     * <p>
     * 查询指定引用编号的库存盘点记录详细信息。
     * </p>
     *
     * @param refNum 库存盘点记录唯一编码
     * @return 返回库存盘点视图对象
     */
    InventoryAuditVO detailByRefNum(String refNum);
}