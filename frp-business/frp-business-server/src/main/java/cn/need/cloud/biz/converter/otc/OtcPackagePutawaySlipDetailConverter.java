package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPackagePutawaySlipDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPackagePutawaySlipDetail;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPackagePutawaySlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 包裹上架详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-25
 */
public class OtcPackagePutawaySlipDetailConverter extends AbstractModelConverter<OtcPackagePutawaySlipDetail, OtcPackagePutawaySlipDetailVO, OtcPackagePutawaySlipDetailDTO> {

} 