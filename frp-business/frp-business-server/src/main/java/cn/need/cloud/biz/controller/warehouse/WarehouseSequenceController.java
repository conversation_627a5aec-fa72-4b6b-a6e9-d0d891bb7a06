package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.WarehouseSequenceConverter;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseSequence;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseSequenceCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseSequenceUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseSequenceQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseSequencePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseSequenceVO;
import cn.need.cloud.biz.service.warehouse.WarehouseSequenceService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 生成仓库唯一refNum 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/warehouse-sequence")
@Tag(name = "生成仓库唯一refNum")
public class WarehouseSequenceController extends AbstractRestController<WarehouseSequenceService, WarehouseSequence, WarehouseSequenceConverter, WarehouseSequenceVO> {

    @Operation(summary = "新增生成仓库唯一refNum", description = "接收生成仓库唯一refNum的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseSequenceCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改生成仓库唯一refNum", description = "接收生成仓库唯一refNum的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseSequenceUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除生成仓库唯一refNum", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取生成仓库唯一refNum详情", description = "根据数据主键id，从数据库中获取其对应的生成仓库唯一refNum详情")
    @GetMapping(value = "/detail/{id}")
    public Result<WarehouseSequenceVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取生成仓库唯一refNum详情
        WarehouseSequenceVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取生成仓库唯一refNum分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的生成仓库唯一refNum列表")
    @PostMapping(value = "/list")
    public Result<PageData<WarehouseSequencePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<WarehouseSequenceQuery> search) {

        // 获取生成仓库唯一refNum分页
        PageData<WarehouseSequencePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
