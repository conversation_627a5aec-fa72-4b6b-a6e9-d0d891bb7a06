package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoWorkorderBinLocationDTO;
import cn.need.cloud.biz.model.entity.oco.OcoWorkorderBinLocation;
import cn.need.cloud.biz.model.vo.oco.OcoWorkorderBinLocationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO工单库位表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoWorkorderBinLocationConverter extends AbstractModelConverter<OcoWorkorderBinLocation, OcoWorkorderBinLocationVO, OcoWorkorderBinLocationDTO> {

}



