package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.converter.log.AuditShowLogConverter;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.query.log.AuditShowLogQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.log.AuditShowLogPageVO;
import cn.need.cloud.biz.model.vo.log.AuditShowLogVO;
import cn.need.cloud.biz.service.log.AuditShowLogService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@RestController
@RequestMapping("/api/biz/audit-show-log")
@Tag(name = "AuditShowLog")
public class AuditShowLogController extends AbstractRestController<AuditShowLogService, AuditShowLog, AuditShowLogConverter, AuditShowLogVO> {

    @Operation(summary = "根据id获取详情", description = "根据数据主键id，从数据库中获取其对应的详情")
    @GetMapping(value = "/detail/{id}")
    public Result<AuditShowLogVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取详情
        AuditShowLogVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的列表")
    @PostMapping(value = "/list")
    public Result<PageData<AuditShowLogPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<AuditShowLogQuery> search) {

        // 获取分页
        PageData<AuditShowLogPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "auditShowLog去重下拉", description = "auditShowLog去重下拉")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "搜索条件参数", required = true) AuditShowLogQuery auditShowLogQuery) {

        // 返回结果
        return success(service.distinctValue(auditShowLogQuery));
    }
}
