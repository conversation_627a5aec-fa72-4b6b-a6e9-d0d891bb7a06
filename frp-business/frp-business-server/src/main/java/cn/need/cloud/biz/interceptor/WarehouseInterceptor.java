package cn.need.cloud.biz.interceptor;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;
import java.util.Set;

/**
 * 仓库权限拦截器，负责检查当前用户是否有权限访问指定仓库。
 * 该拦截器会在请求处理前验证以下条件：
 * 1. 请求的仓库是否存在于缓存中
 * 2. 当前用户是否有权限操作该仓库
 * 3. 仓库是否处于启用状态
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2024-06-13
 */
@Component
@Slf4j
public class WarehouseInterceptor implements HandlerInterceptor {

    /**
     * 请求预处理方法，在Controller处理请求前被调用。
     * 用于验证当前用户是否有权限操作指定的仓库，包括检查仓库是否存在、
     * 用户是否有权限以及仓库是否启用等。
     *
     * @param request  当前HTTP请求对象
     * @param response 当前HTTP响应对象
     * @param handler  选择用于执行请求的处理器对象
     * @return 如果通过验证返回true，否则返回false
     * @throws Exception 处理过程中可能发生的异常
     */
    @Override
    public boolean preHandle(@NotNull HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws Exception {
        //获取仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        if (ObjectUtil.isEmpty(warehouseCache)) {
            // 设置响应内容类型为 JSON
            response.setContentType(MediaType.APPLICATION_JSON_UTF8_VALUE);
            // 设置 HTTP 状态码
            response.setStatus(HttpStatus.OK.value());
            // 将 JSON 数据写入响应体
            response.getWriter().write(JsonUtil.toJson(Result.fail("warehouse cache is empty")));
            // 中断请求处理
            return false;
        }
        //获取仓库操作人
        Set<Long> operationIds = warehouseCache.getOperationIds();
        Validate.isTrue(ObjectUtil.isNotEmpty(operationIds), "current operator don't have permission");
        Validate.isTrue(operationIds.contains(Objects.requireNonNull(Users.getUser()).getId()), "current operator don't have warehouse permission");
        Validate.isTrue(warehouseCache.getActiveFlag(), "The current warehouse has been disabled");
        return true;
    }
}
