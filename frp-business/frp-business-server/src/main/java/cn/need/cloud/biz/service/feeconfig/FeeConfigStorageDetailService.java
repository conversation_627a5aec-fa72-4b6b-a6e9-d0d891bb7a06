package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorageDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigStorageDetailCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigStorageDetailUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStorageDetailPageVO;
import cn.need.cloud.biz.service.base.UpdateService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * <p>
 * 仓库报价费用配置storage详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigStorageDetailService extends
        SuperService<FeeConfigStorageDetail>,
        UpdateService<FeeConfigStorageDetail, FeeConfigStorageDetailService>,
        FeeConfigDetailService<FeeConfigStorageDetail, FeeConfigStorageDetailService> {

    /**
     * 根据参数新增仓库报价费用配置storage详情
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置storage详情的相关信息
     * @return 仓库报价费用配置storage详情ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigStorageDetailCreateParam createParam);


    /**
     * 初始化单个仓库报价费用配置storage详情实体对象
     *
     * @param createParam  需要转换并初始化的仓库报价费用配置storage详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置storage详情实体对象
     */
    FeeConfigStorageDetail initFeeConfigStorageDetail(FeeConfigStorageDetailCreateParam createParam, Consumer<FeeConfigStorageDetail> initConsumer);

    /**
     * 初始化单个仓库报价费用配置storage详情实体对象
     *
     * @param createParams 需要转换并初始化的仓库报价费用配置storage详情参数对象，不能为空
     * @param initConsumer 用于执行初始化操作的消费者对象
     * @return 初始化后的仓库报价费用配置storage详情实体对象
     */
    List<FeeConfigStorageDetail> initFeeConfigStorageDetail(
            List<FeeConfigStorageDetailCreateParam> createParams,
            Consumer<FeeConfigStorageDetail> initConsumer);

    /**
     * 根据参数更新仓库报价费用配置storage详情
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置storage详情的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigStorageDetailUpdateParam updateParam);


    /**
     * 根据仓库报价费用配置storage主表ID批量更新详情
     *
     * @param feeConfigStorageId 仓库报价费用配置storage主表ID
     * @param updateParamList    需要更新的仓库报价费用配置storage详情参数列表，列表及元素均不能为空
     */
    void updateByFeeConfigStorageId(Long feeConfigStorageId, List<FeeConfigStorageDetailUpdateParam> updateParamList);

    /**
     * 根据查询条件获取仓库报价费用配置storage详情列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置storage详情对象的列表(分页)
     */
    List<FeeConfigStorageDetailPageVO> listByQuery(FeeConfigStorageDetailQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置storage详情列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置storage详情对象的列表(分页)
     */
    PageData<FeeConfigStorageDetailPageVO> pageByQuery(PageSearch<FeeConfigStorageDetailQuery> search);

    /**
     * 根据ID获取仓库报价费用配置storage详情
     *
     * @param id 仓库报价费用配置storage详情ID
     * @return 返回仓库报价费用配置storage详情VO对象
     */
    FeeConfigStorageDetailVO detailById(Long id);

    /**
     * 根据仓库报价费用配置storage主表ID获取实体列表
     *
     * @param feeConfigStorageId 仓库报价费用配置storage主表ID
     * @return 对应的仓库报价费用配置storage详情实体列表
     */
    List<FeeConfigStorageDetail> listEntityByFeeConfigStorageId(Long feeConfigStorageId);

    /**
     * 根据仓库报价费用配置storageid获取仓库报价费用配置storage详情集合
     *
     * @param feeConfigStorageId 仓库报价费用配置storageid
     * @return 仓库报价费用配置storage详情集合
     */
    List<FeeConfigStorageDetailVO> listByFeeConfigStorageId(Long feeConfigStorageId);

    /**
     * 根据仓库报价费用配置storage主表ID列表获取详情列表
     *
     * @param feeConfigIds 仓库报价费用配置storage主表ID列表
     * @return 仓库报价费用配置storage详情集合
     */
    List<FeeConfigStorageDetailVO> listByFeeConfigStorageIdList(Collection<Long> feeConfigIds);

    /**
     * 根据仓库报价费用配置storage主表ID列表获取详情映射
     *
     * @param feeConfigIds 仓库报价费用配置storage主表ID列表
     * @return 仓库报价费用配置storage主表ID到详情列表的映射
     */
    Map<Long, List<FeeConfigStorageDetailVO>> mapByFeeConfigStorageIdList(Collection<Long> feeConfigIds);
}