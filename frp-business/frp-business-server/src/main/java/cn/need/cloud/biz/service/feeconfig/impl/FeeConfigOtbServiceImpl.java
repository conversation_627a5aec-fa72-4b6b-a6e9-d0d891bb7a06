package cn.need.cloud.biz.service.feeconfig.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.feeconfig.FeeConfigOtbConverter;
import cn.need.cloud.biz.mapper.feeconfig.FeeConfigOtbMapper;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtb;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtbDetail;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtbCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtbUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtbQuery;
import cn.need.cloud.biz.model.vo.base.feeconfig.RefNumWithNameVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtbPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtbDetailService;
import cn.need.cloud.biz.service.feeconfig.FeeConfigOtbService;
import cn.need.cloud.biz.service.feeconfig.QuoteService;
import cn.need.cloud.biz.service.helper.SectionHelper;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库报价费用配置OTB服务实现类
 * </p>
 * <p>
 * 该类实现了针对出库业务(Outbound)相关费用配置的管理服务，包括费用配置的创建、
 * 更新、查询以及状态管理等功能。OTB费用配置用于定义出库业务中不同服务项目的价格标准，
 * 是仓储费用结算的重要基础数据。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 费用配置的基础CRUD操作
 * 2. 费用配置明细的关联管理
 * 3. 费用配置的启用/禁用管理
 * 4. 与报价单的关联处理
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@Service("feeConfigOtb")
public class FeeConfigOtbServiceImpl extends SuperServiceImpl<FeeConfigOtbMapper, FeeConfigOtb> implements FeeConfigOtbService {

    /**
     * OTB费用配置明细服务，用于管理费用配置的详细项目
     */
    @Resource
    private FeeConfigOtbDetailService feeConfigOtbDetailservice;

    /**
     * 报价服务，用于管理与费用配置关联的报价单信息
     */
    @Resource
    private QuoteService quoteService;


    /**
     * 根据参数创建OTB费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性创建。
     * 主要步骤包括：
     * 1. 验证创建参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 初始化并插入费用配置主记录
     * 4. 关联并插入费用配置明细记录
     * </p>
     *
     * @param createParam OTB费用配置创建参数
     * @return 创建成功的费用配置ID
     * @throws BusinessException 如果参数为空或明细列表校验失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeConfigOtbCreateParam createParam) {
        // 检查传入仓库报价费用配置otb参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(createParam.getDetailList());

        // 获取仓库报价费用配置otb转换器实例，用于将仓库报价费用配置otb参数对象转换为实体对象
        FeeConfigOtbConverter converter = Converters.get(FeeConfigOtbConverter.class);

        // 将仓库报价费用配置otb参数对象转换为实体对象并初始化
        FeeConfigOtb entity = initFeeConfigOtb(converter.toEntity(createParam));

        // 插入仓库报价费用配置otb实体对象到数据库
        super.insert(entity);

        final List<FeeConfigOtbDetail> feeConfigOtbDetails = feeConfigOtbDetailservice.initFeeConfigOtbDetail(createParam.getDetailList(), item -> item.setHeaderId(entity.getId()));

        feeConfigOtbDetailservice.insertBatch(feeConfigOtbDetails);

        // 返回仓库报价费用配置otbID
        return entity.getId();
    }

    /**
     * 根据参数更新OTB费用配置
     * <p>
     * 该方法在事务中执行，确保费用配置及其明细的原子性更新。
     * 主要步骤包括：
     * 1. 验证更新参数的有效性
     * 2. 检查明细列表的合法性
     * 3. 检查是否有关联的报价单（有关联报价单的配置不允许修改）
     * 4. 更新费用配置明细
     * 5. 更新费用配置主记录
     * </p>
     *
     * @param updateParam OTB费用配置更新参数
     * @return 更新影响的记录数
     * @throws BusinessException 如果参数为空、ID为空、明细列表校验失败，或存在关联报价单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeConfigOtbUpdateParam updateParam) {
        // 检查传入仓库报价费用配置otb参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {

            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        SectionHelper.checkDetailList(updateParam.getDetailList());

        checkHasQuote(updateParam.getId());

        // 获取仓库报价费用配置otb转换器实例，用于将仓库报价费用配置otb参数对象转换为实体对象
        FeeConfigOtbConverter converter = Converters.get(FeeConfigOtbConverter.class);
        // 将仓库报价费用配置otb参数对象转换为实体对象
        FeeConfigOtb entity = converter.toEntity(updateParam);

        feeConfigOtbDetailservice.updateByFeeConfigOtbId(updateParam.getId(), updateParam.getDetailList());

        // 执行更新仓库报价费用配置otb操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取OTB费用配置列表
     * <p>
     * 该方法根据指定的查询条件返回符合条件的OTB费用配置列表，不包含分页信息。
     * </p>
     *
     * @param query 包含查询条件的OTB费用配置查询对象
     * @return 符合条件的OTB费用配置分页视图对象列表
     */
    @Override
    public List<FeeConfigOtbPageVO> listByQuery(FeeConfigOtbQuery query) {

        return mapper.listByQuery(query);
    }

    /**
     * 分页查询OTB费用配置列表
     * <p>
     * 该方法根据查询条件和分页参数获取OTB费用配置列表，并填充关联的报价单信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的OTB费用配置分页视图对象
     */
    @Override
    public PageData<FeeConfigOtbPageVO> pageByQuery(PageSearch<FeeConfigOtbQuery> search) {
        Page<FeeConfigOtb> page = Conditions.page(search, entityClass);
        List<FeeConfigOtbPageVO> dataList = mapper.listByQuery(search.getCondition(), page);

        //fillData
        fillData(dataList);

        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取OTB费用配置详情
     * <p>
     * 该方法获取指定ID的OTB费用配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应ID的配置，则抛出业务异常。
     * </p>
     *
     * @param id OTB费用配置ID
     * @return 包含详细信息的OTB费用配置视图对象
     * @throws BusinessException 如果找不到指定ID的配置记录
     */
    @Override
    public FeeConfigOtbVO detailById(Long id) {
        FeeConfigOtb entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeConfigOtb", id));
        }

        return buildFeeConfigOtbVO(entity);
    }

    /**
     * 根据参考编号获取OTB费用配置详情
     * <p>
     * 该方法根据费用配置的参考编号获取配置详细信息，包括配置明细等关联信息。
     * 如果找不到对应参考编号的配置，则抛出业务异常。
     * </p>
     *
     * @param refNum OTB费用配置参考编号
     * @return 包含详细信息的OTB费用配置视图对象
     * @throws BusinessException 如果找不到指定参考编号的配置记录
     */
    @Override
    public FeeConfigOtbVO detailByRefNum(String refNum) {
        FeeConfigOtb entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {

            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeConfigOtb", "refNum", refNum));
        }
        return buildFeeConfigOtbVO(entity);
    }

    /**
     * 根据报价ID获取OTB费用配置列表
     * <p>
     * 该方法获取指定报价ID关联的所有OTB费用配置详细信息，包括配置明细等关联信息。
     * 如果报价ID为空，则返回空列表。
     * </p>
     *
     * @param quoteId 报价ID
     * @return 包含详细信息的OTB费用配置视图对象列表
     */
    @Override
    public List<FeeConfigOtbVO> listDetailByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return java.util.Collections.emptyList();
        }
        List<FeeConfigOtb> feeConfigOtbList = lambdaQuery().eq(FeeConfigOtb::getQuoteId, quoteId).list();
        return buildFeeConfigOtbVOList(feeConfigOtbList);
    }


    @Override
    public void beforeSwitchActive(FeeConfigOtb entity) {
        checkHasQuote(entity);
    }

    /**
     * 初始化OTB费用配置对象
     * <p>
     * 此方法用于设置OTB费用配置对象的必要参数，包括ID和参考编号，
     * 确保其处于有效状态。
     * </p>
     *
     * @param entity OTB费用配置对象，不应为空
     * @return 返回初始化后的OTB费用配置
     * @throws BusinessException 如果传入的费用配置为空，则抛出此异常
     */
    private FeeConfigOtb initFeeConfigOtb(FeeConfigOtb entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("FeeConfigOtb cannot be empty");
        }

        entity.setId(IdWorker.getId());
        entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_CONFIG_OTB));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 填充OTB费用配置分页视图对象的关联数据
     * <p>
     * 该方法用于为OTB费用配置分页视图对象列表填充关联的报价单信息，
     * 提升数据的完整性和展示效果。
     * </p>
     *
     * @param dataList OTB费用配置分页视图对象列表
     *                 <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 TODO: 方法中未对报价单信息的获取失败情况进行处理
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 优化建议：添加异常处理和日志记录，增强方法的健壮性
     */
    private void fillData(List<FeeConfigOtbPageVO> dataList) {
        if (!ObjectUtil.isNotEmpty(dataList)) {
            return;
        }

        final Map<Long, RefNumWithNameVO> quoteMap = quoteService.refNumWithNameMapByIds(FeeConfigUtil.getQuoteIds(dataList));

        if (ObjectUtil.isNotEmpty(quoteMap)) {
            for (FeeConfigOtbPageVO item : dataList) {
                if (!ObjectUtil.isNotEmpty(item.getQuoteId())) {
                    continue;
                }
                item.setQuote(quoteMap.get(item.getQuoteId()));
            }
        }
    }

    @Override
    public int removeAndNote(Long id, String note) {

        checkHasQuote(id);

        return super.removeAndNote(id, note);
    }


    /**
     * 构建仓库报价费用配置otbVO对象
     *
     * @param entity 仓库报价费用配置otb对象
     * @return 返回包含详细信息的仓库报价费用配置otbVO对象
     */
    private FeeConfigOtbVO buildFeeConfigOtbVO(FeeConfigOtb entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        FeeConfigOtbVO result = Converters.get(FeeConfigOtbConverter.class).toVO(entity);

        //详情
        List<FeeConfigOtbDetailVO> detailList = feeConfigOtbDetailservice.listByFeeConfigOtbId(result.getId());

        result.setDetailList(detailList);

        result.setQuote(quoteService.refNumWithNameById(entity.getQuoteId()));

        return result;
    }

    /**
     * 构建OTB费用配置视图对象列表
     *
     * @param entityList OTB费用配置实体对象列表
     * @return 返回包含详细信息的OTB费用配置视图对象列表
     */
    private List<FeeConfigOtbVO> buildFeeConfigOtbVOList(List<FeeConfigOtb> entityList) {
        if (ObjectUtil.isEmpty(entityList)) {
            return java.util.Collections.emptyList();
        }

        List<FeeConfigOtbVO> resultList = Converters.get(FeeConfigOtbConverter.class).toVO(entityList);

        // 获取所有费用配置ID
        List<Long> feeConfigIds = resultList.stream().map(FeeConfigOtbVO::getId).toList();

        // 批量获取所有费用配置的明细
        Map<Long, List<FeeConfigOtbDetailVO>> detailListMap = feeConfigOtbDetailservice.mapByFeeConfigOtbIdList(feeConfigIds);

        // 批量获取所有报价信息
        final Map<Long, RefNumWithNameVO> refNumVOMap = quoteService.refNumWithNameMapByIds(entityList.stream()
                .map(FeeConfigOtb::getQuoteId)
                .filter(ObjectUtil::isNotEmpty)
                .distinct()
                .toList());

        // 组装最终结果
        for (FeeConfigOtbVO feeConfigOtbVO : resultList) {
            // 设置明细
            List<FeeConfigOtbDetailVO> detailList = detailListMap.get(feeConfigOtbVO.getId());
            feeConfigOtbVO.setDetailList(ObjectUtil.isEmpty(detailList) ? java.util.Collections.emptyList() : detailList);

            // 设置报价信息
            RefNumWithNameVO quote = refNumVOMap.get(feeConfigOtbVO.getQuoteId());
            feeConfigOtbVO.setQuote(quote);
        }

        return resultList;
    }
}
