package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeStorageConverter;
import cn.need.cloud.biz.model.entity.fee.FeeStorage;
import cn.need.cloud.biz.model.param.fee.create.FeeStorageBuildParam;
import cn.need.cloud.biz.model.query.fee.FeeStorageQuery;
import cn.need.cloud.biz.model.vo.fee.FeeStorageVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeStoragePageVO;
import cn.need.cloud.biz.service.fee.FeeStorageBuildService;
import cn.need.cloud.biz.service.fee.FeeStorageService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 费用storage 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-storage")
@Tag(name = "费用storage")
public class FeeStorageController extends AbstractRestController<FeeStorageService, FeeStorage, FeeStorageConverter, FeeStorageVO> {

    @Resource
    private FeeStorageBuildService feeStorageBuildService;

    @Operation(summary = "根据id获取费用storage详情", description = "根据数据主键id，从数据库中获取其对应的费用storage详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeStorageVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用storage详情", description = "根据数据RefNum，从数据库中获取其对应的费用storage详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeStorageVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用storage分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用storage列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeStoragePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeStorageQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "构建Storage费用", description = "根据费用原始数据构建Storage费用")
    @PostMapping(value = "/build")
    public Result<Void> build(@Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeStorageBuildParam buildParam) {
        feeStorageBuildService.build(buildParam);
        return success();
    }
}
