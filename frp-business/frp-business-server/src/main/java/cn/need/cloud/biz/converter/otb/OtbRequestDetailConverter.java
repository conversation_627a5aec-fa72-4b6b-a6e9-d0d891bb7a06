package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbRequestDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbRequestDetail;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB请求详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbRequestDetailConverter extends AbstractModelConverter<OtbRequestDetail, OtbRequestDetailVO, OtbRequestDetailDTO> {

}
