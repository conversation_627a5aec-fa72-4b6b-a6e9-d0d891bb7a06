package cn.need.cloud.biz.service.inventory;

import cn.need.cloud.biz.client.constant.enums.otc.OtcOrderTypeEnum;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;

/**
 * <p>
 * OTC工单 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WorkorderService {


    WorkorderModel buildBaseWorkOrder(OtcRequestVO request, OtcOrderTypeEnum orderType);

    String getOtcWorkOrderRefNum();

    WorkorderModel buildWorkOrder(OtcRequestVO request);
}