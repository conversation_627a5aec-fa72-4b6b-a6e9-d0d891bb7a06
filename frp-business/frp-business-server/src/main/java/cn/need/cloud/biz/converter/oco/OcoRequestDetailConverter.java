package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoRequestDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoRequestDetail;
import cn.need.cloud.biz.model.vo.oco.OcoRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO请求明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoRequestDetailConverter extends AbstractModelConverter<OcoRequestDetail, OcoRequestDetailVO, OcoRequestDetailDTO> {

}



