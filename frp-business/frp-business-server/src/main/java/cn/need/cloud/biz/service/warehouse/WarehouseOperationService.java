package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.WarehouseOperation;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseOperationCreateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseOperationQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseOperationPageVO;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseDropVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationRemoveVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 仓库分配 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WarehouseOperationService extends SuperService<WarehouseOperation> {

    /**
     * 根据参数新增仓库分配
     *
     * @param createParam 请求创建参数，包含需要插入的仓库分配的相关信息
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    void insertByParam(WarehouseOperationCreateParam createParam);


    /**
     * 根据查询条件获取仓库分配列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库分配对象的列表(分页)
     */
    List<WarehouseOperationPageVO> listByQuery(WarehouseOperationQuery query);

    /**
     * 根据查询条件获取仓库分配列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库分配对象的列表(分页)
     */
    PageData<WarehouseOperationPageVO> pageByQuery(PageSearch<WarehouseOperationQuery> search);

    /**
     * 根据仓库id获取操作人列表
     *
     * @param id 仓库id
     * @return 仓库操作人列表
     */
    List<WarehouseOperation> listByWarehouseId(Long id);

    /**
     * 根据仓库id判断仓库是否分配操作人
     *
     * @param id 仓库id
     * @return 是否存在分配操作人
     */
    boolean exist(Long id);

    /**
     * 根据仓库id删除，仓库操作人关系
     *
     * @param warehouseId 仓库id
     */
    void removeByWarehouseId(Long warehouseId);

    /**
     * 获取操作人下的仓库
     *
     * @param id 操作人id
     * @return 仓库列表
     */
    List<WarehouseDropVO> listByOperationId(Long id);

    List<WarehouseOperation> listByWarehouseIds(List<Long> warehouseIds);

    /**
     * 填充操作人信息
     *
     * @param dataList 仓库列表
     */
    void fillOperationInfo(List<WarehousePageVO> dataList);

    /**
     * 根据用户id删除仓库分配关系
     */
    void removeByOperationId(WarehouseOperationRemoveVO warehouseOperationRemoveVO);

    void saveOperation(Long userTenantId, Long warehouseId);

    /**
     * 获取操作人id
     *
     * @param warehouseIdList 仓库id
     * @return 仓库id映射操作人id
     */
    Map<Long, Set<Long>> getOperatorId(Collection<Long> warehouseIdList);
}