package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPrepPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPrepPickingSlipDetail;
import cn.need.cloud.biz.model.vo.oco.OcoPrepPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO预处理拣货单明细表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPrepPickingSlipDetailConverter extends AbstractModelConverter<OcoPrepPickingSlipDetail, OcoPrepPickingSlipDetailVO, OcoPrepPickingSlipDetailDTO> {

}



