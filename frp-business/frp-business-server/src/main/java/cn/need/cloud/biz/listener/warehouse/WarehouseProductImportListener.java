package cn.need.cloud.biz.listener.warehouse;

import cn.need.cloud.biz.model.param.warehouse.importparam.WarehouseProductImportParam;
import cn.need.cloud.biz.service.warehouse.WarehouseProductService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.excel.AbstractImportListener;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 仓库产品批量导入监听器
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Slf4j
public class WarehouseProductImportListener extends AbstractImportListener<WarehouseProductImportParam> {

    private final WarehouseProductService warehouseProductService;

    public WarehouseProductImportListener(WarehouseProductService warehouseProductService) {
        this.warehouseProductService = warehouseProductService;
    }

    @Override
    protected void doFinish(List<WarehouseProductImportParam> dataList) {
        try {
            int result = warehouseProductService.batchImport(dataList);
            log.info("仓库产品批量导入完成，成功导入 {} 条记录", result);
        } catch (Exception e) {
            log.error("仓库产品批量导入失败", e);
            throw new RuntimeException("批量导入失败: " + e.getMessage(), e);
        }
    }

    @Override
    protected void doNext(WarehouseProductImportParam data, int index, List<String> messages) {
        // 验证必填字段
        if (ObjectUtil.isEmpty(data.getWarehouseCode())) {
            messages.add("仓库编码不能为空");
        }

        if (ObjectUtil.isEmpty(data.getTenantId())) {
            messages.add("租户ID不能为空");
        }

        if (ObjectUtil.isEmpty(data.getTransactionPartnerId())) {
            messages.add("交易伙伴ID不能为空");
        }

        if (ObjectUtil.isEmpty(data.getProductSupplierSku())) {
            messages.add("产品供应商SKU不能为空");
        }

        if (ObjectUtil.isEmpty(data.getExtraPackageType())) {
            messages.add("额外包装类型不能为空");
        }

        // 记录调试信息
        if (log.isDebugEnabled()) {
            log.debug("处理第{}行数据: {}", index, data);
        }
    }
}
