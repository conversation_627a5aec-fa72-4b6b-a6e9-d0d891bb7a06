package cn.need.cloud.biz.converter;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.dto.TransferOwnerShipRequestDTO;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequestDetail;
import cn.need.cloud.biz.model.vo.base.BasePartnerVO;
import cn.need.cloud.biz.model.vo.base.BaseProductVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestDetailVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestVO;
import cn.need.cloud.biz.service.transfer.TransferOwnerShipRequestDetailService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.upms.cache.TenantCacheService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.convert.AbstractModelConverter;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 货权转移 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public class TransferOwnerShipRequestConverter extends AbstractModelConverter<TransferOwnerShipRequest, TransferOwnerShipRequestVO, TransferOwnerShipRequestDTO> {

    @Override
    public TransferOwnerShipRequestVO toVO(TransferOwnerShipRequest entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }

        // 基础属性复制
        TransferOwnerShipRequestVO vo = BeanUtil.copyNew(entity, TransferOwnerShipRequestVO.class);

        // 填充合作伙伴信息
        fillPartnerInfo(vo, entity);

        // 填充仓库信息
        fillWarehouseInfo(vo);

        // 填充详情信息
        fillDetailInfo(vo, entity);

        return vo;
    }

    /**
     * 填充合作伙伴信息
     */
    private void fillPartnerInfo(TransferOwnerShipRequestVO vo, TransferOwnerShipRequest entity) {
        TenantCacheService tenantCacheService = SpringUtil.getBean(TenantCacheService.class);

        // 填充源合作伙伴信息
        if (entity.getFromPartnerId() != null) {
            vo.setFromPartnerVO(BeanUtil.copyNew(
                tenantCacheService.getById(entity.getFromPartnerId()),
                BasePartnerVO.class
            ));
        }

        // 填充目标合作伙伴信息
        if (entity.getToPartnerId() != null) {
            vo.setToPartnerVO(BeanUtil.copyNew(
                tenantCacheService.getById(entity.getToPartnerId()),
                BasePartnerVO.class
            ));
        }
    }

    /**
     * 填充仓库信息
     */
    private void fillWarehouseInfo(TransferOwnerShipRequestVO vo) {
        WarehouseCacheUtil.filledWarehouse(vo);
    }

    /**
     * 填充详情信息
     */
    private void fillDetailInfo(TransferOwnerShipRequestVO vo, TransferOwnerShipRequest entity) {
        TransferOwnerShipRequestDetailService detailService = SpringUtil.getBean(TransferOwnerShipRequestDetailService.class);

        // 获取详情列表
        List<TransferOwnerShipRequestDetail> detailList = detailService.listByHeaderId(entity.getId());

        // 转换为VO对象
        List<TransferOwnerShipRequestDetailVO> detailVOList = BeanUtil.copyNew(detailList, TransferOwnerShipRequestDetailVO.class);

        // 填充产品信息 - 手动处理 fromProductVO 和 toProductVO
        fillProductInfo(detailVOList);

        vo.setDetails(detailVOList);
    }

    /**
     * 填充详情中的产品信息
     */
    private void fillProductInfo(List<TransferOwnerShipRequestDetailVO> detailVOList) {
        if (ObjectUtil.isEmpty(detailVOList)) {
            return;
        }

        // 收集所有产品ID
        Set<Long> productIds = new HashSet<>();
        detailVOList.forEach(detail -> {
            if (detail.getFromProductId() != null) {
                productIds.add(detail.getFromProductId());
            }
            if (detail.getToProductId() != null) {
                productIds.add(detail.getToProductId());
            }
        });

        // 批量获取产品缓存信息
        Map<Long, ProductCache> productCacheMap = ProductCacheUtil.listByIds(productIds)
                .stream()
                .collect(Collectors.toMap(ProductCache::getId, Function.identity()));

        // 填充产品信息
        detailVOList.forEach(detail -> {
            // 填充源产品信息
            if (detail.getFromProductId() != null) {
                ProductCache fromProductCache = productCacheMap.get(detail.getFromProductId());
                if (fromProductCache != null) {
                    detail.setFromProductVO(BeanUtil.copyNew(fromProductCache, BaseProductVO.class));
                }
            }

            // 填充目标产品信息
            if (detail.getToProductId() != null) {
                ProductCache toProductCache = productCacheMap.get(detail.getToProductId());
                if (toProductCache != null) {
                    detail.setToProductVO(BeanUtil.copyNew(toProductCache, BaseProductVO.class));
                }
            }
        });
    }
}
