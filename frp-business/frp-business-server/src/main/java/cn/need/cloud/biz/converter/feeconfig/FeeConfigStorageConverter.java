package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigStorageDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorage;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置storage 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigStorageConverter extends AbstractModelConverter<FeeConfigStorage, FeeConfigStorageVO, FeeConfigStorageDTO> {

}
