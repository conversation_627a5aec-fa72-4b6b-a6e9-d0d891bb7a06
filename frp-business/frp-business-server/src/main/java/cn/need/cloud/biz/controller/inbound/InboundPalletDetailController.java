package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundPalletDetailConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundPalletDetail;
import cn.need.cloud.biz.model.query.inbound.InboundPalletDetailQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletDetailVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletDetailPageVO;
import cn.need.cloud.biz.service.inbound.InboundPalletDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 入库单打托详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-pallet-detail")
@Tag(name = "入库单打托详情")
public class InboundPalletDetailController extends AbstractRestController<InboundPalletDetailService, InboundPalletDetail, InboundPalletDetailConverter, InboundPalletDetailVO> {

    @Operation(summary = "根据id获取入库单打托详情详情", description = "根据数据主键id，从数据库中获取其对应的入库单打托详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundPalletDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库单打托详情详情
        InboundPalletDetailVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库单打托详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库单打托详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundPalletDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundPalletDetailQuery> search) {

        // 获取入库单打托详情分页
        PageData<InboundPalletDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
