package cn.need.cloud.biz.provider.otb;

import cn.need.cloud.biz.client.api.otb.OtbRequestClient;
import cn.need.cloud.biz.client.api.path.OtbRequestPath;
import cn.need.cloud.biz.client.constant.enums.base.ShipTypeEnum;
import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseFullProductDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbRequestCreateOrUpdateWithWarehouseReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbRequestDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbRequestQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbRequestDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbRequestPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otb.OtbRequestRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.param.otb.create.request.OtbRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestAuditParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestCancelParam;
import cn.need.cloud.biz.model.param.otb.update.request.OtbRequestUpdateParam;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.request.OtbRequestSpecialService;
import cn.need.cloud.dfs.client.constant.enums.FileDataTypeEnum;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(OtbRequestPath.PREFIX)
public class OtbRequestProvider implements OtbRequestClient {
    @Resource
    private OtbRequestService otbRequestService;
    @Resource
    private OtbRequestSpecialService otbRequestSpecialService;

    /**
     * 构建创建参数
     *
     * @param reqDTO 请求参数
     * @return {@link OtbRequestCreateParam}
     */
    private static @NotNull OtbRequestCreateParam buildCreateParam(OtbRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        //新增
        OtbRequestCreateParam createParam = BeanUtil.copyNew(reqDTO, OtbRequestCreateParam.class);
        createParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
        createParam.setRequestRefNum(reqDTO.getRequestOfRequestRefNum());

        if (reqDTO.getShipType().equals(ShipTypeEnum.BY_REQUEST_ROUTING_INSTRUCTION_FILE)) {

            if (ObjectUtil.isEmpty(reqDTO.getRoutingInstructionFileFileType())) {
                throw new BusinessException("routingInstructionFileFileType is required");
            }

            createParam.setRoutingInstructionFileFileType(FileDataTypeEnum.fromType(reqDTO.getRoutingInstructionFileFileType()));
        }

        //构建新增详情
        List<OtbRequestDetailVO> detailList = reqDTO.getDetailList()
                .stream()
                .map(item -> {
                    OtbRequestDetailVO otbRequestDetailVO = BeanUtil.copyNew(item, OtbRequestDetailVO.class);
                    otbRequestDetailVO.setProductId(item.getProductId());
                    return otbRequestDetailVO;
                }).toList();
        //填充请求详情
        createParam.setDetailList(detailList);
        return createParam;
    }

    @Override
    @PostMapping(value = OtbRequestPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(@RequestBody OtbRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());
        //填充产品信息
        List<ProductReqDTO> list = reqDTO.getDetailList()
                .stream()
                .map(OtbRequestDetailReqDTO::getProduct)
                .toList();
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), list);
        //判断是新增还是编辑
        OtbRequest otbRequest;
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            //构建新增参数
            OtbRequestCreateParam createParam = buildCreateParam(reqDTO);
            //持久化
            otbRequest = otbRequestService.insertByParam(createParam);
        } else {
            OtbRequestUpdateParam updateParam = buildUpdateParam(reqDTO);
            //持久化
            otbRequest = otbRequestService.updateByParam(updateParam);
        }
        //返回结果
        RefNumWithRequestRefNumRespDTO respDTO = new RefNumWithRequestRefNumRespDTO();
        respDTO.setRefNum(otbRequest.getRefNum());
        respDTO.setRequestRefNum(otbRequest.getRequestRefNum());
        return Result.ok(respDTO);
    }

    /**
     * 构建更新参数
     *
     * @param reqDTO 请求参数
     * @return {@link OtbRequestUpdateParam}
     */
    private @NotNull OtbRequestUpdateParam buildUpdateParam(OtbRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        //获取requestId
        otbRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getReqDTO()));
        //构建更新参数
        OtbRequestUpdateParam updateParam = BeanUtil.copyNew(reqDTO, OtbRequestUpdateParam.class);
        updateParam.setId(reqDTO.getRequestId());
        updateParam.setRequestRefNum(reqDTO.getRequestOfRequestRefNum());

        if (ObjectUtil.isEmpty(reqDTO.getRoutingInstructionFileFileType())) {
            throw new BusinessException("routingInstructionFileFileType is required");
        }

        updateParam.setRoutingInstructionFileFileType(FileDataTypeEnum.fromType(reqDTO.getRoutingInstructionFileFileType()));

        //构建更新详情
        List<OtbRequestDetailVO> detailList = reqDTO.getDetailList()
                .stream()
                .map(item -> {
                    OtbRequestDetailVO otbRequestDetailVO = BeanUtil.copyNew(item, OtbRequestDetailVO.class);
                    otbRequestDetailVO.setProductId(item.getProductId());
                    return otbRequestDetailVO;
                }).toList();
        //填充请求详情
        updateParam.setDetailList(detailList);
        return updateParam;
    }

    @Override
    @PostMapping(value = OtbRequestPath.DETAIL)
    @IgnoreAuth
    public Result<OtbRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner(), query.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());


        //填充请求单id
        otbRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));
        //获取详情
        OtbRequestVO otbRequestVO = otbRequestService.detailById(query.getRequestId());
        //构建返回参数
        OtbRequestRespDTO otbRequestRespDTO = BeanUtil.copyNew(otbRequestVO, OtbRequestRespDTO.class);
        otbRequestRespDTO.setBaseWarehouseDTO(BeanUtil.copyNew(otbRequestVO.getBaseWarehouseVO(), BaseWarehouseDTO.class));
        otbRequestRespDTO.setTransactionPartnerDTO(BeanUtil.copyNew(otbRequestVO.getTransactionPartnerVO(), BasePartnerDTO.class));
        //构建请求参数
        List<OtbRequestDetailRespDTO> list = otbRequestVO.getDetailList()
                .stream()
                .map(item -> {
                    OtbRequestDetailRespDTO otbRequestDetailRespDTO = BeanUtil.copyNew(item, OtbRequestDetailRespDTO.class);
                    otbRequestDetailRespDTO.setBaseFullProductDTO(BeanUtil.copyNew(item.getBaseProductVO(), BaseFullProductDTO.class));
                    return otbRequestDetailRespDTO;
                }).toList();
        //填充请求详情
        otbRequestRespDTO.setDetailList(list);
        //构建返回参数
        return Result.ok(otbRequestRespDTO);
    }

    @Override
    @PostMapping(value = OtbRequestPath.LIST)
    @IgnoreAuth
    public Result<PageData<OtbRequestPageRespDTO>> list(@RequestBody PageSearch<OtbRequestQueryReqDTO> search) {
        //获取查询参数
        OtbRequestQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());

        if (ObjectUtil.isEmpty(query.getWarehouseReqDTO())) {
            throw new BusinessException("warehouseReqDTO is required");
        }

        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //构建方法入参
        PageSearch<OtbRequestQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, OtbRequestQuery.class));
        //调用列表方法
        PageData<OtbRequestPageVO> data = otbRequestService.pageByQuery(pageSearch);
        //对象转换
        PageData<OtbRequestPageRespDTO> pageData = PageUtil.convert(data, item -> {
            OtbRequestPageRespDTO pageRespDTO = BeanUtil.copyNew(item, OtbRequestPageRespDTO.class);
            pageRespDTO.setWarehouseReqDTO(query.getWarehouseReqDTO());
            pageRespDTO.setLogisticPartner(query.getLogisticPartner());
            pageRespDTO.setTransactionPartnerDTO(BeanUtil.copyNew(item.getTransactionPartnerVO(), BasePartnerDTO.class));
            return pageRespDTO;
        });
        //返回结果
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = OtbRequestPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseRequestDeleteAndCancelReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otbRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        //返回结果
        return Result.ok(otbRequestService.removeAndNote(reqDTO.getRequestId(), reqDTO.getNote()));
    }

    @Override
    @PostMapping(value = OtbRequestPath.CANCEL)
    @IgnoreAuth
    public Result<Integer> cancel(@RequestBody BaseRequestDeleteAndCancelReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otbRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        OtbRequestCancelParam noteParam = BeanUtil.copyNew(reqDTO, OtbRequestCancelParam.class);
        noteParam.setId(reqDTO.getRequestId());
        noteParam.setNote(StringPool.HASH);
        //返回结果
        return Result.ok(otbRequestSpecialService.cancel(noteParam));
    }

    @Override
    @PostMapping(value = OtbRequestPath.AUDIT)
    @IgnoreAuth
    public Result<Integer> audit(@RequestBody BaseRequestAuditReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otbRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        //构建参数
        OtbRequestAuditParam otbRequestAuditParam = BeanUtil.copyNew(reqDTO, OtbRequestAuditParam.class);
        otbRequestAuditParam.setId(reqDTO.getRequestId());
        //调用审批方法
        otbRequestService.audit(otbRequestAuditParam);
        return Result.ok();
    }
}
