package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductConverter;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.param.product.create.ProductCreateParam;
import cn.need.cloud.biz.model.param.product.update.ProductUpdateParam;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 产品 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product")
@Tag(name = "产品")
@Validated
public class ProductController extends AbstractRestController<ProductService, Product, ProductConverter, ProductVO> {


    @Operation(summary = "新增产品", description = "接收产品的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProductCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改产品", description = "接收产品的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProductUpdateParam updateParam) {

        service.updateByParam(updateParam);
        // 返回结果
        return success(1);
    }

    @Operation(summary = "根据id获取产品详情", description = "根据数据主键id，从数据库中获取其对应的产品详情")
    @GetMapping(value = "/detail/{id}")
    public Result<ProductVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取产品详情
        ProductVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取产品分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的产品列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProductVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProductQuery> search) {
        // 获取产品分页
        PageData<ProductVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }


}
