package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigOtcDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtc;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtcVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置otc 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigOtcConverter extends AbstractModelConverter<FeeConfigOtc, FeeConfigOtcVO, FeeConfigOtcDTO> {

}
