package cn.need.cloud.biz.provider.binlocation;

import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.api.binlocation.BinLocationClient;
import cn.need.cloud.biz.client.api.path.BinLocationPath;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseBinLocationDTO;
import cn.need.cloud.biz.client.dto.req.base.info.BinLocationDeleteDTO;
import cn.need.cloud.biz.client.dto.req.base.info.ProductVersionReqDTO;
import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationCreateOrUpdateDTO;
import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationInfoDTO;
import cn.need.cloud.biz.client.dto.req.binlocation.ReSetBinLocationEmptyReqDTO;
import cn.need.cloud.biz.client.dto.resp.binlocation.BinLocationDetailLockedRespDTO;
import cn.need.cloud.biz.client.dto.resp.binlocation.BinLocationDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.binlocation.BinLocationPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.binlocation.BinLocationRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.cloud.biz.provider.base.BinLocationUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(BinLocationPath.PREFIX)
public class BinLocationProvider implements BinLocationClient {
    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    private BinLocationService binLocationService;

    @Override
    @PostMapping(value = BinLocationPath.RESET_BIN_LOCATION_EMPTY)
    @IgnoreAuth
    public Result<Boolean> resetBinLocationEmpty(@RequestBody ReSetBinLocationEmptyReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //所有库存置空
        Boolean result = binLocationDetailService.resetBinLocationEmpty(reqDTO.getWarehouseId());
        //返回更新结果
        return Result.ok(result);
    }

    @Override
    @PostMapping(value = BinLocationPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<Boolean> createOrUpdate(@RequestBody BinLocationCreateOrUpdateDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());
        //填充库位id
        BinLocationUtil.fillBinLocationId(List.of(reqDTO.getBinLocationInfoDTO()));
        //判断库位是否存在
        if (ObjectUtil.isNotEmpty(reqDTO.getBinLocationId())) {
            //更新库位
            reqDTO.init();
            BinLocationUpdateParam binLocationUpdateParam = BeanUtil.copyNew(reqDTO, BinLocationUpdateParam.class);
            //调用库位更新方法
            binLocationService.updateByParam(binLocationUpdateParam);
            return Result.ok(Boolean.TRUE);
        }
        //构建方法参数
        BinLocationCreateParam param = BeanUtil.copyNew(reqDTO, BinLocationCreateParam.class);
        //调用库位新增方法
        binLocationService.insertByParam(param);
        //返回结果
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @PostMapping(value = BinLocationPath.DETAIL)
    @IgnoreAuth
    public Result<BinLocationRespDTO> detail(@RequestBody BaseBinLocationDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());
        //填充库位id
        BinLocationUtil.fillBinLocationId(List.of(query.getBinLocationInfoDTO()));
        //获取详情
        BinLocationVO binLocationVO = binLocationService.detailById(query.getBinLocationId());
        //构建返回参数
        return Result.ok(buildRespDTO(binLocationVO, query));
    }

    @Override
    @PostMapping(value = BinLocationPath.LIST)
    @IgnoreAuth
    public Result<PageData<BinLocationPageRespDTO>> list(@RequestBody PageSearch<BaseBinLocationDTO> search) {
        //获取查询参数
        BaseBinLocationDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());
        //构建方法入参
        PageSearch<BinLocationQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, BinLocationQuery.class));
        //调用列表方法
        PageData<BinLocationPageVO> data = binLocationService.pageByQuery(pageSearch);
        //对象转换
        PageData<BinLocationPageRespDTO> pageData = PageUtil.convert(data, item -> {
            BinLocationPageRespDTO pageRespDTO = BeanUtil.copyNew(item, BinLocationPageRespDTO.class);
            pageRespDTO.setBinLocationInfoDTO(BeanUtil.copyNew(item, BinLocationInfoDTO.class));
            pageRespDTO.setWarehouseReqDTO(query.getWarehouseReqDTO());
            pageRespDTO.setLogisticPartner(query.getLogisticPartner());
            return pageRespDTO;
        });
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = BinLocationPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BinLocationDeleteDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());
        BinLocationUtil.fillBinLocationId(List.of(reqDTO.getBinLocationInfoDTO()));
        //删除传参
        DeletedNoteParam deletedNoteParam = new DeletedNoteParam(
                reqDTO.getBinLocationId(),
                reqDTO.getDeletedNote()
        );
        //返回结果
        return Result.ok(binLocationService.removeBinLocation(deletedNoteParam));
    }

    /**
     * 构建返回参数
     *
     * @param binLocationVO 库位详情
     * @param query         查询参数
     * @return 返回参数
     */
    private BinLocationRespDTO buildRespDTO(BinLocationVO binLocationVO, BaseBinLocationDTO query) {
        BinLocationRespDTO binLocationRespDTO = BeanUtil.copyNew(binLocationVO, BinLocationRespDTO.class);
        BinLocationInfoDTO binLocationInfoDTO = BeanUtil.copyNew(binLocationVO, BinLocationInfoDTO.class);
        binLocationRespDTO.setBinLocationInfoDTO(binLocationInfoDTO);
        //填充租户信息
        binLocationRespDTO.setLogisticPartner(query.getLogisticPartner());
        //填充仓库信息
        binLocationRespDTO.setWarehouseReqDTO(query.getWarehouseReqDTO());
        //获取库位详情
        List<BinLocationDetailVO> detailList = binLocationVO.getDetailList();
        //判空
        if (ObjectUtil.isEmpty(detailList)) {
            return binLocationRespDTO;
        }
        //获取库位详情返回值
        List<BinLocationDetailRespDTO> list = detailList.stream().map(item -> {
            BinLocationDetailRespDTO respDTO = BeanUtil.copyNew(item, BinLocationDetailRespDTO.class);
            List<BinLocationDetailLockedRespDTO> lockedRespList = BeanUtil.copyNew(item.getBinLocationDetailLockedList(), BinLocationDetailLockedRespDTO.class);
            respDTO.setBinLocationDetailLockedList(lockedRespList);
            ProductVersionReqDTO productVersionReqDTO = new ProductVersionReqDTO();
            productVersionReqDTO.setProductVersionId(item.getProductVersionId());
            return respDTO;
        }).toList();
        //版本产品id
        List<Long> productVersionIdList = list.stream()
                .map(BinLocationDetailRespDTO::getProductVersionId)
                .toList();
        //获取版本产品缓存
        List<ProductVersionCache> cacheList = ProductVersionCacheUtil.listByIds(productVersionIdList);
        Map<Long, ProductVersionCache> cacheMap = ObjectUtil.toMap(cacheList, ProductVersionCache::getId);
        //填充版本信息
        list.forEach(item -> {
            ProductVersionCache cache = cacheMap.get(item.getProductVersionId());
            if (ObjectUtil.isNotEmpty(cache)) {
                ProductVersionReqDTO productVersion = item.getProductVersion();
                productVersion.setVersionRefNum(StringUtil.toString(cache.getProductVersionInt()));
                //填充产品信息
                ProductReqDTO productReqDTO = BeanUtil.copyNew(cache, ProductReqDTO.class);
                productVersion.setProduct(productReqDTO);
            }
        });
        return binLocationRespDTO;
    }
}
