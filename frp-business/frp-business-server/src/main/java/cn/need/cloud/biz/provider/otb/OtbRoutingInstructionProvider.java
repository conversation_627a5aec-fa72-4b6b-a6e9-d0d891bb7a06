package cn.need.cloud.biz.provider.otb;

import cn.need.cloud.biz.client.api.path.OtbRoutingInstructionPath;
import cn.need.cloud.biz.client.api.ri.OtbRoutingInstructionClient;
import cn.need.cloud.biz.client.dto.req.otb.*;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.create.ri.OtbRoutingInstructionCreateParam;
import cn.need.cloud.biz.model.param.otb.update.routing.OtbRoutingInstructionUpdateParam;
import cn.need.cloud.biz.model.query.otb.routing.OtbRoutingInstructionQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.cloud.biz.provider.base.RIUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.ri.OtbRoutingInstructionService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(OtbRoutingInstructionPath.PREFIX)
public class OtbRoutingInstructionProvider implements OtbRoutingInstructionClient {
    @Resource
    private OtbRoutingInstructionService otbRoutingInstructionService;
    @Resource
    private OtbShipmentService otbShipmentService;
    @Resource
    private OtbRequestService otbRequestService;

    @Override
    @PostMapping(value = OtbRoutingInstructionPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(OtbRoutingInstructionCreateOrUpdateReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //获取otbShipmentId
        OtbShipment otbShipment = otbShipmentService.getByRefNum(reqDTO.getOtbShipmentRefNum());
        if (ObjectUtil.isEmpty(otbShipment)) {
            throw new BusinessException(StringUtil.format("OtbShipment not found otbShipmentRefNum:{}", reqDTO.getOtbShipmentRefNum()));
        }

        //判断是新增还是编辑
        OtbRoutingInstruction otbRoutingInstruction;
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            OtbRoutingInstructionCreateParam param = BeanUtil.copyNew(reqDTO, OtbRoutingInstructionCreateParam.class);
            param.setOtbShipmentId(otbShipment.getId());
            param.setOtbRequestId(otbShipment.getOtbRequestId());

            // //填充包裹label
            // if (ObjectUtil.isNotEmpty(reqDTO.getRoutingInstructionPackageLabelList())) {
            //     List<OtbRoutingInstructionPackageLabelVO> packageLabelList = BeanUtil.copyNew(reqDTO.getRoutingInstructionPackageLabelList(), OtbRoutingInstructionPackageLabelVO.class);
            //     param.setRoutingInstructionPackageLabelList(packageLabelList);
            // }
            // //填充托盘label
            // if (ObjectUtil.isNotEmpty(reqDTO.getRoutingInstructionPalletLabelList())) {
            //     List<OtbRoutingInstructionPalletLabelVO> palletLabelList = BeanUtil.copyNew(reqDTO.getRoutingInstructionPalletLabelList(), OtbRoutingInstructionPalletLabelVO.class);
            //     param.setRoutingInstructionPalletLabelList(palletLabelList);
            // }

            //调用新增方法
            otbRoutingInstruction = otbRoutingInstructionService.insertByParam(param);
        } else {
            //编辑方法
            OtbRoutingInstructionUpdateParam param = BeanUtil.copyNew(reqDTO, OtbRoutingInstructionUpdateParam.class);
            //获取ri id
            OtbRoutingInstruction instruction = otbRoutingInstructionService.getByRefNum(reqDTO.getRefNum());

            if (ObjectUtil.isEmpty(instruction)) {
                throw new BusinessException(StringUtil.format("OtbRoutingInstruction not found refNum:{}", reqDTO.getRefNum()));
            }

            param.setId(instruction.getId());
            param.setOtbShipmentId(otbShipment.getId());
            param.setOtbRequestId(otbShipment.getOtbRequestId());

            // //填充包裹label
            // if (ObjectUtil.isNotEmpty(reqDTO.getRoutingInstructionPackageLabelList())) {
            //     List<OtbRoutingInstructionPackageLabelVO> packageLabelList = BeanUtil.copyNew(reqDTO.getRoutingInstructionPackageLabelList(), OtbRoutingInstructionPackageLabelVO.class);
            //     packageLabelList.forEach(item -> item.setOtbRoutingInstructionId(instruction.getId()));
            //     param.setRoutingInstructionPackageLabelList(packageLabelList);
            // }
            // //填充托盘label
            // if (ObjectUtil.isNotEmpty(reqDTO.getRoutingInstructionPalletLabelList())) {
            //     List<OtbRoutingInstructionPalletLabelVO> palletLabelList = BeanUtil.copyNew(reqDTO.getRoutingInstructionPalletLabelList(), OtbRoutingInstructionPalletLabelVO.class);
            //     palletLabelList.forEach(item -> item.setOtbRoutingInstructionId(instruction.getId()));
            //     param.setRoutingInstructionPalletLabelList(palletLabelList);
            // }

            //调用编辑方法
            otbRoutingInstruction = otbRoutingInstructionService.updateByParam(param);
        }
        //返回结果
        RefNumWithRequestRefNumRespDTO respDTO = new RefNumWithRequestRefNumRespDTO();
        respDTO.setRefNum(otbRoutingInstruction.getRefNum());
        //respDTO.setRequestRefNum(otbShipment.getOtbRequestId());
        return Result.ok(respDTO);
    }

    @Override
    @PostMapping(value = OtbRoutingInstructionPath.DETAIL)
    @IgnoreAuth
    public Result<OtbRIRespDTO> detail(@RequestBody RIQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取otb ri id
        RIUtil.fillRIId(List.of(query.getReqDTO()));
        //获取ri详情
        OtbRoutingInstructionVO vo = otbRoutingInstructionService.detailById(query.getRiId());
        //获取requestId
        OtbRequest otbRequest = otbRequestService.getById(vo.getOtbRequestId());
        //构建返回结果
        OtbRIRespDTO otbRIRespDTO = BeanUtil.copyNew(vo, OtbRIRespDTO.class);
        otbRIRespDTO.setOtbRequestRefNum(otbRequest.getRefNum());
        if (ObjectUtil.isNotEmpty(vo.getRoutingInstructionPackageLabelList())) {
            List<OtbRoutingInstructionPackageLabelReqDTO> packageLabelList = BeanUtil.copyNew(vo.getRoutingInstructionPackageLabelList(), OtbRoutingInstructionPackageLabelReqDTO.class);

            otbRIRespDTO.setRoutingInstructionPackageLabelList(packageLabelList);
        }
        //填充托盘label
        if (ObjectUtil.isNotEmpty(vo.getRoutingInstructionPalletLabelList())) {
            List<OtbRoutingInstructionPalletLabelReqDTO> palletLabelList = BeanUtil.copyNew(vo.getRoutingInstructionPalletLabelList(), OtbRoutingInstructionPalletLabelReqDTO.class);
            palletLabelList.forEach(item -> item.setOtbRoutingInstructionId(vo.getId()));
            otbRIRespDTO.setRoutingInstructionPalletLabelList(palletLabelList);
        }
        //构建返回参数
        return Result.ok(otbRIRespDTO);
    }

    @Override
    @PostMapping(value = OtbRoutingInstructionPath.AUDIT)
    public Result<Boolean> audit(@RequestBody RIQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取otb ri id
        RIUtil.fillRIId(List.of(query.getReqDTO()));
        //调用审批接口
        otbRoutingInstructionService.committed(query.getRiId());
        //返回结果
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @PostMapping(value = OtbRoutingInstructionPath.LIST)
    @IgnoreAuth
    public Result<PageData<OtbRoutingInstructionPageRespDTO>> list(@RequestBody PageSearch<OtbRoutingInstructionQueryReqDTO> search) {
        //获取查询参数
        OtbRoutingInstructionQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //构建方法入参
        PageSearch<OtbRoutingInstructionQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, OtbRoutingInstructionQuery.class));
        //调用列表方法
        PageData<OtbRoutingInstructionPageVO> data = otbRoutingInstructionService.pageByQuery(pageSearch);
        //获取返回值
        List<OtbRoutingInstructionPageVO> recordList = data.getRecords();
        if (ObjectUtil.isEmpty(recordList)) {
            return Result.ok(new PageData<>());
        }
        //入库请求单id
        List<Long> otbRequestIdList = recordList.stream()
                .map(OtbRoutingInstructionPageVO::getOtbRequestId)
                .toList();
        List<OtbRequest> otbRequestList = otbRequestService.listByIds(otbRequestIdList);
        //根据id获取入库请求单refNum
        Map<Long, String> map = ObjectUtil.toMap(otbRequestList, OtbRequest::getId, OtbRequest::getRefNum);
        PageData<OtbRoutingInstructionPageRespDTO> pageData = PageUtil.convert(data, item -> {
            OtbRoutingInstructionPageRespDTO pageRespDTO = BeanUtil.copyNew(item, OtbRoutingInstructionPageRespDTO.class);
            pageRespDTO.setOtbRequestRefNum(map.get(item.getOtbRequestId()));
            return pageRespDTO;
        });
        //返回结果
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = OtbRoutingInstructionPath.CREATE_WITH_AUDIT)
    public Result<Boolean> createWithAudit(@RequestBody OtbRoutingInstructionCreateOrUpdateReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //获取otbShipmentId
        OtbShipment otbShipment = otbShipmentService.getByRefNum(reqDTO.getOtbShipmentRefNum());
        if (ObjectUtil.isEmpty(otbShipment)) {
            throw new BusinessException(StringUtil.format("OtbShipment not found otbShipmentRefNum:{}", reqDTO.getOtbShipmentRefNum()));
        }

        // 构建创建参数
        OtbRoutingInstructionCreateParam createParam = BeanUtil.copyNew(reqDTO, OtbRoutingInstructionCreateParam.class);
        createParam.setOtbShipmentId(otbShipment.getId());
        createParam.setOtbRequestId(otbShipment.getOtbRequestId());

        // 调用Service层事务方法
        otbRoutingInstructionService.createWithAudit(createParam);

        // 返回结果
        return Result.ok(Boolean.TRUE);
    }

}
