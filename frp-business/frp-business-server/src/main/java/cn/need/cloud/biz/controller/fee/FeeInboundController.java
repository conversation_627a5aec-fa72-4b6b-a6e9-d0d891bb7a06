package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeInboundConverter;
import cn.need.cloud.biz.model.entity.fee.FeeInbound;
import cn.need.cloud.biz.model.param.fee.create.FeeInboundBuildParam;
import cn.need.cloud.biz.model.query.fee.FeeInboundQuery;
import cn.need.cloud.biz.model.vo.fee.FeeInboundVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeInboundPageVO;
import cn.need.cloud.biz.service.fee.FeeInboundBuildService;
import cn.need.cloud.biz.service.fee.FeeInboundService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 费用inbound 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-inbound")
@Tag(name = "费用inbound")
public class FeeInboundController extends AbstractRestController<FeeInboundService, FeeInbound, FeeInboundConverter, FeeInboundVO> {

    @Resource
    private FeeInboundBuildService feeInboundBuildService;

    @Operation(summary = "根据id获取费用inbound详情", description = "根据数据主键id，从数据库中获取其对应的费用inbound详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeInboundVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用inbound详情", description = "根据数据RefNum，从数据库中获取其对应的费用inbound详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeInboundVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用inbound分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用inbound列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeInboundPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeInboundQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "build", description = "build")
    @PostMapping(value = "/build")
    public Result<Void> build(@Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeInboundBuildParam buildParam) {

        feeInboundBuildService.build(buildParam);

        return Result.ok();
    }
}
