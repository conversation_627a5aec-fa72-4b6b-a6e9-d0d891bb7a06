package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlipDetail;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb拣货单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPickingSlipDetailConverter extends AbstractModelConverter<OtbPickingSlipDetail, OtbPickingSlipDetailVO, OtbPickingSlipDetailDTO> {

}
