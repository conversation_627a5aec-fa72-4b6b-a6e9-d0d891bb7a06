package cn.need.cloud.biz.controller.profile;

import cn.need.cloud.biz.converter.profile.ProfileSystemConverter;
import cn.need.cloud.biz.model.entity.setting.ProfileSystem;
import cn.need.cloud.biz.model.param.profile.create.ProfileSystemCreateParam;
import cn.need.cloud.biz.model.param.profile.update.ProfileSystemUpdateParam;
import cn.need.cloud.biz.model.query.profile.ProfileSystemQuery;
import cn.need.cloud.biz.model.vo.profile.ProfileSystemPageVO;
import cn.need.cloud.biz.model.vo.profile.ProfileSystemVO;
import cn.need.cloud.biz.service.profile.ProfileSystemService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@RestController
@RequestMapping("/api/biz/profile-system")
@Tag(name = "系统档案")
public class ProfileSystemController extends AbstractRestController<ProfileSystemService, ProfileSystem, ProfileSystemConverter, ProfileSystemVO> {

    @Operation(summary = "新增", description = "接收的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfileSystemCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改", description = "接收的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfileSystemUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取详情", description = "根据数据主键id，从数据库中获取其对应的详情")
    @GetMapping(value = "/detail/{id}")
    public Result<ProfileSystemVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取详情
        ProfileSystemVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProfileSystemPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProfileSystemQuery> search) {

        // 获取分页
        PageData<ProfileSystemPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
