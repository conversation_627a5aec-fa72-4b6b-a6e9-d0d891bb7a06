package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcShipStationConfigConverter;
import cn.need.cloud.biz.model.entity.otc.OtcShipStationConfig;
import cn.need.cloud.biz.model.param.otc.create.OtcShipStationConfigCreateParam;
import cn.need.cloud.biz.model.param.otc.update.OtcShipStationConfigUpdateParam;
import cn.need.cloud.biz.model.query.otc.ship.OtcShipStationConfigQuery;
import cn.need.cloud.biz.model.vo.otc.OtcShipStationConfigVO;
import cn.need.cloud.biz.model.vo.page.OtcShipStationConfigPageVO;
import cn.need.cloud.biz.service.otc.ship.OtcShipStationConfigService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 快递公司配置 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/ship-station-config")
@Tag(name = "快递公司配置")
public class OtcShipStationConfigController extends AbstractRestController<OtcShipStationConfigService, OtcShipStationConfig, OtcShipStationConfigConverter, OtcShipStationConfigVO> {

    @Operation(summary = "新增快递公司配置", description = "接收快递公司配置的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcShipStationConfigCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改快递公司配置", description = "接收快递公司配置的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcShipStationConfigUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除快递公司配置", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取快递公司配置详情", description = "根据数据主键id，从数据库中获取其对应的快递公司配置详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcShipStationConfigVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取快递公司配置详情
        OtcShipStationConfigVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取快递公司配置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的快递公司配置列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcShipStationConfigPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcShipStationConfigQuery> search) {

        // 获取快递公司配置分页
        PageData<OtcShipStationConfigPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
