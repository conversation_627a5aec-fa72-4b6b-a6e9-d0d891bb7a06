package cn.need.cloud.biz.service.feeoriginalData;

import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 费用原始数据构建工厂类
 */
@Component
@AllArgsConstructor
public class FeeOriginalDataBuildFactory {

    private final Map<String, FeeOriginalDataBuildService<?>> serviceMap;

    /**
     * 获取构建器
     *
     * @param type 费用模型类型
     * @param <T>  返回的业务对象类型
     * @return 费用原始数据构建服务
     */
    @SuppressWarnings("unchecked")
    public <T> FeeOriginalDataBuildService<T> getBuilder(FeeModelTypeEnum type) {
        String serviceKey = StringUtil.format("fodBuild{}", type.getCode());
        FeeOriginalDataBuildService<?> service = serviceMap.get(serviceKey);
        if (service == null) {
            throw new BusinessException("Unknown fee original data build type: " + type);
        }
        return (FeeOriginalDataBuildService<T>) service;
    }
}