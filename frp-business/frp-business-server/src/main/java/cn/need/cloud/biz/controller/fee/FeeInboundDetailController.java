package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeInboundDetailConverter;
import cn.need.cloud.biz.model.entity.fee.FeeInboundDetail;
import cn.need.cloud.biz.model.query.fee.FeeInboundDetailQuery;
import cn.need.cloud.biz.model.vo.fee.FeeInboundDetailVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeInboundDetailPageVO;
import cn.need.cloud.biz.service.fee.FeeInboundDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 费用详情inbound 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-inbound-detail")
@Tag(name = "费用详情inbound")
public class FeeInboundDetailController extends AbstractRestController<FeeInboundDetailService, FeeInboundDetail, FeeInboundDetailConverter, FeeInboundDetailVO> {


    @Operation(summary = "根据id获取费用详情inbound详情", description = "根据数据主键id，从数据库中获取其对应的费用详情inbound详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeInboundDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用详情inbound详情", description = "根据数据RefNum，从数据库中获取其对应的费用详情inbound详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeInboundDetailVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取费用详情inbound分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用详情inbound列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeInboundDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeInboundDetailQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}
