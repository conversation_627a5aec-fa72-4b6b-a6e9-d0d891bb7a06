package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * 库位详情导出 service 接口
 * </p>
 *
 * <AUTHOR> AI
 * @since 2025-03-07
 */
public interface BinLocationExportService {

    /**
     * 导出库位详情数据(仅导出库存>0的数据)
     *
     * @param query    查询条件
     * @param response 响应
     * @return /
     */
    Boolean export(BinLocationQuery query, HttpServletResponse response);
} 