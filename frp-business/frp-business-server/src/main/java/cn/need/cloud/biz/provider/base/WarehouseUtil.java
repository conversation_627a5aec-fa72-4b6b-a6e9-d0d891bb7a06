package cn.need.cloud.biz.provider.base;

import cn.need.cloud.biz.client.dto.req.warehouse.WarehouseReqDTO;
import cn.need.cloud.biz.model.query.warehouse.WarehouseQuery;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.List;
import java.util.Map;

/**
 * 仓库填充 util
 *
 * <AUTHOR>
 */
public class WarehouseUtil {
    private WarehouseUtil() {
    }

    /**
     * 填充仓库id
     *
     * @param warehouseReqList 仓库信息容器
     */
    public static void fillWarehouseId(List<WarehouseReqDTO> warehouseReqList) {

        if (warehouseReqList.isEmpty()) {
            throw new BusinessException("warehouseReqDTO can not empty");
        }

        warehouseReqList.forEach(item -> {
            if (ObjectUtil.isEmpty(item)) {
                throw new BusinessException("warehouseReqDTO can not empty");
            }
        });

        WarehouseService warehouseService = SpringUtil.getBean(WarehouseService.class);
        //获取仓库refNum
        List<String> refNumList = warehouseReqList.stream().map(WarehouseReqDTO::getRefNum).filter(ObjectUtil::isNotEmpty).toList();
        //获取仓库code
        List<String> codeList = warehouseReqList.stream().map(WarehouseReqDTO::getCode).filter(ObjectUtil::isNotEmpty).toList();
        //填充查询条件
        WarehouseQuery warehouseQuery = new WarehouseQuery();
        warehouseQuery.setRefNumList(refNumList);
        warehouseQuery.setCodeList(codeList);
        //获取仓库信息
        if (warehouseService == null) {
            throw new BusinessException("WarehouseService bean is null, please check service implementation");
        }
        List<WarehousePageVO> list = warehouseService.listByQuery(warehouseQuery);

        if (list.isEmpty()) {
            throw new BusinessException("Warehouse not found");
        }

        //根据refNum映射仓库信息
        Map<String, WarehousePageVO> refNumMap = ObjectUtil.toMap(list, WarehousePageVO::getRefNum);
        //根据code映射仓库信息
        Map<String, WarehousePageVO> codeMap = ObjectUtil.toMap(list, WarehousePageVO::getCode);
        //遍历仓库容器 填充仓库id
        warehouseReqList.forEach(item -> {
            WarehousePageVO warehousePageVO = refNumMap.get(item.getRefNum());
            if (ObjectUtil.isNotEmpty(warehousePageVO)) {
                item.setWarehouseId(warehousePageVO.getId());
                return;
            }
            WarehousePageVO pageVO = codeMap.get(item.getCode());
            if (ObjectUtil.isNotEmpty(pageVO)) {
                item.setWarehouseId(pageVO.getId());
            }
        });
    }
}
