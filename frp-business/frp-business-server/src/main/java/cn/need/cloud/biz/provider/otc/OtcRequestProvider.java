package cn.need.cloud.biz.provider.otc;

import cn.need.cloud.biz.client.api.otc.OtcRequestClient;
import cn.need.cloud.biz.client.api.path.BinLocationPath;
import cn.need.cloud.biz.client.api.path.InboundRequestPath;
import cn.need.cloud.biz.client.api.path.OtcRequestPath;
import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestBatchAuditReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otc.*;
import cn.need.cloud.biz.client.dto.req.warehouse.WarehouseReqDTO;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.BaseDetailFullRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.OtcRequestPackageFullRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.OtcRequestPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.OtcRequestRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.request.*;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.request.OtcRequestSpecialService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(OtcRequestPath.PREFIX)
public class OtcRequestProvider implements OtcRequestClient {
    @Resource
    private OtcRequestService otcRequestService;
    @Resource
    private OtcRequestSpecialService otcRequestSpecialService;

    @Override
    @PostMapping(value = BinLocationPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(@RequestBody OtcRequestCreateOrUpdateReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());
        //填充产品信息
        List<ProductReqDTO> list = reqDTO.getDetailList().stream().map(OtcRequestProductReqDTO::getProduct).toList();
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), list);
        //判断是新增还是编辑
        OtcRequest otcRequest;
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            //构建新增参数
            OtcRequestCreateParam createParam = BeanUtil.copyNew(reqDTO, OtcRequestCreateParam.class);
            //构建参数
            buildParam(reqDTO, createParam);
            //调用新增方法
            otcRequest = otcRequestService.insertByParam(createParam);
        } else {
            //获取请求单id
            otcRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getReqDTO()));
            //构建编辑参数
            OtcRequestUpdateParam updateParam = BeanUtil.copyNew(reqDTO, OtcRequestUpdateParam.class);
            updateParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
            updateParam.setId(reqDTO.getRequestId());
            //构建更新参数
            buildParam(reqDTO, updateParam);
            //调用更新方法
            otcRequest = otcRequestService.updateByParam(updateParam);
        }
        //返回结果
        RefNumWithRequestRefNumRespDTO respDTO = new RefNumWithRequestRefNumRespDTO();
        respDTO.setRefNum(otcRequest.getRefNum());
        respDTO.setRequestRefNum(otcRequest.getRequestRefNum());
        return Result.ok(respDTO);
    }

    /**
     * 构建参数
     *
     * @param reqDTO 请求参数
     * @param param  参数
     */
    private void buildParam(OtcRequestCreateOrUpdateReqDTO reqDTO, OtcRequestCreateParam param) {
        param.setRequestRefNum(reqDTO.getRequestOfRequestRefNum());
        param.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
        //构建详情
        List<OtcRequestProductVO> detailList = reqDTO.getDetailList().stream()
                .map(item -> {
                    OtcRequestProductVO otcRequestProductVO = BeanUtil.copyNew(item, OtcRequestProductVO.class);
                    otcRequestProductVO.setProductId(item.getProductId());
                    return otcRequestProductVO;
                }).toList();
        //填充详情
        param.setDetailList(detailList);
        //构建包裹信息
        if (reqDTO.getProvideShippingLabelFlag()) {
            List<OtcRequestPackageFullDTO> packageList = reqDTO.getPackageList();
            Validate.notEmpty(packageList, "packageList cannot empty");
            //获取产品信息
            Stream<ProductReqDTO> dtoStream = packageList.stream()
                    .map(OtcRequestPackageFullDTO::getDetailList)
                    .flatMap(List::stream)
                    .map(BaseDetailFullDTO::getProduct);

            Stream<ProductReqDTO> stream = packageList.stream()
                    .map(OtcRequestPackageFullDTO::getPackageMultiboxProduct);
            //合并流
            List<ProductReqDTO> productList = Stream.concat(dtoStream, stream).filter(Objects::nonNull).toList();            //填充产品id
            ProductUtil.fillProduct(reqDTO.getTransactionPartner(), productList);
            //构建包裹
            List<OtcRequestPackageFullVO> packageFullList = packageList.stream()
                    .map(item -> {
                        item.setPackageMultiboxProductId(item.getProductId());
                        OtcRequestPackageFullVO otcRequestPackageFullVO = BeanUtil.copyNew(item, OtcRequestPackageFullVO.class);
                        //构建label详情
                        List<OtcRequestPackageLabelFullReqDTO> labelList = item.getLabelList();
                        Validate.notEmpty(labelList, "labelList cannot empty");
                        List<OtcRequestPackageLabelFullVO> labelFullList = BeanUtil.copyNew(labelList, OtcRequestPackageLabelFullVO.class);
                        otcRequestPackageFullVO.setLabelList(labelFullList);
                        //构建包裹详情
                        List<BaseDetailFullDTO> detailFullList = item.getDetailList();
                        Validate.notEmpty(detailFullList, "detailFullList cannot empty");
                        List<OtcRequestPackageDetailFullVO> detailVoList = detailFullList.stream()
                                .map(detailItem -> {
                                    OtcRequestPackageDetailFullVO otcRequestPackageDetailFullVO = BeanUtil.copyNew(detailItem, OtcRequestPackageDetailFullVO.class);
                                    otcRequestPackageDetailFullVO.setProductId(detailItem.getProductId());
                                    return otcRequestPackageDetailFullVO;
                                }).toList();
                        otcRequestPackageFullVO.setDetailList(detailVoList);
                        return otcRequestPackageFullVO;
                    }).toList();
            param.setPackageList(packageFullList);
        }
    }

    @Override
    @PostMapping(value = OtcRequestPath.DETAIL)
    @IgnoreAuth
    public Result<OtcRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());


        //获取请求id
        otcRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));
        //获取详情
        OtcRequestVO otcRequestVO = otcRequestService.detailById(query.getRequestId());
        OtcRequestRespDTO otcRequestRespDTO = BeanUtil.copyNew(otcRequestVO, OtcRequestRespDTO.class);
        otcRequestRespDTO.setLogisticPartner(query.getLogisticPartner());
        otcRequestRespDTO.setTransactionPartner(BeanUtil.copyNew(otcRequestVO.getTransactionPartnerVO(), TenantReqDTO.class));
        otcRequestRespDTO.setWarehouseReqDTO(BeanUtil.copyNew(otcRequestVO.getBaseWarehouseVO(), WarehouseReqDTO.class));
        //构建详情
        List<OtcRequestProductRespDTO> detailList = otcRequestVO.getDetailList().stream()
                .map(item -> {
                    OtcRequestProductRespDTO otcRequestProductRespDTO = BeanUtil.copyNew(item, OtcRequestProductRespDTO.class);
                    otcRequestProductRespDTO.setBaseProductDTO(ProductUtil.convert(item.getBaseProductVO()));
                    return otcRequestProductRespDTO;
                }).toList();
        //填充详情
        otcRequestRespDTO.setDetailList(detailList);
        //构建包裹信息
        if (otcRequestVO.getProvideShippingLabelFlag()) {
            List<OtcRequestPackageFullVO> packageList = otcRequestVO.getPackageList();
            //构建包裹
            List<OtcRequestPackageFullRespDTO> packageFullList = packageList.stream()
                    .map(item -> {
                        OtcRequestPackageFullRespDTO otcRequestPackageFullDTO = BeanUtil.copyNew(item, OtcRequestPackageFullRespDTO.class);
                        otcRequestPackageFullDTO.setPackageMultiboxProductDTO(ProductUtil.convert(item.getPackageMultiboxProductVo()));
                        //构建label详情
                        List<OtcRequestPackageLabelFullVO> labelList = item.getLabelList();
                        List<OtcRequestPackageLabelFullReqDTO> labelFullList = BeanUtil.copyNew(labelList, OtcRequestPackageLabelFullReqDTO.class);
                        otcRequestPackageFullDTO.setLabelList(labelFullList);
                        //构建包裹详情
                        List<OtcRequestPackageDetailFullVO> detailFullList = item.getDetailList();
                        List<BaseDetailFullRespDTO> detailVoList = detailFullList.stream()
                                .map(detailItem -> {
                                    BaseDetailFullRespDTO baseDetailFullDTO = BeanUtil.copyNew(detailItem, BaseDetailFullRespDTO.class);
                                    baseDetailFullDTO.setBaseProductDTO(ProductUtil.convert(detailItem.getBaseProductVO()));
                                    return baseDetailFullDTO;
                                }).toList();
                        otcRequestPackageFullDTO.setDetailList(detailVoList);
                        return otcRequestPackageFullDTO;
                    }).toList();
            otcRequestRespDTO.setPackageList(packageFullList);
        }
        //构建返回参数
        return Result.ok(otcRequestRespDTO);
    }

    @Override
    @PostMapping(value = OtcRequestPath.LIST)
    @IgnoreAuth
    public Result<PageData<OtcRequestPageRespDTO>> list(@RequestBody PageSearch<OtcRequestQueryReqDTO> search) {
        OtcRequestQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());
        //构建方法入参
        PageSearch<OtcRequestQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, OtcRequestQuery.class));
        //调用列表方法
        PageData<OtcRequestPageVO> data = otcRequestService.pageByQuery(pageSearch);
        //对象转换
        PageData<OtcRequestPageRespDTO> pageData = PageUtil.convert(data, item -> {
            OtcRequestPageRespDTO pageRespDTO = BeanUtil.copyNew(item, OtcRequestPageRespDTO.class);
            pageRespDTO.setBaseWarehouseDTO(BeanUtil.copyNew(item.getBaseWarehouseVO(), BaseWarehouseDTO.class));
            pageRespDTO.setTransactionPartnerDTO(BeanUtil.copyNew(item.getTransactionPartnerVO(), BasePartnerDTO.class));
            return pageRespDTO;
        });
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = InboundRequestPath.CANCEL)
    @IgnoreAuth
    public Result<Integer> cancel(@RequestBody BaseRequestDeleteAndCancelReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otcRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        OtcRequestCancelParam noteParam = BeanUtil.copyNew(reqDTO, OtcRequestCancelParam.class);
        noteParam.setId(reqDTO.getRequestId());
        noteParam.setNote("");
        //noteParam.setPartCancel(false);
        //返回结果
        return Result.ok(otcRequestSpecialService.cancel(noteParam));
    }

    @Override
    @PostMapping(value = OtcRequestPath.AUDIT)
    @IgnoreAuth
    public Result<Boolean> audit(@RequestBody BaseRequestAuditReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otcRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        //调用审批方法
        otcRequestService.batchAudit(
                reqDTO.getType(),
                reqDTO.getNote(),
                List.of(reqDTO.getRequestId())
        );
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @PostMapping(value = OtcRequestPath.BATCH_AUDIT)
    @IgnoreAuth
    public Result<Boolean> batchAudit(@RequestBody BaseRequestBatchAuditReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充请求单id
        otcRequestService.fillRequestId(reqDTO.getTransactionPartner(), reqDTO.getRequestReqList());
        //调用审批方法
        otcRequestService.batchAudit(
                reqDTO.getType(),
                reqDTO.getNote(),
                reqDTO.getRequestIdList()
        );
        return Result.ok(Boolean.TRUE);
    }
}

