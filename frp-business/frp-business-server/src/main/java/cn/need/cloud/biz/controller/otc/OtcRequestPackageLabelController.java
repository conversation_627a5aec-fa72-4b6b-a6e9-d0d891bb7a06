package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcRequestPackageLabelConverter;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageLabel;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestPackageLabelCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestPackageLabelUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageLabelQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackageLabelPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageLabelVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestPackageLabelService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC请求包裹标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-request-package-label")
@Tag(name = "OTC请求包裹标签")
public class OtcRequestPackageLabelController extends AbstractRestController<OtcRequestPackageLabelService, OtcRequestPackageLabel, OtcRequestPackageLabelConverter, OtcRequestPackageLabelVO> {

    @Operation(summary = "新增OTC请求包裹标签", description = "接收OTC请求包裹标签的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageLabelCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OTC请求包裹标签", description = "接收OTC请求包裹标签的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageLabelUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OTC请求包裹标签", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OTC请求包裹标签详情", description = "根据数据主键id，从数据库中获取其对应的OTC请求包裹标签详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcRequestPackageLabelVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC请求包裹标签详情
        OtcRequestPackageLabelVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC请求包裹标签分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC请求包裹标签列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcRequestPackageLabelPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcRequestPackageLabelQuery> search) {

        // 获取OTC请求包裹标签分页
        PageData<OtcRequestPackageLabelPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
