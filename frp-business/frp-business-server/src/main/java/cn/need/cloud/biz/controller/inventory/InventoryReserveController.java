package cn.need.cloud.biz.controller.inventory;

import cn.need.cloud.biz.converter.inventory.InventoryReserveConverter;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.param.inventory.create.InventoryReserveCreateParam;
import cn.need.cloud.biz.model.param.inventory.update.InventoryReserveUpdateParam;
import cn.need.cloud.biz.model.query.inventory.InventoryReserveQuery;
import cn.need.cloud.biz.model.vo.inventory.InventoryReserveVO;
import cn.need.cloud.biz.model.vo.page.InventoryReservePageVO;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 预留库存 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inventory-reserve")
@Tag(name = "预留库存")
public class InventoryReserveController extends AbstractRestController<InventoryReserveService, InventoryReserve, InventoryReserveConverter, InventoryReserveVO> {

    @Operation(summary = "新增预留库存", description = "接收预留库存的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InventoryReserveCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改预留库存", description = "接收预留库存的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InventoryReserveUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除预留库存", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取预留库存详情", description = "根据数据主键id，从数据库中获取其对应的预留库存详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InventoryReserveVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取预留库存详情
        InventoryReserveVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取预留库存详情", description = "根据数据RefNum，从数据库中获取其对应的预留库存详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InventoryReserveVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取预留库存详情
        InventoryReserveVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取预留库存分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的预留库存列表")
    @PostMapping(value = "/list")
    public Result<PageData<InventoryReservePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InventoryReserveQuery> search) {

        // 获取预留库存分页
        PageData<InventoryReservePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
