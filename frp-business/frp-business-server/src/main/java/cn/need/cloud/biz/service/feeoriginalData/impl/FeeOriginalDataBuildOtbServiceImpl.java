package cn.need.cloud.biz.service.feeoriginalData.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.model.bo.fee.otb.*;
import cn.need.cloud.biz.model.entity.otb.*;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestDetailVO;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataBuildService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageService;
import cn.need.cloud.biz.service.otb.request.OtbRequestDetailService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;


/**
 * 出库费用原始数据构建服务实现
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Service("fodBuildOtb")
@AllArgsConstructor
public class FeeOriginalDataBuildOtbServiceImpl implements FeeOriginalDataBuildService<FodExtraDataOtbBO> {

    private final OtbRequestService otbRequestService;
    private final OtbRequestDetailService otbRequestDetailService;
    private final OtbWorkorderService otbWorkorderService;
    private final OtbWorkorderDetailService otbWorkorderDetailService;
    private final OtbPrepWorkorderService otbPrepWorkorderService;
    private final OtbPrepWorkorderDetailService otbPrepWorkorderDetailService;
    private final OtbPackageService otbPackageService;

    @Override
    public FodExtraDataOtbBO buildFeeOriginalData() {

        // 根据 FeeStatus & RequestStatus 来获取需要构建费用的OTB请求

        PageSearch<OtbRequestQuery> otbRequestQueryPageSearch = new PageSearch<>();
        otbRequestQueryPageSearch.setCurrent(1);
        otbRequestQueryPageSearch.setSize(1);
        OtbRequestQuery otbRequestQuery = new OtbRequestQuery();
        otbRequestQuery.setOtbRequestStatus(RequestStatusEnum.PROCESSED.getStatus());
        otbRequestQuery.setFeeStatus(FeeStatusEnum.NEW.getStatus());
        otbRequestQueryPageSearch.setCondition(otbRequestQuery);

        final PageData<OtbRequestPageVO> otbRequestPageVOPageData =
                otbRequestService.pageByQuery(otbRequestQueryPageSearch);

        if (ObjectUtil.isEmpty(otbRequestPageVOPageData.getRecords())) {
            return null;
        }
        // TODO: 这里使用Redis锁
        return buildFeeOriginalData(otbRequestPageVOPageData.getRecords().get(0).getId());
    }


    @Override
    public FodExtraDataOtbBO buildFeeOriginalData(Long requestId) {

        if (ObjectUtil.isEmpty(requestId)) {
            return null;
        }

        OtbRequestVO otbRequestVO = otbRequestService.detailById(requestId);

        return buildFeeOriginalData(otbRequestVO);
    }

    @NotNull
    private FodExtraDataOtbBO buildFeeOriginalData(OtbRequestVO otbRequestVO) {
        if (!otbRequestVO.getOtbRequestStatus().equals(RequestStatusEnum.PROCESSED.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "Processed",
                    otbRequestVO.getOtbRequestStatus()));
        }

        if (!FeeStatusEnum.getCanBuildStatuses().contains(otbRequestVO.getFeeStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "New",
                    otbRequestVO.getFeeStatus()));
        }

        // 查询相关数据
        List<OtbWorkorder> otbWorkorders = otbWorkorderService.listByRequestIds(List.of(otbRequestVO.getId()));
        List<OtbRequestDetailVO> otbRequestDetails = otbRequestDetailService.listByOtbRequestId(otbRequestVO.getId());

        // 查询工单详情
        List<OtbWorkorderDetail> otbWorkorderDetails = List.of();
        if (!ObjectUtil.isEmpty(otbWorkorders)) {
            List<Long> workorderIds = otbWorkorders.stream().map(OtbWorkorder::getId).toList();
            var workorderDetailMap = otbWorkorderDetailService.groupByOtbWorkOrderIdList(workorderIds);
            otbWorkorderDetails = workorderDetailMap.values().stream()
                    .flatMap(List::stream)
                    .toList();
        }

        // 查询预处理工单和详情
        List<OtbPrepWorkorder> otbPrepWorkorders = List.of();
        List<OtbPrepWorkorderDetail> otbPrepWorkorderDetails = List.of();
        if (!ObjectUtil.isEmpty(otbWorkorders)) {
            List<Long> workorderIds = otbWorkorders.stream().map(OtbWorkorder::getId).toList();
            // 通过工单ID查询预处理工单
            otbPrepWorkorders = otbPrepWorkorderService.lambdaQuery()
                    .in(OtbPrepWorkorder::getOtbWorkorderId, workorderIds)
                    .list();
            if (!ObjectUtil.isEmpty(otbPrepWorkorders)) {
                List<Long> prepWorkorderIds = otbPrepWorkorders.stream().map(OtbPrepWorkorder::getId).toList();
                // 通过预处理工单ID查询详情
                otbPrepWorkorderDetails = otbPrepWorkorderDetailService.lambdaQuery()
                        .in(OtbPrepWorkorderDetail::getOtbPrepWorkorderId, prepWorkorderIds)
                        .list();
            }
        }

        // 查询包裹相关数据
        List<OtbPackage> otbPackages = List.of();
        if (!ObjectUtil.isEmpty(otbWorkorders)) {
            List<Long> workorderIds = otbWorkorders.stream().map(OtbWorkorder::getId).toList();
            otbPackages = otbPackageService.listByWorkOrderIdList(workorderIds);
        }

        // 创建费用原始数据对象
        FodExtraDataOtbBO extraData = new FodExtraDataOtbBO();

        // 填充基本信息
        extraData.setSnapshotRequestId(otbRequestVO.getId());
        extraData.setSnapshotRequestRefNum(otbRequestVO.getRequestRefNum());
        extraData.setSnapshotRefNum(otbRequestVO.getRequestRefNum());
        extraData.setTransactionPartnerId(otbRequestVO.getTransactionPartnerId());
        extraData.setWarehouseId(otbRequestVO.getWarehouseId());
        extraData.setRefNum(otbRequestVO.getRequestRefNum());
        extraData.setProcessStartTime(otbRequestVO.getProcessStartTime());
        extraData.setProcessEndTime(otbRequestVO.getProcessEndTime());
        extraData.setFeeCalculationTime(otbRequestVO.getProcessEndTime());
        extraData.setNote(otbRequestVO.getNote());

        // 填充业务数据
        extraData.setRequest(BeanUtil.copyNew(otbRequestVO, FodOtbRequestBO.class));
        extraData.setRequestDetailList(BeanUtil.copyNew(otbRequestDetails, FodOtbRequestDetailBO.class));
        extraData.setWorkorderList(BeanUtil.copyNew(otbWorkorders, FodOtbWorkorderBO.class));
        extraData.setWorkorderDetailList(BeanUtil.copyNew(otbWorkorderDetails, FodOtbWorkorderDetailBO.class));
        extraData.setPrepWorkorderList(BeanUtil.copyNew(otbPrepWorkorders, FodOtbPrepWorkorderBO.class));
        extraData.setPrepWorkorderDetailList(BeanUtil.copyNew(otbPrepWorkorderDetails, FodOtbPrepWorkorderDetailBO.class));
        extraData.setPackageList(BeanUtil.copyNew(otbPackages, FodOtbPackageBO.class));

        // TODO: 包裹标签数据需要根据包裹ID查询
        // extraData.setPackageLabelList(BeanUtil.copyNew(otbPackageLabels, FodOtbPackageLabelBO.class));

        return extraData;
    }
}
