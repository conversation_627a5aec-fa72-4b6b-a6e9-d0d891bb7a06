package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcRequestPackageDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackageDetail;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC请求包裹详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcRequestPackageDetailConverter extends AbstractModelConverter<OtcRequestPackageDetail, OtcRequestPackageDetailVO, OtcRequestPackageDetailDTO> {

}
