package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcWorkorderDTO;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC工单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcWorkorderConverter extends AbstractModelConverter<OtcWorkorder, OtcWorkorderVO, OtcWorkorderDTO> {

}
