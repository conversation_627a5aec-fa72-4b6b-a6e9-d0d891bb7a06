package cn.need.cloud.biz.provider.base;

import cn.need.cloud.biz.client.dto.req.binlocation.BinLocationInfoDTO;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 库位填充 util
 *
 * <AUTHOR>
 */
public class BinLocationUtil {
    private BinLocationUtil() {
    }

    public static void fillBinLocationId(List<BinLocationInfoDTO> binLocationReqList) {
        //获取库位service bean
        BinLocationService binLocationService = SpringUtil.getBean(BinLocationService.class);
        //获取库位refNum
        Set<String> refNumList = binLocationReqList.stream().map(BinLocationInfoDTO::getRefNum).collect(Collectors.toSet());
        //获取库位locationName
        Set<String> locationNameList = binLocationReqList.stream().map(BinLocationInfoDTO::getLocationName).collect(Collectors.toSet());
        //填充查询条件
        BinLocationQuery binLocationQuery = new BinLocationQuery();
        binLocationQuery.setRefNumList(refNumList);
        binLocationQuery.setLocationNameList(locationNameList);
        //获取库位信息
        List<BinLocationPageVO> list = Objects.requireNonNull(binLocationService).listByQuery(binLocationQuery);
        //根据refNum映射库位信息
        Map<String, BinLocationPageVO> refNumMap = ObjectUtil.toMap(list, BinLocationPageVO::getRefNum);
        //根据locationName映射库位信息
        Map<String, BinLocationPageVO> locationNameMap = ObjectUtil.toMap(list, BinLocationPageVO::getLocationName);
        //遍历库位容器 填充库位id
        binLocationReqList.forEach(item -> {
            BinLocationPageVO binLocationPageVO = refNumMap.get(item.getRefNum());
            if (ObjectUtil.isNotEmpty(binLocationPageVO)) {
                item.setBinLocationId(binLocationPageVO.getId());
                return;
            }
            BinLocationPageVO pageVO = locationNameMap.get(item.getLocationName());
            if (ObjectUtil.isNotEmpty(pageVO)) {
                item.setBinLocationId(pageVO.getId());
            }
        });
    }
}
