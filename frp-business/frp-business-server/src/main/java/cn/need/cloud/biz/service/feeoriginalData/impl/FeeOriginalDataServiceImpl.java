package cn.need.cloud.biz.service.feeoriginalData.impl;


import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.fee.FeeOriginalStatusEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeModelTypeEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inbound.InboundRequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.storage.StorageSnapshotRequestStatusEnum;
import cn.need.cloud.biz.converter.fee.FeeOriginalDataConverter;
import cn.need.cloud.biz.mapper.fee.FeeOriginalDataMapper;
import cn.need.cloud.biz.model.bo.fee.inbound.FodExtraDataInBoundBO;
import cn.need.cloud.biz.model.bo.fee.otb.FodExtraDataOtbBO;
import cn.need.cloud.biz.model.bo.fee.otc.FodExtraDataOtcBO;
import cn.need.cloud.biz.model.bo.fee.storage.FodExtraDataStorageBO;
import cn.need.cloud.biz.model.entity.fee.FeeOriginalData;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.entity.otb.OtbRequest;
import cn.need.cloud.biz.model.entity.otc.OtcRequest;
import cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequest;
import cn.need.cloud.biz.model.param.fee.create.FeeOriginalDataCreateParam;
import cn.need.cloud.biz.model.param.fee.update.FeeOriginalDataUpdateParam;
import cn.need.cloud.biz.model.query.fee.FeeOriginalDataQuery;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.query.otb.request.OtbRequestQuery;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.query.storage.StorageSnapshotRequestQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOriginalDataVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOriginalDataPageVO;
import cn.need.cloud.biz.model.vo.otb.page.OtbRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestPageVO;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataBuildFactory;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataBuildService;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataService;
import cn.need.cloud.biz.service.helper.auditshowlog.fee.StorageSnapshotRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.inbound.InboundRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otb.OtbRequestAuditLogHelper;
import cn.need.cloud.biz.service.helper.auditshowlog.otc.OtcRequestAuditLogHelper;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.storage.StorageSnapshotRequestService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RSet;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 费用原始数据表 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@Slf4j
@Service
public class FeeOriginalDataServiceImpl extends SuperServiceImpl<FeeOriginalDataMapper, FeeOriginalData> implements FeeOriginalDataService {

    /**
     * Redis中已查询RequestIds的过期时间（秒）
     */
    private static final int QUERIED_REQUEST_IDS_EXPIRE_SECONDS = 300; // 5分钟
    @Resource
    private FeeOriginalDataBuildFactory feeOriginalDataBuildFactory;
    @Resource
    private InboundRequestService inboundRequestService;
    @Resource
    private OtcRequestService otcRequestService;
    @Resource
    private OtbRequestService otbRequestService;
    @Resource
    private StorageSnapshotRequestService storageSnapshotRequestService;

    @Autowired
    private ApplicationContext applicationContext;


    /**
     * 获取当前类的代理对象，用于调用带有事务注解的方法
     */
    private FeeOriginalDataServiceImpl getSelf() {
        return applicationContext.getBean(FeeOriginalDataServiceImpl.class);
    }

    /**
     * 处理构建失败的情况，更新状态并记录日志（使用新事务）
     *
     * @param requestId   请求ID
     * @param exception   异常信息
     * @param requestType 请求类型（INBOUND, OTC, OTB, STORAGE）
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void handleBuildFailure(Long requestId, Exception exception, FeeModelTypeEnum requestType) {
        try {
            String errorDescription = String.format("Build fee original data failed for %s ID: %d. Exception: %s - %s",
                    requestType.name(), requestId, exception.getClass().getSimpleName(), exception.getMessage());

            switch (requestType) {
                case INBOUND -> {
                    InboundRequest request = inboundRequestService.getById(requestId);
                    if (ObjectUtil.isNotEmpty(request)) {
                        request.setFeeStatus(FeeStatusEnum.BUILD_FAILED);
                        inboundRequestService.update(request);

                        InboundRequestAuditLogHelper.recordLog(request,
                                FeeStatusEnum.BUILD_FAILED.getStatus(),
                                "BuildFee",
                                null,
                                errorDescription);
                    }
                }
                case OTC -> {
                    OtcRequest request = otcRequestService.getById(requestId);
                    if (ObjectUtil.isNotEmpty(request)) {
                        request.setFeeStatus(FeeStatusEnum.BUILD_FAILED);
                        otcRequestService.update(request);

                        OtcRequestAuditLogHelper.recordLog(request,
                                FeeStatusEnum.BUILD_FAILED.getStatus(),
                                "BuildFee",
                                errorDescription,
                                null);
                    }
                }
                case OTB -> {
                    OtbRequest request = otbRequestService.getById(requestId);
                    if (ObjectUtil.isNotEmpty(request)) {
                        request.setFeeStatus(FeeStatusEnum.BUILD_FAILED);
                        otbRequestService.update(request);

                        OtbRequestAuditLogHelper.recordLog(request,
                                FeeStatusEnum.BUILD_FAILED.getStatus(),
                                "BuildFee",
                                null,
                                errorDescription);
                    }
                }
                case STORAGE -> {
                    StorageSnapshotRequest request = storageSnapshotRequestService.getById(requestId);
                    if (ObjectUtil.isNotEmpty(request)) {
                        request.setFeeStatus(FeeStatusEnum.BUILD_FAILED);
                        storageSnapshotRequestService.update(request);

                        StorageSnapshotRequestAuditLogHelper.recordLog(request,
                                FeeStatusEnum.BUILD_FAILED.getStatus(),
                                "BuildFee",
                                null,
                                errorDescription);
                    }
                }
                default -> log.warn("Unsupported request type for build failure handling: {}", requestType);
            }

            log.info("Successfully handled build failure for {} request ID: {}", requestType, requestId);

        } catch (Exception updateException) {
            log.error("Failed to handle build failure for {} request ID: {}, original error: {}, update error: {}",
                    requestType, requestId, exception.getMessage(), updateException.getMessage(), updateException);
            // 不重新抛出异常，避免影响新事务的提交
        }
    }

    /**
     * 构建Redis Key
     *
     * @param prefix      前缀
     * @param tenantId    租户ID
     * @param warehouseId 仓库ID
     * @return Redis Key
     */
    private String buildRedisKey(String prefix, Long tenantId, Long warehouseId) {
        return String.format("%s:%d:%d", prefix, tenantId, warehouseId);
    }

    /**
     * 从Redis获取已查询的RequestIds
     *
     * @param redisKey Redis Key
     * @return 已查询的RequestIds集合
     */
    private Set<Long> getQueriedRequestIdsFromRedis(String redisKey) {
        try {
            RSet<Long> redisSet = RedissonKit.getInstance().getSet(redisKey);
            return redisSet.readAll().stream().collect(Collectors.toSet());
        } catch (Exception e) {
            log.warn("Failed to get queried request IDs from Redis, key: {}, error: {}", redisKey, e.getMessage());
            return Collections.emptySet();
        }
    }

    /**
     * 将新查询的RequestIds添加到Redis
     *
     * @param redisKey   Redis Key
     * @param requestIds 新查询的RequestIds
     */
    private void addQueriedRequestIdsToRedis(String redisKey, List<Long> requestIds) {
        if (ObjectUtil.isEmpty(requestIds)) {
            return;
        }

        try {
            RSet<Long> redisSet = RedissonKit.getInstance().getSet(redisKey);
            redisSet.addAll(requestIds);
            redisSet.expire(QUERIED_REQUEST_IDS_EXPIRE_SECONDS, TimeUnit.SECONDS);

            if (log.isDebugEnabled()) {
                log.debug("Added {} request IDs to Redis cache, key: {}", requestIds.size(), redisKey);
            }
        } catch (Exception e) {
            log.warn("Failed to add queried request IDs to Redis, key: {}, requestIds: {}, error: {}",
                    redisKey, requestIds, e.getMessage());
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void build() {
        // 调用各个具体的构建方法
        buildInbound();
        buildOTC();
        buildOTB();
        buildStorage();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildInbound() {
        //获取所有仓库id
        final Map<Long, List<Long>> allTenantAndWarehouseIdMap = WarehouseCacheUtil.getAllTenantAndWarehouseIdMap();

        for (Map.Entry<Long, List<Long>> entry : allTenantAndWarehouseIdMap.entrySet()) {
            List<Long> warehouseIds = entry.getValue();
            if (ObjectUtil.isEmpty(warehouseIds)) {
                continue;
            }
            TenantContextHolder.setTenantId(entry.getKey());
            for (Long warehouseId : warehouseIds) {
                WarehouseContextHolder.setWarehouseId(warehouseId);
                try {
                    buildInboundForCurrentContext();
                } catch (Exception e) {
                    log.error("Failed to build inbound fee for tenant: {}, warehouse: {}, error: {}",
                            entry.getKey(), warehouseId, e.getMessage(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildInboundForCurrentContext() {
        // 查询需要构建费用的入库请求，排除正在执行的记录
        List<Long> availableRequestIds = getAvailableInboundRequestIds(10);

        if (ObjectUtil.isNotEmpty(availableRequestIds)) {
            for (Long requestId : availableRequestIds) {
                try {
                    buildInboundById(requestId);
                } catch (Exception e) {
                    // 记录错误但继续处理其他请求
                    log.error("Failed to build inbound fee for request ID: {}, error: {}",
                            requestId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 获取可用的入库请求ID列表（排除已查询的记录）
     *
     * @param limit 限制数量
     * @return 可用的请求ID列表
     */
    private List<Long> getAvailableInboundRequestIds(int limit) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        String redisKey = buildRedisKey("fee_queried_inbound_request_ids", tenantId, warehouseId);

        // 从Redis获取已查询的RequestIds
        Set<Long> queriedRequestIds = getQueriedRequestIdsFromRedis(redisKey);

        // 查询需要构建费用的入库请求
        PageSearch<InboundRequestQuery> pageSearch = new PageSearch<>();
        pageSearch.setCurrent(1);
        pageSearch.setSize(limit); // 直接查询指定数量，不再查询2倍
        InboundRequestQuery query = new InboundRequestQuery();
        query.setInboundRequestStatus(InboundRequestStatusEnum.PROCESSED.getStatus());
        query.setFeeStatus(FeeStatusEnum.NEW.getStatus());

        // 排除已查询的RequestIds
        if (ObjectUtil.isNotEmpty(queriedRequestIds)) {
            query.setIdNiList(new ArrayList<>(queriedRequestIds));
        }

        pageSearch.setCondition(query);

        PageData<InboundRequestPageVO> pageData = inboundRequestService.pageByQuery(pageSearch);
        if (ObjectUtil.isEmpty(pageData.getRecords())) {
            return Collections.emptyList();
        }

        // 提取新查询到的RequestIds
        List<Long> newRequestIds = pageData.getRecords().stream()
                .map(InboundRequestPageVO::getId)
                .collect(Collectors.toList());

        // 将新查询到的RequestIds添加到Redis缓存
        addQueriedRequestIdsToRedis(redisKey, newRequestIds);

        return newRequestIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTC() {
        //获取所有仓库id
        final Map<Long, List<Long>> allTenantAndWarehouseIdMap = WarehouseCacheUtil.getAllTenantAndWarehouseIdMap();

        for (Map.Entry<Long, List<Long>> entry : allTenantAndWarehouseIdMap.entrySet()) {
            List<Long> warehouseIds = entry.getValue();
            if (ObjectUtil.isEmpty(warehouseIds)) {
                continue;
            }
            TenantContextHolder.setTenantId(entry.getKey());
            for (Long warehouseId : warehouseIds) {
                WarehouseContextHolder.setWarehouseId(warehouseId);
                try {
                    buildOTCForCurrentContext();
                } catch (Exception e) {
                    log.error("Failed to build OTC fee for tenant: {}, warehouse: {}, error: {}",
                            entry.getKey(), warehouseId, e.getMessage(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTCForCurrentContext() {
        // 查询需要构建费用的OTC请求，排除正在执行的记录
        List<Long> availableRequestIds = getAvailableOtcRequestIds(10);

        if (ObjectUtil.isNotEmpty(availableRequestIds)) {
            for (Long requestId : availableRequestIds) {
                try {
                    buildOTCById(requestId);
                } catch (Exception e) {
                    // 记录错误但继续处理其他请求
                    log.error("Failed to build OTC fee for request ID: {}, error: {}",
                            requestId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 获取可用的OTC请求ID列表（排除已查询的记录）
     *
     * @param limit 限制数量
     * @return 可用的请求ID列表
     */
    private List<Long> getAvailableOtcRequestIds(int limit) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        String redisKey = buildRedisKey("fee_queried_otc_request_ids", tenantId, warehouseId);

        // 从Redis获取已查询的RequestIds
        Set<Long> queriedRequestIds = getQueriedRequestIdsFromRedis(redisKey);

        // 查询需要构建费用的OTC请求
        PageSearch<OtcRequestQuery> pageSearch = new PageSearch<>();
        pageSearch.setCurrent(1);
        pageSearch.setSize(limit); // 直接查询指定数量，不再查询2倍
        OtcRequestQuery query = new OtcRequestQuery();
        query.setOtcRequestStatus(RequestStatusEnum.PROCESSED.getStatus());
        query.setFeeStatus(FeeStatusEnum.NEW.getStatus());

        // 排除已查询的RequestIds
        if (ObjectUtil.isNotEmpty(queriedRequestIds)) {
            query.setIdNiList(new ArrayList<>(queriedRequestIds));
        }

        pageSearch.setCondition(query);

        PageData<OtcRequestPageVO> pageData = otcRequestService.pageByQuery(pageSearch);
        if (ObjectUtil.isEmpty(pageData.getRecords())) {
            return Collections.emptyList();
        }

        // 提取新查询到的RequestIds
        List<Long> newRequestIds = pageData.getRecords().stream()
                .map(OtcRequestPageVO::getId)
                .collect(Collectors.toList());

        // 将新查询到的RequestIds添加到Redis缓存
        addQueriedRequestIdsToRedis(redisKey, newRequestIds);

        return newRequestIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTB() {
        //获取所有仓库id
        final Map<Long, List<Long>> allTenantAndWarehouseIdMap = WarehouseCacheUtil.getAllTenantAndWarehouseIdMap();

        for (Map.Entry<Long, List<Long>> entry : allTenantAndWarehouseIdMap.entrySet()) {
            List<Long> warehouseIds = entry.getValue();
            if (ObjectUtil.isEmpty(warehouseIds)) {
                continue;
            }
            TenantContextHolder.setTenantId(entry.getKey());
            for (Long warehouseId : warehouseIds) {
                WarehouseContextHolder.setWarehouseId(warehouseId);
                try {
                    buildOTBForCurrentContext();
                } catch (Exception e) {
                    log.error("Failed to build OTB fee for tenant: {}, warehouse: {}, error: {}",
                            entry.getKey(), warehouseId, e.getMessage(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTBForCurrentContext() {
        // 查询需要构建费用的OTB请求，排除正在执行的记录
        List<Long> availableRequestIds = getAvailableOtbRequestIds(10);

        if (ObjectUtil.isNotEmpty(availableRequestIds)) {
            for (Long requestId : availableRequestIds) {
                try {
                    buildOTBById(requestId);
                } catch (Exception e) {
                    // 记录错误但继续处理其他请求
                    log.error("Failed to build OTB fee for request ID: {}, error: {}",
                            requestId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 获取可用的OTB请求ID列表（排除已查询的记录）
     *
     * @param limit 限制数量
     * @return 可用的请求ID列表
     */
    private List<Long> getAvailableOtbRequestIds(int limit) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        String redisKey = buildRedisKey("fee_queried_otb_request_ids", tenantId, warehouseId);

        // 从Redis获取已查询的RequestIds
        Set<Long> queriedRequestIds = getQueriedRequestIdsFromRedis(redisKey);

        // 查询需要构建费用的OTB请求
        PageSearch<OtbRequestQuery> pageSearch = new PageSearch<>();
        pageSearch.setCurrent(1);
        pageSearch.setSize(limit); // 直接查询指定数量，不再查询2倍
        OtbRequestQuery query = new OtbRequestQuery();
        query.setOtbRequestStatus(RequestStatusEnum.PROCESSED.getStatus());
        query.setFeeStatus(FeeStatusEnum.NEW.getStatus());

        // 排除已查询的RequestIds
        if (ObjectUtil.isNotEmpty(queriedRequestIds)) {
            query.setIdNiList(new ArrayList<>(queriedRequestIds));
        }

        pageSearch.setCondition(query);

        PageData<OtbRequestPageVO> pageData = otbRequestService.pageByQuery(pageSearch);
        if (ObjectUtil.isEmpty(pageData.getRecords())) {
            return Collections.emptyList();
        }

        // 提取新查询到的RequestIds
        List<Long> newRequestIds = pageData.getRecords().stream()
                .map(OtbRequestPageVO::getId)
                .collect(Collectors.toList());

        // 将新查询到的RequestIds添加到Redis缓存
        addQueriedRequestIdsToRedis(redisKey, newRequestIds);

        return newRequestIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildStorage() {
        //获取所有仓库id
        final Map<Long, List<Long>> allTenantAndWarehouseIdMap = WarehouseCacheUtil.getAllTenantAndWarehouseIdMap();

        for (Map.Entry<Long, List<Long>> entry : allTenantAndWarehouseIdMap.entrySet()) {
            List<Long> warehouseIds = entry.getValue();
            if (ObjectUtil.isEmpty(warehouseIds)) {
                continue;
            }
            TenantContextHolder.setTenantId(entry.getKey());
            for (Long warehouseId : warehouseIds) {
                WarehouseContextHolder.setWarehouseId(warehouseId);
                try {
                    buildStorageForCurrentContext();
                } catch (Exception e) {
                    log.error("Failed to build Storage fee for tenant: {}, warehouse: {}, error: {}",
                            entry.getKey(), warehouseId, e.getMessage(), e);
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildStorageForCurrentContext() {
        // TODO: Storage 类型可能需要特殊的查询逻辑
        // 获取可用的Storage请求ID，排除正在执行的记录
        List<Long> availableRequestIds = getAvailableStorageRequestIds(10);

        if (ObjectUtil.isNotEmpty(availableRequestIds)) {
            for (Long requestId : availableRequestIds) {
                try {
                    buildStorageById(requestId);
                } catch (Exception e) {
                    // 记录错误但继续处理其他请求
                    log.error("Failed to build Storage fee for request ID: {}, error: {}",
                            requestId, e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 获取可用的Storage请求ID列表（排除已查询的记录）
     *
     * @param limit 限制数量
     * @return 可用的请求ID列表
     */
    private List<Long> getAvailableStorageRequestIds(int limit) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        String redisKey = buildRedisKey("fee_queried_storage_request_ids", tenantId, warehouseId);

        // 从Redis获取已查询的RequestIds
        Set<Long> queriedRequestIds = getQueriedRequestIdsFromRedis(redisKey);

        // 查询需要构建费用的Storage请求
        PageSearch<StorageSnapshotRequestQuery> pageSearch = new PageSearch<>();
        pageSearch.setCurrent(1);
        pageSearch.setSize(limit); // 直接查询指定数量，不再查询2倍
        StorageSnapshotRequestQuery query = new StorageSnapshotRequestQuery();
        query.setRequestStatus(StorageSnapshotRequestStatusEnum.PROCESSED.getStatus());
        query.setFeeStatus(FeeStatusEnum.NEW.getStatus());

        // 排除已查询的RequestIds
        if (ObjectUtil.isNotEmpty(queriedRequestIds)) {
            query.setIdNiList(new ArrayList<>(queriedRequestIds));
        }

        pageSearch.setCondition(query);

        PageData<StorageSnapshotRequestPageVO> pageData = storageSnapshotRequestService.pageByQuery(pageSearch);
        if (ObjectUtil.isEmpty(pageData.getRecords())) {
            return Collections.emptyList();
        }

        // 提取新查询到的RequestIds
        List<Long> newRequestIds = pageData.getRecords().stream()
                .map(StorageSnapshotRequestPageVO::getId)
                .collect(Collectors.toList());

        // 将新查询到的RequestIds添加到Redis缓存
        addQueriedRequestIdsToRedis(redisKey, newRequestIds);

        return newRequestIds;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildInboundById(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            throw new BusinessException("Request ID cannot be null");
        }

        // 使用Redis锁防止相同的requestId并发执行
        String lockKey = "fee_inbound_build_request_" + requestId;
        RedissonKit.getInstance().lock(lockKey, 30, lock -> {
            try {
                FodExtraDataInBoundBO fodExtraDataInBoundBO = feeOriginalDataBuildFactory.<FodExtraDataInBoundBO>getBuilder(FeeModelTypeEnum.INBOUND).buildFeeOriginalData(requestId);

                if (ObjectUtil.isNotEmpty(fodExtraDataInBoundBO)) {
                    FeeOriginalDataCreateParam feeOriginalDataCreateParam = new FeeOriginalDataCreateParam();
                    feeOriginalDataCreateParam.setFeeModelType(FeeModelTypeEnum.INBOUND);
                    feeOriginalDataCreateParam.setRequestId(fodExtraDataInBoundBO.getSnapshotRequestId());
                    insertByParam(feeOriginalDataCreateParam);

                    // 更新请求的 feeStatus 为 BuildFeeOriginalData
                    InboundRequest request = inboundRequestService.getById(fodExtraDataInBoundBO.getSnapshotRequestId());

                    if (!ObjectUtil.isNotEmpty(request)) {
                        throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "InboundRequest", "id", fodExtraDataInBoundBO.getSnapshotRequestId()));
                    }
                    request.setFeeStatus(FeeStatusEnum.BuildFeeOriginalData);
                    inboundRequestService.update(request);

                    // 记录InboundRequest审计日志
                    InboundRequestAuditLogHelper.recordLog(request,
                            FeeStatusEnum.BuildFeeOriginalData.getStatus(),
                            "BuildFee",
                            null,
                            feeOriginalDataCreateParam.toString());
                }
            } catch (Exception e) {
                // 使用代理对象调用新事务方法处理构建失败的情况
                getSelf().handleBuildFailure(requestId, e, FeeModelTypeEnum.INBOUND);

                // 重新抛出原始异常
                throw e;
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTCById(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            throw new BusinessException("Request ID cannot be null");
        }

        // 使用Redis锁防止相同的requestId并发执行
        String lockKey = "fee_otc_build_request_" + requestId;
        RedissonKit.getInstance().lock(lockKey, 30, lock -> {
            try {
                FodExtraDataOtcBO fodExtraDataOtcBO = feeOriginalDataBuildFactory.<FodExtraDataOtcBO>getBuilder(FeeModelTypeEnum.OTC).buildFeeOriginalData(requestId);

                if (ObjectUtil.isNotEmpty(fodExtraDataOtcBO)) {
                    FeeOriginalDataCreateParam feeOriginalDataCreateParam = new FeeOriginalDataCreateParam();
                    feeOriginalDataCreateParam.setFeeModelType(FeeModelTypeEnum.OTC);
                    feeOriginalDataCreateParam.setRequestId(fodExtraDataOtcBO.getSnapshotRequestId());
                    insertByParam(feeOriginalDataCreateParam);

                    // 更新请求的 feeStatus 为 BuildFeeOriginalData
                    OtcRequest request = otcRequestService.getById(fodExtraDataOtcBO.getSnapshotRequestId());

                    if (!ObjectUtil.isNotEmpty(request)) {
                        throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtcRequest", "id", fodExtraDataOtcBO.getSnapshotRequestId()));
                    }
                    request.setFeeStatus(FeeStatusEnum.BuildFeeOriginalData);
                    otcRequestService.update(request);

                    // 记录OtcRequest审计日志
                    OtcRequestAuditLogHelper.recordLog(request,
                            FeeStatusEnum.BuildFeeOriginalData.getStatus(),
                            "BuildFee",
                            feeOriginalDataCreateParam.toString(),
                            null);
                }
            } catch (Exception e) {
                // 使用代理对象调用新事务方法处理构建失败的情况
                getSelf().handleBuildFailure(requestId, e, FeeModelTypeEnum.OTC);

                // 重新抛出原始异常
                throw e;
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildOTBById(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            throw new BusinessException("Request ID cannot be null");
        }

        // 使用Redis锁防止相同的requestId并发执行
        String lockKey = "fee_otb_build_request_" + requestId;
        RedissonKit.getInstance().lock(lockKey, 30, lock -> {
            try {
                FodExtraDataOtbBO fodExtraDataOtbBO = feeOriginalDataBuildFactory.<FodExtraDataOtbBO>getBuilder(FeeModelTypeEnum.OTB).buildFeeOriginalData(requestId);

                if (ObjectUtil.isNotEmpty(fodExtraDataOtbBO)) {
                    FeeOriginalDataCreateParam feeOriginalDataCreateParam = new FeeOriginalDataCreateParam();
                    feeOriginalDataCreateParam.setFeeModelType(FeeModelTypeEnum.OTB);
                    feeOriginalDataCreateParam.setRequestId(fodExtraDataOtbBO.getSnapshotRequestId());
                    insertByParam(feeOriginalDataCreateParam);

                    OtbRequest request = otbRequestService.getById(fodExtraDataOtbBO.getSnapshotRequestId());

                    if (!ObjectUtil.isNotEmpty(request)) {
                        throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "OtbRequest", "id", fodExtraDataOtbBO.getSnapshotRequestId()));
                    }
                    request.setFeeStatus(FeeStatusEnum.BuildFeeOriginalData);
                    otbRequestService.update(request);

                    // 记录OtbRequest审计日志
                    OtbRequestAuditLogHelper.recordLog(request,
                            FeeStatusEnum.BuildFeeOriginalData.getStatus(),
                            "BuildFee",
                            null,
                            feeOriginalDataCreateParam.toString());
                }
            } catch (Exception e) {
                // 使用代理对象调用新事务方法处理构建失败的情况
                getSelf().handleBuildFailure(requestId, e, FeeModelTypeEnum.OTB);

                // 重新抛出原始异常
                throw e;
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void buildStorageById(Long requestId) {
        if (ObjectUtil.isEmpty(requestId)) {
            throw new BusinessException("Request ID cannot be null");
        }

        // 使用Redis锁防止相同的requestId并发执行
        String lockKey = "fee_storage_build_request_" + requestId;
        RedissonKit.getInstance().lock(lockKey, 30, lock -> {
            try {
                FodExtraDataStorageBO fodExtraDataStorageBO = feeOriginalDataBuildFactory.<FodExtraDataStorageBO>getBuilder(FeeModelTypeEnum.STORAGE).buildFeeOriginalData(requestId);

                if (ObjectUtil.isNotEmpty(fodExtraDataStorageBO)) {
                    FeeOriginalDataCreateParam feeOriginalDataCreateParam = new FeeOriginalDataCreateParam();
                    feeOriginalDataCreateParam.setFeeModelType(FeeModelTypeEnum.STORAGE);
                    feeOriginalDataCreateParam.setRequestId(fodExtraDataStorageBO.getSnapshotRequestId());
                    insertByParam(feeOriginalDataCreateParam);

                    StorageSnapshotRequest request = storageSnapshotRequestService.getById(fodExtraDataStorageBO.getSnapshotRequestId());

                    if (!ObjectUtil.isNotEmpty(request)) {
                        throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "StorageRequest", "id", fodExtraDataStorageBO.getSnapshotRequestId()));
                    }
                    request.setFeeStatus(FeeStatusEnum.BuildFeeOriginalData);
                    storageSnapshotRequestService.update(request);

                    // 记录StorageRequest审计日志
                    StorageSnapshotRequestAuditLogHelper.recordLog(request,
                            FeeStatusEnum.BuildFeeOriginalData.getStatus(),
                            "BuildFee",
                            null,
                            feeOriginalDataCreateParam.toString());
                }
            } catch (Exception e) {
                // 使用代理对象调用新事务方法处理构建失败的情况
                getSelf().handleBuildFailure(requestId, e, FeeModelTypeEnum.STORAGE);

                // 重新抛出原始异常
                throw e;
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(FeeOriginalDataCreateParam createParam) {
        // 检查传入费用原始数据表参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        final FeeOriginalDataBuildService fodBuild = feeOriginalDataBuildFactory.getBuilder(createParam.getFeeModelType());

        final Object fodExtraData = fodBuild.buildFeeOriginalData(createParam.getRequestId());

        // 将费用原始数据表参数对象转换为实体对象并初始化
        FeeOriginalData entity = BeanUtil.copyNew(fodExtraData, FeeOriginalData.class);

        entity.setId(IdWorker.getId());

        entity.setExtraData(JsonUtil.toJson(fodExtraData));

        setRefNumByFeeModelType(createParam.getFeeModelType(), entity);

        // 设置默认状态为 NEW
        entity.setFeeOriginalStatus(FeeOriginalStatusEnum.NEW);

        // 插入费用原始数据表实体对象到数据库A
        super.insert(entity);

        // 返回费用原始数据表ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(FeeOriginalDataUpdateParam updateParam) {
        // 检查传入费用原始数据表参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 将费用原始数据表参数对象转换为实体对象
        FeeOriginalData entity = initFeeOriginalData(updateParam);

        // 执行更新费用原始数据表操作
        return super.update(entity);

    }

    @Override
    public List<FeeOriginalDataPageVO> listByQuery(FeeOriginalDataQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<FeeOriginalDataPageVO> pageByQuery(PageSearch<FeeOriginalDataQuery> search) {
        Page<FeeOriginalData> page = Conditions.page(search, entityClass);
        List<FeeOriginalDataPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public FeeOriginalDataVO detailById(Long id) {
        FeeOriginalData entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "FeeOriginalData", id));
        }
        return buildFeeOriginalDataVO(entity);
    }

    @Override
    public FeeOriginalDataVO detailByRefNum(String refNum) {
        FeeOriginalData entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "FeeOriginalData", "refNum", refNum));
        }
        return buildFeeOriginalDataVO(entity);
    }

    private void setRefNumByFeeModelType(FeeModelTypeEnum feeModelType, FeeOriginalData entity) {
        switch (feeModelType) {
            case INBOUND -> entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_ORIGINAL_DATA_INBOUND));
            case OTC -> entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_ORIGINAL_DATA_OTC));
            case OTB -> entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_ORIGINAL_DATA_OTB));
            case STORAGE -> entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_ORIGINAL_DATA_STORAGE));
            default -> throw new BusinessException("Unsupported fee model type");
        }

        entity.setFeeModelType(feeModelType);

    }

    /**
     * 初始化费用原始数据表对象
     * 此方法用于设置费用原始数据表对象的必要参数，确保其处于有效状态
     *
     * @param createParam 费用原始数据表 新增对象，不应为空
     * @return 返回初始化后的费用原始数据表
     * @throws BusinessException 如果传入的费用原始数据表为空，则抛出此异常
     */
    private FeeOriginalData initFeeOriginalData(FeeOriginalDataCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("FeeOriginalDataCreateParam cannot be empty");
        }

        // 获取费用原始数据表转换器实例，用于将费用原始数据表参数对象转换为实体对象
        FeeOriginalDataConverter converter = Converters.get(FeeOriginalDataConverter.class);

        // 将费用原始数据表参数对象转换为实体对象并初始化
        FeeOriginalData entity = converter.toEntity(createParam);

        entity.setId(IdWorker.getId());
        //entity.setRefNum(FormatUtil.generateRefNum(RefNumTypeEnum.FEE_ORIGINAL_DATA));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 初始化费用原始数据表对象
     * 此方法用于设置费用原始数据表对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 费用原始数据表 修改对象，不应为空
     * @return 返回初始化后的费用原始数据表
     * @throws BusinessException 如果传入的费用原始数据表为空，则抛出此异常
     */
    private FeeOriginalData initFeeOriginalData(FeeOriginalDataUpdateParam updateParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "FeeOriginalDataUpdateParam"));
        }

        // 获取费用原始数据表转换器实例，用于将费用原始数据表参数对象转换为实体对象
        FeeOriginalDataConverter converter = Converters.get(FeeOriginalDataConverter.class);

        // 将费用原始数据表参数对象转换为实体对象并初始化
        FeeOriginalData entity = converter.toEntity(updateParam);


        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建费用原始数据表VO对象
     *
     * @param entity 费用原始数据表对象
     * @return 返回包含详细信息的费用原始数据表VO对象
     */
    private FeeOriginalDataVO buildFeeOriginalDataVO(FeeOriginalData entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的费用原始数据表VO对象
        return Converters.get(FeeOriginalDataConverter.class).toVO(entity);
    }

    @Override
    public List<Long> getNewFeeOriginalDataIds(String feeModelType, int limit) {
        Long tenantId = TenantContextHolder.getTenantId();
        Long warehouseId = WarehouseContextHolder.getWarehouseId();

        if (tenantId == null || warehouseId == null) {
            log.warn("租户ID或仓库ID为空，无法获取费用原始数据");
            return Collections.emptyList();
        }

        String redisKey = buildRedisKey("fee_queried_original_data_ids_" + feeModelType, tenantId, warehouseId);

        // 从Redis获取已查询的费用原始数据IDs
        Set<Long> queriedIds = getQueriedRequestIdsFromRedis(redisKey);

        // 查询状态为NEW的费用原始数据
        PageSearch<FeeOriginalDataQuery> pageSearch = new PageSearch<>();
        pageSearch.setCurrent(1);
        pageSearch.setSize(limit);

        FeeOriginalDataQuery query = new FeeOriginalDataQuery();
        query.setFeeOriginalStatus(FeeOriginalStatusEnum.NEW.getStatus());
        query.setFeeModelType(feeModelType);

        // 排除已查询的IDs
        if (ObjectUtil.isNotEmpty(queriedIds)) {
            query.setIdNiList(new ArrayList<>(queriedIds));
        }

        pageSearch.setCondition(query);

        PageData<FeeOriginalDataPageVO> pageData = pageByQuery(pageSearch);
        if (ObjectUtil.isEmpty(pageData.getRecords())) {
            return Collections.emptyList();
        }

        List<Long> newIds = pageData.getRecords().stream()
                .map(FeeOriginalDataPageVO::getId)
                .collect(Collectors.toList());

        // 将新查询的IDs添加到Redis中
        addQueriedRequestIdsToRedis(redisKey, newIds);

        log.info("租户ID: {}, 仓库ID: {}, 费用模型类型: {} - 获取到 {} 个状态为NEW的费用原始数据",
                tenantId, warehouseId, feeModelType, newIds.size());

        return newIds;
    }

}
