package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 库位详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryBinLocationDetailConverter extends AbstractModelConverter<BinLocationDetail, InventoryBinLocationDetailVO, InventoryBinLocationDetailVO> {

}
