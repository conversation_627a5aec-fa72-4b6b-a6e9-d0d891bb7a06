package cn.need.cloud.biz.converter.binlocation;

import cn.need.cloud.biz.client.dto.binlocation.BinLocationDetailDTO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 库位详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class BinLocationDetailConverter extends AbstractModelConverter<BinLocationDetail, BinLocationDetailVO, BinLocationDetailDTO> {

    @Override
    protected boolean isFillDict() {
        return true;
    }
}
