package cn.need.cloud.biz.converter;

import cn.need.cloud.biz.client.dto.PrepRequestDetailDTO;
import cn.need.cloud.biz.model.entity.PrepRequestDetail;
import cn.need.cloud.biz.model.vo.PrepRequestDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 预请求详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class PrepRequestDetailConverter extends AbstractModelConverter<PrepRequestDetail, PrepRequestDetailVO, PrepRequestDetailDTO> {

}
