package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPrepWorkorderConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.workorder.prep.OtcPrepWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderFilterBuildPickingSlipCountVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderBinLocationPageVO;
import cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderPageVO;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * OTC预提工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-prep-workorder")
@Tag(name = "OTC预提工单")
public class OtcPrepWorkorderController extends AbstractRestController<OtcPrepWorkorderService, OtcPrepWorkorder, OtcPrepWorkorderConverter, OtcPrepWorkorderVO> {

    @Resource
    private OtcPrepWorkorderBinLocationService otcPrepWorkorderBinLocationService;

    @Operation(summary = "根据id获取OTC预提工单详情", description = "根据数据主键id，从数据库中获取其对应的OTC预提工单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPrepWorkorderVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC预提工单详情
        OtcPrepWorkorderVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC预提工单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC预提工单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPrepWorkorderVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC预提工单详情
        OtcPrepWorkorderVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC预提工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC预提工单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPrepWorkorderPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPrepWorkOrderListQuery> search) {

        // 获取OTC预提工单分页
        PageData<OtcPrepWorkorderPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "count 数量", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC预提工单数量")
    @PostMapping(value = "/count")
    public Result<Long> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtcPrepWorkOrderListQuery search) {

        // 获取OTC预提工单分页
        OtcWorkorderFilterBuildPickingSlipCountVO resultPage = service.filterBuildPickingSlipCount(search);
        // 返回结果
        return success(resultPage.getFilterSum());
    }

    @Operation(summary = "获取OTC预提工单仓储位置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC预提工单仓储位置列表")
    @PostMapping(value = "/bin-location/list")
    public Result<PageData<OtcPrepWorkorderBinLocationPageVO>> binLocationList(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPrepWorkorderBinLocationQuery> search) {

        // 获取OTC预提工单仓储位置分页
        PageData<OtcPrepWorkorderBinLocationPageVO> resultPage = otcPrepWorkorderBinLocationService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "下拉列表Pro", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValuePro(@RequestBody @Parameter(description = "数据主键id", required = true) OtcPrepWorkorderQuery query) {

        return success(service.distinctValuePro(query));
    }
}
