package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigInboundDetailDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInboundDetail;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置inbound详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigInboundDetailConverter extends AbstractModelConverter<FeeConfigInboundDetail, FeeConfigInboundDetailVO, FeeConfigInboundDetailDTO> {

}
