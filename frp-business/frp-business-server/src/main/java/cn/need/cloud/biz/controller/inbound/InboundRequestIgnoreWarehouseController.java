package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.inbound.InboundRequestIgnoreWarehouseService;
import cn.need.cloud.biz.service.inbound.InboundRequestSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 入库上架 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@RestController
@RequestMapping("/api/biz/inbound-request/ignore-warehouse")
@Tag(name = "inboundRequest ignoreWarehouse接口")
@Slf4j
public class InboundRequestIgnoreWarehouseController extends AbstractController {

    @Resource
    private InboundRequestIgnoreWarehouseService service;
    @Resource
    private InboundRequestSpecialService inboundRequestSpecialService;

    @Operation(summary = "获取入库请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundRequestQuery> search) {
        //过滤仓库
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        // 获取入库请求分页
        PageData<InboundRequestPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return Result.ok(resultPage);
    }

    @Operation(summary = "入库审批", description = "入库审批")
    @PostMapping(value = "/audit")
    public Result<Object> process(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundRequestAuditVO inboundRequestAuditVO) {
        //过滤仓库
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        service.audit(inboundRequestAuditVO);
        return Result.ok();
    }

    @Operation(summary = "根据id删除入库请求", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 过滤仓库
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return Result.ok(inboundRequestSpecialService.removeRequest(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }
}
