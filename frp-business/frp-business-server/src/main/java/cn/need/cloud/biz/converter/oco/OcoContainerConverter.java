package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoContainerDTO;
import cn.need.cloud.biz.model.entity.oco.OcoContainer;
import cn.need.cloud.biz.model.vo.oco.OcoContainerVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO集装箱表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoContainerConverter extends AbstractModelConverter<OcoContainer, OcoContainerVO, OcoContainerDTO> {

}


