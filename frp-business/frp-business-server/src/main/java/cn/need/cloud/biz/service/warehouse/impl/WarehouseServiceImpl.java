package cn.need.cloud.biz.service.warehouse.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.cache.WarehouseCacheRepertory;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.converter.warehouse.WarehouseConverter;
import cn.need.cloud.biz.mapper.warehouse.WarehouseMapper;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseQuery;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseDropVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseVO;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.warehouse.WarehouseOperationService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.util.FormatUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.date.TimeMeter;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.session.JwtUser;
import cn.need.framework.common.core.session.Users;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.isEmpty;

/**
 * <p>
 * 仓库基础信息服务实现类
 * </p>
 * <p>
 * 该类实现了仓库管理相关的业务逻辑，包括仓库的创建、更新、查询等功能。
 * 还提供了仓库状态管理、缓存同步、下拉列表获取等功能。
 * </p>
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-26
 */
@Service
public class WarehouseServiceImpl extends SuperServiceImpl<WarehouseMapper, Warehouse> implements WarehouseService {

    /**
     * 库位服务，用于管理仓库中的库位信息
     */
    @Resource
    private BinLocationService binLocationService;

    /**
     * 仓库操作服务，用于记录和管理仓库的操作记录
     */
    @Resource
    private WarehouseOperationService warehouseOperationService;

    /**
     * 仓库缓存仓库，用于存储和获取仓库缓存信息
     */
    @Resource
    private WarehouseCacheRepertory warehouseCacheRepertory;

    /**
     * 实体类数据拷贝,只拷贝不为空的属性
     * <p>
     * 拷贝原则：
     * 1.只拷贝名称和类型都相同的属性，即使jdk的原类型与包装类型，都会被看成不同的类型，如int与Integer
     * 2.拷贝的属性必须存在getter和setter方法
     * </p>
     *
     * @param orig 源对象
     * @param dest 目标对象
     */
    public static void copyNotEmpty(Object orig, Object dest) {
        Class<?> origClass = orig.getClass();
        Field[] fieldList = origClass.getDeclaredFields();
        String[] ignoreList = Arrays.stream(fieldList).filter(item -> {
            try {
                item.setAccessible(Boolean.TRUE);
                return isEmpty(item.get(orig));
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }).map(Field::getName).toArray(String[]::new);
        BeanUtil.copy(orig, dest, ignoreList);
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 根据参数创建新仓库
     * <p>
     * 该方法在事务中执行，确保数据一致性。创建仓库时会进行以下步骤：
     * 1. 检查参数有效性
     * 2. 构建仓库对象
     * 3. 忽略当前请求头的仓库ID
     * 4. 插入仓库记录
     * 5. 生成默认库位
     * 6. 更新仓库中的默认库位ID
     * 7. 添加仓库操作记录
     * 8. 更新仓库缓存
     * </p>
     *
     * @param createParam 仓库创建参数，包含仓库的基本信息
     * @return 创建的仓库ID
     * @throws IllegalArgumentException 如果参数为空，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(WarehouseCreateParam createParam) {
        // 检查传入仓库基础信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }
        //构建仓库对象
        Warehouse warehouse = buildWarehouse(createParam);
        //忽略当前请求头的仓库id
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        //插入仓库
        super.insert(warehouse);
        //异步生成默认库位
        List<BinLocation> binLocationList = binLocationService.insertDefaultBinLocation(warehouse);
        //填充默认库位id
        fillDefaultBinLocationId(warehouse, binLocationList);
        super.update(warehouse);
        //将当前仓库创建人作为操作人之一
        JwtUser user = Users.getUser();
        warehouseOperationService.saveOperation(Objects.requireNonNull(user).getId(), warehouse.getId());
        //更新缓存
        updateRedis(warehouse, CollUtil.newHashSet(Users.id()));
        //返回仓库id
        return warehouse.getId();
    }

    /**
     * 根据参数更新仓库信息
     * <p>
     * 该方法在事务中执行，确保数据一致性。更新仓库时会进行以下步骤：
     * 1. 检查参数有效性
     * 2. 验证仓库是否存在
     * 3. 转换参数为实体对象
     * 4. 更新仓库缓存
     * 5. 更新数据库中的仓库记录
     * </p>
     *
     * @param updateParam 仓库更新参数，包含需要更新的字段
     * @return 更新的记录数
     * @throws IllegalArgumentException 如果参数为空或ID为空，则抛出异常
     * @throws BusinessException        如果找不到指定ID的仓库，则抛出业务异常
     *                                  <p>
     *                                  // 如果仓库代码有变更，检查唯一性
     *                                  if (StringUtil.isNotEmpty(updateParam.getCode()) && !updateParam.getCode().equals(existingWarehouse.getCode())) {
     *                                  // 检查代码是否被使用
     *                                  boolean exists = lambdaQuery()
     *                                  .eq(Warehouse::getCode, updateParam.getCode().toUpperCase())
     *                                  .ne(Warehouse::getId, updateParam.getId())
     *                                  .exists();
     *                                  if (exists) {
     *                                  throw new BusinessException("Warehouse code already exists: " + updateParam.getCode());
     *                                  }
     *                                  }
     *                                  <p>
     *                                  // 获取仓库基础信息转换器实例，用于将仓库基础信息参数对象转换为实体对象
     *                                  WarehouseConverter converter = Converters.get(WarehouseConverter.class);
     *                                  // 将仓库基础信息参数对象转换为实体对象
     *                                  Warehouse entity = converter.toEntity(updateParam);
     *                                  // 编码code转大写
     *                                  if (StringUtil.isNotEmpty(entity.getCode())) {
     *                                  entity.setCode(entity.getCode().toUpperCase());
     *                                  }
     *                                  //更新缓存
     *                                  updateRedis(entity);
     *                                  // 执行更新仓库基础信息操作
     *                                  return super.update(entity);
     *                                  }
     *                                  </pre>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(WarehouseUpdateParam updateParam) {
        // 检查传入仓库基础信息参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }
        // 检查是否存在仓库基础信息，如果为空则抛出异常
        if (ObjectUtil.isEmpty(getById(updateParam.getId()))) {
            // throw new BusinessException("id: " + updateParam.getId() + " not found in Warehouse");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Warehouse", updateParam.getId()));
        }
        // 获取仓库基础信息转换器实例，用于将仓库基础信息参数对象转换为实体对象
        WarehouseConverter converter = Converters.get(WarehouseConverter.class);
        // 将仓库基础信息参数对象转换为实体对象
        Warehouse entity = converter.toEntity(updateParam);
        // 编码code转大写
        entity.setCode(entity.getCode().toUpperCase());
        //更新缓存
        updateRedis(entity);
        // 执行更新仓库基础信息操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取仓库列表
     * <p>
     * 该方法根据指定的查询条件查询仓库列表。
     * </p>
     *
     * @param query 查询条件
     * @return 仓库分页视图对象列表
     */
    @Override
    public List<WarehousePageVO> listByQuery(WarehouseQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取仓库
     * <p>
     * 该方法按照指定的查询条件和分页参数查询仓库列表。
     * 查询结果会填充操作人信息。
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的仓库分页视图对象
     * <p>
     * TODO: 考虑添加结果的排序功能
     * 优化建议：支持按照不同字段和排序方式进行排序
     */
    @Override
    public PageData<WarehousePageVO> pageByQuery(PageSearch<WarehouseQuery> search) {
        //分页参数
        Page<Warehouse> page = Conditions.page(search, entityClass);
        //条件参数
        WarehouseQuery condition = search.getCondition();
        //仓库分页列表
        List<WarehousePageVO> dataList = mapper.listByQuery(condition, page);
        //校验列表是否为空
        if (ObjectUtil.isEmpty(dataList)) {
            return new PageData<>(Lists.arrayList(), page);
        }
        //填充操作人
        warehouseOperationService.fillOperationInfo(dataList);
        //返回仓库列表
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取仓库详情
     * <p>
     * 该方法用于获取指定ID的仓库详细信息。
     * </p>
     *
     * @param id 仓库ID
     * @return 仓库视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定ID的仓库，则抛出业务异常
     */
    @Override
    public WarehouseVO detailById(Long id) {
        Warehouse entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in Warehouse");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Warehouse", id));
        }
        //返回仓库vo对象
        return buildWarehouseVO(entity);
    }


    @Override
    public WarehouseVO getByCode(String code) {
        Warehouse entity = lambdaQuery().eq(Warehouse::getCode, code).one();

        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in Warehouse");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "Warehouse", code));
        }
        //返回仓库vo对象
        return buildWarehouseVO(entity);
    }

    /**
     * 根据参考编号获取仓库详情
     * <p>
     * 该方法用于获取指定参考编号的仓库详细信息。
     * </p>
     *
     * @param refNum 仓库参考编号
     * @return 仓库视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定参考编号的仓库，则抛出业务异常
     *                           <p>
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     TODO: 方法中调用了未定义的getByRefNum方法，需要补充实现
     *                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     优化建议：实现getByRefNum方法或使用Lambda查询
     */
    @Override
    public WarehouseVO detailByRefNum(String refNum) {
        Warehouse entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in Warehouse");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "Warehouse", "refNum", refNum));
        }
        //返回仓库vo对象
        return buildWarehouseVO(entity);
    }

    /**
     * 启用禁用
     *
     * @param id 仓库id
     * @return 影响行数
     */
    @Override
    public Integer switchActive(Long id) {
        //数据初始化
        Warehouse warehouse = super.getById(id);
        //更新状态
        if (warehouse.getActiveFlag()) {
            warehouse.setActiveFlag(Boolean.FALSE);
            WarehouseUtils.executeIgnore(() -> {
                binLocationService.switchActive(id, Boolean.FALSE);
            });
        } else {
            warehouse.setActiveFlag(Boolean.TRUE);
            WarehouseUtils.executeIgnore(() -> {
                binLocationService.switchActive(id, Boolean.TRUE);
            });
        }
        //取出缓存 因为数据库的仓库状态被修改，缓存也应该同步更新
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(id);
        if (ObjectUtil.isNotEmpty(warehouseCache)) {
            warehouseCache.setActiveFlag(warehouse.getActiveFlag());
            warehouseCacheRepertory.addWarehouse(warehouseCache);
        }
        return super.update(warehouse);

    }

    /**
     * 该方法用于获取全量仓库下拉
     * 该方法用于获取全量仓库下拉，无需传入参数
     *
     * @return 仓库列表
     */
    @Override
    public List<WarehouseDropVO> dropList(Long id) {
        //返回当前操作人仓库列表
        return warehouseOperationService.listByOperationId(id);
    }

    @Override
    public Map<Long, String> codeRelationByIds(List<Long> warehouseIds) {
        if (ObjectUtil.isEmpty(warehouseIds)) {
            return Collections.emptyMap();
        }
        List<WarehouseCache> warehouseCacheList = WarehouseCacheUtil.listByIds(warehouseIds);
        return warehouseCacheList.stream()
                .collect(Collectors.toMap(WarehouseCache::getId, WarehouseCache::getCode));
    }

    @Override
    public BaseWarehouseVO baseWarehouseById(Long id) {
        Map<Long, BaseWarehouseVO> binLocationMap = baseWarehouseByIds(Collections.singletonList(id));
        return ObjectUtil.isEmpty(binLocationMap) ? null : binLocationMap.get(id);
    }

    @Override
    public Map<Long, BaseWarehouseVO> baseWarehouseByIds(List<Long> idList) {
        if (ObjectUtil.isEmpty(idList)) {
            return Collections.emptyMap();
        }
        // 缓存获取
        List<WarehouseCache> warehouseList = WarehouseCacheUtil.listByIds(idList);
        List<BaseWarehouseVO> baseWarehouses = BeanUtil.copyNew(warehouseList, BaseWarehouseVO.class);
        return StreamUtils.toMap(baseWarehouses, BaseWarehouseVO::getId);
    }

    /**
     * 初始化仓库缓存
     */
    @Override
    public void initWarehouseCache() {
        TimeMeter meter = new TimeMeter();
        log.info(">>>>>>>>>>>>>>>>>>>>> 初始化仓库数据至redis【开始】 >>>>>>>>>>>>>>>>>>>>>");
        //获取仓库数据
        List<Warehouse> warehouseList = list();
        if (ObjectUtil.isEmpty(warehouseList)) {
            return;
        }
        //获取仓库id
        List<Long> warehouseIdList = warehouseList.stream().map(Warehouse::getId).toList();
        //获取仓库操作人id
        Map<Long, Set<Long>> operatorIdMap = warehouseOperationService.getOperatorId(warehouseIdList);
        //过滤掉失效的数据
        List<WarehouseCache> warehouseCaches = BeanUtil.copyNew(warehouseList, WarehouseCache.class);
        //填充操作人id
        warehouseCaches.forEach(item -> item.setOperationIds(operatorIdMap.get(item.getId())));
        //初始化缓存
        warehouseCacheRepertory.initWarehouse(warehouseCaches);
        log.info("<<<<<<<<<<<<<<<<<<<<< 初始化仓库数据至redis【完成】，总耗时：{}ms <<<<<<<<<<<<<<<<<<<<<", meter.sign());
    }


    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 填充仓库信息
     * 该方法用于填充仓库信息，传入仓库信息
     *
     * @param warehouse 仓库信息
     */
    private void fillDefaultBinLocationId(Warehouse warehouse, List<BinLocation> binLocationList) {
        //根据类型获取默认库位id
        Map<String, Long> map = ObjectUtil.toMap(binLocationList, BinLocation::getType, BinLocation::getId);
        map.forEach((key, value) -> {
            switch (BinTypeEnum.getByBinType(key)) {
                case OTB_CONVERT -> warehouse.setDefaultOtbConvertBinLocationId(value);

                case OTB_MULTIBOX -> warehouse.setDefaultOtbMultiboxBinLocationId(value);

                case OTB_ASSEMBLY -> warehouse.setDefaultOtbAssemblyBinLocationId(value);

                case OTB_OUTSIDE -> warehouse.setDefaultOtbOutsideBinLocationId(value);

                case OTB_READYTOGO -> warehouse.setDefaultOtbReadytogoBinLocationId(value);

                case OTC_READYTOGO -> warehouse.setDefaultOtcReadytogoBinLocationId(value);

                case OTC_CONVERT -> warehouse.setDefaultOtcConvertBinLocationId(value);

                case OTC_MULTIBOX -> warehouse.setDefaultOtcMultiboxBinLocationId(value);

                case OTC_ASSEMBLY -> warehouse.setDefaultOtcAssemblyBinLocationId(value);

                case OTC_OUTSIDE -> warehouse.setDefaultOtcOutsideBinLocationId(value);

                default -> throw new BusinessException(StringUtil.format("binType:{} is not exist", value));
            }
        });
    }

    /**
     * 更新仓库缓存
     * 该方法用于更新仓库缓存，传入仓库信息
     *
     * @param warehouse 仓库信息
     */
    private void updateRedis(Warehouse warehouse) {
        //获取仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(warehouse.getId());
        Validate.notNull(warehouseCache, "warehouse is not exist");
        copyNotEmpty(warehouse, warehouseCache);
        //获取操作人id
        Map<Long, Set<Long>> operatorIdMap = warehouseOperationService.getOperatorId(Lists.arrayList(warehouse.getId()));
        //填充操作人
        warehouseCache.setOperationIds(operatorIdMap.get(warehouse.getId()));
        //添加仓库缓存
        warehouseCacheRepertory.addWarehouse(warehouseCache);
    }

    /**
     * 更新仓库缓存
     * 该方法用于更新仓库缓存，传入仓库信息
     *
     * @param warehouse 仓库信息
     */
    private void updateRedis(Warehouse warehouse, Set<Long> operatorIdList) {
        WarehouseCache warehouseCache = BeanUtil.copyNew(warehouse, WarehouseCache.class);
        warehouseCache.setOperationIds(operatorIdList);
        warehouseCacheRepertory.addWarehouse(warehouseCache);
    }

    /**
     * 构建 仓库持久化实体
     * 该方法用于构建仓库持久化实体，传入仓库基本信息
     *
     * @param createParam 仓库基础信息
     * @return 仓库持久化实体
     */
    private Warehouse buildWarehouse(WarehouseCreateParam createParam) {
        // 构建warehouse对象
        Warehouse warehouse = BeanUtil.copyNew(createParam, Warehouse.class);
        // 填充有效无效
        warehouse.setActiveFlag(Boolean.TRUE);
        // 编码code转大写
        warehouse.setCode(warehouse.getCode());
        // 生成RefNum
        warehouse.setRefNum(FormatUtil.generateRefNumNoWarehouse(RefNumTypeEnum.WAREHOUSE));
        //生成仓库id
        warehouse.setId(IdWorker.getId());
        //返回仓库对象
        return warehouse;
    }


    /**
     * 构建仓库基础信息VO对象
     *
     * @param entity 仓库基础信息对象
     * @return 返回包含详细信息的仓库基础信息VO对象
     */
    private WarehouseVO buildWarehouseVO(Warehouse entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 仓库基础信息VO对象
        WarehouseVO warehouseVO = Converters.get(WarehouseConverter.class).toVO(entity);
        //填充库位信息
        WarehouseUtils.execute(warehouseVO.getId(), () -> fillBinLocationName(warehouseVO));
        //仓库对象vo
        return warehouseVO;
    }

    /**
     * 填充默认库位名称
     * 该方法用于填充库位名称，传入仓库vo对象
     *
     * @param warehouseVO 仓库vo对象
     */
    private void fillBinLocationName(WarehouseVO warehouseVO) {
        //获取默认库位
        List<BinLocation> binLocations = binLocationService.listByWarehouse(warehouseVO.getId());
        //默认库位判空
        if (ObjectUtil.isEmpty(binLocations)) {
            return;
        }
        //获取默认库位
        List<BinLocation> list = binLocations.stream().filter(binLocation -> ObjectUtil.isNotEmpty(binLocation.getType())).toList();
        //判空
        if (ObjectUtil.isNotEmpty(list)) {
            //填充默认库位
            for (BinLocation binLocation : list) {
                //OBWAssembly
                if (ObjectUtil.equals(BinTypeEnum.OTB_ASSEMBLY.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTBAssemblyBinLocationId(binLocation.getId());
                    warehouseVO.setDefaultOTBAssemblyBinLocationName(binLocation.getLocationName());
                    continue;
                }
                //OBWConvert
                if (ObjectUtil.equals(BinTypeEnum.OTB_CONVERT.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTBConvertBinLocationName(binLocation.getLocationName());
                    warehouseVO.setDefaultOTBConvertBinLocationId(binLocation.getId());
                    continue;
                }
                //OBWMultiBox
                if (ObjectUtil.equals(BinTypeEnum.OTB_MULTIBOX.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTBMultiBoxBinLocationName(binLocation.getLocationName());
                    warehouseVO.setDefaultOTBMultiBoxBinLocationId(binLocation.getId());
                    continue;
                }
                //OTBReadyToGo
                if (ObjectUtil.equals(BinTypeEnum.OTB_READYTOGO.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTBReadyToGoBinLocationId(binLocation.getId());
                    warehouseVO.setDefaultOTBReadyToGoBinLocationName(binLocation.getLocationName());
                    continue;
                }
                //OTBOutSide
                if (ObjectUtil.equals(BinTypeEnum.OTB_OUTSIDE.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTBOutSideBinLocationId(binLocation.getId());
                    warehouseVO.setDefaultOTBOutSideBinLocationName(binLocation.getLocationName());
                    continue;
                }
                //OCWAssembly
                if (ObjectUtil.equals(BinTypeEnum.OTC_ASSEMBLY.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTCAssemblyBinLocationName(binLocation.getLocationName());
                    warehouseVO.setDefaultOTCAssemblyBinLocationId(binLocation.getId());
                    continue;
                }
                //OCWConvert
                if (ObjectUtil.equals(BinTypeEnum.OTC_CONVERT.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTCConvertBinLocationName(binLocation.getLocationName());
                    warehouseVO.setDefaultOTCConvertBinLocationId(binLocation.getId());
                    continue;
                }
                //OCWMultiBox
                if (ObjectUtil.equals(BinTypeEnum.OTC_MULTIBOX.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTCMultiBoxBinLocationName(binLocation.getLocationName());
                    warehouseVO.setDefaultOTCMultiBoxBinLocationId(binLocation.getId());
                    continue;
                }
                //OTCReadyToGo
                if (ObjectUtil.equals(BinTypeEnum.OTC_READYTOGO.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTCReadyToGoBinLocationId(binLocation.getId());
                    warehouseVO.setDefaultOTCReadyToGoBinLocationName(binLocation.getLocationName());
                    continue;
                }
                //OTCOutSide
                if (ObjectUtil.equals(BinTypeEnum.OTC_OUTSIDE.getBinType(), binLocation.getType())) {
                    warehouseVO.setDefaultOTCOutSideBinLocationId(binLocation.getId());
                    warehouseVO.setDefaultOTCOutSideBinLocationName(binLocation.getLocationName());
                }

            }

        }

    }

}
