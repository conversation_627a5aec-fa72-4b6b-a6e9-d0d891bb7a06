package cn.need.cloud.biz.converter.profile;

import cn.need.cloud.biz.client.dto.proflie.ProfilePartnerDTO;
import cn.need.cloud.biz.model.entity.setting.ProfilePartner;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 企业伙伴档案 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
public class ProfilePartnerConverter extends AbstractModelConverter<ProfilePartner, ProfilePartnerVO, ProfilePartnerDTO> {

}
