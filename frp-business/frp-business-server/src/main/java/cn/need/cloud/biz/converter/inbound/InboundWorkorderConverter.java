package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundWorkorderDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.vo.inbound.workorder.InboundWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库工单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundWorkorderConverter extends AbstractModelConverter<InboundWorkorder, InboundWorkorderVO, InboundWorkorderDTO> {

}
