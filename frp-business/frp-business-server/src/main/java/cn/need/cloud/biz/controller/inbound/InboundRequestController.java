package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundRequestConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestCreateParam;
import cn.need.cloud.biz.model.param.inbound.update.InboundRequestUpdateParam;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 入库请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-request")
@Tag(name = "入库请求")
public class InboundRequestController extends AbstractRestController<InboundRequestService, InboundRequest, InboundRequestConverter, InboundRequestVO> {

    @Operation(summary = "新增入库请求", description = "接收入库请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundRequestCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam).getId());
    }

    @Operation(summary = "修改入库请求", description = "接收入库请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundRequestUpdateParam updateParam) {

        service.updateByParam(updateParam);
        // 返回结果
        return success();
    }

    @Operation(summary = "根据id获取入库请求详情", description = "根据数据主键id，从数据库中获取其对应的入库请求详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库请求详情
        InboundRequestVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取入库请求详情", description = "根据数据RefNum，从数据库中获取其对应的入库请求详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InboundRequestVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取入库请求详情
        InboundRequestVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundRequestQuery> search) {

        // 获取入库请求分页
        PageData<InboundRequestPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "下拉列表", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "数据主键id", required = true) InboundRequestQuery query) {

        return success(service.distinctValue(query));
    }
}
