package cn.need.cloud.biz.provider.base;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.client.dto.req.base.BaseLogisticAndTransactionPartnerReqDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.upms.cache.bean.TenantCache;
import cn.need.cloud.upms.cache.util.TenantCacheUtil;
import cn.need.cloud.upms.client.api.TenantClient;
import cn.need.cloud.upms.client.dto.TenantDTO;
import cn.need.cloud.upms.client.dto.TenantQueryDTO;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.util.ApiUtil;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */

public class TenantUtil {


    public static void fillTenant(TenantReqDTO transactionPartner) {
        if (transactionPartner == null) {
            throw new BusinessException("transactionPartner can not null");
        }
        fillTenant(List.of(transactionPartner));
    }

    public static void fillTenant(List<TenantReqDTO> transactionPartners) {

        TenantClient tenantClient = SpringUtil.getBean(TenantClient.class);
        if (ObjectUtil.isEmpty(transactionPartners)) {
            throw new BusinessException("transactionPartner can not null");
        }
        //获取租d户code
        Set<String> tenantCodeList = transactionPartners.stream()
                .map(TenantReqDTO::getRefNum)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toSet());
        //获取租户abbrName
        Set<String> abbrNameList = transactionPartners.stream()
                .map(TenantReqDTO::getAbbrName)
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toSet());
        //创建client请求对象
        TenantQueryDTO queryDTO = new TenantQueryDTO(
                tenantCodeList,
                abbrNameList
        );
        //获取租d户信息
        if (tenantClient == null) {
            throw new BusinessException("TenantClient bean is null, please check RPC/Feign configuration");
        }

        Result<List<TenantDTO>> result = tenantClient.list(queryDTO);
        //解析返回结果
        List<TenantDTO> tenantList = ApiUtil.getResultData(result);
        //遍历租户信息
        tenantList.forEach(item -> {
            tenantCodeList.remove(item.getTenantCode());
            abbrNameList.remove(item.getAbbrName());
        });
        //校验租户
        Validate.isTrue(
                ObjectUtil.isEmpty(tenantCodeList) && ObjectUtil.isEmpty(abbrNameList),
                String.format("The tenant code %s does not exist", tenantCodeList)
        );
        //根据租户code映射租户id
        Map<String, Long> map = ObjectUtil.toMap(tenantList, TenantDTO::getTenantCode, TenantDTO::getId);
        //根据租户abbrName映射租户id
        Map<String, Long> abbrNameMap = ObjectUtil.toMap(tenantList, TenantDTO::getAbbrName, TenantDTO::getId);
        //根据租户code映射租户id
        transactionPartners.forEach(item -> {
            item.setTenantId(map.get(item.getRefNum()));
            if (ObjectUtil.isEmpty(item.getTenantId())) {
                item.setTenantId(abbrNameMap.get(item.getAbbrName()));
            }
        });
    }

    public static void fillTenant(TenantReqDTO... transactionPartners) {
        if (ObjectUtil.isEmpty(transactionPartners)) {
            throw new BusinessException("transactionPartners cannot be null or empty");
        }

        // 检查数组中是否包含null元素
        for (TenantReqDTO dto : transactionPartners) {
            if (dto == null) {
                throw new BusinessException("transactionPartner中存在null元素");
            }
        }

        List<TenantReqDTO> list = CollUtil.newArrayList(transactionPartners);

        fillTenant(list);

    }

    public static void fillTenant(BaseLogisticAndTransactionPartnerReqDTO reqDTO) {
        if (reqDTO == null) {
            throw new BusinessException("reqDTO不能为null");
        }

        TenantReqDTO logisticPartner = reqDTO.getLogisticPartner();
        TenantReqDTO transactionPartner = reqDTO.getTransactionPartner();

        if (logisticPartner == null || transactionPartner == null) {
            throw new BusinessException("logisticPartner OR transactionPartner can not be null");
        }

        fillTenant(logisticPartner, transactionPartner);
    }

    public static void fillTenantInfo(TenantReqDTO... tenants) {
        if (ObjectUtil.isEmpty(tenants)) {
            throw new BusinessException("tenants不能为null或空");
        }

        // 检查数组中是否包含null元素
        for (TenantReqDTO tenant : tenants) {
            if (tenant == null) {
                throw new BusinessException("tenants中存在null元素");
            }
        }

        fillTenantInfo(Lists.arrayList(tenants));
    }

    public static void fillTenantInfo(List<TenantReqDTO> tenantList) {
        if (ObjectUtil.isEmpty(tenantList)) {
            throw new BusinessException("tenantList不能为null或空");
        }

        // 检查数组中是否包含null元素
        for (TenantReqDTO tenant : tenantList) {
            if (tenant == null) {
                throw new BusinessException("tenantList中存在null元素");
            }
        }

        //租户id
        Set<Long> tenantIdList = tenantList.stream()
                .map(TenantReqDTO::getTenantId)
                .collect(Collectors.toSet());
        //获取租户缓存
        List<TenantCache> tenantCaches = TenantCacheUtil.listByIds(tenantIdList);
        //根据租户id映射租户信息
        Map<Long, TenantCache> tenantMap = ObjectUtil.toMap(tenantCaches, TenantCache::getId);
        //遍历租户信息
        tenantList.forEach(item -> {
            TenantCache tenantCache = tenantMap.get(item.getTenantId());
            if (ObjectUtil.isNotEmpty(tenantCache)) {
                item.setRefNum(tenantCache.getTenantCode());
                item.setAbbrName(item.getAbbrName());
            }
        });
    }
}
