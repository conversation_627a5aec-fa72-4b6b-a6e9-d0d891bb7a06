package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPackageLabelDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPackageLabel;
import cn.need.cloud.biz.model.vo.oco.OcoPackageLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO包裹标签表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPackageLabelConverter extends AbstractModelConverter<OcoPackageLabel, OcoPackageLabelVO, OcoPackageLabelDTO> {

}



