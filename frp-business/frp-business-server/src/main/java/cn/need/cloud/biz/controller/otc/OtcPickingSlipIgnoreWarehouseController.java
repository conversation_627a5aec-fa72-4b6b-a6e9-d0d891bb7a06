package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPickingSlipVO;
import cn.need.cloud.biz.service.otc.pickingslip.OtcPickingSlipService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * OTC拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-picking-slip/ignore-warehouse")
@Tag(name = "OTC拣货单-不区分仓库")
@AllArgsConstructor
@Slf4j
@Validated
public class OtcPickingSlipIgnoreWarehouseController extends AbstractController {

    private final OtcPickingSlipService service;

    @Operation(summary = "根据id获取OTC拣货单详情", description = "根据数据主键id，从数据库中获取其对应的OTC拣货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        OtcPickingSlipVO detailVo = WarehouseUtils.executeIgnore(() -> service.detailById(id));
        // 返回结果
        return Result.ok(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC拣货单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC拣货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC拣货单详情
        OtcPickingSlipVO detailVo = WarehouseUtils.executeIgnore(() -> service.detailByRefNum(refNum));
        // 返回结果
        return Result.ok(detailVo);
    }
}
