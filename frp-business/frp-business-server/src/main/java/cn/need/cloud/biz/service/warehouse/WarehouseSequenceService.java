package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.WarehouseSequence;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseSequenceCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseSequenceUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseSequenceQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseSequencePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseSequenceVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 生成仓库唯一refNum service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WarehouseSequenceService extends SuperService<WarehouseSequence> {

    /**
     * 根据参数新增生成仓库唯一refNum
     *
     * @param createParam 请求创建参数，包含需要插入的生成仓库唯一refNum的相关信息
     * @return 生成仓库唯一refNumID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(WarehouseSequenceCreateParam createParam);


    /**
     * 根据参数更新生成仓库唯一refNum
     *
     * @param updateParam 请求创建参数，包含需要更新的生成仓库唯一refNum的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(WarehouseSequenceUpdateParam updateParam);

    /**
     * 根据查询条件获取生成仓库唯一refNum列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个生成仓库唯一refNum对象的列表(分页)
     */
    List<WarehouseSequencePageVO> listByQuery(WarehouseSequenceQuery query);

    /**
     * 根据查询条件获取生成仓库唯一refNum列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个生成仓库唯一refNum对象的列表(分页)
     */
    PageData<WarehouseSequencePageVO> pageByQuery(PageSearch<WarehouseSequenceQuery> search);

    /**
     * 根据ID获取生成仓库唯一refNum
     *
     * @param id 生成仓库唯一refNumID
     * @return 返回生成仓库唯一refNumVO对象
     */
    WarehouseSequenceVO detailById(Long id);


}