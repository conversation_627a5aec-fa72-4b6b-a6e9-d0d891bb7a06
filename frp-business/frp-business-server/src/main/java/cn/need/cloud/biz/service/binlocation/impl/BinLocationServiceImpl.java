package cn.need.cloud.biz.service.binlocation.impl;

import cn.hutool.core.collection.CollUtil;
import cn.need.cloud.biz.cache.BinLocationCacheRepertory;
import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.ExceptionConstant;
import cn.need.cloud.biz.client.constant.enums.base.ShowLogEnum;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.WarehousePrefixEnum;
import cn.need.cloud.biz.converter.binlocation.BinLocationConverter;
import cn.need.cloud.biz.mapper.binlocation.BinLocationMapper;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailListQuery;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.base.BaseCheckOrderVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.binlocation.*;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.*;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.need.framework.common.core.lang.ObjectUtil.isEmpty;

/**
 * <p>
 * 库位服务实现类
 * </p>
 * <p>
 * 该类实现了库位相关的业务逻辑，包括库位的创建、更新、查询、删除等功能。
 * 库位（BinLocation）是仓库中存放产品的具体位置，支持不同类型的库位管理，
 * 如虚拟库位、默认库位等，并提供库位产品移动、库位状态管理等功能。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Service
@DS("master")
public class BinLocationServiceImpl extends SuperServiceImpl<BinLocationMapper, BinLocation> implements BinLocationService {

    /**
     * 库位详情服务，用于管理库位中存放的产品详情信息
     */
    @Resource
    private BinLocationDetailService binLocationDetailService;

    /**
     * 库位详情锁定服务，用于管理库位中被锁定的产品
     */
    @Resource
    private BinLocationDetailLockedService binLocationDetailLockedService;

    /**
     * 产品版本服务，用于获取和管理产品版本信息
     */
    @Resource
    private ProductVersionService productVersionService;

    /**
     * 库位缓存仓库，用于存储和获取库位缓存信息
     */
    @Resource
    private BinLocationCacheRepertory binLocationCacheRepertory;

    /**
     * 出库请求服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtbRequestService otbRequestService;

    /**
     * 出库客户请求服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcRequestService otcRequestService;

    /**
     * 入库请求服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private InboundRequestService inboundRequestService;

    /**
     * 出库工单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtbWorkorderService otbWorkorderService;

    /**
     * 出库客户工单服务，使用懒加载避免循环依赖
     */
    @Resource
    @Lazy
    private OtcWorkorderService otcWorkorderService;

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    /**
     * 获取默认库位id
     *
     * @param binTypeEnum 类型
     * @return /
     */
    private static Long choseDefaultBinLocationIdByWarehouse(BinTypeEnum binTypeEnum) {
        Long warehouseId = WarehouseContextHolder.getWarehouseId();
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(warehouseId);
        Validate.notNull(warehouseCache, "BinType: {} warehouse can not null", binTypeEnum.getBinType());
        Long binLocationId = null;
        switch (binTypeEnum) {
            case OTC_READYTOGO -> binLocationId = warehouseCache.getDefaultOtcReadytogoBinLocationId();
            case OTB_READYTOGO -> binLocationId = warehouseCache.getDefaultOtbReadytogoBinLocationId();
            case OTC_ASSEMBLY -> binLocationId = warehouseCache.getDefaultOtcAssemblyBinLocationId();
            case OTB_ASSEMBLY -> binLocationId = warehouseCache.getDefaultOtbAssemblyBinLocationId();
            case OTC_CONVERT -> binLocationId = warehouseCache.getDefaultOtcConvertBinLocationId();
            case OTB_CONVERT -> binLocationId = warehouseCache.getDefaultOtbConvertBinLocationId();
            case OTC_OUTSIDE -> binLocationId = warehouseCache.getDefaultOtcOutsideBinLocationId();
            case OTB_OUTSIDE -> binLocationId = warehouseCache.getDefaultOtbOutsideBinLocationId();
            case OTC_MULTIBOX -> binLocationId = warehouseCache.getDefaultOtcMultiboxBinLocationId();
            case OTB_MULTIBOX -> binLocationId = warehouseCache.getDefaultOtbMultiboxBinLocationId();
            default -> throw new IllegalStateException("Unexpected value: " + binTypeEnum);
        }
        Validate.notNull(binLocationId, "Warehouse: {} not fount BinType: {} default BinLocation",
                warehouseCache.getName(),
                binTypeEnum.getBinType()
        );
        return binLocationId;
    }

    /**
     * 根据参数创建新库位
     * <p>
     * 该方法在事务中执行，确保数据一致性。创建库位时会进行以下步骤：
     * 1. 检查参数有效性
     * 2. 构建库位对象
     * 3. 插入库位记录
     * </p>
     *
     * @param createParam 库位创建参数，包含库位的基本信息
     * @return 创建的库位ID
     * @throws IllegalArgumentException 如果参数为空，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(BinLocationCreateParam createParam) {
        // 检查传入库位参数是否为空，如果为空则抛出异常
        if (isEmpty(createParam)) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }
        //组装库位持久化数据
        BinLocation binLocation = buildBinLocation(createParam);
        //持久化库位信息
        super.insert(binLocation);
        //返回库位id
        return binLocation.getId();
    }

    /**
     * 根据参数更新库位信息
     * <p>
     * 该方法在事务中执行，确保数据一致性。更新库位时会进行以下步骤：
     * 1. 检查参数有效性
     * 2. 验证库位是否存在
     * 3. 检查是否为默认库位（默认库位不允许修改）
     * 4. 转换参数为实体对象
     * 5. 更新数据库中的库位记录
     * 6. 更新库位缓存
     * </p>
     *
     * @param updateParam 库位更新参数，包含需要更新的字段
     * @return 更新的记录数
     * @throws IllegalArgumentException 如果参数为空或ID为空，则抛出异常
     * @throws BusinessException        如果找不到指定ID的库位或尝试修改默认库位，则抛出业务异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(BinLocationUpdateParam updateParam) {
        // 检查传入库位参数是否为空，如果为空则抛出异常
        if (isEmpty(updateParam) || isEmpty(updateParam.getId())) {
            throw new IllegalArgumentException("Parameter cannot be empty");
        }
        // 检查是否存在库位，如果为空则抛出异常
        if (isEmpty(getById(updateParam.getId()))) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocation", updateParam.getId()));
        }
        // 校验是否为默认库位
        if (Boolean.TRUE.equals(updateParam.getDefaultFlag())) {
            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, "Default bin location cannot be modified or deleted"));
        }
        // 获取库位转换器实例，用于将库位参数对象转换为实体对象
        BinLocationConverter converter = Converters.get(BinLocationConverter.class);
        // 将库位参数对象转换为实体对象
        BinLocation entity = converter.toEntity(updateParam);
        // 执行更新库位操作
        int update = super.update(entity);
        //更新缓存
        updateRedis(entity);
        return update;

    }

    /**
     * 根据工单查询条件获取库位ID集合
     * <p>
     * 该方法用于获取满足指定查询条件的库位ID集合。
     * </p>
     *
     * @param query 出库工单库位查询条件
     * @return 库位ID集合
     */
    @Override
    public List<BinLocationPageVO> listByQuery(BinLocationQuery query) {
        return mapper.listByQuery(query, null);
    }

    /**
     * 根据查询条件分页获取库位
     * <p>
     * 该方法按照指定的查询条件和分页参数查询库位列表。
     * 会进行以下处理：
     * 1. 处理库位名称（移除特殊字符）
     * 2. 设置模糊查询条件
     * 3. 获取产品版本ID列表
     * 4. 执行分页查询
     * </p>
     *
     * @param search 包含查询条件和分页参数的搜索对象
     * @return 包含分页信息的库位分页视图对象
     * <p>
     * TODO: 查询可能涉及多表联查，性能优化空间较大
     * 优化建议：考虑使用索引优化或缓存策略提升查询性能
     */
    @Override
    public PageData<BinLocationPageVO> pageByQuery(PageSearch<BinLocationQuery> search) {
        /* 优化建议: 当前实现存在以下问题：
         * 1. 查询可能涉及多表联查，性能优化空间较大
         * 2. 没有对查询条件进行全面的有效性检查
         * 3. 可能存在不必要的数据库查询
         *
         * 优化建议：
         * 1. 考虑使用索引优化或缓存策略提升查询性能
         * 2. 对查询条件进行更全面的验证，避免无效查询
         * 3. 如果可能，使用缓存来减少数据库访问
         * 4. 对于频繁使用的查询，可以考虑预先加载并缓存结果
         *
         * 示例优化代码：
         * // 检查查询条件是否有效
         * if (isInvalidQuery(search.getCondition())) {
         *     return new PageData<>(Collections.emptyList(), 0L, search.getCurrent(), search.getSize());
         * }
         *
         * // 尝试从缓存中获取结果
         * String cacheKey = generateCacheKey(search);
         * PageData<BinLocationPageVO> cachedResult = cacheService.get(cacheKey);
         * if (cachedResult != null) {
         *     return cachedResult;
         * }
         */
        //分页参数
        Page<BinLocation> page = Conditions.page(search, entityClass);
        //获取查询条件
        BinLocationQuery condition = search.getCondition();
        String locationName = condition.getLocationName();
        condition.setLocationName(isEmpty(locationName) ? locationName : FormatUtil.removeSpecialCharacters(locationName));
        //填充库位名称模糊查询
        locationNameLike(condition);
        //填充产品
        condition.setProductVersionIdList(getProductVersionId(condition));
        //分页列表
        List<BinLocationPageVO> dataList = mapper.listByQuery(condition, page);
        //返回分页列表
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取库位详情
     * <p>
     * 该方法用于获取指定ID的库位详细信息。
     * </p>
     *
     * @param id 库位ID
     * @return 库位视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定ID的库位，则抛出业务异常
     */
    @Override
    public BinLocationVO detailById(Long id) {
        BinLocation entity = getById(id);
        //  获取不到返回异常信息
        if (isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocation");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocation", id));
        }
        return buildBinLocationVO(entity);
    }

    @Override
    public List<BinLocationDetailVO> detailListByIds(BinLocationDetailListQuery query) {
        return BeanUtil.copyNew(binLocationDetailService.listByIds(query.getIdList()), BinLocationDetailVO.class);
    }

    /**
     * 根据参考编号获取库位详情
     * <p>
     * 该方法用于获取指定参考编号的库位详细信息。
     * </p>
     *
     * @param refNum 库位参考编号
     * @return 库位视图对象，包含详细信息
     * @throws BusinessException 如果找不到指定参考编号的库位，则抛出业务异常
     */
    @Override
    public BinLocationVO detailByRefNum(String refNum) {
        BinLocation entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + ExceptionConstant.BIN_LOCATION_NOT_EXIST);
        }
        return buildBinLocationVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer removeBinLocation(DeletedNoteParam deletedNoteParam) {
        //校验是否为默认库位
        BinLocation binLocation = super.getById(deletedNoteParam.getId());
        if (Boolean.TRUE.equals(binLocation.getDefaultFlag())) {
            //多语言
            throw new BusinessException(ExceptionConstant.MODIFIED_DELETE);
        }
        //  库位有产品
        if (binLocationDetailService.exist(deletedNoteParam.getId())) {
            throw new BusinessException("exist product  cannot be delete");
        }
        //删除库位详情
        binLocationDetailService.removeByBinLocationId(deletedNoteParam.getId());
        //删除缓存
        binLocationCacheRepertory.delBinLocation(StringUtil.toString(deletedNoteParam.getId()));
        // 删除库位
        return super.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote());
    }

    @Override
    public List<DropProVO> distinctValue(BinLocationQuery query) {
        return DropListUtil.dropProList(query.getColumnNameList(),
                BinLocation.class,
                columnNameList -> mapper.dropProList(columnNameList, query)
        );
    }

    @Override
    public List<BinLocation> listByWarehouse(Long id) {
        return super.lambdaQuery().eq(BinLocation::getWarehouseId, id).list();
    }

    @Override
    public Map<Long, BinLocationVO> binLocationByIds(List<Long> binLocationIds) {
        if (isEmpty(binLocationIds)) {
            return Collections.emptyMap();
        }
        List<BinLocationCache> caches = BinLocationCacheUtil.listByIds(binLocationIds).stream().filter(Objects::nonNull).toList();
        if (isEmpty(caches)) {
            return Collections.emptyMap();
        }
        // 缓存获取
        return StreamUtils.toMap(BeanUtil.copyNew(caches, BinLocationVO.class), BinLocationVO::getId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeBinByWarehouseId(Long id) {
        //根据仓库id删除库位
        removeBywarehouseId(id);
        //删除库位详情
        binLocationDetailService.removeByWarehouseId(id);
    }

    @Override
    public void removeBywarehouseId(Long id) {
        lambdaUpdate()
                .eq(BinLocation::getWarehouseId, id)
                .set(BinLocation::getRemoveFlag, DataState.ENABLED)
                .update();
    }

    @Override
    public List<BinLocation> insertDefaultBinLocation(Warehouse warehouse) {
        //构建默认库位
        List<BinLocation> binLocationList = buildDefaultBinLocation(warehouse);
        //插入默认库位
        super.insertBatch(binLocationList);
        return binLocationList;
    }

    @Override
    public Set<Long> getBinLocationIds(InboundPalletQuery condition) {
        //库位名称
        Set<String> locationNameList = condition.getLocationNameList();
        //数据判空
        if (isEmpty(locationNameList)) {
            return CollUtil.newHashSet();
        }
        //获取库位集合
        List<BinLocation> list = lambdaQuery().in(BinLocation::getLocationName, locationNameList).list();
        //判空校验,未查到数据则返回一个不可查询的id集合
        if (isEmpty(list)) {
            return CollUtil.newHashSet(-1L);
        }
        //返回库位id集合
        return list.stream().map(BinLocation::getId).collect(Collectors.toSet());
    }

    @Override
    public BinLocation findVirtualBinLocationByType(BinTypeEnum binTypeEnum) {
        Long binLocationId = choseDefaultBinLocationIdByWarehouse(binTypeEnum);
        BinLocation binLocation = getById(binLocationId);
        Validate.notNull(binLocation, "Not found BinLocation by type: " + binTypeEnum.getBinType());
        return binLocation;
    }


    @Override
    public Integer switchActive(Long id) {
        //获取库位信息
        BinLocation binLocation = getById(id);
        //校验是否为默认库位
        if (isEmpty(binLocation.getDefaultFlag())) {
            throw new BusinessException("default value cannot be Modified or Deleted !!!");
        }
        // 获取库位信息
        BinLocation location = getById(id);
        //当前状态为启用状态则禁用，为禁用状态则启用
        location.setActiveFlag(Boolean.TRUE.equals(location.getActiveFlag()) ? Boolean.FALSE : Boolean.TRUE);
        int update = super.update(location);
        BinLocationCache binLocationCache = BinLocationCacheUtil.getById(id);
        if (ObjectUtil.isNotEmpty(binLocationCache)) {
            binLocationCache.setActiveFlag(location.getActiveFlag());
            binLocationCacheRepertory.addBinLocation(binLocationCache);
        }
        //更新库位状态
        return update;
    }

    @Override
    public Map<Long, BinLocation> allVirtualBinLocationList() {
        return lambdaQuery()
                // 虚拟库位类型
                .eq(BinLocation::getDefaultFlag, true)
                .list()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    @Override
    public void switchActive(Long warehouseId, Boolean activeFlag) {
        List<BinLocation> binLocationList = listByWarehouseId(warehouseId, Boolean.FALSE.equals(activeFlag));
        Validate.notEmpty(binLocationList, "warehouseId:{} binLocation is not exist", warehouseId);
        //遍历库位集合
        binLocationList.forEach(item -> item.setActiveFlag(activeFlag));
        super.updateBatch(binLocationList);
        //更新缓存
        List<BinLocationCache> binLocationCacheList = BeanUtil.copyNew(binLocationList, BinLocationCache.class);
        binLocationCacheRepertory.addBinLocation(binLocationCacheList);
    }

    @Override
    public Boolean existUnfinishedOrder(BaseCheckOrderVO param) {
        Long warehouseId = param.getWarehouseId();
        Long binLocationId = param.getBinLocationId();
        //校验库位下是否存在未完成订单
        if (ObjectUtil.isNotEmpty(binLocationId)) {
            //otb
            Boolean otbFlag = otbWorkorderService.existUnfinishedOrder(binLocationId);
            //otc
            Boolean otcFlag = otcWorkorderService.existUnfinishedOrder(binLocationId);
            return otbFlag || otcFlag;
        }

        //校验仓库下是否存在未完成订单
        if (ObjectUtil.isNotEmpty(warehouseId)) {
            //otb
            Boolean otbFlag = otbRequestService.existUnfinishedOrder(warehouseId);
            //inbound
            Boolean inboundFlag = inboundRequestService.existUnfinishedOrder(warehouseId);
            //otc
            Boolean otcFlag = otcRequestService.existUnfinishedOrder(warehouseId);
            return otbFlag || inboundFlag || otcFlag;
        }
        return false;
    }

    @Override
    public List<BinLocation> listByWarehouseId(Long warehouseId, boolean activeFlag) {
        return lambdaQuery()
                .eq(BinLocation::getWarehouseId, warehouseId)
                .eq(BinLocation::getActiveFlag, activeFlag)
                .list();
    }

    @Override
    public List<BinLocationVO> detailByProduct(Long productId) {
        //获取库位id
        List<BinLocationDetail> binLocationDetailList = binLocationDetailService.listByProductId(productId);
        return buildBinLocationVO(binLocationDetailList);
    }

    @Override
    public List<BinLocationVO> detailByProductVersion(Long productVersionId) {
        List<BinLocationDetail> binLocationDetailList = binLocationDetailService.listByProductVersionId(productVersionId);
        return buildBinLocationVO(binLocationDetailList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void productMove(BinLocationMoveVO param) {
        List<BinLocationMoveDetailVO> detailList = param.getDetailList();
        //获取库位缓存
        BinLocationCache cache = BinLocationCacheUtil.getById(param.getSourceBinLocationId());
        //校验
        Validate.isTrue(
                !ObjectUtil.equal(param.getDestBinLocationId(), param.getSourceBinLocationId()),
                StringUtil.format("binlocation:{},the source location and the target location cannot be the same.", cache.getRefNum())
        );
        //库位产品校验
        dataValid(param);
        //根据产品版本id映射移动数量
        Map<Long, Integer> changeQtyMap = ObjectUtil.toMap(detailList, BinLocationMoveDetailVO::getProductVersionId, BinLocationMoveDetailVO::getChangeQty);
        //获取产品版本id集合
        List<Long> productVersionIdList = detailList.stream().map(BinLocationMoveDetailVO::getProductVersionId).toList();
        //获取源库位详情集合
        List<BinLocationDetail> sourceBinLocationDetailList = binLocationDetailService.list(productVersionIdList, param.getSourceBinLocationId());
        //根据产品版本id映射库位详情映射移动数量
        Map<Long, BinLocationDetail> sourceBinLocationDetailMap = ObjectUtil.toMap(sourceBinLocationDetailList, BinLocationDetail::getProductVersionId);
        //获取目标库位详情集合
        List<BinLocationDetail> destBinLocationDetailList = binLocationDetailService.list(productVersionIdList, param.getDestBinLocationId());
        //根据产品版本id映射库位详情
        Map<Long, BinLocationDetail> destBinLocationDetailMap = ObjectUtil.toMap(destBinLocationDetailList, BinLocationDetail::getProductVersionId);
        //遍历产品版本id集合
        List<BinLocationDetailChangeBO> list = buildBinLocationDetailChangeBO(
                productVersionIdList,
                sourceBinLocationDetailMap,
                destBinLocationDetailMap,
                changeQtyMap,
                param
        );
        //持久化库位详情
        List<BinLocationDetail> locationDetailList = list.stream()
                .map(BinLocationDetailChangeBO::getDest)
                .filter(dest -> isEmpty(dest.getId()))
                .toList();
        binLocationDetailService.insertBatch(locationDetailList);
        //移动产品
        binLocationDetailService.updateInStockByChange(list);
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 更新库位缓存
     * <p>
     * 该方法用于在库位信息更新后同步更新缓存中的数据。
     * 如果缓存中不存在该库位的信息，则不进行操作。
     * </p>
     *
     * @param entity 更新后的库位实体对象
     */
    private void updateRedis(BinLocation entity) {
        BinLocationCache binLocationCache = BinLocationCacheUtil.getById(entity.getId());
        if (isEmpty(binLocationCache)) {
            return;
        }
        copyNotEmpty(entity, binLocationCache);
        binLocationCacheRepertory.addBinLocation(binLocationCache);
    }

    /**
     * 校验
     *
     * @param param 参数
     */
    private void dataValid(BinLocationMoveVO param) {
        //校验库位是否为默认库位
        BinLocationCheckUtil.checkType(Lists.arrayList(param.getSourceBinLocationId(), param.getDestBinLocationId()));
        //获取产品版本id
        List<BinLocationMoveDetailVO> detailList = param.getDetailList();
        List<Long> productVersionIdList = detailList.stream().map(BinLocationMoveDetailVO::getProductVersionId).toList();
        //获取产品版本信息
        List<ProductVersionCache> productVersionCacheList = ProductVersionCacheUtil.listByIds(productVersionIdList);
        //判空
        Validate.notEmpty(productVersionCacheList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "productVersionCacheList:{}"));
        //根据产品版本id映射产品id
        Map<Long, Long> map = ObjectUtil.toMap(productVersionCacheList, ProductVersionCache::getId, ProductVersionCache::getProductId);
        //校验产品id
        List<BinLocationDetail> list = binLocationDetailService.list(
                param.getDestBinLocationId(),
                map.values()
        );
        //遍历库位详情
        detailList.forEach(item -> {
            Map<String, List<BinLocationDetail>> binlocationDetailMap = list.stream()
                    .collect(Collectors.groupingBy(obj -> StringUtil.format("{}/{}/{}", obj.getProductId(), obj.getBinLocationId())));
            String key = StringUtil.format("{}/{}/{}", map.get(item.getProductVersionId()), param.getDestBinLocationId());
            List<BinLocationDetail> binLocationDetailList = binlocationDetailMap.get(key);
            //校验库位下是否存在相同产品不同版本
            String message = StringUtil.format(
                    "Different versions of the same product cannot be stocked in the same storage location; productVersionId:{},binLocationId:{}",
                    item.getProductVersionId(),
                    param.getDestBinLocationId()
            );
            Validate.isTrue(isEmpty(binLocationDetailList) || binLocationDetailList.size() <= 1, message);
        });
    }

    /**
     * 构建库位详情变更BO
     *
     * @param productVersionIdList       产品版本id集合
     * @param sourceBinLocationDetailMap 源库位详情映射
     * @param destBinLocationDetailMap   目标库位详情映射
     * @param changeQtyMap               移动数量映射
     * @return 库位详情变更BO集合
     */
    @NotNull
    private List<BinLocationDetailChangeBO> buildBinLocationDetailChangeBO(
            List<Long> productVersionIdList,
            Map<Long, BinLocationDetail> sourceBinLocationDetailMap,
            Map<Long, BinLocationDetail> destBinLocationDetailMap,
            Map<Long, Integer> changeQtyMap,
            BinLocationMoveVO param) {
        //获取库位信息
        BinLocation binLocation = super.getById(param.getSourceBinLocationId());
        return productVersionIdList.stream().map(item -> {
            Validate.notEmpty(productVersionIdList, "productVersionIdList cannot be empty");
            //库位移动BO对象
            BinLocationDetailChangeBO changeBO = new BinLocationDetailChangeBO();
            //源库位详情
            BinLocationDetail sourceBinLocationDetail = sourceBinLocationDetailMap.get(item);
            changeBO.setSource(sourceBinLocationDetail);
            //目标库位详情判空
            BinLocationDetail destBinLocationDetail = destBinLocationDetailMap.get(item);
            if (isEmpty(destBinLocationDetail)) {
                destBinLocationDetail = new BinLocationDetail();
                destBinLocationDetail.setProductId(sourceBinLocationDetail.getProductId());
                destBinLocationDetail.setProductVersionId(sourceBinLocationDetail.getProductVersionId());
                destBinLocationDetail.setId(null);
                destBinLocationDetail.setBinLocationId(param.getDestBinLocationId());
                destBinLocationDetail.setInStockQty(0);
            }
            changeBO.setDest(destBinLocationDetail);
            changeBO.setChangeQty(changeQtyMap.get(item));
            changeBO.setChangeType(BinLocation.class.getSimpleName());
            RefTableBO refTableBO = new RefTableBO();
            refTableBO.setRefTableId(binLocation.getId());
            refTableBO.setRefTableShowRefNum(binLocation.getRefNum());
            refTableBO.setRefTableRefNum(binLocation.getRefNum());
            refTableBO.setRefTableName(BinLocation.class.getSimpleName());
            refTableBO.setRefTableShowName(ShowLogEnum.BIN_LOCATION.name());
            changeBO.setRefTable(refTableBO);
            return changeBO;
        }).toList();
    }

    /**
     * 构建库位VO
     *
     * @param binLocationDetailList 库位详情集合
     * @return 库位VO集合
     */
    @NotNull
    private List<BinLocationVO> buildBinLocationVO(List<BinLocationDetail> binLocationDetailList) {
        List<BinLocationDetailVO> detailList = BeanUtil.copyNew(binLocationDetailList, BinLocationDetailVO.class);
        // 填充产品
        ProductVersionCacheUtil.filledProductVersion(detailList);
        //填充锁定库存
        fillLockBinLocation(detailList);
        //获取库位id
        Set<Long> binLocationIdList = binLocationDetailList.stream().map(BinLocationDetail::getBinLocationId).collect(Collectors.toSet());
        //获取库位信息
        List<BinLocation> binLocationList = super.listByIds(binLocationIdList);
        //转换为VO
        List<BinLocationVO> voList = BeanUtil.copyNew(binLocationList, BinLocationVO.class);
        //根据库位id映射库位详情
        Map<Long, List<BinLocationDetailVO>> binLocationDetailMap = ObjectUtil.toMapList(detailList, BinLocationDetailVO::getBinLocationId);
        //遍历库位信息
        voList.forEach(item -> {
            //填充库位详情
            item.setDetailList(binLocationDetailMap.get(item.getId()));
        });
        return voList;
    }

    /**
     * 库位名称模糊查询
     *
     * @param condition 查询条件
     */
    private void locationNameLike(BinLocationQuery condition) {
        Set<String> locationNameList = condition.getLocationNameList();
        if (ObjectUtil.isNotEmpty(locationNameList)) {
            Set<String> list = locationNameList
                    .stream()
                    .filter(item -> item.contains("??")).map(FormatUtil::removeSpecialCharacters)
                    .collect(Collectors.toSet());
            if (ObjectUtil.isNotEmpty(list)) {
                condition.setLocationNameLikeList(list);
                condition.setLocationNameList(null);
            }
        }
    }

    /**
     * 填充锁定库存信息
     * 该方法用于填充锁定库存信息，传入库位详情集合
     *
     * @param binLocationDetailList 库位详情
     */
    private void fillLockBinLocation(List<BinLocationDetailVO> binLocationDetailList) {
        //判空
        if (isEmpty(binLocationDetailList)) {
            return;
        }
        //获取库位详情id
        List<Long> binLocationDetailIds = binLocationDetailList.stream().map(BinLocationDetailVO::getId).toList();
        //获取锁定库存
        List<BinLocationDetailLocked> binLocationDetailLockedList = binLocationDetailLockedService.listBinDetailIds(binLocationDetailIds);
        //填充锁定和可用库存数量
        fillLockAndAvailableQty(binLocationDetailList, binLocationDetailLockedList);
        //根据详情id映射锁定库位详情
        Map<Long, List<BinLocationDetailLocked>> binLocationDetailLockedMap = ObjectUtil.toMapList(binLocationDetailLockedList, BinLocationDetailLocked::getBinLocationDetailId);
        //填充锁定库存信息
        binLocationDetailList.forEach(item -> {
            //填充锁定库存信息
            List<BinLocationDetailLocked> locationDetailLockedList = binLocationDetailLockedMap.get(item.getId());
            item.setBinLocationDetailLockedList(BeanUtil.copyNew(locationDetailLockedList, BinLocationDetailLockedVO.class));
        });
    }

    /**
     * 获取可用库存
     *
     * @param binLocationDetailList       库位详情集合
     * @param binLocationDetailLockedList 锁定库位详情集合
     */
    private void fillLockAndAvailableQty(List<BinLocationDetailVO> binLocationDetailList, List<BinLocationDetailLocked> binLocationDetailLockedList) {
        //按照库位详情id映射锁定库存数量
        Map<Long, Integer> numMap = binLocationDetailLockedList.stream()
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getBinLocationDetailId, Collectors.summingInt(value -> value.getQty() - value.getFinishQty())));
        //填充锁定库存
        binLocationDetailList.forEach(item -> {
            //填充可用库存
            item.setLockedQty(numMap.getOrDefault(item.getId(), 0));
            //填充可用库存
            item.setAvailableQty(item.getInStockQty() - item.getLockedQty());
        });
    }

    /**
     * 获取产品id
     *
     * @param condition 查询条件
     */
    private Set<Long> getProductVersionId(BinLocationQuery condition) {
        //获取版本产品条件选择器
        LambdaQueryWrapper<ProductVersion> queryWrapper = new LambdaQueryWrapper<>();
        //开关
        boolean flag = false;
        //产品sku判空
        if (ObjectUtil.isNotEmpty(condition.getSupplierSkuList())) {
            queryWrapper.in(ProductVersion::getSupplierSku, condition.getSupplierSkuList());
            flag = true;
        }
        //upc
        if (ObjectUtil.isNotEmpty(condition.getUpcList())) {
            queryWrapper.in(ProductVersion::getUpc, condition.getUpcList());
            flag = true;
        }
        //产品refNum
        if (ObjectUtil.isNotEmpty(condition.getProductRefNumList())) {
            queryWrapper.in(ProductVersion::getRefNum, condition.getProductRefNumList());
            flag = true;
        }
        if (!flag) {
            return CollUtil.newHashSet();
        }
        //搜索版本产品
        List<ProductVersion> list = productVersionService.list(queryWrapper);
        //盘空
        if (isEmpty(list)) {
            return CollUtil.newHashSet(-1L);
        }
        //返回产品id
        return list.stream().map(ProductVersion::getId).collect(Collectors.toSet());
    }

    /**
     * 构建默认库位
     * 该方法用于构建默认库位，传入仓库id
     *
     * @return 默认库位列表
     */
    private List<BinLocation> buildDefaultBinLocation(Warehouse warehouse) {
        //库位容器
        List<BinLocation> binLocationList = Lists.arrayList();
        //构建默认库位
        for (BinTypeEnum binTypeEnum : BinTypeEnum.values()) {
            BinLocation binLocation = new BinLocation();
            //库位类型（用户可修改）
            binLocation.setBinType(binTypeEnum.getBinType().toUpperCase());
            //仓库分区
            binLocation.setWarehouseZoneType(binTypeEnum.getWarehouseZoneType().toUpperCase());
            //设置为默认
            binLocation.setDefaultFlag(Boolean.TRUE);
            //备注
            binLocation.setNote(binTypeEnum.getNote());
            //行
            binLocation.setLrow(binTypeEnum.getBinType().toUpperCase());
            //列
            binLocation.setLdepth("1");
            //层
            binLocation.setLlevel("1");
            //格
            binLocation.setLsplit("1");
            //填充仓库id
            binLocation.setWarehouseId(warehouse.getId());
            // 库位类型填充（用于不可修改）
            binLocation.setType(binTypeEnum.getBinType());
            // 生成流水号
            binLocation.setRefNum(FormatUtil.generateRefNumNoDate(RefNumTypeEnum.BIN_LOCATION, warehouse.getId(), WarehousePrefixEnum.PREFIX.getPrefix()));
            // 库位名称
            List<String> list = Lists.arrayList(warehouse.getCode(), binLocation.getLrow(), binLocation.getLdepth(), binLocation.getLlevel(), binLocation.getLsplit());
            //设置库位名称
            binLocation.setLocationName(StringUtils.join(list, StringPool.DASH));
            // 添加库位
            binLocationList.add(binLocation);
        }
        //返回默认库位集合
        return binLocationList;
    }

    /**
     * 构建库位VO对象
     *
     * @param entity 库位对象
     * @return 返回包含详细信息的库位VO对象
     */
    private BinLocationVO buildBinLocationVO(BinLocation entity) {
        if (isEmpty(entity)) {
            return null;
        }
        //持久化实体转为vo对象
        BinLocationVO binLocationVO = Converters.get(BinLocationConverter.class).toVO(entity);
        //获取库位详情数据
        List<BinLocationDetailVO> binLocationDetailList = binLocationDetailService.listByBinLocationId(binLocationVO.getId());
        // 填充产品
        ProductVersionCacheUtil.filledProductVersion(binLocationDetailList);
        //填充锁定库存
        fillLockBinLocation(binLocationDetailList);
        //填充库位详情 换到detail 实现
        binLocationVO.setDetailList(binLocationDetailList);
        // 返回包含详细信息的库位VO对象
        return binLocationVO;
    }

    /**
     * 构建库位持久化实体
     * 该方法用于构建库位持久化实体，传入库位信息 行、列、层、格
     *
     * @param insertVo 库位信息 行、列、层、格
     *                 <p>
     *                 优化建议:
     *                 1. 添加参数验证，确保创建的库位数据有效，如检查必填字段是否存在
     *                 2. 实现库位代码和名称的唯一性检查，避免创建重复库位
     *                 3. 对库位类型进行验证，确保其符合系统定义的类型
     *                 4. 添加日志记录，跟踪库位创建过程
     *                 5. 考虑使用工厂模式或构建者模式来构建复杂的库位对象
     */
    private BinLocation buildBinLocation(BinLocationCreateParam insertVo) {
        //获取当前仓库缓存
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(WarehouseContextHolder.getWarehouseId());
        //前端接收对象转为库位对象
        BinLocation binLocation = BeanUtil.copyNew(insertVo, BinLocation.class);
        // 生成refNum
        binLocation.setRefNum(FormatUtil.generateRefNumNoDate(RefNumTypeEnum.BIN_LOCATION, Objects.requireNonNull(warehouseCache).getId(), WarehousePrefixEnum.PREFIX.getPrefix()));
        if (ObjectUtil.isNotEmpty(binLocation.getBinType())) {
            binLocation.setBinType(binLocation.getBinType().toUpperCase());
        }
        //返回库位对象
        return binLocation;
    }

    /**
     * 实体类数据拷贝,只拷贝不为空
     * 拷贝原则：
     * 1.只拷贝名称和类型都相同的属性，即使jdk的原类型与包装类型，都会被看成不同的类型，如int与Integer
     * 2.拷贝的属性必须存在getter和setter方法
     *
     * @param orig 源对象
     * @param dest 目标对象
     */
    public void copyNotEmpty(Object orig, Object dest) {
        Class<?> origClass = orig.getClass();
        Field[] fieldList = origClass.getDeclaredFields();
        String[] ignoreList = Arrays.stream(fieldList).filter(item -> {
            try {
                item.setAccessible(Boolean.TRUE);
                return isEmpty(item.get(orig));
            } catch (IllegalAccessException e) {
                throw new BusinessException(e);
            }
        }).map(Field::getName).toArray(String[]::new);
        BeanUtil.copy(orig, dest, ignoreList);
    }

}
