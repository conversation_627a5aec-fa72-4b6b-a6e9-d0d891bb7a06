package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcPutawaySlipConverter;
import cn.need.cloud.biz.model.entity.otc.OtcPutawaySlip;
import cn.need.cloud.biz.model.param.base.update.PutawaySlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otc.putaway.OtcPutawaySlipPutawayParam;
import cn.need.cloud.biz.model.param.otc.update.putawayslip.OtcPutawaySlipPutAwayUpdateParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otc.putawayslip.OtcPutawaySlipQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcPutawaySlipPageVO;
import cn.need.cloud.biz.model.vo.otc.putawayslip.OtcPutawaySlipVO;
import cn.need.cloud.biz.service.otc.putawayslip.OtcPutawaySlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC上架单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-31
 */
@RestController
@RequestMapping("/api/biz/otc-putaway-slip")
@Tag(name = "OTC上架单")
public class OtcPutawaySlipController extends AbstractRestController<OtcPutawaySlipService, OtcPutawaySlip, OtcPutawaySlipConverter, OtcPutawaySlipVO> {

    @Operation(summary = "根据id获取OTC上架单详情", description = "根据数据主键id，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcPutawaySlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OTC上架单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcPutawaySlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "根据拣货单ID获取OTC上架单详情", description = "根据拣货单ID，从数据库中获取其对应的OTC上架单详情")
    @GetMapping(value = "/detail-by-picking-slip-id/{pickingSlipId}")
    public Result<OtcPutawaySlipVO> detailByPickingSlipId(@PathVariable("pickingSlipId") @Parameter(description = "拣货单ID", required = true) Long pickingSlipId) {
        // 返回结果
        return success(service.detailByPickingSlipId(pickingSlipId));
    }

    @Operation(summary = "获取OTC上架单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC上架单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcPutawaySlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcPutawaySlipQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "Cancel", description = "取消上架")
    @PostMapping(value = "/cancel")
    public Result<Boolean> cancel(@RequestBody @Valid PutawaySlipCancelUpdateParam param) {
        // 返回结果
        return success(service.cancel(param));
    }

    @Operation(summary = "PutAway", description = "上架")
    @PostMapping(value = "/put-away")
    public Result<Boolean> putAway(@RequestBody @Valid OtcPutawaySlipPutAwayUpdateParam param) {
        // 返回结果
        return success(service.putAway(param));
    }

    @Operation(summary = "Execute Putaway", description = "执行OTC上架操作")
    @PostMapping(value = "/putaway")
    public Result<Boolean> putaway(@RequestBody @Valid @Parameter(description = "上架参数") OtcPutawaySlipPutawayParam param) {

        // 执行上架操作
        boolean result = service.putaway(param);
        return success(result);
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }
}
