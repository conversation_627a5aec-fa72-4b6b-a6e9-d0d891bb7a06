package cn.need.cloud.biz.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * Javers 比较注解
 * <p>
 * 标记需要被 Javers 自动注册的类。
 * 可以用于实体类（Entity）或值对象（ValueObject）。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface JaversComparable {

    /**
     * 对象类型
     */
    Type value() default Type.VALUE_OBJECT;

    /**
     * 实体标识字段名（仅对 ENTITY 类型有效）
     */
    String idField() default "id";

    /**
     * 对象类型枚举
     */
    enum Type {
        /**
         * 实体对象（有唯一标识的对象）
         */
        ENTITY,
        
        /**
         * 值对象（无唯一标识的对象）
         */
        VALUE_OBJECT
    }
}
