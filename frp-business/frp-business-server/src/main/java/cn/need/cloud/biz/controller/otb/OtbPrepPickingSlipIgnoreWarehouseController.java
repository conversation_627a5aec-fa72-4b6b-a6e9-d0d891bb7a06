package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipVO;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPrepPickingSlipService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import cn.need.framework.starter.warehouse.util.WarehouseUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * OTB拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-prep-picking-slip/ignore-warehouse")
@Tag(name = "OTB拣货单-不区分仓库")
@AllArgsConstructor
@Slf4j
@Validated
public class OtbPrepPickingSlipIgnoreWarehouseController extends AbstractController {

    private final OtbPrepPickingSlipService service;

    @Operation(summary = "根据id获取OTB拣货单详情", description = "根据数据主键id，从数据库中获取其对应的OTB拣货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPrepPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        OtbPrepPickingSlipVO detailVo = WarehouseUtils.executeIgnore(() -> service.detailById(id));
        // 返回结果
        return Result.ok(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB拣货单详情", description = "根据数据RefNum，从数据库中获取其对应的OTB拣货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPrepPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC拣货单详情
        OtbPrepPickingSlipVO detailVo = WarehouseUtils.executeIgnore(() -> service.detailByRefNum(refNum));
        // 返回结果
        return Result.ok(detailVo);
    }
}
