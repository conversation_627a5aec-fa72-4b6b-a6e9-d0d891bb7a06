package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundPalletConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundPallet;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadPrintVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletVO;
import cn.need.cloud.biz.model.vo.page.InboundPalletPageVO;
import cn.need.cloud.biz.service.inbound.InboundPalletService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 入库单打托 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-pallet")
@Tag(name = "入库单打托")
public class InboundPalletController extends AbstractRestController<InboundPalletService, InboundPallet, InboundPalletConverter, InboundPalletVO> {

    @Operation(summary = "根据id删除入库单打托", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取入库单打托详情", description = "根据数据主键id，从数据库中获取其对应的入库单打托详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundPalletVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库单打托详情
        InboundPalletVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取入库单打托详情", description = "根据数据RefNum，从数据库中获取其对应的入库单打托详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InboundPalletVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取入库单打托详情
        InboundPalletVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库单打托分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库单打托列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundPalletPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundPalletQuery> search) {

        // 获取入库单打托分页
        PageData<InboundPalletPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "打托", description = "打托")
    @PostMapping(value = "/withReturn")
    public Result<InboundPalletUnloadPrintVO> pallet(@RequestBody @Parameter(description = "打托对象", required = true) InboundPalletUnloadVO inboundPalletUnloadVO) {

        return success(service.pallet(inboundPalletUnloadVO));
    }

    @Operation(summary = "打印入库打托单更新状态", description = "打印入库打托单更新状态")
    @PostMapping(value = "/mark-printed")
    public Result<Integer> markPrinted(@RequestBody PrintQuery printQuery) {

        service.markPrinted(printQuery);
        return success();
    }
}
