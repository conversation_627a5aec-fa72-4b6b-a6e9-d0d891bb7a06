package cn.need.cloud.biz.controller.binlocation;

import cn.need.cloud.biz.converter.binlocation.BinLocationConverter;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailListQuery;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.vo.base.BaseCheckOrderVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationMoveVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 库位 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@RestController
@RequestMapping("/api/biz/bin-location")
@Tag(name = "库位")
public class BinLocationController extends AbstractRestController<BinLocationService, BinLocation, BinLocationConverter, BinLocationVO> {

    @Operation(summary = "新增库位", description = "接收库位的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改库位", description = "接收库位的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "移动产品到指定库位", description = "移动产品到指定库位")
    @PostMapping(value = "/productMove")
    public Result<Boolean> productMove(@RequestBody BinLocationMoveVO param) {
        // 执行删除并返回结果
        service.productMove(param);
        return success(Boolean.TRUE);
    }

    @Operation(summary = "根据id获取库位详情", description = "根据数据主键id，从数据库中获取其对应的库位详情")
    @GetMapping(value = "/detail/{id}")
    public Result<BinLocationVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取库位详情
        BinLocationVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "库位详情列表", description = "根据数据主键id，从数据库中获取其对应的库位详情")
    @PostMapping(value = "/detail/list")
    public Result<List<BinLocationDetailVO>> listDetailByIds(@RequestBody BinLocationDetailListQuery id) {

        // 返回结果
        return success(service.detailListByIds(id));
    }

    @Operation(summary = "根据RefNum获取库位详情", description = "根据数据RefNum，从数据库中获取其对应的库位详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<BinLocationVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取库位详情
        BinLocationVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取库位详情", description = "根据数据RefNum，从数据库中获取其对应的库位详情")
    @GetMapping(value = "/detail-by-product-id/{productId}")
    public Result<List<BinLocationVO>> detailByProduct(@PathVariable("productId") @Parameter(description = "数据主键id", required = true) Long productId) {
        // 返回结果
        return success(service.detailByProduct(productId));
    }

    @Operation(summary = "根据RefNum获取库位详情", description = "根据数据RefNum，从数据库中获取其对应的库位详情")
    @GetMapping(value = "/detail-by-product-version-id/{productVersionId}")
    public Result<List<BinLocationVO>> detailByProductVersion(@PathVariable("productVersionId") @Parameter(description = "数据主键id", required = true) Long productVersionId) {
        // 返回结果
        return success(service.detailByProductVersion(productVersionId));
    }

    @Operation(summary = "获取库位分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的库位列表")
    @PostMapping(value = "/list")
    public Result<PageData<BinLocationPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<BinLocationQuery> search) {

        // 获取库位分页
        PageData<BinLocationPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "库位去重下拉", description = "库位去重下拉")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValue(@RequestBody @Parameter(description = "搜索条件参数", required = true) BinLocationQuery binLocationQuery) {

        // 返回结果
        return success(service.distinctValue(binLocationQuery));
    }

    @Operation(summary = "启用禁用", description = "启用禁用")
    @PostMapping(value = "/switch-active")
    public Result<Integer> switchActive(@RequestBody @Parameter(description = "更新库位状态 vo对象", required = true) IdCondition id) {

        Validate.notNull(id.getId(), "The id value cannot be null.");
        return Result.ok(service.switchActive(id.getId()));
    }

    @Operation(summary = "是否存在未完成工单", description = "是否存在未完成工单")
    @PostMapping(value = "/existUnfinishedOrder")
    public Result<Boolean> existUnfinishedOrder(@RequestBody @Parameter(description = "更新库位状态 vo对象", required = true) BaseCheckOrderVO param) {

        Validate.isTrue(ObjectUtil.isNotEmpty(param.getBinLocationId()) || ObjectUtil.isNotEmpty(param.getWarehouseId()), "The warehouseId or binLocationId value cannot be null.");
        return Result.ok(service.existUnfinishedOrder(param));
    }
}
