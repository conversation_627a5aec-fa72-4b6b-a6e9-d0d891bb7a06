package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductComponentConverter;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.param.product.update.ProductComponentCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.product.AssemblyProductListVO;
import cn.need.cloud.biz.model.vo.product.ComponentProductListVO;
import cn.need.cloud.biz.model.vo.product.ProductComponentVO;
import cn.need.cloud.biz.service.product.ProductComponentService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 产品组装 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product-component")
@Tag(name = "产品组装")
@Validated
public class ProductComponentController extends AbstractRestController<ProductComponentService, ProductComponent, ProductComponentConverter, ProductComponentVO> {


    @Operation(summary = "新建or修改产品组装", description = "接收产品组装的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/createOrUpdate")
    public Result<Integer> createOrUpdate(@Valid @RequestBody @Parameter(description = "数据对象", required = true) List<ProductComponentCreateOrUpdateParam> paramList) {

        // 返回结果
        return success(service.createOrUpdate(paramList));
    }

    @Operation(summary = "获取主件产品的配件component列表接口", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @GetMapping(value = "/component/list/{productId}")
    public Result<List<ComponentProductListVO>> componentList(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        return success(service.componentList(productId));
    }

    @Operation(summary = "获取配件产品的主件assembly列表接口", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @GetMapping(value = "/assembly/list/{productId}")
    public Result<List<AssemblyProductListVO>> assemblyList(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        return success(service.assemblyList(productId));
    }
}
