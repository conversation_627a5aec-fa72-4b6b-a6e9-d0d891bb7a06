package cn.need.cloud.biz.service.binlocation.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.inventory.BinLocationDetailLockedStatusEnum;
import cn.need.cloud.biz.converter.binlocation.BinLocationDetailLockedConverter;
import cn.need.cloud.biz.converter.inventory.InventoryBinLocationDetailLockedConverter;
import cn.need.cloud.biz.mapper.binlocation.BinLocationDetailLockedMapper;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailChangeBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedBatchCreateBO;
import cn.need.cloud.biz.model.bo.binlocation.BinLocationDetailLockedChangeBO;
import cn.need.cloud.biz.model.bo.common.RefTableBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationDetailLockedCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationDetailLockedUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailLockedQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.page.BinLocationDetailLockedPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.framework.common.core.collection.Lists;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import cn.need.framework.common.support.util.ApiUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 库位详情锁定服务实现类
 * </p>
 * <p>
 * 该类实现了库位详情锁定的核心业务逻辑，负责库位详情的锁定、释放和管理。
 * 主要功能包括：
 * 1. 库位详情锁定的创建和管理，包括单个锁定和批量锁定
 * 2. 库位详情锁定的查询功能，支持根据ID、参考编号、产品ID等多种方式查询
 * 3. 库位详情锁定的状态管理，包括完全锁定、部分释放和完全释放
 * 4. 库位详情锁定的变更管理，包括锁定数量的增加和减少
 * 5. 库位详情锁定的分页查询和详情展示
 * </p>
 * <p>
 * 库位详情锁定是库存管理的重要组成部分，用于防止库存超卖和确保库存的准确性。
 * 当系统需要为某个业务操作（如拣货、上架等）预留库存时，会创建库位详情锁定记录。
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
@Slf4j
public class BinLocationDetailLockedServiceImpl extends SuperServiceImpl<BinLocationDetailLockedMapper, BinLocationDetailLocked> implements BinLocationDetailLockedService {

    /**
     * 编号生成客户端，用于生成库位详情锁定的参考编号
     */
    @Resource
    private NumberGenerateClient numberGenerateClient;

    /**
     * 库位详情服务，用于获取库位详情信息
     * 使用@Lazy注解避免循环依赖
     */
    @Resource
    @Lazy
    private BinLocationDetailService binLocationDetailService;


    /**
     * 根据参数创建库位详情锁定
     * <p>
     * 该方法用于根据传入的参数创建库位详情锁定记录。
     * 主要流程包括：
     * 1. 验证参数是否为空
     * 2. 将参数转换为实体对象
     * 3. 初始化实体对象，包括生成参考编号
     * 4. 将实体对象插入数据库
     * 5. 返回创建的库位详情锁定ID
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据的一致性。
     * </p>
     *
     * @param createParam 库位详情锁定创建参数
     * @return 创建的库位详情锁定ID
     * @throws BusinessException 如果参数为空，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(BinLocationDetailLockedCreateParam createParam) {
        // 检查传入锁定 库位详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "BinLocationDetailLocked"));
        }

        // 获取锁定 库位详情转换器实例，用于将锁定 库位详情参数对象转换为实体对象
        BinLocationDetailLockedConverter converter = Converters.get(BinLocationDetailLockedConverter.class);

        // 将锁定 库位详情参数对象转换为实体对象并初始化
        BinLocationDetailLocked entity = initBinLocationDetailLocked(converter.toEntity(createParam));

        // 插入锁定 库位详情实体对象到数据库
        super.insert(entity);

        // 返回锁定 库位详情ID
        return entity.getId();
    }

    /**
     * 根据参数更新库位详情锁定
     * <p>
     * 该方法用于根据传入的参数更新库位详情锁定记录。
     * 主要流程包括：
     * 1. 验证参数是否为空，以及是否包含ID
     * 2. 将参数转换为实体对象
     * 3. 执行更新操作
     * 4. 返回更新的记录数量
     * </p>
     * <p>
     * 该方法在事务中执行，确保数据的一致性。
     * </p>
     *
     * @param updateParam 库位详情锁定更新参数
     * @return 更新的记录数量
     * @throws BusinessException 如果参数为空或不包含ID，则抛出异常
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(BinLocationDetailLockedUpdateParam updateParam) {
        // 检查传入锁定 库位详情参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "BinLocationDetailLocked"));
        }
        if (ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "id"));
        }

        // 获取锁定 库位详情转换器实例，用于将锁定 库位详情参数对象转换为实体对象
        BinLocationDetailLockedConverter converter = Converters.get(BinLocationDetailLockedConverter.class);

        // 将锁定 库位详情参数对象转换为实体对象
        BinLocationDetailLocked entity = converter.toEntity(updateParam);

        // 执行更新锁定 库位详情操作
        return super.update(entity);

    }

    /**
     * 根据查询条件获取库位详情锁定列表
     * <p>
     * 该方法用于根据指定的查询条件获取库位详情锁定列表，不带分页。
     * 查询条件可以包括库位详情ID、产品ID、锁定状态等多种条件。
     * </p>
     *
     * @param query 库位详情锁定查询条件对象
     * @return 库位详情锁定列表，包含库位详情锁定基本信息和关联的产品信息
     */
    @Override
    public List<BinLocationDetailLockedPageVO> listByQuery(BinLocationDetailLockedQuery query) {
        return mapper.listByQuery(query);
    }

    /**
     * 根据查询条件分页获取库位详情锁定列表
     * <p>
     * 该方法用于根据指定的查询条件和分页参数获取库位详情锁定列表。
     * 主要流程包括：
     * 1. 根据分页参数创建分页对象
     * 2. 执行分页查询并获取数据列表
     * 3. 返回带分页信息的数据列表
     * </p>
     *
     * @param search 包含查询条件和分页参数的对象
     * @return 带分页信息的库位详情锁定列表，包含库位详情锁定基本信息和关联的产品信息
     */
    @Override
    public PageData<BinLocationDetailLockedPageVO> pageByQuery(PageSearch<BinLocationDetailLockedQuery> search) {
        Page<BinLocationDetailLocked> page = Conditions.page(search, entityClass);
        List<BinLocationDetailLockedPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    /**
     * 根据ID获取库位详情锁定详情
     * <p>
     * 该方法用于根据库位详情锁定ID获取库位详情锁定的详细信息。
     * 如果指定ID的库位详情锁定不存在，则抛出业务异常。
     * </p>
     *
     * @param id 库位详情锁定ID
     * @return 库位详情锁定VO对象，包含库位详情锁定基本信息和关联的产品信息
     * @throws BusinessException 如果库位详情锁定不存在，则抛出业务异常
     */
    @Override
    public BinLocationDetailLockedVO detailById(Long id) {
        BinLocationDetailLocked entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocationDetailLocked");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationDetailLocked", id));
        }
        return buildBinLocationDetailLockedVO(entity);
    }

    /**
     * 根据参考编号获取库位详情锁定详情
     * <p>
     * 该方法用于根据库位详情锁定的参考编号（RefNum）获取库位详情锁定的详细信息。
     * 如果指定参考编号的库位详情锁定不存在，则抛出业务异常。
     * </p>
     *
     * @param refNum 库位详情锁定参考编号
     * @return 库位详情锁定VO对象，包含库位详情锁定基本信息和关联的产品信息
     * @throws BusinessException 如果库位详情锁定不存在，则抛出业务异常
     */
    @Override
    public BinLocationDetailLockedVO detailByRefNum(String refNum) {
        BinLocationDetailLocked entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("RefNum: " + refNum + " not found in BinLocationDetailLocked");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND_BY_FIELD, "BinLocationDetailLocked", "refNum", refNum));
        }
        return buildBinLocationDetailLockedVO(entity);
    }

    /**
     * 根据产品ID列表和库位详情ID列表获取库存锁定列表
     * <p>
     * 该方法用于根据产品ID列表和库位详情ID列表获取库存锁定列表。
     * 这个方法主要用于查询特定产品在特定库位上的锁定情况，便于库存管理和拣货操作。
     * </p>
     * <p>
     * 返回的库存锁定列表包含产品ID、库位详情ID、锁定数量、完成数量等信息，
     * 可以用于分析库存锁定状态和进行库存规划。
     * </p>
     *
     * @param productIds           产品ID列表
     * @param binLocationDetailIds 库位详情ID列表
     * @return 库存锁定VO对象列表，包含产品ID、库位详情ID、锁定数量等信息
     */
    @Override
    public List<InventoryBinLocationDetailLockedVO> listInventoryLockedByProductIdsAndBinLocationIds(List<Long> productIds, List<Long> binLocationDetailIds) {
        /* 优化建议: 当前实现存在以下问题：
         * 1. 调用的listEntitiesLockedByProductIdsAndBinLocationIds方法没有使用binLocationDetailIds参数
         * 2. 没有对查询结果进行缓存，可能导致重复查询
         * 3. 当产品ID列表很大时，可能会导致性能问题
         *
         * 优化建议：
         * 1. 修改listEntitiesLockedByProductIdsAndBinLocationIds方法实现，使其正确使用binLocationDetailIds参数
         * 2. 对于频繁查询的产品，考虑缓存查询结果
         * 3. 当产品ID列表超过一定数量时，考虑分批查询
         *
         * 示例优化代码：
         * if (ObjectUtil.isEmpty(productIds)) {
         *     return Collections.emptyList();
         * }
         *
         * // 尝试从缓存中获取
         * String cacheKey = generateCacheKey(productIds, binLocationDetailIds);
         * List<InventoryBinLocationDetailLockedVO> cachedResult = cacheService.get(cacheKey);
         * if (cachedResult != null) {
         *     return cachedResult;
         * }
         *
         * // 如果产品ID列表过大，则分批查询
         * if (productIds.size() > 500) {
         *     return batchQueryInventoryLocked(productIds, binLocationDetailIds);
         * }
         *
         * List<BinLocationDetailLocked> binLocationDetailLockeds = listEntitiesLockedByProductIdsAndBinLocationIds(productIds, binLocationDetailIds);
         * List<InventoryBinLocationDetailLockedVO> result = Converters.get(InventoryBinLocationDetailLockedConverter.class).toVO(binLocationDetailLockeds);
         *
         * // 将结果存入缓存
         * cacheService.put(cacheKey, result, CACHE_EXPIRY_TIME);
         *
         * return result;
         */
        List<BinLocationDetailLocked> binLocationDetailLockeds = listEntitiesLockedByProductIdsAndBinLocationIds(productIds, binLocationDetailIds);

        return Converters.get(InventoryBinLocationDetailLockedConverter.class).toVO(binLocationDetailLockeds);
    }

    /**
     * 根据产品ID列表和库位详情ID列表获取库位详情锁定实体列表
     * <p>
     * 该方法用于根据产品ID列表和库位详情ID列表获取库位详情锁定实体列表。
     * 与 listInventoryLockedByProductIdsAndBinLocationIds 方法类似，但返回的是实体对象而非VO对象。
     * </p>
     * <p>
     * 该方法主要用于内部处理，当需要直接操作实体对象时使用。
     * </p>
     *
     * @param productIds           产品ID列表
     * @param binLocationDetailIds 库位详情ID列表
     * @return 库位详情锁定实体列表
     */
    @Override
    public List<BinLocationDetailLocked> listEntitiesLockedByProductIdsAndBinLocationIds(List<Long> productIds, List<Long> binLocationDetailIds) {
        /* 优化建议: 当前实现存在以下问题：
         * 1. 方法名称包含"AndBinLocationIds"，但实际上没有使用binLocationDetailIds参数
         * 2. 当产品ID列表很大时，可能会导致数据库查询性能下降
         * 3. 没有对查询结果进行缓存，可能导致重复查询
         *
         * 优化建议：
         * 1. 修改方法名称或实现以保持一致性，如果不需要binLocationDetailIds参数，应该删除或重命名方法
         * 2. 当产品ID列表超过一定数量时，考虑分批查询
         * 3. 如果确实需要使用binLocationDetailIds参数，应该在查询条件中添加对应的过滤条件
         *
         * 示例优化代码：
         * if (ObjectUtil.isEmpty(productIds)) {
         *     return Collections.emptyList();
         * }
         *
         * // 构建查询条件
         * LambdaQueryWrapper<BinLocationDetailLocked> queryWrapper = new LambdaQueryWrapper<>()
         *         .in(BinLocationDetailLocked::getProductId, productIds)
         *         .in(BinLocationDetailLocked::getLockedStatus,
         *             BinLocationDetailLockedStatusEnum.LOCKED.getStatus(),
         *             BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus());
         *
         * // 如果提供了库位详情ID列表，则添加到查询条件中
         * if (ObjectUtil.isNotEmpty(binLocationDetailIds)) {
         *     queryWrapper.in(BinLocationDetailLocked::getBinLocationDetailId, binLocationDetailIds);
         * }
         *
         * return lambdaQuery().getBaseMapper().selectList(queryWrapper);
         */
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<BinLocationDetailLocked> binLocationDetailLockeds = lambdaQuery()
                .in(BinLocationDetailLocked::getProductId, productIds)
                .in(BinLocationDetailLocked::getLockedStatus, BinLocationDetailLockedStatusEnum.LOCKED.getStatus(), BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                .list();

        return binLocationDetailLockeds;
    }

    @Override
    public List<BinLocationDetailLocked> listBinDetailIds(List<Long> binLocationDetailIds) {
        // 判空
        if (ObjectUtil.isEmpty(binLocationDetailIds)) {
            return Lists.arrayList();
        }
        // 返回锁定库存集合
        return lambdaQuery()
                .in(BinLocationDetailLocked::getBinLocationDetailId, binLocationDetailIds)
                .ne(BinLocationDetailLocked::getLockedStatus, BinLocationDetailLockedStatusEnum.RELEASE.getStatus())
                .list();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void lockedBinLocationInventory(List<BinLocationDetailLocked> lockedList) {
        if (ObjectUtil.isEmpty(lockedList)) {
            return;
        }
        List<Long> binLocationDetailIdList = StreamUtils.distinctMap(lockedList, BinLocationDetailLocked::getBinLocationDetailId);
        // 库位详情id列表不存在
        if (ObjectUtil.isEmpty(binLocationDetailIdList)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_REQUIRED, "binLocationDetailIds"));
        }
        // 库位详情映射
        Map<Long, BinLocationDetail> binLocationDetailMap = StreamUtils.toMap(binLocationDetailService.listByIds(binLocationDetailIdList), IdModel::getId);

        // 未查到库位详情
        if (ObjectUtil.isEmpty(binLocationDetailMap)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationDetail", String.join(", ", binLocationDetailIdList.stream().map(String::valueOf).toList())));
        }

        // 锁定库存集合
        Map<Long, Integer> binLocationDetailInStoreLockedAvailQtyMap = this.lambdaQuery()
                .in(BinLocationDetailLocked::getBinLocationId, binLocationDetailIdList)
                .ne(BinLocationDetailLocked::getLockedStatus, BinLocationDetailLockedStatusEnum.RELEASE.getStatus())
                .list()
                .stream()
                // 根据库位详情id分组
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getBinLocationDetailId,
                        // 累加锁定库存数量
                        Collectors.mapping(obj -> obj.getQty() - obj.getFinishQty(), Collectors.summingInt(Integer::intValue))));

        // 校验库存是否足够
        List<BinLocationDetailLocked> noStockList = lockedList.stream()
                .filter(obj -> {
                    BinLocationDetail bl = binLocationDetailMap.get(obj.getBinLocationDetailId());
                    int lockedQty = binLocationDetailInStoreLockedAvailQtyMap.getOrDefault(obj.getBinLocationDetailId(), 0);
                    Integer inStockQty = bl.getInStockQty();
                    // 库存不足
                    return inStockQty - lockedQty < obj.getQty();
                })
                .toList();
        // 库存不足
        if (ObjectUtil.isNotEmpty(noStockList)) {
            String message = noStockList.stream()
                    .map(obj -> {
                        BinLocationCache binLocation = BinLocationCacheUtil.getById(obj.getBinLocationId());
                        ProductCache product = ProductCacheUtil.getById(obj.getProductId());
                        return String.format(
                                "Location [%s]: Product [%s/%s] has insufficient stock quantity",
                                binLocation.getLocationName(), product.getRefNum(), product.getSupplierSku()
                        );
                    })
                    .collect(Collectors.joining(StringPool.NEWLINE));

            throw new BusinessException(String.format(ErrorMessages.BUSINESS_RULE_VIOLATION, message));
        }
        this.insertBatch(lockedList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BinLocationDetailLocked> batchCreateIfAbsent(List<BinLocationDetailLockedBatchCreateBO> candidates) {
        Validate.notEmpty(candidates, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "locked candidates"));

        List<BinLocationDetailLocked> details = new ArrayList<>();

        Long binLocationId = candidates.get(0).getBinLocationDetail().getBinLocationId();
        List<Long> refIdList = candidates.stream()
                .map(BinLocationDetailLockedBatchCreateBO::getLockedRefTable)
                .map(RefTableBO::getRefTableId).
                distinct()
                .toList();

        List<BinLocationDetail> binLocationDetails = candidates.stream()
                .map(BinLocationDetailLockedBatchCreateBO::getBinLocationDetail)
                .toList();

        RedissonKit.getInstance().lock(RedisConstant.BIN_LOCATION_DETAIL_LOCKED_CREATE_LOCK_PREFIX + binLocationId, lock -> {

            List<Long> binLocationDetailIdList = StreamUtils.distinctMap(binLocationDetails, IdModel::getId);
            // 获取存在库里的锁信息
            List<BinLocationDetailLocked> inStoreLockedList = this.lambdaQuery()
                    // 虚拟库位
                    .eq(BinLocationDetailLocked::getBinLocationId, binLocationId)
                    .in(BinLocationDetailLocked::getBinLocationDetailId, binLocationDetailIdList)
                    .in(BinLocationDetailLocked::getRefTableId, refIdList)
                    .in(BinLocationDetailLocked::getLockedStatus, Arrays.asList(
                            BinLocationDetailLockedStatusEnum.LOCKED.getStatus(),
                            BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                    )
                    .list();

            // 存在库里的锁信息
            Map<String, List<BinLocationDetailLocked>> binLocationDetailLockMap
                    = StreamUtils.groupBy(inStoreLockedList, locked -> locked.getRefTableId() + StringPool.COLON + locked.getBinLocationDetailId());

            // 需要创建的锁信息
            List<BinLocationDetailLocked> needAddList = candidates.stream()
                    // readyToGo库位详情id没有Locked的就创建
                    .filter(pick -> !binLocationDetailLockMap.containsKey(pick.getLockedRefTable().getRefTableId() + StringPool.COLON + pick.getBinLocationDetail().getId()))
                    .map(pick -> {
                        BinLocationDetail detail = pick.getBinLocationDetail();
                        BinLocationDetailLocked locked = new BinLocationDetailLocked();
                        locked.setLockedStatus(BinLocationDetailLockedStatusEnum.LOCKED.getStatus());
                        locked.setFinishQty(0);
                        locked.setVersion(0L);

                        locked.setBinLocationDetailId(detail.getId());
                        locked.setBinLocationId(detail.getBinLocationId());
                        locked.setProductId(detail.getProductId());
                        locked.setProductVersionId(detail.getProductVersionId());
                        // 锁定设置0，后面更新
                        locked.setQty(0);
                        RefTableBO refTable = pick.getLockedRefTable();
                        locked.setRefTableId(refTable.getRefTableId());
                        locked.setRefTableName(refTable.getRefTableName());
                        locked.setRefTableRefNum(refTable.getRefTableRefNum());
                        locked.setRefTableShowName(refTable.getRefTableShowName());
                        locked.setRefTableShowRefNum(refTable.getRefTableShowRefNum());
                        return locked;
                    })
                    .collect(Collectors.groupingBy(
                            // 按照库位详情id进行合并，因为相同产品版本的库存锁定数量需要合并
                            locked -> locked.getRefTableId() + StringPool.COLON + locked.getBinLocationDetailId(),
                            Collectors.reducing((first, second) -> {
                                first.setQty(first.getQty() + second.getQty());
                                return first;
                            })))
                    .values()
                    .stream()
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .toList();
            // 新增
            this.insertBatch(needAddList);
            details.addAll(needAddList);
            details.addAll(inStoreLockedList);

        });
        return details;
    }

    @Override
    public void updateByChange(Collection<? extends BinLocationDetailChangeBO> changeList) {
        Validate.notEmpty(changeList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Location lock"));

        Set<BinLocationDetailLocked> lockedList = new HashSet<>();
        for (BinLocationDetailChangeBO move : changeList) {
            Validate.notNull(move.getSourceLock(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "source bin location detail locked"));
            Validate.notNull(move.getDestLock(), String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "destination bin location detail locked"));

            BinLocationDetailLocked locked = move.getSourceLock();
            int finishQty = move.moveLock();

            Validate.isTrue(finishQty <= locked.getQty(),
                    "{}, The released quantity cannot exceed the locked quantity, {} > {}",
                    move.toLockLog(), finishQty, locked.getQty()
            );

            locked.setLockedStatus(finishQty == locked.getQty()
                    ? BinLocationDetailLockedStatusEnum.RELEASE.getStatus()
                    : BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus());

            lockedList.add(move.getSourceLock());
            lockedList.add(move.getDestLock());
        }

        // 使用防死锁的批量更新方法
        updateBatchWithDeadlockPrevention(lockedList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void releaseAll(List<Long> ids) {
        List<BinLocationDetailLocked> lockedList = this.listByIds(ids);
        List<BinLocationDetailLockedChangeBO> changeList = lockedList.stream()
                .map(obj -> {
                    obj.setFinishQty(obj.getQty());
                    obj.setLockedStatus(BinLocationDetailLockedStatusEnum.RELEASE.getStatus());
                    BinLocationDetailLockedChangeBO change = new BinLocationDetailLockedChangeBO();
                    change.setSourceLock(obj);
                    change.setChangeQty(obj.getQty() - obj.getFinishQty());
                    return change;
                })
                .toList();
        // 更新
        this.updateByChange(changeList);
    }

    @Override
    public Map<Long, List<BinLocationDetailLocked>> groupByBinLocationIdList(List<Long> binLocationDetailIdList) {
        if (ObjectUtil.isEmpty(binLocationDetailIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(BinLocationDetailLocked::getBinLocationDetailId, binLocationDetailIdList)
                .in(BinLocationDetailLocked::getLockedStatus,
                        BinLocationDetailLockedStatusEnum.LOCKED.getStatus(), BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                .list()
                .stream()
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getBinLocationDetailId));
    }

    @Override
    public List<BinLocationDetailLocked> listByLocked(Collection<Long> lockedIdList) {
        // 返回锁的
        return ObjectUtil.isEmpty(lockedIdList) ? Collections.emptyList()
                : lambdaQuery()
                .in(BinLocationDetailLocked::getLockedStatus,
                        BinLocationDetailLockedStatusEnum.LOCKED.getStatus(), BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                .in(BinLocationDetailLocked::getId, lockedIdList)
                .list();
    }

    @Override
    public Map<Long, List<BinLocationDetailLocked>> groupByRefTableIdLocked(List<Long> refTableIdList) {
        // 返回锁的
        return ObjectUtil.isEmpty(refTableIdList) ? Collections.emptyMap()
                : lambdaQuery()
                .in(BinLocationDetailLocked::getLockedStatus,
                        BinLocationDetailLockedStatusEnum.LOCKED.getStatus(), BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                .in(BinLocationDetailLocked::getRefTableId, refTableIdList)
                .list()
                .stream()
                .collect(Collectors.groupingBy(BinLocationDetailLocked::getRefTableId));
    }

    @Override
    public List<BinLocationDetailLocked> listByRefTableIdLocked(Long refTableId) {
        return lambdaQuery()
                .in(BinLocationDetailLocked::getLockedStatus,
                        BinLocationDetailLockedStatusEnum.LOCKED.getStatus(), BinLocationDetailLockedStatusEnum.PART_RELEASE.getStatus())
                .eq(BinLocationDetailLocked::getRefTableId, refTableId)
                .list();
    }

    @Override
    public List<BinLocationDetailLocked> listByRefTableIds(Collection<Long> refTableId) {
        if (ObjectUtil.isNotEmpty(refTableId)) {
            return lambdaQuery()
                    .in(BinLocationDetailLocked::getRefTableId, refTableId)
                    .list();
        }
        return List.of();
    }

    /**
     * 初始化库位详情锁定对象
     * <p>
     * 该方法用于初始化库位详情锁定对象，设置必要的参数以确保其处于有效状态。
     * 主要操作包括：
     * 1. 验证实体对象是否为空
     * 2. 生成并设置参考编号（RefNum）
     * </p>
     * <p>
     * 参考编号是库位详情锁定的唯一标识符，用于在业务系统中识别和跟踪锁定记录。
     * </p>
     *
     * @param entity 库位详情锁定实体对象
     * @return 初始化后的库位详情锁定实体对象
     * @throws BusinessException 如果实体对象为空，则抛出异常
     */
    private BinLocationDetailLocked initBinLocationDetailLocked(BinLocationDetailLocked entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "BinLocationDetailLocked"));
        }

        // 生成RefNum
        entity.setRefTableRefNum(ApiUtil.getResultData(numberGenerateClient.generateNumber("BinLocationDetailLocked")));

        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建库位详情锁定VO对象
     * <p>
     * 该方法用于将库位详情锁定实体转换为库位详情锁定VO对象。
     * 如果输入的实体为空，则返回null。
     * </p>
     * <p>
     * 该方法使用转换器将实体对象转换为VO对象，便于向前端展示库位详情锁定信息。
     * </p>
     *
     * @param entity 库位详情锁定实体对象
     * @return 库位详情锁定VO对象，如果实体为空则返回null
     */
    private BinLocationDetailLockedVO buildBinLocationDetailLockedVO(BinLocationDetailLocked entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的锁定 库位详情VO对象
        return Converters.get(BinLocationDetailLockedConverter.class).toVO(entity);
    }

    /**
     * 防死锁的批量更新方法
     * <p>
     * 通过以下措施预防死锁：
     * 1. 按ID排序，确保锁定顺序一致
     * 2. 分批处理，减少锁竞争
     * 3. 重试机制，处理偶发死锁
     *
     * @param lockedList 需要更新的锁定记录集合
     */
    private void updateBatchWithDeadlockPrevention(Set<BinLocationDetailLocked> lockedList) {
        if (lockedList.isEmpty()) {
            return;
        }

        // 1. 按ID排序，确保所有事务以相同顺序获取锁，避免循环等待
        List<BinLocationDetailLocked> sortedList = lockedList.stream()
                .sorted(Comparator.comparing(BinLocationDetailLocked::getId))
                .collect(Collectors.toList());

        log.debug("Starting batch update with deadlock prevention for {} records", sortedList.size());

        // 2. 分批处理，减少单次批量操作的记录数，降低锁竞争
        int batchSize = 50; // 可根据实际情况调整
        List<List<BinLocationDetailLocked>> batches = partitionList(sortedList, batchSize);

        // 3. 逐批更新，带重试机制
        int totalUpdated = 0;
        for (int i = 0; i < batches.size(); i++) {
            List<BinLocationDetailLocked> batch = batches.get(i);
            int updated = updateBatchWithRetry(batch, i + 1, batches.size());
            totalUpdated += updated;
        }

        // 4. 验证更新结果
        Validate.isTrue(totalUpdated == lockedList.size(),
                String.format(ErrorMessages.BUSINESS_RULE_VIOLATION,
                        "Failed to update bin location detail locked quantity: expected %d, actual %d",
                        lockedList.size(), totalUpdated)
        );

        log.debug("Successfully completed batch update for {} records", totalUpdated);
    }

    /**
     * 带重试机制的批量更新
     *
     * @param batch        批次数据
     * @param batchNum     批次号
     * @param totalBatches 总批次数
     * @return 更新的记录数
     */
    private int updateBatchWithRetry(List<BinLocationDetailLocked> batch, int batchNum, int totalBatches) {
        int maxRetries = 3;
        int retryBaseDelay = 100; // 毫秒

        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                log.debug("Updating batch {}/{}, size: {}, attempt: {}",
                        batchNum, totalBatches, batch.size(), attempt);

                int updated = super.updateBatch(batch);

                if (updated != batch.size()) {
                    log.warn("Batch update incomplete: expected {}, actual {}", batch.size(), updated);
                }

                return updated;

            } catch (Exception e) {
                if (isDeadlockException(e) && attempt < maxRetries) {
                    int delay = retryBaseDelay * attempt; // 递增延迟
                    log.warn("Deadlock detected in batch {}/{}, attempt {}/{}, retrying in {}ms. Error: {}",
                            batchNum, totalBatches, attempt, maxRetries, delay, e.getMessage());

                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new BusinessException("Update interrupted", ie);
                    }
                } else {
                    log.error("Failed to update batch {}/{} after {} attempts",
                            batchNum, totalBatches, attempt, e);
                    throw new BusinessException("Failed to update bin location detail locked", e);
                }
            }
        }

        throw new BusinessException(String.format(
                "Failed to update batch %d/%d after %d attempts due to persistent deadlocks",
                batchNum, totalBatches, maxRetries));
    }

    /**
     * 判断是否为死锁异常
     *
     * @param e 异常对象
     * @return 是否为死锁异常
     */
    private boolean isDeadlockException(Exception e) {
        if (e == null) {
            return false;
        }

        String message = e.getMessage();
        if (message == null) {
            return false;
        }

        // 检查常见的死锁错误信息
        return message.contains("Deadlock found when trying to get lock") ||
                message.contains("deadlock") ||
                message.contains("Lock wait timeout exceeded") ||
                message.contains("DeadlockLoserDataAccessException");
    }

    /**
     * 将列表分割为指定大小的批次
     *
     * @param list      原始列表
     * @param batchSize 批次大小
     * @return 分割后的批次列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

}
