package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPackageLabelConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPackageLabel;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.vo.otb.pkg.BuildPackageShippingLabelVO;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageLabelVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentLabelVO;
import cn.need.cloud.biz.service.otb.pkg.OtbBuildPackageService;
import cn.need.cloud.biz.service.otb.pkg.OtbPackageLabelService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * OTB包裹标签 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-package-label")
@Tag(name = "OTB包裹标签")
@Slf4j
public class OtbPackageLabelController extends AbstractRestController<OtbPackageLabelService, OtbPackageLabel, OtbPackageLabelConverter, OtbPackageLabelVO> {

    @Resource
    private OtbBuildPackageService otbBuildPackageService;

    @Operation(summary = "构建shipmentLabel", description = "构建shipmentLabel")
    @PostMapping(value = "/buildPackageShippingLabel")
    public Result<OtbShipmentLabelVO> buildPackageShippingLabel(@RequestBody @Parameter(description = "发货单id", required = true) BuildPackageShippingLabelVO param) {

        // 返回结果
        return Result.ok(otbBuildPackageService.buildPackageShippingLabel(param.getOtbShipmentId(), param.getOtbPackageId()));
    }

    @Operation(summary = "打印包裹标签", description = "打印包裹标签")
    @PostMapping(value = "/markPrinted")
    public Result<Boolean> markPrinted(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        service.markPrinted(printQuery);
        // 返回结果
        return Result.ok(Boolean.TRUE);
    }
}
