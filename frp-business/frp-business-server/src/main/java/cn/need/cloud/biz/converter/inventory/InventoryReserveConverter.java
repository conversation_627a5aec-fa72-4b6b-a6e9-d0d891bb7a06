package cn.need.cloud.biz.converter.inventory;

import cn.need.cloud.biz.client.dto.inventory.InventoryReserveDTO;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.cloud.biz.model.vo.inventory.InventoryReserveVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 预留库存 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InventoryReserveConverter extends AbstractModelConverter<InventoryReserve, InventoryReserveVO, InventoryReserveDTO> {

}
