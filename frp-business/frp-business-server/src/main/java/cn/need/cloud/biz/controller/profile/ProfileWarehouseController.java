package cn.need.cloud.biz.controller.profile;

import cn.need.cloud.biz.converter.profile.ProfileWarehouseConverter;
import cn.need.cloud.biz.model.entity.setting.ProfileWarehouse;
import cn.need.cloud.biz.model.param.profile.create.ProfileWarehouseCreateParam;
import cn.need.cloud.biz.model.param.profile.update.ProfileWarehouseUpdateParam;
import cn.need.cloud.biz.model.query.profile.ProfileWarehouseQuery;
import cn.need.cloud.biz.model.vo.page.ProfileWarehousePageVO;
import cn.need.cloud.biz.model.vo.profile.ProfileBaseVO;
import cn.need.cloud.biz.model.vo.profile.ProfileWarehouseVO;
import cn.need.cloud.biz.service.profile.ProfileWarehouseService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 仓库档案 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/profile-warehouse")
@Tag(name = "仓库档案")
public class ProfileWarehouseController extends AbstractRestController<ProfileWarehouseService, ProfileWarehouse, ProfileWarehouseConverter, ProfileWarehouseVO> {

    @Operation(summary = "新增仓库档案", description = "接收仓库档案的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfileWarehouseCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改仓库档案", description = "接收仓库档案的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfileWarehouseUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除仓库档案", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取仓库档案详情", description = "根据数据主键id，从数据库中获取其对应的仓库档案详情")
    @GetMapping(value = "/detail/{id}")
    public Result<ProfileWarehouseVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取仓库档案详情
        ProfileWarehouseVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取仓库档案分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库档案列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProfileWarehousePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProfileWarehouseQuery> search) {

        // 获取仓库档案分页
        PageData<ProfileWarehousePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "获取仓库档案列表", description = "根据传入的搜索条件参数，仓库档案列表")
    @PostMapping(value = "/pro-list")
    public Result<List<ProfileWarehouseVO>> proWarehouseList() {
        //获取字典列表
        List<ProfileWarehouseVO> list = BeanUtil.copyNew(service.list(), ProfileWarehouseVO.class);
        // 返回结果
        return success(list);
    }

    @Operation(summary = "获取聚合后的配置", description = "根据传入的搜索条件参数，仓库档案列表")
    @GetMapping(value = "/all-merge-profile")
    public Result<List<ProfileBaseVO>> allMergeProfile() {

        //获取字典列表
        List<ProfileBaseVO> list = service.allMergeProfile();
        // 返回结果
        return success(list);
    }
}
