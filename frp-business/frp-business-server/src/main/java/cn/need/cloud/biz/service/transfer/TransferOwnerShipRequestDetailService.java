package cn.need.cloud.biz.service.transfer;

import cn.need.cloud.biz.model.entity.TransferOwnerShipRequestDetail;
import cn.need.framework.common.mybatis.base.SuperService;

import java.util.List;

/**
 * <p>
 * 货权转移详情 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface TransferOwnerShipRequestDetailService extends SuperService<TransferOwnerShipRequestDetail> {

    /**
     * 根据HeaderId获取列表
     *
     * @param id Header
     * @return /
     */
    List<TransferOwnerShipRequestDetail> listByHeaderId(Long id);
}