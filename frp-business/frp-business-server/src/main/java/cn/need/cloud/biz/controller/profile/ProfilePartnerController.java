package cn.need.cloud.biz.controller.profile;

import cn.need.cloud.biz.converter.profile.ProfilePartnerConverter;
import cn.need.cloud.biz.model.entity.setting.ProfilePartner;
import cn.need.cloud.biz.model.param.profile.create.ProfilePartnerCreateParam;
import cn.need.cloud.biz.model.param.profile.update.ProfilePartnerUpdateParam;
import cn.need.cloud.biz.model.query.profile.ProfilePartnerQuery;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerPageVO;
import cn.need.cloud.biz.model.vo.profile.ProfilePartnerVO;
import cn.need.cloud.biz.service.profile.ProfilePartnerService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 企业伙伴档案 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@RestController
@RequestMapping("/api/biz/profile-partner")
@Tag(name = "企业伙伴档案")
public class ProfilePartnerController extends AbstractRestController<ProfilePartnerService, ProfilePartner, ProfilePartnerConverter, ProfilePartnerVO> {

    @Operation(summary = "新增企业伙伴档案", description = "接收企业伙伴档案的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfilePartnerCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改企业伙伴档案", description = "接收企业伙伴档案的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) ProfilePartnerUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除企业伙伴档案", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取企业伙伴档案详情", description = "根据数据主键id，从数据库中获取其对应的企业伙伴档案详情")
    @GetMapping(value = "/detail/{id}")
    public Result<ProfilePartnerVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取企业伙伴档案详情
        ProfilePartnerVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取企业伙伴档案分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的企业伙伴档案列表")
    @PostMapping(value = "/list")
    public Result<PageData<ProfilePartnerPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<ProfilePartnerQuery> search) {

        // 获取企业伙伴档案分页
        PageData<ProfilePartnerPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
