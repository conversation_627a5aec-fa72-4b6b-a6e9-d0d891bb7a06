package cn.need.cloud.biz.controller.inbound;

import cn.need.cloud.biz.converter.inbound.InboundUnloadConverter;
import cn.need.cloud.biz.model.entity.inbound.InboundUnload;
import cn.need.cloud.biz.model.query.inbound.InboundUnloadQuery;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadPrintVO;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletUnloadVO;
import cn.need.cloud.biz.model.vo.inbound.putaway.InboundPutawaySlipVO;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundAggregatedUploadVO;
import cn.need.cloud.biz.model.vo.inbound.unload.InboundUnloadVO;
import cn.need.cloud.biz.model.vo.page.InboundUnloadPageVO;
import cn.need.cloud.biz.service.inbound.InboundUnloadService;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 入库工单卸货表 根据这个来生成上架单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/inbound-unload")
@Tag(name = "入库工单卸货表 根据这个来生成上架单")
public class InboundUnloadController extends AbstractRestController<InboundUnloadService, InboundUnload, InboundUnloadConverter, InboundUnloadVO> {

    @Operation(summary = "根据id获取入库工单卸货表 根据这个来生成上架单详情", description = "根据数据主键id，从数据库中获取其对应的入库工单卸货表 根据这个来生成上架单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<InboundUnloadVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取入库工单卸货表 根据这个来生成上架单详情
        InboundUnloadVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取入库工单卸货表 根据这个来生成上架单详情", description = "根据数据RefNum，从数据库中获取其对应的入库工单卸货表 根据这个来生成上架单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<InboundUnloadVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取入库工单卸货表 根据这个来生成上架单详情
        InboundUnloadVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取入库工单卸货表 根据这个来生成上架单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的入库工单卸货表 根据这个来生成上架单列表")
    @PostMapping(value = "/list")
    public Result<PageData<InboundUnloadPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<InboundUnloadQuery> search) {

        // 获取入库工单卸货表 根据这个来生成上架单分页
        PageData<InboundUnloadPageVO> resultPage = service.pageByQuery(search);
        // 填充版本产品
        ProductVersionCacheUtil.filledProductVersion(resultPage.getRecords());
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "按产品卸货", description = "按产品卸货")
    @PostMapping(value = "/regular-unload-by-product")
    public Result<InboundPutawaySlipVO> regularUnloadByProduct(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundAggregatedUploadVO inBoundUnloadsVO) {

        return Result.ok(service.regularUnloadByProduct(inBoundUnloadsVO));
    }

    /**
     * <p>
     * 按产品打托卸货
     * </p>
     *
     * @param inboundPalletUnloadVO 收货数量
     * @return 受影响行数
     */
    @Operation(summary = "按产品打托卸货", description = "按产品打托卸货")
    @PostMapping(value = "/pallet-unload-by-product")
    public Result<InboundPalletUnloadPrintVO> palletUnloadByProduct(@Valid @RequestBody @Parameter(description = "数据对象", required = true) InboundPalletUnloadVO inboundPalletUnloadVO) {

        return Result.ok(service.palletUnloadByProduct(inboundPalletUnloadVO));
    }

}
