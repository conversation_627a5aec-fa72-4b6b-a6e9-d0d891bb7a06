package cn.need.cloud.biz.provider.product;

import cn.need.cloud.biz.client.api.path.ProductComponentPath;
import cn.need.cloud.biz.client.api.product.ProductComponentClient;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductComponentCreateOrUpdateDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductComponentCreateOrUpdateReqDTO;
import cn.need.cloud.biz.model.param.product.update.ProductComponentCreateOrUpdateParam;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.service.product.ProductComponentService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(ProductComponentPath.PREFIX)
public class ProductComponentProvider implements ProductComponentClient {
    @Resource
    private ProductComponentService productComponentService;
    @Resource
    private ProductSpecialService productSpecialService;

    @Override
    @PostMapping(value = ProductComponentPath.CREATE_UPDATE)
    @IgnoreAuth
    public Result<Integer> createOrUpdate(@RequestBody ProductComponentCreateOrUpdateReqDTO reqDTO) {
        // 填充租户信息
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());
        // 填充租户信息到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        // 组装产品集合
        List<ProductReqDTO> list = reqDTO.getDetailList()
                .stream()
                .map(ProductComponentCreateOrUpdateDetailReqDTO::getProduct)
                .collect(Collectors.toList());
        list.add(reqDTO.getProduct());
        // 填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), list);
        // 组装方法参数
        List<ProductComponentCreateOrUpdateParam> paramList = buildParam(reqDTO);
        // 返回结果
        return Result.ok(productComponentService.createOrUpdate(paramList));
    }

    @Override
    @PostMapping(value = ProductComponentPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseDeleteReqDTO reqDTO) {
        // 填充租户信息
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());
        // 填充租户信息到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        // 填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());
        //返回影响行数
        return Result.ok(productSpecialService.removeComponent(reqDTO.getProductId(), reqDTO.getDeletedNote()));
    }

    /**
     * 组装方法参数
     *
     * @param reqDTO 组装参数
     * @return 方法参数
     */
    private List<ProductComponentCreateOrUpdateParam> buildParam(ProductComponentCreateOrUpdateReqDTO reqDTO) {
        return reqDTO.getDetailList().stream()
                .map(item ->
                        new ProductComponentCreateOrUpdateParam(
                                reqDTO.getProductId(),
                                item.getProductId(),
                                reqDTO.getAssemblyInstructionNote(),
                                item.getComponentQty()
                        )
                )
                .toList();
    }
}
