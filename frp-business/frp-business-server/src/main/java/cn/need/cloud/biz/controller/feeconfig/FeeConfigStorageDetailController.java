package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.FeeConfigStorageDetailConverter;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorageDetail;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStorageDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigStorageDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 仓库报价费用配置storage详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/fee-config-storage-detail")
@Tag(name = "仓库报价费用配置storage详情")
public class FeeConfigStorageDetailController extends AbstractRestController<FeeConfigStorageDetailService, FeeConfigStorageDetail, FeeConfigStorageDetailConverter, FeeConfigStorageDetailVO> {


    @Operation(summary = "根据id获取仓库报价费用配置storage详情详情", description = "根据数据主键id，从数据库中获取其对应的仓库报价费用配置storage详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeConfigStorageDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓库报价费用配置storage详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库报价费用配置storage详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeConfigStorageDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeConfigStorageDetailQuery> search) {

        // 获取仓库报价费用配置storage详情分页
        PageData<FeeConfigStorageDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
