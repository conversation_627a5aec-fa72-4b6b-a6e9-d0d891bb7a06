package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcWorkorderConverter;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.param.base.WorkorderFinishUpdateParam;
import cn.need.cloud.biz.model.param.base.WorkorderStartUpdateParam;
import cn.need.cloud.biz.model.query.base.SplitWorkorderParam;
import cn.need.cloud.biz.model.query.base.WorkorderRollbackListQuery;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderConfirmDetailVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkorderFinishConfirmVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderVO;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderSpecialService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * OtcWorkorderSpecialController
 *
 * <AUTHOR>
 * @since 2025-03-24
 */
@RestController
@RequestMapping("/api/biz/otc-workorder/special")
@Tag(name = "OTC工单-Special")
@Validated
public class OtcWorkorderSpecialController extends AbstractRestController<OtcWorkorderService, OtcWorkorder, OtcWorkorderConverter, OtcWorkorderVO> {

    @Resource
    private OtcWorkorderSpecialService otcWorkorderSpecialService;

    @Operation(summary = "Start Rollback", description = "启动 Rollback")
    @PostMapping(value = "/start-rollback")
    public Result<Boolean> startRollback(@RequestBody @Valid WorkorderStartUpdateParam query) {

        throw new BusinessException(StringUtil.format("Can not Support,Please wait development finish the function"));
        //return success(otcWorkorderSpecialService.startRollback(query));
    }

    @Operation(summary = "Start Cancel", description = "启动 Cancel")
    @PostMapping(value = "/start-cancel")
    public Result<Boolean> startCancel(@RequestBody @Valid WorkorderStartUpdateParam query) {
        otcWorkorderSpecialService.startCancel(query);
        return success(true);
    }

    @Operation(summary = "Finish Rollback", description = "完成 Rollback")
    @PostMapping(value = "/finish-rollback")
    public Result<Boolean> finishRollback(@RequestBody @Valid WorkorderFinishUpdateParam query) {
        return success(otcWorkorderSpecialService.finishRollback(query));
    }

    @Operation(summary = "Start Rollback 确认页", description = "Start Rollback 确认页")
    @PostMapping(value = "/rollback/list")
    public Result<List<WorkorderConfirmDetailVO>> rollbackList(@RequestBody @Valid WorkorderRollbackListQuery query) {

        return success(otcWorkorderSpecialService.confirmDetailList(query));
    }

    @Operation(summary = "Finish Rollback 确认页", description = "Finish Rollback 确认页")
    @PostMapping(value = "/finish-rollback/confirm")
    public Result<WorkorderFinishConfirmVO> finishRollbackConfirm(@RequestBody @Valid WorkorderRollbackListQuery query) {

        return success(otcWorkorderSpecialService.finishConfirm(query));
    }

    @Operation(summary = "Finish Cancel", description = "Finish Cancel")
    @PostMapping(value = "/finish-cancel")
    public Result<Boolean> startCancel(@RequestBody @Valid WorkorderFinishUpdateParam query) {
        return success(otcWorkorderSpecialService.finishCancel(query));
    }

    @Operation(summary = "Finish Cancel 确认页", description = "Finish Cancel 确认页")
    @PostMapping(value = "/finish-cancel/confirm")
    public Result<WorkorderFinishConfirmVO> startCancel(@RequestBody @Valid WorkorderRollbackListQuery query) {
        return success(otcWorkorderSpecialService.finishConfirm(query));
    }

    @Operation(summary = "拆单", description = "拆单")
    @PostMapping(value = "/split")
    public Result<Boolean> split(@RequestBody @Valid List<SplitWorkorderParam> query) {
        return success(otcWorkorderSpecialService.split(query));
    }

}
