package cn.need.cloud.biz.provider.product;

import cn.need.cloud.biz.client.api.path.ProductGroupPath;
import cn.need.cloud.biz.client.api.product.ProductGroupClient;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseDeleteReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductGroupCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.req.product.ProductGroupCreateOrUpdateReqDetailDTO;
import cn.need.cloud.biz.model.param.product.update.ProductGroupCreateOrUpdateParam;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.service.product.ProductGroupService;
import cn.need.cloud.biz.service.product.ProductSpecialService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(ProductGroupPath.PREFIX)
public class ProductGroupProvider implements ProductGroupClient {

    @Resource
    private ProductSpecialService productSpecialService;
    @Resource
    private ProductGroupService productGroupService;

    @Override
    @PostMapping(value = ProductGroupPath.CREATE_UPDATE)
    @IgnoreAuth
    public Result<Integer> createOrUpdate(@RequestBody ProductGroupCreateOrUpdateReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO);

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        // 填充产品信息
        List<ProductReqDTO> list = reqDTO.getDetailList()
                .stream()
                .map(ProductGroupCreateOrUpdateReqDetailDTO::getProduct)
                .collect(Collectors.toList());
        list.add(reqDTO.getProduct());
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), list);

        // 构建入参
        List<ProductGroupCreateOrUpdateParam> paramList = buildParam(reqDTO);

        // 返回影响行数
        return Result.ok(productGroupService.createOrUpdate(paramList));
    }

    @Override
    @PostMapping(value = ProductGroupPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseDeleteReqDTO reqDTO) {
        // 填充租户id
        TenantUtil.fillTenant(reqDTO);

        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        //填充产品信息
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), reqDTO.getProduct());

        //返回影响行数
        return Result.ok(productSpecialService.removeGroup(reqDTO.getProductId(), reqDTO.getDeletedNote()));
    }

    /**
     * 构建方法入参
     *
     * @param reqDTO 请求参数
     */
    private List<ProductGroupCreateOrUpdateParam> buildParam(ProductGroupCreateOrUpdateReqDTO reqDTO) {
        return reqDTO.getDetailList().stream().map(item ->
                new ProductGroupCreateOrUpdateParam(
                        reqDTO.getProductId(),
                        item.getProductId(),
                        item.getInstructionNote(),
                        item.getRevertInstructionNote(),
                        item.getConvertGroupType()
                )
        ).toList();
    }
}
