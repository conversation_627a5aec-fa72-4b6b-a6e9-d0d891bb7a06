package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcRequestPackageConverter;
import cn.need.cloud.biz.model.entity.otc.OtcRequestPackage;
import cn.need.cloud.biz.model.param.otc.create.request.OtcRequestPackageCreateParam;
import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestPackageUpdateParam;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestPackageQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPackagePageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestPackageService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OTC请求包裹 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-request-package")
@Tag(name = "OTC请求包裹")
public class OtcRequestPackageController extends AbstractRestController<OtcRequestPackageService, OtcRequestPackage, OtcRequestPackageConverter, OtcRequestPackageVO> {

    @Operation(summary = "新增OTC请求包裹", description = "接收OTC请求包裹的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OTC请求包裹", description = "接收OTC请求包裹的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OtcRequestPackageUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OTC请求包裹", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OTC请求包裹详情", description = "根据数据主键id，从数据库中获取其对应的OTC请求包裹详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcRequestPackageVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTC请求包裹详情
        OtcRequestPackageVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTC请求包裹分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC请求包裹列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcRequestPackagePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcRequestPackageQuery> search) {

        // 获取OTC请求包裹分页
        PageData<OtcRequestPackagePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
