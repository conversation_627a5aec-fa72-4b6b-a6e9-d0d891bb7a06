package cn.need.cloud.biz.controller.oco;


import cn.need.cloud.biz.converter.oco.OcoArrangementConverter;
import cn.need.cloud.biz.model.entity.oco.OcoArrangement;
import cn.need.cloud.biz.model.param.oco.create.OcoArrangementCreateParam;
import cn.need.cloud.biz.model.param.oco.update.OcoArrangementUpdateParam;
import cn.need.cloud.biz.model.query.oco.OcoArrangementQuery;
import cn.need.cloud.biz.model.vo.oco.OcoArrangementVO;
import cn.need.cloud.biz.model.vo.oco.OcoRequestDetailVO;
import cn.need.cloud.biz.model.vo.page.OcoArrangementPageVO;
import cn.need.cloud.biz.service.oco.OcoArrangementService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OCO安排表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/api/biz/oco-arrangement")
@Tag(name = "OCO安排表")
public class OcoArrangementController extends AbstractRestController<OcoArrangementService, OcoArrangement, OcoArrangementConverter, OcoArrangementVO> {

    // 已移除 createOrUpdate 接口，请使用 /insert 与 /update

    @Operation(summary = "新增OCO安排表", description = "接收OCO安排表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoArrangementCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OCO安排表", description = "接收OCO安排表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoArrangementUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OCO安排表", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OCO安排表详情", description = "根据数据主键id，从数据库中获取其对应的OCO安排表详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OcoArrangementVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取OCO安排表详情", description = "根据数据RefNum，从数据库中获取其对应的OCO安排表详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OcoArrangementVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }


    @Operation(summary = "获取OCO安排表分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OCO安排表列表")
    @PostMapping(value = "/list")
    public Result<PageData<OcoArrangementPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OcoArrangementQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "获取可选择的OCO请求详情", description = "获取指定OCO请求中可用于创建安排的详情列表")
    @GetMapping(value = "/selectable-details")
    public Result<List<OcoRequestDetailVO>> getSelectableDetails(
            @RequestParam @Parameter(description = "OCO请求ID", required = true) Long ocoRequestId,
            @RequestParam(defaultValue = "true") @Parameter(description = "是否过滤剩余数量为0的记录") Boolean filterZeroRemaining) {
        // 返回结果
        return success(service.getSelectableDetails(ocoRequestId, filterZeroRemaining));
    }
}



