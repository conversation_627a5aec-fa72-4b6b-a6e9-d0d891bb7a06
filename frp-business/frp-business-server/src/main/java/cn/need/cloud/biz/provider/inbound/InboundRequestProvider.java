package cn.need.cloud.biz.provider.inbound;

import cn.need.cloud.biz.client.api.inbound.InboundRequestClient;
import cn.need.cloud.biz.client.api.path.InboundRequestPath;
import cn.need.cloud.biz.client.dto.base.BasePartnerDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseRequestDeleteAndCancelReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.base.info.ProductVersionReqDTO;
import cn.need.cloud.biz.client.dto.req.base.info.TenantReqDTO;
import cn.need.cloud.biz.client.dto.req.inbound.*;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundRequestDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.inbound.InboundRequestRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.inbound.InboundRequest;
import cn.need.cloud.biz.model.entity.inbound.InboundRequestDetail;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestCreateParam;
import cn.need.cloud.biz.model.param.inbound.create.InboundRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.inbound.update.InboundRequestUpdateParam;
import cn.need.cloud.biz.model.query.inbound.InboundRequestQuery;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestAuditVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestDetailVO;
import cn.need.cloud.biz.model.vo.inbound.request.InboundRequestVO;
import cn.need.cloud.biz.model.vo.page.InboundRequestPageVO;
import cn.need.cloud.biz.provider.base.ProductVersionUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.inbound.InboundRequestDetailService;
import cn.need.cloud.biz.service.inbound.InboundRequestIgnoreWarehouseService;
import cn.need.cloud.biz.service.inbound.InboundRequestService;
import cn.need.cloud.biz.service.inbound.InboundRequestSpecialService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.NoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(InboundRequestPath.PREFIX)
public class InboundRequestProvider implements InboundRequestClient {
    @Resource
    private InboundRequestService inboundRequestService;
    @Resource
    private InboundRequestDetailService inboundRequestDetailService;
    @Resource
    private InboundRequestSpecialService inboundRequestSpecialService;
    @Resource
    private InboundRequestIgnoreWarehouseService inboundRequestIgnoreWarehouseService;

    /**
     * 构建创建参数
     *
     * @param reqDTO 请求参数
     * @return 创建参数
     */
    private static @NotNull InboundRequestCreateParam buildCreateParam(InboundRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        InboundRequestCreateParam createParam = BeanUtil.copyNew(reqDTO, InboundRequestCreateParam.class);
        createParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
        List<InboundRequestDetailCreateParam> detailCreateParamList = reqDTO.getDetails().stream().map(item -> {
            InboundRequestDetailCreateParam detailCreateParam = BeanUtil.copyNew(item, InboundRequestDetailCreateParam.class);
            detailCreateParam.setProductId(item.getProductId());
            detailCreateParam.setProductVersionId(item.getProductVersionId());
            return detailCreateParam;
        }).toList();
        createParam.setDetails(detailCreateParamList);
        return createParam;
    }

    @Override
    @PostMapping(value = InboundRequestPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<Boolean> createOrUpdate(@RequestBody InboundRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getTransactionPartner(), reqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        List<ProductVersionReqDTO> list = reqDTO.getDetails().stream().map(InboundRequestDetailReqDTO::getProductVersion).toList();
        ProductVersionUtil.fillProductVersionId(list);
        //判断是新增还是更新
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            //构建新增参数
            InboundRequestCreateParam createParam = buildCreateParam(reqDTO);
            //调用新增方法
            inboundRequestService.insertByParam(createParam);
        } else {
            //构建更新参数
            InboundRequestUpdateParam updateParam = buildUpdateParam(reqDTO);
            //调用新增方法
            inboundRequestService.updateByParam(updateParam);
        }
        //返回结果
        return Result.ok(Boolean.TRUE);
    }

    @Override
    @PostMapping(value = InboundRequestPath.DELETE)
    @IgnoreAuth
    public Result<Integer> remove(@RequestBody BaseRequestDeleteAndCancelReqDTO baseDeleteReqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(baseDeleteReqDTO.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(baseDeleteReqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(baseDeleteReqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(baseDeleteReqDTO.getWarehouseId());
        //填充inboundRequestId
        inboundRequestService.fillRequestId(baseDeleteReqDTO.getTransactionPartner(), List.of(baseDeleteReqDTO.getRequestReqDTO()));
        //返回结果
        return Result.ok(inboundRequestSpecialService.removeRequest(baseDeleteReqDTO.getRequestId(), baseDeleteReqDTO.getNote()));
    }

    /**
     * 构建更新参数
     *
     * @param reqDTO 请求参数
     * @return 更新参数
     */
    private @NotNull InboundRequestUpdateParam buildUpdateParam(InboundRequestCreateOrUpdateWithWarehouseReqDTO reqDTO) {
        //获取入库请求单信息
        InboundRequest inboundRequest = inboundRequestService.getByRefNum(reqDTO.getRefNum());
        //构建入库请求更新
        InboundRequestUpdateParam updateParam = BeanUtil.copyNew(reqDTO, InboundRequestUpdateParam.class);
        updateParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
        updateParam.setId(inboundRequest.getId());
        //获取lineNum+inboundRequestId  映射  详情id
        List<Integer> lineNumList = reqDTO.getDetails()
                .stream()
                .map(InboundRequestDetailReqDTO::getLineNum)
                .toList();
        //获取详情信息
        List<InboundRequestDetail> detailList = inboundRequestDetailService.list(inboundRequest.getId(), lineNumList);
        //根据lineNum+inboundRequestId  映射  详情id
        Map<String, Long> map = ObjectUtil.toMap(
                detailList,
                item ->
                        StringUtil.format(
                                "{}/{}",
                                item.getInboundRequestId(),
                                item.getLineNum()
                        ),
                InboundRequestDetail::getId
        );
        //遍历入库详情
        List<InboundRequestDetailVO> detailCreateParamList = reqDTO.getDetails().stream().map(item -> {
            InboundRequestDetailVO detailVO = BeanUtil.copyNew(item, InboundRequestDetailVO.class);
            detailVO.setProductId(item.getProductId());
            detailVO.setProductVersionId(item.getProductVersionId());
            detailVO.setId(map.get(StringUtil.format("{}/{}", inboundRequest.getId(), item.getLineNum())));
            return detailVO;
        }).toList();
        updateParam.setDetails(detailCreateParamList);
        return updateParam;
    }

    @Override
    @PostMapping(value = InboundRequestPath.DETAIL)
    @IgnoreAuth
    public Result<InboundRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query) {
        // 填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner(), query.getTransactionPartner());
        // 设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        // 填充inboundRequestId
        inboundRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));
        //获取详情信息
        InboundRequestVO inboundRequestVO = inboundRequestService.detailById(query.getRequestId());
        //对象copy
        InboundRequestRespDTO inboundRequestRespDTO = BeanUtil.copyNew(inboundRequestVO, InboundRequestRespDTO.class);
        inboundRequestRespDTO.setLogisticPartner(query.getLogisticPartner());
        inboundRequestRespDTO.setTransactionPartner(BeanUtil.copyNew(inboundRequestVO.getTransactionPartner(), TenantReqDTO.class));
        inboundRequestRespDTO.setBaseWarehouseDTO(BeanUtil.copyNew(inboundRequestVO.getBaseWarehouseVO(), BaseWarehouseDTO.class));
        List<InboundRequestDetailRespDTO> list = inboundRequestVO.getDetails()
                .stream()
                .map(item -> {
                    InboundRequestDetailRespDTO respDTO = BeanUtil.copyNew(item, InboundRequestDetailRespDTO.class);
                    respDTO.setBaseProductVersionDTO(ProductVersionUtil.convert(item.getBaseProductVersionVO()));
                    return respDTO;
                }).toList();
        //填充详情
        inboundRequestRespDTO.setDetails(list);
        //构建返回参数
        return Result.ok(inboundRequestRespDTO);
    }

    @Override
    @PostMapping(value = InboundRequestPath.LIST)
    @IgnoreAuth
    public Result<PageData<InboundRequestPageReqDTO>> list(@RequestBody PageSearch<InboundRequestQueryReqDTO> search) {
        //获取查询参数
        InboundRequestQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //构建方法入参
        PageSearch<InboundRequestQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, InboundRequestQuery.class));
        //调用列表方法
        PageData<InboundRequestPageVO> data = inboundRequestService.pageByQuery(pageSearch);
        //对象转换
        PageData<InboundRequestPageReqDTO> pageData = PageUtil.convert(data, item -> {
            InboundRequestPageReqDTO pageRespDTO = BeanUtil.copyNew(item, InboundRequestPageReqDTO.class);
            pageRespDTO.setLogisticPartner(query.getLogisticPartner());
            pageRespDTO.setTransactionPartnerDTO(BeanUtil.copyNew(item.getTransactionPartnerVO(), BasePartnerDTO.class));
            pageRespDTO.setBaseWarehouseDTO(BeanUtil.copyNew(item.getBaseWarehouseVO(), BaseWarehouseDTO.class));
            return pageRespDTO;
        });
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = InboundRequestPath.CANCEL)
    @IgnoreAuth
    public Result<Integer> cancel(@RequestBody BaseRequestDeleteAndCancelReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        //填充inboundRequestId
        inboundRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getRequestReqDTO()));
        //构建参数
        NoteParam noteParam = BeanUtil.copyNew(reqDTO, NoteParam.class);
        noteParam.setId(reqDTO.getRequestId());
        //返回结果
        return Result.ok(inboundRequestSpecialService.cancelRequest(noteParam));
    }

    @Override
    @PostMapping(value = InboundRequestPath.AUDIT)
    @IgnoreAuth
    public Result<Boolean> audit(@RequestBody InboundRequestAuditWithWarehouseReqDTO reqDTO) {
        //填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());
        //填充请求单id
        inboundRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getReqDTO()));
        //构建参数
        InboundRequestAuditVO inboundRequestAuditVO = BeanUtil.copyNew(reqDTO, InboundRequestAuditVO.class);
        inboundRequestAuditVO.setId(reqDTO.getRequestId());
        //调用审批接口
        inboundRequestIgnoreWarehouseService.audit(inboundRequestAuditVO);
        //返回结果
        return Result.ok(Boolean.TRUE);
    }
}
