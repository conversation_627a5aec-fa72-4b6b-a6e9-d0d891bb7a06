package cn.need.cloud.biz.service.feeoriginalData.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.feeconfig.FeeStatusEnum;
import cn.need.cloud.biz.model.bo.fee.otc.*;
import cn.need.cloud.biz.model.entity.otc.*;
import cn.need.cloud.biz.model.query.otc.request.OtcRequestQuery;
import cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailVO;
import cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataBuildService;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageService;
import cn.need.cloud.biz.service.otc.request.OtcRequestDetailService;
import cn.need.cloud.biz.service.otc.request.OtcRequestService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcPrepWorkorderService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderDetailService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 其他入库费用原始数据构建服务实现
 *
 * <AUTHOR>
 * @since 2025/4/8
 */
@Service("fodBuildOtc")
@AllArgsConstructor
public class FeeOriginalDataBuildOtcServiceImpl implements FeeOriginalDataBuildService<FodExtraDataOtcBO> {

    private final OtcRequestService otcRequestService;
    private final OtcRequestDetailService otcRequestDetailService;
    private final OtcWorkorderService otcWorkorderService;
    private final OtcWorkorderDetailService otcWorkorderDetailService;
    private final OtcPrepWorkorderService otcPrepWorkorderService;
    private final OtcPrepWorkorderDetailService otcPrepWorkorderDetailService;
    private final OtcPackageService otcPackageService;

    @Override
    public FodExtraDataOtcBO buildFeeOriginalData() {

        //todo: 这里要根据 FeeStatus & OtcRequestStatus来获取

        PageSearch<OtcRequestQuery> otcRequestQueryPageSearch = new PageSearch<>();
        otcRequestQueryPageSearch.setCurrent(1);
        otcRequestQueryPageSearch.setSize(1);
        OtcRequestQuery otcRequestQuery = new OtcRequestQuery();
        otcRequestQuery.setOtcRequestStatus(RequestStatusEnum.PROCESSED.getStatus());
        otcRequestQuery.setFeeStatus(FeeStatusEnum.NEW.getStatus());
        otcRequestQueryPageSearch.setCondition(otcRequestQuery);

        final PageData<OtcRequestPageVO> otcRequestPageVOPageData = otcRequestService.pageByQuery(otcRequestQueryPageSearch);

        if (ObjectUtil.isEmpty(otcRequestPageVOPageData.getRecords())) {
            return null;
        }
        //todo:这里使用Redis锁
        return buildFeeOriginalData(otcRequestPageVOPageData.getRecords().get(0).getId());
    }


    @Override
    public FodExtraDataOtcBO buildFeeOriginalData(Long requestId) {

        if (ObjectUtil.isEmpty(requestId)) {
            return null;
        }

        OtcRequestVO otcRequestVO = otcRequestService.detailById(requestId);

        return buildFeeOriginalData(otcRequestVO);
    }

    @NotNull
    private FodExtraDataOtcBO buildFeeOriginalData(OtcRequestVO otcRequestVO) {
        if (!otcRequestVO.getOtcRequestStatus().equals(RequestStatusEnum.PROCESSED.getStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "Processed",
                    otcRequestVO.getOtcRequestStatus()));
        }

        if (!FeeStatusEnum.getCanBuildStatuses().contains(otcRequestVO.getFeeStatus())) {
            throw new BusinessException(String.format(ErrorMessages.STATUS_REQUIRED,
                    "buildFeeOriginalData",
                    "New",
                    otcRequestVO.getFeeStatus()));
        }

        // 查询相关数据
        List<OtcWorkorder> otcWorkorders = otcWorkorderService.listByRequestId(otcRequestVO.getId());
        List<OtcRequestDetailVO> otcRequestDetails = otcRequestDetailService.listByOtcRequestId(otcRequestVO.getId());

        // 查询工单详情
        List<OtcWorkorderDetail> otcWorkorderDetails = List.of();
        if (!ObjectUtil.isEmpty(otcWorkorders)) {
            List<Long> workorderIds = otcWorkorders.stream().map(OtcWorkorder::getId).toList();
            var workorderDetailMap = otcWorkorderDetailService.groupByOtcWorkOrderIdList(workorderIds);
            otcWorkorderDetails = workorderDetailMap.values().stream()
                    .flatMap(List::stream)
                    .toList();
        }

        // 查询预处理工单和详情 - 使用基础查询方法
        List<OtcPrepWorkorder> otcPrepWorkorders = List.of();
        List<OtcPrepWorkorderDetail> otcPrepWorkorderDetails = List.of();
        if (!ObjectUtil.isEmpty(otcWorkorders)) {
            List<Long> workorderIds = otcWorkorders.stream().map(OtcWorkorder::getId).toList();
            // 通过工单ID查询预处理工单
            otcPrepWorkorders = otcPrepWorkorderService.lambdaQuery()
                    .in(OtcPrepWorkorder::getOtcWorkorderId, workorderIds)
                    .list();
            if (!ObjectUtil.isEmpty(otcPrepWorkorders)) {
                List<Long> prepWorkorderIds = otcPrepWorkorders.stream().map(OtcPrepWorkorder::getId).toList();
                // 通过预处理工单ID查询详情
                otcPrepWorkorderDetails = otcPrepWorkorderDetailService.lambdaQuery()
                        .in(OtcPrepWorkorderDetail::getOtcPrepWorkorderId, prepWorkorderIds)
                        .list();
            }
        }

        // 查询包裹相关数据
        List<OtcPackage> otcPackages = List.of();
        if (!ObjectUtil.isEmpty(otcWorkorders)) {
            List<Long> workorderIds = otcWorkorders.stream().map(OtcWorkorder::getId).toList();
            otcPackages = otcPackageService.lambdaQuery()
                    .in(OtcPackage::getOtcWorkorderId, workorderIds)
                    .list();
        }

        // 创建费用原始数据对象
        FodExtraDataOtcBO extraData = new FodExtraDataOtcBO();

        // 填充基本信息
        extraData.setSnapshotRequestId(otcRequestVO.getId());
        extraData.setSnapshotRequestRefNum(otcRequestVO.getRequestRefNum());
        extraData.setSnapshotRefNum(otcRequestVO.getRequestRefNum());
        extraData.setTransactionPartnerId(otcRequestVO.getTransactionPartnerId());
        extraData.setWarehouseId(otcRequestVO.getWarehouseId());
        extraData.setRefNum(otcRequestVO.getRequestRefNum());
        extraData.setProcessStartTime(otcRequestVO.getProcessStartTime());
        extraData.setProcessEndTime(otcRequestVO.getProcessEndTime());
        extraData.setFeeCalculationTime(otcRequestVO.getProcessEndTime());
        extraData.setNote(otcRequestVO.getNote());

        // 填充业务数据
        extraData.setRequest(BeanUtil.copyNew(otcRequestVO, FodOtcRequestBO.class));
        extraData.setRequestDetailList(BeanUtil.copyNew(otcRequestDetails, FodOtcRequestDetailBO.class));
        extraData.setWorkorderList(BeanUtil.copyNew(otcWorkorders, FodOtcWorkorderBO.class));
        extraData.setWorkorderDetailList(BeanUtil.copyNew(otcWorkorderDetails, FodOtcWorkorderDetailBO.class));
        extraData.setPrepWorkorderList(BeanUtil.copyNew(otcPrepWorkorders, FodOtcPrepWorkorderBO.class));
        extraData.setPrepWorkorderDetailList(BeanUtil.copyNew(otcPrepWorkorderDetails, FodOtcPrepWorkorderDetailBO.class));
        extraData.setPackageList(BeanUtil.copyNew(otcPackages, FodOtcPackageBO.class));

        // TODO: 包裹标签数据需要根据包裹ID查询
        // extraData.setPackageLabelList(BeanUtil.copyNew(otcPackageLabels, FodOtcPackageLabelBO.class));

        return extraData;
    }
}
