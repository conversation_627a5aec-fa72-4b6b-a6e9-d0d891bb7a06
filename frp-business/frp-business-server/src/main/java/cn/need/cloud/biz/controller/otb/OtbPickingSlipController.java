package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.pickingslip.*;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.base.workorder.WorkOrderNoEnoughAvailQtyVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipSummaryPrintVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipVO;
import cn.need.cloud.biz.model.vo.page.OtbPickingSlipPageVO;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipBuildService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * otb拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-picking-slip")
@Tag(name = "otb拣货单")
public class OtbPickingSlipController extends AbstractRestController<OtbPickingSlipService, OtbPickingSlip, OtbPickingSlipConverter, OtbPickingSlipVO> {

    @Resource
    private OtbPickingSlipBuildService otbPickingSlipBuildService;

    @Operation(summary = "根据id获取otb拣货单详情", description = "根据数据主键id，从数据库中获取其对应的otb拣货单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbPickingSlipVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取otb拣货单详情
        OtbPickingSlipVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取otb拣货单详情", description = "根据数据RefNum，从数据库中获取其对应的otb拣货单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbPickingSlipVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取otb拣货单详情
        OtbPickingSlipVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取otb拣货单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的otb拣货单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbPickingSlipPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbPickingSlipListQuery> search) {

        // 获取otb拣货单分页
        PageData<OtbPickingSlipPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "FilterBuildPickingSlip", description = "构建拣货单")
    @PostMapping(value = "/filter-build")
    public Result<List<WorkOrderNoEnoughAvailQtyVO>> buildPickingSlip(@RequestBody @Parameter(description = "Otb构建拣货单 vo对象", required = true)
                                                                      @Valid OtbPickingSlipFilterBuildQuery query) {

        // 返回结果
        return success(otbPickingSlipBuildService.filterBuild(query));
    }

    @Operation(summary = "Print", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-printed")
    public Result<Boolean> print(@RequestBody @Valid PrintQuery query) {

        service.markPrinted(query);
        // 返回结果
        return success(true);
    }

    @Operation(summary = "Summary Print", description = "根据传入的搜索条件参数，汇总拣货单")
    @PostMapping(value = "/summary-picking-slips")
    public Result<OtbPickingSlipSummaryPrintVO> summaryPrint(@RequestBody OtbPickingSlipListQuery query) {

        // 返回结果
        return success(service.summaryPrint(query));
    }

    @Operation(summary = "拣货Pick", description = "根据传入的条件参数，拣货")
    @PostMapping(value = "/pick")
    public Result<Boolean> pick(@RequestBody @Valid OtbPickingSlipPickQuery query) {

        // 返回结果
        return success(service.pick(query));
    }

    @Operation(summary = "下拉列表Pro", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValuePro(@RequestBody @Parameter(description = "查询条件", required = true) OtbPickingSlipQuery query) {

        return success(service.distinctValue(query));
    }

    @Operation(summary = "Dashboard CountPreDay", description = "下拉列表")
    @PostMapping(value = "/count-pre-day")
    public Result<List<DropProVO>> countPreDay(@RequestBody @Parameter(description = "查询条件", required = true) OtbPickingSlipQuery query) {

        return success(service.countPreDay(query));
    }

    @Operation(summary = "MarkReLabelPrint", description = "根据传入的搜索条件参数，更新拣货单PrintStatus")
    @PostMapping(value = "/mark-relabel-printed")
    public Result<Boolean> markReLabelPrint(@RequestBody @Valid OtbPickingSlipMarkReLabelPrintQuery query) {

        // 返回结果
        return success(service.markReLabelPrint(query));
    }

    @Operation(summary = "MarkReLabelPrint 根据产品Barcode打印", description = "根据传入的搜索条件参数，根据产品Barcode 更新拣货单PrintStatus")
    @PostMapping(value = "/mark-relabel-printed-by-product-barcode")
    public Result<Boolean> markReLabelPrint(@RequestBody @Valid OtbPickingSlipMarkReLabelPrintByBarcodeQuery query) {

        // 返回结果
        return success(service.markReLabelPrintByProductBarcode(query));
    }

    @Operation(summary = "RemeasureCartonSize", description = "根据传入的搜索条件参数，重新设置盒子尺寸")
    @PostMapping(value = "/remeasure-carton-size")
    public Result<Boolean> remeasureCartonSize(@RequestBody @Valid OtbPickingSlipMarkRemeasureCartonSizeQuery query) {

        // 返回结果
        return success(service.remeasureCartonSize(query));
    }
}
