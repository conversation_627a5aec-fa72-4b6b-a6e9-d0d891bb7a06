package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbShipmentDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbShipmentDetail;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB装运详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbShipmentDetailConverter extends AbstractModelConverter<OtbShipmentDetail, OtbShipmentDetailVO, OtbShipmentDetailDTO> {

}
