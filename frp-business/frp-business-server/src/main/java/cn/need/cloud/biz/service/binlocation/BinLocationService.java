package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.client.constant.enums.binlocation.BinTypeEnum;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailListQuery;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.query.inbound.InboundPalletQuery;
import cn.need.cloud.biz.model.vo.base.BaseCheckOrderVO;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationMoveVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.cloud.biz.model.vo.page.BinLocationPageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 库位管理服务接口
 * </p>
 * <p>
 * 该接口提供库位管理的核心业务功能，包括库位的创建、查询、更新及删除等基本操作，
 * 以及库位相关的业务操作（如产品移动、库位状态切换等）。
 * </p>
 * <p>
 * 库位是仓库管理系统的基础组件，用于定义货物存放的具体位置信息。本接口支持多种库位类型，
 * 包括实体库位和虚拟库位，并提供库位全生命周期的管理功能。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 库位基础信息管理（创建、更新、查询、删除）
 * 2. 库位状态管理（启用、禁用等）
 * 3. 虚拟库位管理（根据类型查找、获取所有虚拟库位）
 * 4. 库位与产品关联查询（根据产品查库位、产品库位移动）
 * 5. 库位业务校验（检查未完成订单等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-28
 */
public interface BinLocationService extends SuperService<BinLocation> {

    /**
     * 根据参数新增库位
     * <p>
     * 创建新的库位记录，包括库位的基本信息、属性等。
     * 在创建过程中会进行数据验证、编号生成、关联信息校验等操作。
     * </p>
     *
     * @param createParam 请求创建参数，包含需要插入的库位的相关信息
     * @return 创建成功的库位ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(BinLocationCreateParam createParam);


    /**
     * 根据参数更新库位
     * <p>
     * 更新已有库位的信息，可以修改库位的基本属性、状态等。
     * 在更新过程中会进行数据验证、状态校验、权限验证等操作。
     * </p>
     *
     * @param updateParam 请求更新参数，包含需要更新的库位的相关信息
     * @return 更新影响的记录数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(BinLocationUpdateParam updateParam);

    /**
     * 根据查询条件获取库位列表
     * <p>
     * 根据指定的查询条件查询库位列表，支持多种筛选条件组合。
     * 返回符合条件的库位视图对象列表，不包含分页信息。
     * </p>
     *
     * @param query 查询条件对象，包含了用于筛选库位的各种条件
     * @return 返回符合条件的库位视图对象列表
     */
    List<BinLocationPageVO> listByQuery(BinLocationQuery query);

    /**
     * 根据查询条件获取库位列表(分页)
     * <p>
     * 根据指定的查询条件和分页参数查询库位列表。
     * 返回的结果包含分页信息和库位视图对象列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的库位视图对象列表
     */
    PageData<BinLocationPageVO> pageByQuery(PageSearch<BinLocationQuery> search);

    /**
     * 根据ID获取库位详情
     * <p>
     * 根据库位ID查询库位的详细信息，包括基本属性、状态等。
     * 返回结果为库位视图对象，包含完整的库位信息。
     * </p>
     *
     * @param id 库位ID
     * @return 返回库位视图对象
     */
    BinLocationVO detailById(Long id);

    /**
     * 根据ID列表获取库位详情列表
     * <p>
     * 批量查询指定ID列表的库位详情，包括库位中存储的产品信息。
     * 此方法用于获取多个库位的详细信息，避免多次数据库查询。
     * </p>
     *
     * @param query 包含库位ID列表的查询条件对象
     * @return 返回库位详情视图对象列表
     */
    List<BinLocationDetailVO> detailListByIds(BinLocationDetailListQuery query);

    /**
     * 根据唯一编码获取库位详情
     * <p>
     * 根据库位的唯一引用编号查询库位的详细信息。
     * 返回结果为库位视图对象，包含完整的库位信息。
     * </p>
     *
     * @param refNum 库位唯一编码
     * @return 返回库位视图对象
     */
    BinLocationVO detailByRefNum(String refNum);

    /**
     * 删除库位
     * <p>
     * 根据ID删除指定的库位，并记录删除原因。
     * 删除操作可能为逻辑删除或物理删除，取决于具体实现。
     * </p>
     *
     * @param deletedNoteParam 包含库位ID和删除备注的参数对象
     * @return 删除影响的记录数
     */
    Integer removeBinLocation(DeletedNoteParam deletedNoteParam);

    /**
     * 获取库位字段去重下拉选项
     * <p>
     * 根据查询条件获取库位中指定字段的去重值列表，
     * 用于前端下拉框的数据展示。支持根据查询条件过滤数据范围。
     * </p>
     *
     * @param query 库位查询条件对象
     * @return 返回字段去重的下拉选项列表
     */
    List<DropProVO> distinctValue(BinLocationQuery query);

    /**
     * 根据仓库ID获取库位列表
     * <p>
     * 查询指定仓库下的所有库位信息，用于仓库管理和库位规划。
     * 返回结果为库位实体对象列表，包含库位的基本信息。
     * </p>
     *
     * @param id 仓库ID
     * @return 返回库位实体对象列表
     */
    List<BinLocation> listByWarehouse(Long id);

    /**
     * 根据库位ID列表获取库位映射
     * <p>
     * 批量查询指定ID列表的库位，并以ID为键构建映射。
     * 此方法用于高效获取多个库位的信息，避免多次数据库查询。
     * </p>
     *
     * @param binLocationIds 库位ID集合
     * @return 以库位ID为键，库位视图对象为值的映射
     */
    Map<Long, BinLocationVO> binLocationByIds(List<Long> binLocationIds);

    /**
     * 根据仓库ID删除库位及库位详情
     * <p>
     * 删除指定仓库下的所有库位和库位详情信息。
     * 此方法通常在删除仓库时调用，确保关联数据的完整删除。
     * </p>
     *
     * @param id 仓库ID
     */
    void removeBinByWarehouseId(Long id);

    /**
     * 根据仓库ID仅删除库位不删除库位详情
     * <p>
     * 删除指定仓库下的所有库位，但保留库位详情信息。
     * 此方法用于特定的业务场景，如仓库重组时保留库存记录。
     * </p>
     *
     * @param id 仓库ID
     */
    void removeBywarehouseId(Long id);

    /**
     * 为仓库创建默认库位
     * <p>
     * 为新创建的仓库自动生成一组默认库位，包括各种必要的虚拟库位和实体库位。
     * 此方法通常在创建新仓库时调用，确保仓库具备基本的库位结构。
     * </p>
     *
     * @param warehouse 仓库信息对象
     * @return 创建的默认库位列表
     */
    List<BinLocation> insertDefaultBinLocation(Warehouse warehouse);

    /**
     * 根据库位名称查询获取库位ID集合
     * <p>
     * 通过入库托盘查询条件中的库位名称条件查询匹配的库位ID集合。
     * 此方法用于根据名称模糊查询库位，支持多种匹配模式。
     * </p>
     *
     * @param condition 入库托盘查询条件，包含库位名称查询条件
     * @return 符合条件的库位ID集合
     */
    Set<Long> getBinLocationIds(InboundPalletQuery condition);

    /**
     * 根据库位类型查找虚拟库位
     * <p>
     * 查询指定类型的虚拟库位，如收货区、发货区、临时区等。
     * 虚拟库位用于表示货物在特定流程中的逻辑位置，不对应实际的物理位置。
     * </p>
     *
     * @param binTypeEnum 库位类型枚举值
     * @return 匹配的虚拟库位对象，如果不存在则返回null
     */
    BinLocation findVirtualBinLocationByType(BinTypeEnum binTypeEnum);

    /**
     * 切换库位的启用状态
     * <p>
     * 根据库位ID切换库位的启用/禁用状态。
     * 已启用的库位将被禁用，已禁用的库位将被启用。
     * </p>
     *
     * @param id 库位ID
     * @return 更新影响的记录数
     */
    Integer switchActive(Long id);

    /**
     * 获取所有虚拟库位
     * <p>
     * 查询系统中所有的虚拟库位，并以ID为键构建映射。
     * 虚拟库位用于表示货物在特定流程中的逻辑位置，如收货区、发货区等。
     * </p>
     *
     * @return 以库位ID为键，库位对象为值的映射
     */
    Map<Long, BinLocation> allVirtualBinLocationList();

    /**
     * 批量切换仓库下库位的启用状态
     * <p>
     * 根据仓库ID和目标状态，批量更新该仓库下所有库位的启用状态。
     * 此方法用于仓库状态变更时同步更新库位状态。
     * </p>
     *
     * @param warehouseId 仓库ID
     * @param activeFlag  目标启用状态，true表示启用，false表示禁用
     */
    void switchActive(Long warehouseId, Boolean activeFlag);

    /**
     * 检查是否存在未完成订单
     * <p>
     * 检查指定库位是否有未完成的订单，用于库位状态变更前的业务校验。
     * 如果存在未完成订单，通常不允许禁用或删除库位。
     * </p>
     *
     * @param param 检查参数对象，包含库位ID和检查类型
     * @return 如果存在未完成订单返回true，否则返回false
     */
    Boolean existUnfinishedOrder(BaseCheckOrderVO param);

    /**
     * 根据仓库ID和启用状态获取库位列表
     * <p>
     * 查询指定仓库下特定启用状态的库位列表。
     * 此方法用于筛选获取仓库中的可用/不可用库位。
     * </p>
     *
     * @param warehouseId 仓库ID
     * @param activeFlag  启用状态过滤条件，true表示查询启用的库位，false表示查询禁用的库位
     * @return 符合条件的库位列表
     */
    List<BinLocation> listByWarehouseId(Long warehouseId, boolean activeFlag);

    /**
     * 根据产品ID获取相关库位列表
     * <p>
     * 查询存放了指定产品的所有库位信息。
     * 此方法用于产品库存查询和产品位置追踪。
     * </p>
     *
     * @param productId 产品ID
     * @return 存放了该产品的库位视图对象列表
     */
    List<BinLocationVO> detailByProduct(Long productId);

    /**
     * 根据产品版本ID获取相关库位列表
     * <p>
     * 查询存放了指定产品版本的所有库位信息。
     * 此方法用于产品版本库存查询和位置追踪，支持产品版本管理。
     * </p>
     *
     * @param productVersionId 产品版本ID
     * @return 存放了该产品版本的库位视图对象列表
     */
    List<BinLocationVO> detailByProductVersion(Long productVersionId);

    /**
     * 移动产品到指定库位
     * <p>
     * 执行产品从一个库位到另一个库位的移动操作。
     * 此方法涉及源库位的库存减少和目标库位的库存增加，需要在事务中执行。
     * </p>
     *
     * @param param 产品移动参数对象，包含源库位、目标库位、产品信息和数量
     */
    void productMove(BinLocationMoveVO param);
}