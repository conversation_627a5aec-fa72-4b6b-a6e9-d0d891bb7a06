package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInbound;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigInboundCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigInboundUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundFrpQuery;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigInboundPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置inbound service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigInboundService extends
        SuperService<FeeConfigInbound>,
        RefNumService<FeeConfigInbound, FeeConfigInboundService>,
        RefNumWithNameService<FeeConfigInbound, FeeConfigInboundService>,
        FeeConfigService<FeeConfigInbound, FeeConfigInboundService> {

    /**
     * 根据参数新增仓库报价费用配置inbound
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置inbound的相关信息
     * @return 仓库报价费用配置inboundID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigInboundCreateParam createParam);


    /**
     * 根据参数更新仓库报价费用配置inbound
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置inbound的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigInboundUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库报价费用配置inbound列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置inbound对象的列表(分页)
     */
    List<FeeConfigInboundPageVO> listByQuery(FeeConfigInboundQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置inbound列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置inbound对象的列表(分页)
     */
    PageData<FeeConfigInboundPageVO> pageByQuery(PageSearch<FeeConfigInboundQuery> search);

    /**
     * 根据ID获取仓库报价费用配置inbound
     *
     * @param id 仓库报价费用配置inboundID
     * @return 返回仓库报价费用配置inboundVO对象
     */
    FeeConfigInboundVO detailById(Long id);


    /**
     * 根据仓库报价费用配置inbound唯一编码获取仓库报价费用配置inbound
     *
     * @param refNum 仓库报价费用配置inbound唯一编码
     * @return 返回仓库报价费用配置inboundVO对象
     */
    FeeConfigInboundVO detailByRefNum(String refNum);

    /**
     * 根据FRP查询条件获取仓库报价费用配置inbound列表(分页)
     *
     * @param search FRP分页查询条件对象，包含了用于筛选仓库报价费用配置的各种条件
     * @return 返回一个仓库报价费用配置inbound对象的分页结果
     */
    PageData<FeeConfigInboundPageVO> pageByQueryPro(PageSearch<FeeConfigInboundFrpQuery> search);

    /**
     * 根据FRP查询条件获取仓库报价费用配置inbound列表
     *
     * @param query FRP查询条件对象，包含了用于筛选仓库报价费用配置的各种条件
     * @return 返回一个仓库报价费用配置inbound对象的列表
     */
    List<FeeConfigInboundPageVO> listByQueryPro(FeeConfigInboundFrpQuery query);

    /**
     * 根据报价ID获取仓库报价费用配置inbound详情列表
     *
     * @param quoteId 报价ID
     * @return 返回仓库报价费用配置inbound详情对象列表
     */
    List<FeeConfigInboundVO> listDetailByQuoteId(Long quoteId);
}