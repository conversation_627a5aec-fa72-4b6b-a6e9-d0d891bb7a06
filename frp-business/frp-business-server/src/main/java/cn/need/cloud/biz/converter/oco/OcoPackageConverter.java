package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPackageDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPackage;
import cn.need.cloud.biz.model.vo.oco.OcoPackageVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO包裹表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPackageConverter extends AbstractModelConverter<OcoPackage, OcoPackageVO, OcoPackageDTO> {

}



