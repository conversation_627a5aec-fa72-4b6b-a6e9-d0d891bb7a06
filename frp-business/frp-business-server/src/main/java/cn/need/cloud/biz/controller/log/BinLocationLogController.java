package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.converter.log.BinLocationLogConverter;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.model.vo.log.BinLocationLogPageVO;
import cn.need.cloud.biz.model.vo.log.BinLocationLogVO;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
@RestController
@RequestMapping("/api/biz/bin-location-log")
@Tag(name = "BinLocationLog")
public class BinLocationLogController extends AbstractRestController<BinLocationLogService, BinLocationLog, BinLocationLogConverter, BinLocationLogVO> {

    @Operation(summary = "根据id获取详情", description = "根据数据主键id，从数据库中获取其对应的详情")
    @GetMapping(value = "/detail/{id}")
    public Result<BinLocationLogVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的列表")
    @PostMapping(value = "/list")
    public Result<PageData<BinLocationLogPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<BinLocationLogQuery> search) {

        // 获取分页
        PageData<BinLocationLogPageVO> resultPage = service.pageByQuery(search);
        List<BinLocationLogPageVO> records = resultPage.getRecords();

        // 返回结果
        return success(resultPage);
    }
}
