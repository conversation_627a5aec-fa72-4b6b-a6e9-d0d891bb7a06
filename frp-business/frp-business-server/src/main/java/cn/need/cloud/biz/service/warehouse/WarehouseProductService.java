package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseProductCreateParam;
import cn.need.cloud.biz.model.param.warehouse.importparam.WarehouseProductImportParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseProductCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseProductQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseProductPageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductListVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库产品关联服务接口
 * </p>
 * <p>
 * 该接口提供仓库与产品关联管理的核心业务功能，包括关联关系的创建、查询、更新等。
 * 仓库产品关联用于定义产品在特定仓库中的属性和配置，如产品在仓库中的位置策略、补货规则等。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 仓库产品关联信息管理（创建、更新、查询）
 * 2. 产品在仓库中的配置管理
 * 3. 批量关联关系查询
 * 4. 关联关系映射获取
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface WarehouseProductService extends SuperService<WarehouseProduct> {

    /**
     * 创建仓库产品关联
     * <p>
     * 创建新的仓库与产品的关联记录，定义产品在特定仓库中的配置和属性。
     * 在创建过程中会进行数据验证、关联信息校验等操作。
     * </p>
     *
     * @param createParam 请求创建参数，包含需要插入的仓库产品关联的相关信息
     * @return 创建成功的仓库产品关联ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(WarehouseProductCreateParam createParam);


    /**
     * 创建或更新仓库产品关联
     * <p>
     * 根据参数创建新的关联记录或更新已存在的关联记录。
     * 如果指定的仓库和产品关联已存在，则更新其配置；否则创建新的关联记录。
     * </p>
     *
     * @param param 创建或更新参数，包含仓库ID、产品ID及相关配置信息
     * @return 操作影响的记录数
     * @throws IllegalArgumentException 如果传入的param为空，则抛出此异常
     */
    int createOrUpdate(WarehouseProductCreateOrUpdateParam param);


    /**
     * 根据查询条件获取仓库产品关联列表
     * <p>
     * 根据指定的查询条件查询仓库产品关联列表，支持多种筛选条件组合。
     * 返回符合条件的仓库产品关联视图对象列表，不包含分页信息。
     * </p>
     *
     * @param query 查询条件对象，包含了用于筛选仓库产品关联的各种条件
     * @return 返回符合条件的仓库产品关联视图对象列表
     */
    List<WarehouseProductPageVO> listByQuery(WarehouseProductQuery query);

    /**
     * 根据查询条件获取仓库产品关联列表(分页)
     * <p>
     * 根据指定的查询条件和分页参数查询仓库产品关联列表。
     * 返回的结果包含分页信息和仓库产品关联视图对象列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的仓库产品关联视图对象列表
     */
    PageData<WarehouseProductPageVO> pageByQuery(PageSearch<WarehouseProductQuery> search);

    /**
     * 根据ID获取仓库产品关联详情
     * <p>
     * 根据仓库产品关联ID查询关联的详细信息，包括基本属性、配置等。
     * 返回结果为仓库产品关联视图对象，包含完整的关联信息。
     * </p>
     *
     * @param id 仓库产品关联ID
     * @return 返回仓库产品关联视图对象
     */
    WarehouseProductVO detailById(Long id);

    /**
     * 根据产品ID和仓库ID获取仓库产品关联信息
     * <p>
     * 查询指定产品在指定仓库中的关联配置信息。
     * 此方法用于获取特定产品-仓库组合的详细配置。
     * </p>
     *
     * @param productId   产品ID
     * @param warehouseId 仓库ID
     * @return 返回仓库产品关联实体对象，如果不存在则返回null
     */
    WarehouseProduct getWarehouseProduct(Long productId, Long warehouseId);

    /**
     * 根据产品ID获取所有关联仓库列表
     * <p>
     * 查询指定产品关联的所有仓库信息，包括在各仓库中的配置情况。
     * 此方法用于产品多仓库管理，了解产品在各仓库中的分布情况。
     * </p>
     *
     * @param productId 产品ID
     * @return 返回包含仓库信息的产品仓库关联列表视图对象
     */
    List<WarehouseProductListVO> listByProductId(Long productId);

    /**
     * 根据产品ID列表批量获取仓库产品关联信息
     * <p>
     * 批量查询指定产品ID列表的仓库关联信息，并以产品ID为键构建映射。
     * 此方法用于高效获取多个产品的仓库关联信息，避免多次数据库查询。
     * </p>
     *
     * @param productIdList 产品ID集合
     * @return 以产品ID为键，仓库产品关联对象为值的映射
     */
    Map<Long, WarehouseProduct> listByProductIdList(List<Long> productIdList);

    /**
     * 批量导入仓库产品额外包装类型
     * <p>
     * 根据导入参数列表批量处理仓库产品的额外包装类型设置。
     * 对于已存在的记录，只更新额外包装类型，不修改slapAndGoFlag。
     * 对于不存在的记录，创建新记录，slapAndGoFlag默认为false。
     * </p>
     *
     * @param importList 导入参数列表
     * @return 导入成功的记录数量
     */
    Integer batchImport(List<WarehouseProductImportParam> importList);
}