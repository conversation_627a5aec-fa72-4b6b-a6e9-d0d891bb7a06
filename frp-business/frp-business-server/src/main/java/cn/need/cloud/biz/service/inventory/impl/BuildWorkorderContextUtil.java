package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.model.bo.inventory.BuildPrepWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderDetailReturnContext;
import cn.need.cloud.biz.model.bo.inventory.BuildWorkOrderReturnContext;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.PrepWorkorderModel;
import cn.need.cloud.biz.model.entity.base.WorkorderDetailModel;
import cn.need.cloud.biz.model.entity.base.WorkorderModel;
import cn.need.cloud.biz.model.entity.inventory.InventoryLocked;
import cn.need.cloud.biz.model.entity.inventory.InventoryReserve;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Utility class for building work order contexts.
 *
 * <AUTHOR>
 * @since 2025/1/14
 */
public class BuildWorkorderContextUtil {

    /**
     * 提取所有WorkorderDetailContext的流。
     *
     * @param contextList 工单上下文列表
     * @return WorkorderDetailContext的流
     */
    private static Stream<BuildWorkOrderDetailReturnContext> getWorkOrderDetailStream(List<? extends BuildWorkOrderReturnContext> contextList) {
        return contextList.stream()
                .flatMap(context -> context.getWorkorderDetailContextList().stream())
                .filter(Objects::nonNull);
    }

    /**
     * 收集PrepWorkorder实体列表
     *
     * @param contextList       工单上下文列表
     * @param clazz             期望的PrepWorkorderModel子类类型
     * @param <TWorkorderModel> PrepWorkorderModel的子类
     * @return PrepWorkorder实体列表
     */
    public static <TWorkorderModel extends WorkorderModel> List<TWorkorderModel> collectWorkorderList(
            List<? extends BuildWorkOrderReturnContext> contextList,
            Class<TWorkorderModel> clazz) {

        return contextList.stream()
                .map(BuildWorkOrderReturnContext::getWorkorder)
                .filter(Objects::nonNull)
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .collect(Collectors.toList());
    }


    /**
     * 收集PrepWorkorder实体列表
     *
     * @param contextList           工单上下文列表
     * @param clazz                 期望的PrepWorkorderModel子类类型
     * @param <TPrepWorkorderModel> PrepWorkorderModel的子类
     * @return PrepWorkorder实体列表
     */
    public static <TPrepWorkorderModel extends PrepWorkorderModel> List<TPrepWorkorderModel> collectPrepWorkorderList(
            List<? extends BuildWorkOrderReturnContext> contextList,
            Class<TPrepWorkorderModel> clazz) {

        return getWorkOrderDetailStream(contextList)
                .map(BuildWorkOrderDetailReturnContext::getPrepWorkorder)
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .collect(Collectors.toList());
    }

    /**
     * 收集WorkorderDetail实体列表
     *
     * @param contextList             工单上下文列表
     * @param clazz                   期望的PrepWorkorderModel子类类型
     * @param <TWorkorderDetailModel> PrepWorkorderModel的子类
     * @return PrepWorkorder实体列表
     */
    public static <TWorkorderDetailModel extends WorkorderDetailModel> List<TWorkorderDetailModel> collectWorkorderDetailList(
            List<? extends BuildWorkOrderReturnContext> contextList,
            Class<TWorkorderDetailModel> clazz) {

        return getWorkOrderDetailStream(contextList)
                .map(BuildWorkOrderDetailReturnContext::getWorkorderDetail)
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .collect(Collectors.toList());
    }

    /**
     * 收集PrepWorkorderDetail实体列表
     *
     * @param contextList                 工单上下文列表
     * @param clazz                       期望的PrepWorkorderDetailModel子类类型
     * @param <TPrepWorkorderDetailModel> PrepWorkorderDetailModel的子类
     * @return PrepWorkorderDetail实体列表
     */
    public static <TPrepWorkorderDetailModel extends PrepWorkorderDetailModel> List<TPrepWorkorderDetailModel> collectPrepWorkorderDetailList(
            List<? extends BuildWorkOrderReturnContext> contextList,
            Class<TPrepWorkorderDetailModel> clazz) {

        return getWorkOrderDetailStream(contextList)
                .flatMap(detailContext -> safeStream(detailContext.getPrepWorkOrderDetailReturnContextList()))
                .map(BuildPrepWorkOrderDetailReturnContext::getPrepWorkorderDetail)
                .filter(clazz::isInstance)
                .map(clazz::cast)
                .collect(Collectors.toList());
    }

    /**
     * 收集PrepWorkorderDetail相关的锁定记录
     *
     * @param contextList 工单上下文列表
     * @return InventoryLocked列表
     */
    public static List<InventoryLocked> collectPrepLockedList(List<? extends BuildWorkOrderReturnContext> contextList) {
        return getWorkOrderDetailStream(contextList)
                .flatMap(detailContext -> safeStream(detailContext.getPrepWorkOrderDetailReturnContextList()))
                .map(BuildPrepWorkOrderDetailReturnContext::getInventoryLocked)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 收集WorkOrderDetail相关的预留记录
     *
     * @param contextList 工单上下文列表
     * @return InventoryReserve列表
     */
    public static List<InventoryReserve> collectInventoryReserveList(List<? extends BuildWorkOrderReturnContext> contextList) {
        return getWorkOrderDetailStream(contextList)
                .map(BuildWorkOrderDetailReturnContext::getInventoryReserve)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 收集WorkOrderDetail相关的锁定记录
     *
     * @param contextList 工单上下文列表
     * @return InventoryLocked列表
     */
    public static List<InventoryLocked> collectWorkOrderLockedList(List<? extends BuildWorkOrderReturnContext> contextList) {
        return getWorkOrderDetailStream(contextList)
                .map(BuildWorkOrderDetailReturnContext::getInventoryLocked)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 安全地将可能为null的列表转换为流。
     *
     * @param list 可能为null的列表
     * @param <T>  列表元素类型
     * @return 非null的流
     */
    private static <T> Stream<T> safeStream(List<T> list) {
        return list == null ? Stream.empty() : list.stream();
    }


    /**
     * 格式化行号、填充InventoryLocked / Reserve等引用
     *
     * @param contextList 工单上下文列表
     */
    public static void finalizeLineNumberAndInventoryRefs(List<? extends BuildWorkOrderReturnContext> contextList) {
        // 遍历每一个工单上下文
        for (BuildWorkOrderReturnContext ctx : contextList) {
            List<BuildWorkOrderDetailReturnContext> detailCtxList = ctx.getWorkorderDetailContextList();
            if (ObjectUtil.isEmpty(detailCtxList)) {
                throw new BusinessException("Error Allocate WorkOrderDetail, Current Is Empty");
            }
            // 遍历每一个明细上下文
            for (int i = 0; i < detailCtxList.size(); i++) {
                BuildWorkOrderDetailReturnContext dCtx = detailCtxList.get(i);
                int lineNum = i + 1;
                // 设置明细的行号
                dCtx.getWorkorderDetail().setLineNum(lineNum);

                // 如果有库存锁定，设置引用行号
                if (dCtx.getInventoryLocked() != null) {
                    dCtx.getInventoryLocked().setRefTableRefNum(String.valueOf(lineNum));
                    dCtx.getInventoryLocked().setRefTableShowRefNum(ctx.getWorkorder().getRefNum());
                }
                // 如果有库存预留，设置引用行号
                if (dCtx.getInventoryReserve() != null) {
                    dCtx.getInventoryReserve().setRefTableRefNum(String.valueOf(lineNum));
                    dCtx.getInventoryReserve().setRefTableShowRefNum(ctx.getWorkorder().getRefNum());
                }

                // 处理预处理工单明细列表
                List<BuildPrepWorkOrderDetailReturnContext> prepList = dCtx.getPrepWorkOrderDetailReturnContextList();
                if (ObjectUtil.isNotEmpty(prepList)) {
                    for (int j = 0; j < prepList.size(); j++) {
                        BuildPrepWorkOrderDetailReturnContext pdCtx = prepList.get(j);
                        // 设置预处理明细的行号
                        pdCtx.getPrepWorkorderDetail().setLineNum(j + 1);
                        // 如果有库存锁定，设置引用行号
                        if (pdCtx.getInventoryLocked() != null) {
                            pdCtx.getInventoryLocked().setRefTableRefNum(String.valueOf(j + 1));
                            pdCtx.getInventoryLocked().setRefTableShowRefNum(dCtx.getPrepWorkorder().getRefNum());
                        }
                    }
                }
            }
        }
    }
}
