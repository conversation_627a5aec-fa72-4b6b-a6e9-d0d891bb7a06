package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPrepPickingSlipDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPrepPickingSlip;
import cn.need.cloud.biz.model.vo.oco.OcoPrepPickingSlipVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO预处理拣货单表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPrepPickingSlipConverter extends AbstractModelConverter<OcoPrepPickingSlip, OcoPrepPickingSlipVO, OcoPrepPickingSlipDTO> {

}



