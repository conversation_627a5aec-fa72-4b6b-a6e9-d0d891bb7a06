package cn.need.cloud.biz.controller.product;

import cn.need.cloud.biz.converter.product.ProductGroupConverter;
import cn.need.cloud.biz.model.entity.product.ProductGroup;
import cn.need.cloud.biz.model.param.product.update.ProductGroupCreateOrUpdateParam;
import cn.need.cloud.biz.model.vo.product.ProductGroupListVO;
import cn.need.cloud.biz.model.vo.product.ProductGroupVO;
import cn.need.cloud.biz.service.product.ProductGroupService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * 产品同类 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/product-group")
@Tag(name = "产品同类")
public class ProductGroupController extends AbstractRestController<ProductGroupService, ProductGroup, ProductGroupConverter, ProductGroupVO> {


    @Operation(summary = "新增or修改产品同类", description = "接收产品同类的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/createOrUpdate")
    public Result<Integer> createOrUpdate(@Valid @RequestBody @Parameter(description = "数据对象", required = true) List<ProductGroupCreateOrUpdateParam> paramList) {

        // 返回结果
        return success(service.createOrUpdate(paramList));
    }

    /**
     * <p>
     * 根据搜索条件参数获取分页后的列表数据
     * </p>
     *
     * @param productId 搜索条件参数
     * @return 分页后的列表数据
     */
    @Operation(summary = "获取产品同类parentList分页父类下的子类列表", description = "根据传入的搜索条件参数，从数据库中获取后的数据列表")
    @GetMapping(value = "/parentList/{productId}")
    public Result<List<ProductGroupListVO>> parentList(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        List<ProductGroupListVO> detailVo = service.parentList(productId);
        return success(detailVo);
    }

    /**
     * <p>
     * 根据搜索条件参数获取列表数据
     * </p>
     *
     * @param productId 搜索参数
     * @return 列表数据
     */
    @Operation(summary = "获取数据group中groupBeConvert接口", description = "根据传入的搜索条件参数，从数据库中获取分页后的数据列表")
    @GetMapping(value = "/childList/{productId}")
    public Result<List<ProductGroupListVO>> childList(@PathVariable("productId") @Parameter(description = "搜索条件参数", required = true) Long productId) {

        List<ProductGroupListVO> detailVo = service.childList(productId);
        return success(detailVo);
    }
}
