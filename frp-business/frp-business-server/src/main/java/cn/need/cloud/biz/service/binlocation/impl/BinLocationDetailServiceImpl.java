package cn.need.cloud.biz.service.binlocation.impl;

import cn.need.cloud.biz.cache.bean.BinLocationCache;
import cn.need.cloud.biz.cache.bean.ProductVersionCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.client.constant.enums.binlocation.BinLocationLogEnum;
import cn.need.cloud.biz.converter.binlocation.BinLocationDetailConverter;
import cn.need.cloud.biz.converter.inventory.InventoryBinLocationDetailConverter;
import cn.need.cloud.biz.mapper.binlocation.BinLocationDetailMapper;
import cn.need.cloud.biz.model.bo.binlocation.*;
import cn.need.cloud.biz.model.bo.inbound.BinLocationDetailContextBO;
import cn.need.cloud.biz.model.entity.base.RefNumModel;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.entity.inbound.InboundWorkorder;
import cn.need.cloud.biz.model.entity.log.BinLocationLog;
import cn.need.cloud.biz.model.entity.product.ProductVersion;
import cn.need.cloud.biz.model.query.base.BaseBinLocationQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationChangeVO;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.inventory.InventoryBinLocationDetailVO;
import cn.need.cloud.biz.model.vo.product.ProductVersionVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderService;
import cn.need.cloud.biz.service.log.BinLocationLogService;
import cn.need.cloud.biz.service.product.ProductVersionService;
import cn.need.cloud.biz.util.BinLocationCacheUtil;
import cn.need.cloud.biz.util.Builder;
import cn.need.cloud.biz.util.ProductVersionCacheUtil;
import cn.need.cloud.biz.util.StreamUtils;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.constant.StringPool;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.redis.RedissonKit;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 库位详情 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@Service
public class BinLocationDetailServiceImpl extends SuperServiceImpl<BinLocationDetailMapper, BinLocationDetail> implements BinLocationDetailService {

    @Resource
    private BinLocationDetailLockedService binLocationDetailLockedService;
    @Resource
    private BinLocationLogService binLocationLogService;
    @Resource
    @Lazy
    private BinLocationService binLocationService;
    @Resource
    @Lazy
    private InboundWorkorderService inboundWorkorderService;
    @Resource
    @Lazy
    private ProductVersionService productVersionService;

    /**
     * 校验上架参数合法
     *
     * @param putAwayContext 上架参数
     */
    private static void checkPut(BinLocationDetailPutawayBO putAwayContext) {
        Validate.notNull(putAwayContext, "PutAway Product must not null");

        // 上架日志对象
        RefNumModel putawayModel = putAwayContext.getRefNumModel();
        Validate.notNull(putawayModel, "Putaway Model must not null");

        // 产品
        Validate.notNull(putAwayContext.getProductId(),
                "{} PutAway Product must not null",
                putawayModel.refNumLog()
        );

        // 产品版本
        Validate.notNull(putAwayContext.getProductVersionId(),
                "{} PutAway Product Version must not null",
                putawayModel.refNumLog()
        );

        // 上架类型
        Validate.notNull(putAwayContext.getChangeType(),
                "{} PutAway ChangeType must not null",
                putawayModel.refNumLog()
        );

        // 上架库位
        Validate.notNull(putAwayContext.getBinLocationId(),
                "{} PutAway binLocationId must not null",
                putawayModel.refNumLog()
        );

        // 校验库位是否存在
        BinLocationCache binLocationCache = BinLocationCacheUtil.getById(putAwayContext.getBinLocationId());
        Validate.notNull(binLocationCache, "{} PutAway binLocation is not exits",
                putAwayContext.getRefNumModel().refNumLog()
        );

        // 上架数量
        Validate.isTrue(putAwayContext.getPutawayQty() >= 0,
                "{} PutAway Product quantity must be greater than 0",
                putawayModel.refNumLog()
        );
    }

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Override
    public BinLocationDetailVO detailById(Long id) {
        BinLocationDetail entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocationDetail");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationDetail", id));
        }
        return buildBinLocationDetailVO(entity);
    }

    @Override
    public List<BinLocationDetailVO> listByBinLocationId(Long binLocationId) {
        List<BinLocationDetail> list = lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .gt(BinLocationDetail::getInStockQty, 0)
                .orderByDesc(BinLocationDetail::getCreateTime)
                .list();
        return Converters.get(BinLocationDetailConverter.class).toVO(list);
    }

    /**
     * 判断库位是否存在产品
     * 该方法用于判断库位是否存在产品，传入库位id
     *
     * @param id 库位id
     * @return 详情列表
     */
    @Override
    public boolean exist(Long id) {
        return mapper.exists(Wrappers.<BinLocationDetail>lambdaQuery().eq(BinLocationDetail::getBinLocationId, id));
    }

    /**
     * 根据产品id及库位id获取库位详情
     *
     * @param productVersionId 产品id
     * @param binLocationId    库位id
     * @return 库位详情
     */
    @Override
    public BinLocationDetail getByBinLocationIdAndProductVersionId(Long productVersionId, Long binLocationId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .eq(BinLocationDetail::getProductVersionId, productVersionId)
                .one();
    }

    @Override
    public BinLocationDetail getOrGenerateBinLocationDetailByBinLocationIdAndProductVersionId(Long binLocationId, Long productVersionId) {
        // 第一次检查：先尝试查询，大部分情况下可以直接返回，避免加锁开销
        BinLocationDetail dbBinLocationDetail = getByBinLocationIdAndProductVersionId(productVersionId, binLocationId);
        if (ObjectUtil.isNotEmpty(dbBinLocationDetail)) {
            return dbBinLocationDetail;
        }

        // 只有在没查询到的情况下才加锁
        AtomicReference<BinLocationDetail> result = new AtomicReference<>();
        RedissonKit.getInstance().lock("GENERATE_BIN_LOCATION_DETAIL:" + binLocationId + ":" + productVersionId, lock -> {
            // 第二次检查：在锁内再次查询，防止并发情况下重复创建
            BinLocationDetail doubleCheckResult = getByBinLocationIdAndProductVersionId(productVersionId, binLocationId);
            if (ObjectUtil.isNotEmpty(doubleCheckResult)) {
                result.set(doubleCheckResult);
                return;
            }

            // 确认没有记录后，创建新的库位详情
            ProductVersionCache productVersion = ProductVersionCacheUtil.getById(productVersionId);
            if (ObjectUtil.isEmpty(productVersion)) {
                //todo: 这种要抛出 Impossible
                throw new BusinessException(StringUtil.format("{} Can not Found ProductVersion In ProductVersionCacheUtil", productVersionId));
            }

            BinLocationDetail binLocationDetail = new BinLocationDetail();
            binLocationDetail.setInStockQty(0);
            binLocationDetail.setDeletedNote(null);
            binLocationDetail.setTenantId(null);
            binLocationDetail.setWarehouseId(null);
            binLocationDetail.setProductVersionId(productVersionId);
            binLocationDetail.setProductId(productVersion.getProductId());
            binLocationDetail.setBinLocationId(binLocationId);
            binLocationDetail.setCreateBy(null);
            binLocationDetail.setUpdateBy(null);
            binLocationDetail.setCreateTime(null);
            binLocationDetail.setUpdateTime(null);
            binLocationDetail.setRemoveFlag(null);
            binLocationDetail.setVersion(null);
            binLocationDetail.setId(IdWorker.getId());
            super.insert(binLocationDetail);
            result.set(binLocationDetail);
        });
        return result.get();
    }

    public BinLocationDetail getByBinLocationIdAndProductId(Long productId, Long binLocationId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .eq(BinLocationDetail::getProductId, productId)
                .orderByDesc(BinLocationDetail::getInStockQty)
                .orderByDesc(BinLocationDetail::getUpdateTime)
                .one();
    }

    @Override
    public BinLocationDetail getOrGenerateBinLocationDetailByBinLocationIdAndProductId(Long binLocationId, Long productId) {
        // 第一次检查：先尝试查询，大部分情况下可以直接返回，避免加锁开销
        BinLocationDetail dbBinLocationDetail = getByBinLocationIdAndProductId(productId, binLocationId);
        if (ObjectUtil.isNotEmpty(dbBinLocationDetail)) {
            return dbBinLocationDetail;
        }

        // 只有在没查询到的情况下才加锁
        AtomicReference<BinLocationDetail> result = new AtomicReference<>();
        RedissonKit.getInstance().lock("GENERATE_BIN_LOCATION_DETAIL:" + binLocationId + ":" + productId, lock -> {
            // 第二次检查：在锁内再次查询，防止并发情况下重复创建
            BinLocationDetail doubleCheckResult = getByBinLocationIdAndProductId(productId, binLocationId);
            if (ObjectUtil.isNotEmpty(doubleCheckResult)) {
                result.set(doubleCheckResult);
                return;
            }

            // 确认没有记录后，创建新的库位详情
            final ProductVersionVO productVersion = productVersionService.getLatestProductVersionByProductId(productId);
            if (ObjectUtil.isEmpty(productVersion)) {
                //todo: 这种要抛出 Impossible
                throw new BusinessException(StringUtil.format("ProductId: {} Can not Found ProductVersion In productVersionService getLatestProductVersionByProductId", productId));
            }

            BinLocationDetail binLocationDetail = new BinLocationDetail();
            binLocationDetail.setInStockQty(0);
            binLocationDetail.setDeletedNote(null);
            binLocationDetail.setTenantId(null);
            binLocationDetail.setWarehouseId(null);
            binLocationDetail.setProductVersionId(productVersion.getId());
            binLocationDetail.setProductId(productVersion.getProductId());
            binLocationDetail.setBinLocationId(binLocationId);
            binLocationDetail.setCreateBy(null);
            binLocationDetail.setUpdateBy(null);
            binLocationDetail.setCreateTime(null);
            binLocationDetail.setUpdateTime(null);
            binLocationDetail.setRemoveFlag(null);
            binLocationDetail.setVersion(null);
            binLocationDetail.setId(IdWorker.getId());
            super.insert(binLocationDetail);
            result.set(binLocationDetail);
        });
        return result.get();
    }


    /**
     * 根据产品id查询库存大于0的库位详情
     *
     * @param productIds 产品id集合
     * @return 库位详情集合
     */
    @Override
    public List<InventoryBinLocationDetailVO> listInventoryPositiveInStockQtyByProductIds(List<Long> productIds) {
        // 获取库位信息
        List<BinLocationDetail> binLocationDetails = listEntitiesPositiveInStockQtyByProductIds(productIds);

        List<InventoryBinLocationDetailVO> result = Converters.get(InventoryBinLocationDetailConverter.class).toVO(binLocationDetails);

        List<Long> binLocationDetailIds = binLocationDetails.stream().map(BinLocationDetail::getId).toList();

        // 库位锁定
        List<InventoryBinLocationDetailLockedVO> binLocationDetailLockedList = binLocationDetailLockedService.listInventoryLockedByProductIdsAndBinLocationIds(productIds, binLocationDetailIds);

        result.forEach(item ->
        {
            List<InventoryBinLocationDetailLockedVO> currentLockedList = binLocationDetailLockedList.stream()
                    .filter(x -> x.getProductId().equals(item.getProductId())
                            && x.getBinLocationDetailId().equals(item.getId()))
                    .toList();
            item.setBinLocationDetailLockedList(currentLockedList);
        });
        // 设置 binLocation
        BinLocationCacheUtil.filledBinLocation(result);
        // 按照 locationName 排序
        result = result.stream()
                .sorted(Comparator.comparing(x -> x.getBaseBinLocationFullVO().getLocationName()))
                .toList();

        return result;
    }

    /**
     * 根据产品id查询库存大于0的库位详情
     *
     * @param productIds 产品id集合
     * @return 库位详情集合
     */
    @Override
    public List<BinLocationDetail> listEntitiesPositiveInStockQtyByProductIds(List<Long> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        // 返回库位信息
        return lambdaQuery()
                .in(BinLocationDetail::getProductId, productIds)
                .gt(BinLocationDetail::getInStockQty, NumberUtils.INTEGER_ZERO)
                .list();
    }

    /**
     * 根据仓库id获取库位详情
     * 该方法用于获取库位详情，传入仓库id
     *
     * @param id 仓库id
     * @return 库位详情集合
     */
    @Override
    public List<BinLocationDetail> listByWarehouseId(Long id) {
        return lambdaQuery().eq(BinLocationDetail::getWarehouseId, id).list();
    }

    /**
     * 根据仓库id删除库位详情
     * 该方法用于删除库位详情，传入仓库id
     *
     * @param id 仓库id
     */
    @Override
    public void removeByWarehouseId(Long id) {
        lambdaUpdate()
                .eq(BinLocationDetail::getWarehouseId, id)
                .set(BinLocationDetail::getRemoveFlag, DataState.ENABLED)
                .update();
    }

    /**
     * 根据库位id删除库位详情
     * 该方法用于删除库位详情，传入库位id
     *
     * @param id 库位id
     */
    @Override
    public void removeByBinLocationId(Long id) {
        lambdaUpdate().eq(BinLocationDetail::getBinLocationId, id).set(BinLocationDetail::getRemoveFlag, DataState.ENABLED);
    }

    @Override
    public void generateBinLocationDetail(BinLocationDetailContextBO context) {
        // 获取库位详情
        BinLocationDetail binLocationDetail = getByBinLocationIdAndProductVersionId(context.getProductVersionId(), context.getBinLocationId());
        // 库位变化对象
        BinLocationChangeVO binLocationChangeVO = new BinLocationChangeVO();
        // 获取工单信息
        InboundWorkorder inboundWorkorder = inboundWorkorderService.getById(context.getInboundWorkOrderId());
        binLocationChangeVO.setChangeInStockQty(context.getInStockQty());
        // 判空
        if (ObjectUtil.isNotEmpty(binLocationDetail)) {
            // 若存在库位详情则只做更新
            binLocationDetail.setInStockQty(binLocationDetail.getInStockQty() + context.getInStockQty());
        } else {
            // 生成库位详情
            binLocationDetail = BeanUtil.copyNew(context, BinLocationDetail.class);
        }
        // 持久化数据库
        super.insertOrUpdate(binLocationDetail);
        // 填充库位详情
        context.setBinLocationDetailId(binLocationDetail.getId());
        // 记录日志 上架保存带有changeType库位日志
        BinLocationLog binLocationLog = binLocationLogService.buildBinLocationLogWithChangeType(inboundWorkorder, binLocationDetail, binLocationChangeVO, BinLocationLogEnum.INBOUND_WORKORDER.getStatus());
        AuditLogHolder.binLocationRecord(binLocationLog);
    }

    @Override
    public void updateInStockByChange(Collection<? extends BinLocationDetailChangeBO> changeList) {

        Validate.notEmpty(changeList, String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "Location information"));

        Set<BinLocationDetail> binLocationDetailList = new HashSet<>();
        for (BinLocationDetailChangeBO move : changeList) {
            Validate.notNull(move.getSource(), "updateInStockByChange Source bin location detail cannot be empty");
            Validate.notNull(move.getDest(), "updateInStockByChange Dest bin location detail cannot be empty");

            // 移动库位
            move.move();

            // 增加库位日志
            AuditLogHolder.binLocationRecord(move.toBinLocationLog());

            // 添加源和目标库位详情到集合中（自动去重）
            binLocationDetailList.add(move.getSource());
            binLocationDetailList.add(move.getDest());
        }
        // 更新库位详情
        Validate.isTrue(binLocationDetailList.size() == super.updateBatch(binLocationDetailList),
                "Failed to update bin location detail inventory quantity"
        );
    }

    @Override
    public Map<Long, Integer> realAvailableInStockGroupByProductId(BaseBinLocationQuery binLocationQuery, List<Long> productIdList) {
        return realAvailableGroupByProductId(binLocationQuery, productIdList).entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream().mapToInt(BinLocationDetail::getInStockQty).sum())
                );
    }

    @Override
    public Map<Long, List<BinLocationDetail>> realAvailableGroupByProductId(BaseBinLocationQuery binLocationQuery, List<Long> productIdList) {
        if (ObjectUtil.isEmpty(productIdList) && ObjectUtil.isNull(binLocationQuery)) {
            return Collections.emptyMap();
        }
        return reduceRealAvailableGroupByProductId(mapper.listByBinLocationAndProductId(binLocationQuery, productIdList));
    }

    /**
     * @param realAvailableList 可用库位详情集合
     * @return
     */
    @Override
    public Map<Long, List<BinLocationDetail>> groupByProductAndSortByInStockAndLocationName(List<BinLocationDetail> realAvailableList) {
        // 库位id
        List<Long> binLocationIdList = StreamUtils.distinctMap(realAvailableList, BinLocationDetail::getBinLocationId);
        // 库位缓存
        List<BinLocationCache> binLocationCacheList = BinLocationCacheUtil.listByIds(binLocationIdList);
        // 获取库位名称
        Map<Long, String> binLocationNameMap = binLocationCacheList
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(BinLocationCache::getId, BinLocationCache::getLocationName));

        // 先按 productId 分组
        Map<Long, List<BinLocationDetail>> groupedMap = new HashMap<>();
        for (BinLocationDetail detail : realAvailableList) {
            Long productId = detail.getProductId();
            groupedMap.computeIfAbsent(productId, k -> new ArrayList<>()).add(detail);
        }

        for (Map.Entry<Long, List<BinLocationDetail>> entry : groupedMap.entrySet()) {
            List<BinLocationDetail> list = entry.getValue();
            list.sort(Comparator.comparing(BinLocationDetail::getInStockQty)
                    .thenComparing(obj -> binLocationNameMap.getOrDefault(obj.getBinLocationId(), StringPool.EMPTY)));
        }

        return groupedMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @Deprecated
    public BinLocationDetail putAwayVirtual(BinLocationDetailContextBO putAwayContext) {
        /* 优化建议: 当前实现存在以下问题：
         * 1. 使用了分布式锁来保证原子性，但锁的粒度可能过大，影响并发性能
         * 2. 没有对库存数量进行验证，可能导致数据异常
         * 3. 方法已经标记为过时（@Deprecated），但仍然在使用
         * 4. 错误处理不够完善，可能导致问题难以跟踪
         *
         * 优化建议：
         * 1. 使用更细粒度的锁，如基于库位ID和产品ID的组合锁，提高并发性能
         * 2. 添加库存数量验证，确保数据有效性
         * 3. 如果该方法确实过时，应该提供替代方法并迁移调用代码
         * 4. 增强错误处理和日志记录，便于问题跟踪
         * 5. 考虑使用事务模板或重试机制处理并发冲突
         */
        Validate.notNull(putAwayContext, "PutAway Product must not null");
        Long binLocationId = putAwayContext.getBinLocationId();
        int putAwayQty = putAwayContext.getInStockQty();

        AtomicReference<BinLocationDetail> result = new AtomicReference<>();
        RedissonKit.getInstance().lock(RedisConstant.BIN_LOCATION_DETAIL_CREATE_LOCK_PREFIX + binLocationId, lock -> {
            BinLocationDetail binLocationDetail = getByBinLocationIdAndProductVersionId(putAwayContext.getProductVersionId(), binLocationId);
            // 不存在则创建
            if (ObjectUtil.isNull(binLocationDetail)) {
                binLocationDetail = new BinLocationDetail();
                binLocationDetail.setBinLocationId(binLocationId);
                binLocationDetail.setProductId(putAwayContext.getProductId());
                binLocationDetail.setProductVersionId(putAwayContext.getProductVersionId());
                binLocationDetail.setInStockQty(0);
            }

            binLocationDetail.setInStockQty(binLocationDetail.getInStockQty() + putAwayQty);
            // 增加库位库存
            int update = insertOrUpdate(binLocationDetail);
            Validate.isTrue(update == 1,
                    "{} Failed to update bin location detail inventory quantity",
                    putAwayContext.toLog()
            );
            result.set(binLocationDetail);
        });
        BinLocationDetail binLocationDetail = result.get();

        // 构建库位日志
        BinLocationLog putAwayBinLocationLog = AuditLogUtil.sameBinLocationLog(putAwayContext.getRefNumModel(), binLocationDetail)
                .with(BinLocationLog::setChangeType, BinLocationLogEnum.OTC_PREP_PUT_AWAY.getStatus())
                .with(BinLocationLog::setSourceChangeInStockQty, putAwayQty)
                .with(BinLocationLog::setSourceBeforeInStockQty, binLocationDetail.getInStockQty() - putAwayQty)
                .build();

        // 记录日志
        AuditLogHolder.binLocationRecord(putAwayBinLocationLog);
        return binLocationDetail;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BinLocationDetail putIfAbsent(BinLocationDetailPutawayBO putAwayContext) {
        // 校验
        checkPut(putAwayContext);

        Long binLocationId = putAwayContext.getBinLocationId();
        int putAwayQty = putAwayContext.getPutawayQty();

        AtomicReference<BinLocationDetail> result = new AtomicReference<>();
        RedissonKit.getInstance().lock(RedisConstant.BIN_LOCATION_DETAIL_CREATE_LOCK_PREFIX + binLocationId, lock -> {
            BinLocationDetail binLocationDetail = getByBinLocationIdAndProductVersionId(putAwayContext.getProductVersionId(), binLocationId);
            // 不存在则创建
            if (ObjectUtil.isNull(binLocationDetail)) {
                binLocationDetail = new BinLocationDetail();
                binLocationDetail.setBinLocationId(binLocationId);
                binLocationDetail.setProductId(putAwayContext.getProductId());
                binLocationDetail.setProductVersionId(putAwayContext.getProductVersionId());
                binLocationDetail.setInStockQty(0);
            }

            binLocationDetail.setInStockQty(binLocationDetail.getInStockQty() + putAwayQty);

            // 增加库位库存
            int update = super.insertOrUpdate(binLocationDetail);
            Validate.isTrue(update == 1,
                    "{} Failed to update bin location detail inventory quantity",
                    putAwayContext.toLog()
            );
            result.set(binLocationDetail);
        });
        return result.get();
    }

    @Override
    public BinLocationDetail getNoZero(Long binLocationId, Long productId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .eq(BinLocationDetail::getProductId, productId)
                .gt(BinLocationDetail::getInStockQty, 0)
                .one();
    }

    @Override
    public boolean exist(Long binLocationId, Long productId, Long productVersionId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .eq(BinLocationDetail::getProductId, productId)
                .ne(BinLocationDetail::getProductVersionId, productVersionId)
                .gt(BinLocationDetail::getInStockQty, 0)
                .exists();
    }

    @Override
    public List<BinLocationDetail> listByProductId(Long productId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getProductId, productId)
                .list();
    }

    @Override
    public List<BinLocationDetail> listByProductVersionId(Long productVersionId) {
        return lambdaQuery()
                .eq(BinLocationDetail::getProductVersionId, productVersionId)
                .list();
    }

    @Override
    public List<BinLocationDetail> list(List<Long> productVersionIdList, Long binLocationId) {
        return lambdaQuery()
                .in(BinLocationDetail::getProductVersionId, productVersionIdList)
                .eq(BinLocationDetail::getBinLocationId, binLocationId)
                .list();
    }

    @Override
    public List<BinLocationDetail> list(Long destBinLocationId, Collection<Long> values) {
        return lambdaQuery()
                .eq(BinLocationDetail::getBinLocationId, destBinLocationId)
                .in(BinLocationDetail::getProductId, values)
                .gt(BinLocationDetail::getInStockQty, 0)
                .list();
    }

    /**
     * 查询指定仓库中有库存的库位详情
     * <p>
     * 用于快照构建，查询指定仓库中所有库存数量大于0的库位详情。
     * 这些库位详情将用于历史库存倒推计算。
     * </p>
     *
     * @param warehouseId 仓库ID
     * @return 有库存的库位详情列表
     */
    @Override
    public List<BinLocationDetail> listByWarehouseIdWithStock(Long warehouseId) {
        if (ObjectUtil.isEmpty(warehouseId)) {
            return Collections.emptyList();
        }

        return lambdaQuery()
                .eq(BinLocationDetail::getWarehouseId, warehouseId)
                .gt(BinLocationDetail::getInStockQty, NumberUtils.INTEGER_ZERO)
                .list();
    }

    @Override
    public Boolean resetBinLocationEmpty(Long warehouseId) {
        return lambdaUpdate()
                .eq(BinLocationDetail::getWarehouseId, warehouseId)
                .set(BinLocationDetail::getInStockQty, 0)
                .update();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public <T extends RefNumModel> BinLocationDetail stocktaking(BinLocationStocktakingBO<T> stocktaking) {
        Validate.notNull(stocktaking, "stocktaking must not null");
        Long binLocationId = stocktaking.getBinLocationId();

        AtomicReference<BinLocationDetail> result = new AtomicReference<>();
        RedissonKit.getInstance().lock(RedisConstant.BIN_LOCATION_DETAIL_CREATE_LOCK_PREFIX + binLocationId, lock -> {
            BinLocationDetail binLocationDetail = getNoZero(stocktaking.getProductId(), binLocationId);
            // 不存在则创建
            if (ObjectUtil.isNull(binLocationDetail)) {
                binLocationDetail = new BinLocationDetail();
                binLocationDetail.setBinLocationId(binLocationId);
                binLocationDetail.setProductId(stocktaking.getProductId());
                binLocationDetail.setProductVersionId(stocktaking.getProductVersionId());
                binLocationDetail.setInStockQty(0);
            }

            // 同产品不同版本不允许在一个库位中(虚拟库位除外)
            boolean isDefault = Optional.ofNullable(stocktaking.getBinLocationId())
                    .map(BinLocationCacheUtil::getById)
                    .filter(BinLocationCache::getDefaultFlag)
                    .isPresent();
            boolean canStocktaking = isDefault
                    // 同产品不同版本不允许在一个库位中
                    || Objects.equals(binLocationDetail.getProductVersionId(), stocktaking.getProductVersionId());
            Validate.isTrue(canStocktaking, "{} Inventory: Other versions of the product already exist", stocktaking.toLog());

            // 构建库位日志
            Builder<BinLocationLog> builder = AuditLogUtil.sameBinLocationLog(stocktaking.getModel(), binLocationDetail)
                    .with(BinLocationLog::setChangeType, stocktaking.getChangeType())
                    .with(BinLocationLog::setSourceChangeInStockQty, stocktaking.getStocktakingQty() - binLocationDetail.getInStockQty())
                    .with(BinLocationLog::setSourceBeforeInStockQty, binLocationDetail.getInStockQty())
                    .with(BinLocationLog::setSourceAfterInStockQty, stocktaking.getStocktakingQty());

            binLocationDetail.setInStockQty(stocktaking.getStocktakingQty());
            // 盘点更新
            Validate.isTrue(super.insertOrUpdate(binLocationDetail) == 1,
                    "{} Failed to update bin location detail inventory quantity",
                    stocktaking.toLog()
            );

            // 记录日志
            AuditLogHolder.binLocationRecord(builder.build());
            result.set(binLocationDetail);
        });

        return result.get();
    }

    @Override
    public void updateBinLocationDetail(BinLocationDetailContextBO context) {
        // 获取库位详情
        BinLocationDetail binLocationDetail = getByBinLocationIdAndProductVersionId(context.getProductVersionId(), context.getBinLocationId());
        // 库位变化对象
        BinLocationChangeVO binLocationChangeVO = new BinLocationChangeVO();
        binLocationChangeVO.setChangeInStockQty(context.getInStockQty());
        // 判空
        if (ObjectUtil.isNotEmpty(binLocationDetail)) {
            // 填充当前值
            context.setCurrentInStockQty(binLocationDetail.getInStockQty());
            // 获取变化数量
            binLocationChangeVO.setChangeInStockQty(context.getInStockQty() - binLocationDetail.getInStockQty());
            // 若存在库位详情则只做更新
            binLocationDetail.setInStockQty(context.getInStockQty());
        } else {
            // 生成库位详情
            binLocationDetail = BeanUtil.copyNew(context, BinLocationDetail.class);
        }
        // 持久化数据库
        super.insertOrUpdate(binLocationDetail);
        // 填充库位详情
        context.setBinLocationDetailId(binLocationDetail.getId());
        // 记录日志 上架保存带有changeType库位日志
        BinLocationLog binLocationLog = binLocationLogService.buildBinLocationLogWithChangeType(context.getRefNumModel(), binLocationDetail, binLocationChangeVO, BinLocationLogEnum.INVENTORY_AUDIT.getStatus());
        AuditLogHolder.binLocationRecord(binLocationLog);
    }

    /**
     * 根据库位/库位详情获取/创建库位详情锁信息
     *
     * @param binLocationId readyToGo库位
     * @param pickList      拣货列表
     * @return /
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<BinLocationDetail> batchCreateIfAbsent(Long binLocationId, List<BinLocationDetailBatchCreateBO> pickList) {

        // todo: 应该要 generateBinLocationDetail 方法要统一
        // 唯一的区别的就是 虚拟库位 一个库位上 可以多个 productVersionId

        List<Long> productVersionIdList = StreamUtils.distinctMap(pickList, BinLocationDetailBatchCreateBO::getProductVersionId);
        if (ObjectUtil.isEmpty(productVersionIdList)) {
            return Collections.emptyList();
        }
        List<BinLocationDetail> details = new ArrayList<>();
        RedissonKit.getInstance().lock(RedisConstant.BIN_LOCATION_DETAIL_CREATE_LOCK_PREFIX + binLocationId, lock -> {
            // 存在数据库中
            List<BinLocationDetail> binLocationDetailInStoreList = lambdaQuery()
                    .eq(BinLocationDetail::getBinLocationId, binLocationId)
                    .in(BinLocationDetail::getProductVersionId, productVersionIdList)
                    .list();
            Map<Long, BinLocationDetail> binLocationDetailMap = StreamUtils.toMap(binLocationDetailInStoreList, BinLocationDetail::getProductVersionId);
            // 需要创建的库位详情
            List<Long> needAddWithProductVersionIdList = productVersionIdList.stream()
                    .filter(obj -> !binLocationDetailMap.containsKey(obj))
                    .toList();

            // 产品版本id和productId的映射
            Map<Long, Long> productVersionIdMap = pickList.stream()
                    .collect(Collectors.groupingBy(
                            BinLocationDetailBatchCreateBO::getProductVersionId,
                            // 按照产品版本id分组，取第一个产品id
                            Collectors.mapping(BinLocationDetailBatchCreateBO::getProductId, Collectors.reducing((o1, o2) -> o1)))
                    )
                    .entrySet()
                    .stream()
                    // 过滤不存在的产品id
                    .filter(entry -> entry.getValue().isPresent())
                    .collect(Collectors.toMap(Map.Entry::getKey, obj -> obj.getValue().get()));
            // 创建
            List<BinLocationDetail> needAddList = needAddWithProductVersionIdList.stream()
                    .filter(productVersionIdMap::containsKey)
                    .map(productVersionId -> {
                        BinLocationDetail bl = new BinLocationDetail();
                        bl.setBinLocationId(binLocationId);
                        bl.setProductVersionId(productVersionId);
                        bl.setProductId(productVersionIdMap.get(productVersionId));
                        bl.setInStockQty(0);
                        return bl;
                    })
                    .toList();

            // 新建
            insertBatch(needAddList);
            details.addAll(binLocationDetailInStoreList);
            details.addAll(needAddList);
        });
        return details;
    }


    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////


    /**
     * 构建库位详情VO对象
     *
     * @param entity 库位详情对象
     * @return 返回包含详细信息的库位详情VO对象
     */
    private BinLocationDetailVO buildBinLocationDetailVO(BinLocationDetail entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的库位详情VO对象
        return Converters.get(BinLocationDetailConverter.class).toVO(entity);
    }

    @Override
    public boolean existInStockQtyByProductVersionId(Long productVersionId) {
        // 是否信息记录
        return lambdaQuery().eq(BinLocationDetail::getProductVersionId, productVersionId).count() > 0;
    }

    /**
     * 扣减计算实际可用库存
     *
     * @param binLocationDetailList 库位想起
     * @return /
     */
    private Map<Long, List<BinLocationDetail>> reduceRealAvailableGroupByProductId(List<BinLocationDetail> binLocationDetailList) {
        // 库存详情id
        List<Long> binLocationDetailIdList = StreamUtils.distinctMap(binLocationDetailList, IdModel::getId);
        // 获取锁定库存
        Map<Long, List<BinLocationDetailLocked>> lockedMap = binLocationDetailLockedService.groupByBinLocationIdList(binLocationDetailIdList);
        // 库位详情锁定数量映射
        Map<Long, Integer> lockedQtyMap = lockedMap.entrySet()
                .stream()
                // 获取锁定数量
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(obj -> obj.getQty() - obj.getFinishQty())
                        .mapToInt(Integer::intValue)
                        .sum())
                );
        Map<Long, BinLocation> virtualBinLocationMap = binLocationService.allVirtualBinLocationList();

        // 获取可用库存列表
        List<BinLocationDetail> realAvailableList = binLocationDetailList
                .stream()
                // 非虚拟库位
                .filter(obj -> !virtualBinLocationMap.containsKey(obj.getBinLocationId()))
                // 过滤掉有库存
                .filter(obj -> {
                    // 设置可用库存
                    obj.setInStockQty(obj.getInStockQty() - lockedQtyMap.getOrDefault(obj.getId(), 0));
                    return obj.getInStockQty() > 0;
                })
                .toList();

        return groupByProductAndSortByInStockAndLocationName(realAvailableList);
    }

    @Override
    public void updateInStockQtyWithLog(
            BinLocationDetail binLocationDetail, Integer changeQty,
            BinLocationLogRefBO refInfo, String changeType, String note) {
        // 参数验证
        Validate.notNull(binLocationDetail, "BinLocationDetail cannot be null");
        Validate.notNull(binLocationDetail.getId(), "BinLocationDetail ID cannot be null");
        Validate.notNull(changeQty, "Change quantity cannot be null");
        Validate.notNull(refInfo, "Reference info cannot be null");
        Validate.notEmpty(changeType, "Change type cannot be empty");

        // 使用分布式锁防止并发
        String lockKey = "BIN_LOCATION_DETAIL_UPDATE:" + binLocationDetail.getId();
        AtomicReference<Void> result = new AtomicReference<>();

        RedissonKit.getInstance().lock(lockKey, lock -> {
            // 重新查询最新数据，防止并发问题
            BinLocationDetail latestDetail = super.getById(binLocationDetail.getId());
            Validate.notNull(latestDetail, "BinLocationDetail with ID {} not found", binLocationDetail.getId());

            // 计算库存变更前后的数量
            int beforeInStockQty = ObjectUtil.nullToDefault(latestDetail.getInStockQty(), 0);
            int afterInStockQty = beforeInStockQty + changeQty;

            // 验证库存不能为负数
            Validate.isTrue(afterInStockQty >= 0,
                    "Insufficient stock. Current: {}, Change: {}, Result: {}",
                    beforeInStockQty, changeQty, afterInStockQty);

            // 更新库存数量
            latestDetail.setInStockQty(afterInStockQty);
            super.update(latestDetail);

            // 记录库位日志
            recordBinLocationLog(latestDetail, changeQty, beforeInStockQty, afterInStockQty, refInfo, changeType, note);

            result.set(null);
        });
    }

    @Override
    public void transferInStockQtyWithLog(
            BinLocationDetail sourceBinLocationDetail,
            BinLocationDetail destBinLocationDetail,
            Integer changeQty, BinLocationLogRefBO refInfo, String changeType, String note) {
        // 参数验证
        Validate.notNull(sourceBinLocationDetail, "Source BinLocationDetail cannot be null");
        Validate.notNull(destBinLocationDetail, "Destination BinLocationDetail cannot be null");
        Validate.notNull(sourceBinLocationDetail.getId(), "Source BinLocationDetail ID cannot be null");
        Validate.notNull(destBinLocationDetail.getId(), "Destination BinLocationDetail ID cannot be null");
        Validate.notNull(changeQty, "Change quantity cannot be null");
        Validate.notNull(refInfo, "Reference info cannot be null");
        Validate.notEmpty(changeType, "Change type cannot be empty");

        //如果库位相同，updateInStockQtyWithLog
        if (sourceBinLocationDetail.getBinLocationId().equals(destBinLocationDetail.getBinLocationId())) {
            updateInStockQtyWithLog(sourceBinLocationDetail, changeQty, refInfo, changeType, note);
            return;
        }

        // 使用分布式锁防止并发，按ID排序避免死锁
        Long sourceId = sourceBinLocationDetail.getId();
        Long destId = destBinLocationDetail.getId();
        String lockKey1 = "BIN_LOCATION_DETAIL_UPDATE:" + Math.min(sourceId, destId);
        String lockKey2 = "BIN_LOCATION_DETAIL_UPDATE:" + Math.max(sourceId, destId);

        AtomicReference<Void> result = new AtomicReference<>();

        RedissonKit.getInstance().lock(lockKey1, lock1 -> {
            RedissonKit.getInstance().lock(lockKey2, lock2 -> {
                // 重新查询最新数据，防止并发问题
                BinLocationDetail latestSourceDetail = super.getById(sourceId);
                BinLocationDetail latestDestDetail = super.getById(destId);

                Validate.notNull(latestSourceDetail, "Source BinLocationDetail with ID {} not found", sourceId);
                Validate.notNull(latestDestDetail, "Destination BinLocationDetail with ID {} not found", destId);

                // 计算源库位变更前后的数量
                int sourceBeforeQty = ObjectUtil.nullToDefault(latestSourceDetail.getInStockQty(), 0);
                int sourceAfterQty = sourceBeforeQty + changeQty;
                // 验证库存不能为负数
                Validate.isTrue(sourceAfterQty >= 0,
                        "Insufficient stock. Current: {}, Change: {}, Result: {}",
                        sourceBeforeQty, changeQty, sourceAfterQty);

                // 计算目标库位变更前后的数量
                int destBeforeQty = ObjectUtil.nullToDefault(latestDestDetail.getInStockQty(), 0);
                int destAfterQty = destBeforeQty - changeQty;
                // 验证库存不能为负数
                Validate.isTrue(destAfterQty >= 0,
                        "Insufficient stock. Current: {}, Change: {}, Result: {}",
                        destBeforeQty, changeQty, destAfterQty);

                // 更新源库位库存（减少）
                latestSourceDetail.setInStockQty(sourceAfterQty);
                super.update(latestSourceDetail);

                // 更新目标库位库存（增加）
                latestDestDetail.setInStockQty(destAfterQty);
                super.update(latestDestDetail);

                // 记录库位转移日志
                recordBinLocationTransferLog(latestSourceDetail, latestDestDetail, changeQty,
                        sourceBeforeQty, sourceAfterQty, destBeforeQty, destAfterQty,
                        refInfo, changeType, note);

                result.set(null);
            });
        });
    }

    /**
     * 记录库位日志
     */
    private void recordBinLocationLog(
            BinLocationDetail binLocationDetail, Integer changeQty,
            Integer beforeInStockQty, Integer afterInStockQty,
            BinLocationLogRefBO refInfo, String changeType, String note) {
        BinLocationLog log = new BinLocationLog();

        // 源库位信息
        log.setSourceBinLocationDetailId(binLocationDetail.getBinLocationId());
        log.setSourceBinLocationId(binLocationDetail.getBinLocationId());
        log.setSourceChangeInStockQty(changeQty);
        log.setSourceBeforeInStockQty(beforeInStockQty);
        log.setSourceAfterInStockQty(afterInStockQty);

        // 目标库位信息
        log.setDestBinLocationDetailId(binLocationDetail.getBinLocationId());
        log.setDestBinLocationId(binLocationDetail.getBinLocationId());
        log.setDestChangeInStockQty(changeQty);
        log.setDestBeforeInStockQty(beforeInStockQty);
        log.setDestAfterInStockQty(afterInStockQty);

        // 通用信息
        log.setWarehouseId(binLocationDetail.getWarehouseId());
        log.setProductId(binLocationDetail.getProductId());
        log.setProductVersionId(binLocationDetail.getProductVersionId());


        log.setChangeType(changeType);
        log.setRefTableId(refInfo.getRefTableId());
        log.setRefTableName(refInfo.getRefTableName());
        log.setRefTableRefNum(refInfo.getRefTableRefNum());
        log.setRefTableShowName(refInfo.getRefTableShowName());
        log.setRefTableShowRefNum(refInfo.getRefTableShowRefNum());
        log.setNote(note);

        // 保存库位日志
        AuditLogHolder.binLocationRecord(log);
    }

    /**
     * 记录库位转移日志（涉及源库位和目标库位）
     */
    private void recordBinLocationTransferLog(
            BinLocationDetail sourceBinLocationDetail,
            BinLocationDetail destBinLocationDetail,
            Integer transferQty,
            Integer sourceBeforeQty, Integer sourceAfterQty,
            Integer destBeforeQty, Integer destAfterQty,
            BinLocationLogRefBO refInfo, String changeType, String note) {
        // 创建转移日志记录
        BinLocationLog log = new BinLocationLog();

        // 源库位信息
        log.setSourceBinLocationId(sourceBinLocationDetail.getBinLocationId());
        log.setSourceBinLocationDetailId(sourceBinLocationDetail.getId());
        log.setSourceChangeInStockQty(transferQty);
        log.setSourceBeforeInStockQty(sourceBeforeQty);
        log.setSourceAfterInStockQty(sourceAfterQty);

        // 目标库位信息
        log.setDestBinLocationId(destBinLocationDetail.getBinLocationId());
        log.setDestBinLocationDetailId(destBinLocationDetail.getId());
        log.setDestChangeInStockQty(-transferQty);
        log.setDestBeforeInStockQty(destBeforeQty);
        log.setDestAfterInStockQty(destAfterQty);

        // 通用信息
        log.setChangeType(changeType);
        log.setRefTableId(refInfo.getRefTableId());
        log.setRefTableName(refInfo.getRefTableName());
        log.setRefTableRefNum(refInfo.getRefTableRefNum());
        log.setRefTableShowName(refInfo.getRefTableShowName());
        log.setRefTableShowRefNum(refInfo.getRefTableShowRefNum());
        log.setNote(note);

        // 保存库位日志
        AuditLogHolder.binLocationRecord(log);
    }

    @Override
    public void updateInStockQtyWithLog(Long binLocationDetailId, Integer changeQty,
                                        BinLocationLogRefBO refInfo, String changeType, String note) {
        // 参数验证
        Validate.notNull(binLocationDetailId, "BinLocationDetail ID cannot be null");

        // 获取库位详情对象
        BinLocationDetail binLocationDetail = super.getById(binLocationDetailId);
        Validate.notNull(binLocationDetail, "BinLocationDetail not found with ID: {}", binLocationDetailId);

        // 调用原有方法
        updateInStockQtyWithLog(binLocationDetail, changeQty, refInfo, changeType, note);
    }

    @Override
    public void transferInStockQtyWithLog(Long sourceBinLocationDetailId, Long destBinLocationDetailId,
                                          Integer changeQty, BinLocationLogRefBO refInfo, String changeType, String note) {
        // 参数验证
        Validate.notNull(sourceBinLocationDetailId, "Source BinLocationDetail ID cannot be null");
        Validate.notNull(destBinLocationDetailId, "Destination BinLocationDetail ID cannot be null");

        // 获取源库位详情对象
        BinLocationDetail sourceBinLocationDetail = super.getById(sourceBinLocationDetailId);
        Validate.notNull(sourceBinLocationDetail, "Source BinLocationDetail not found with ID: {}", sourceBinLocationDetailId);

        // 获取目标库位详情对象
        BinLocationDetail destBinLocationDetail = super.getById(destBinLocationDetailId);
        Validate.notNull(destBinLocationDetail, "Destination BinLocationDetail not found with ID: {}", destBinLocationDetailId);

        // 调用原有方法
        transferInStockQtyWithLog(sourceBinLocationDetail, destBinLocationDetail, changeQty, refInfo, changeType, note);
    }

    @Override
    public Map<Long, List<BinLocationDetail>> listByWarehouseIdWithStockGroupByTransactionPartnerId(Long warehouseId) {
        if (ObjectUtil.isEmpty(warehouseId)) {
            return Collections.emptyMap();
        }

        // 1. 查询所有有库存的库位详情
        List<BinLocationDetail> binLocationDetails = listByWarehouseIdWithStock(warehouseId);
        if (ObjectUtil.isEmpty(binLocationDetails)) {
            return Collections.emptyMap();
        }

        // 2. 提取所有ProductVersionId
        List<Long> productVersionIds = binLocationDetails.stream()
                .map(BinLocationDetail::getProductVersionId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        if (ObjectUtil.isEmpty(productVersionIds)) {
            log.warn("No valid ProductVersionId found in BinLocationDetails for warehouse: {}", warehouseId);
            return Collections.emptyMap();
        }

        // 3. 批量查询ProductVersion获取TransactionPartnerId
        List<ProductVersion> productVersions = productVersionService.listByIds(productVersionIds);
        if (ObjectUtil.isEmpty(productVersions)) {
            log.warn("No ProductVersion found for warehouse: {}", warehouseId);
            return Collections.emptyMap();
        }

        // 4. 构建ProductVersionId到TransactionPartnerId的映射
        Map<Long, Long> productVersionToTransactionPartnerMap = productVersions.stream()
                .filter(pv -> pv.getTransactionPartnerId() != null)
                .collect(Collectors.toMap(
                        ProductVersion::getId,
                        ProductVersion::getTransactionPartnerId,
                        (existing, replacement) -> existing // 处理重复key的情况
                ));

        // 5. 按TransactionPartnerId分组BinLocationDetail
        Map<Long, List<BinLocationDetail>> result = new HashMap<>();

        for (BinLocationDetail detail : binLocationDetails) {
            Long productVersionId = detail.getProductVersionId();
            if (productVersionId == null) {
                log.warn("BinLocationDetail {} has null ProductVersionId, skipping", detail.getId());
                continue;
            }

            Long transactionPartnerId = productVersionToTransactionPartnerMap.get(productVersionId);
            if (transactionPartnerId == null) {
                log.warn("No TransactionPartnerId found for ProductVersionId: {}, skipping BinLocationDetail: {}",
                        productVersionId, detail.getId());
                continue;
            }

            result.computeIfAbsent(transactionPartnerId, k -> new ArrayList<>()).add(detail);
        }

        log.info("Grouped {} BinLocationDetails into {} TransactionPartnerIds for warehouse: {}",
                binLocationDetails.size(), result.size(), warehouseId);

        return result;
    }

    @Override
    public List<BinLocationDetail> listByWarehouseIdAndTransactionPartnerId(Long warehouseId, Long transactionPartnerId) {
        if (ObjectUtil.isEmpty(warehouseId) || ObjectUtil.isEmpty(transactionPartnerId)) {
            return Collections.emptyList();
        }

        // 通过关联Product表查询指定TransactionPartnerId的库位详情
        return lambdaQuery()
                .eq(BinLocationDetail::getWarehouseId, warehouseId)
                //.gt(BinLocationDetail::getInStockQty, NumberUtils.INTEGER_ZERO)
                .exists("SELECT 1 FROM product p WHERE p.id = product_id AND p.transaction_partner_id = {0}"
                        , transactionPartnerId)
                .list();
    }

}
