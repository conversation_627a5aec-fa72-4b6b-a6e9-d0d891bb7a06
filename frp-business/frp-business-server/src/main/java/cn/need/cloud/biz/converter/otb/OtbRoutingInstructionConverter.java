package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbRoutingInstructionDTO;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction;
import cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb发货指南 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbRoutingInstructionConverter extends AbstractModelConverter<OtbRoutingInstruction, OtbRoutingInstructionVO, OtbRoutingInstructionDTO> {

}
