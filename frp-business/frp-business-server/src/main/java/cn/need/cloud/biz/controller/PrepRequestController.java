package cn.need.cloud.biz.controller;

import cn.need.cloud.biz.converter.PrepRequestConverter;
import cn.need.cloud.biz.model.entity.PrepRequest;
import cn.need.cloud.biz.model.param.otb.create.PrepRequestCreateParam;
import cn.need.cloud.biz.model.param.otb.update.request.prep.PrepRequestUpdateParam;
import cn.need.cloud.biz.model.query.PrepRequestQuery;
import cn.need.cloud.biz.model.vo.PrepRequestVO;
import cn.need.cloud.biz.model.vo.page.PrepRequestPageVO;
import cn.need.cloud.biz.service.PrepRequestService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 预请求控制器，提供预请求相关的REST API接口。
 * 该控制器负责处理预请求的创建、修改、删除、查询等操作，
 * 并提供了按ID和引用编号查询详情的功能。
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023-10-28
 */
@RestController
@RequestMapping("/api/biz/prep-request")
@Tag(name = "预请求")
public class PrepRequestController extends AbstractRestController<PrepRequestService, PrepRequest, PrepRequestConverter, PrepRequestVO> {

    /**
     * 创建新的预请求。
     * 接收预请求创建参数，验证后将数据持久化到数据库，并返回新创建的预请求ID。
     *
     * @param insertParam 预请求创建参数，包含创建预请求所需的所有信息
     * @return 新创建的预请求ID
     */
    @Operation(summary = "新增预请求", description = "接收预请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrepRequestCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    /**
     * 修改现有的预请求。
     * 接收预请求更新参数，验证后更新数据库中对应的记录，并返回受影响的记录数。
     *
     * @param updateParam 预请求更新参数，包含要更新的预请求信息
     * @return 更新操作影响的记录数
     */
    @Operation(summary = "修改预请求", description = "接收预请求的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PrepRequestUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    /**
     * 删除预请求记录。
     * 根据提供的ID和删除原因，从数据库中删除对应的预请求记录，并记录删除原因。
     *
     * @param deletedNoteParam 删除参数，包含ID和删除原因
     * @return 删除操作影响的记录数
     */
    @Operation(summary = "根据id删除预请求", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    /**
     * 根据ID获取预请求详情。
     * 查询并返回指定ID的预请求完整信息，包括关联数据。
     *
     * @param id 预请求记录的主键ID
     * @return 预请求详细信息视图对象
     */
    @Operation(summary = "根据id获取预请求详情", description = "根据数据主键id，从数据库中获取其对应的预请求详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PrepRequestVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 获取预请求详情
        PrepRequestVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    /**
     * 根据引用编号获取预请求详情。
     * 查询并返回指定引用编号的预请求完整信息，提供了一种通过业务标识而非主键ID查询的方式。
     *
     * @param refNum 预请求的引用编号
     * @return 预请求详细信息视图对象
     */
    @Operation(summary = "根据RefNum获取预请求详情", description = "根据数据RefNum，从数据库中获取其对应的预请求详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<PrepRequestVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 获取预请求详情
        PrepRequestVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    /**
     * 分页查询预请求列表。
     * 根据提供的查询条件和分页参数，返回满足条件的预请求分页数据。
     *
     * @param search 分页查询参数，包含页码、每页大小和查询条件
     * @return 预请求分页数据，包含当前页的预请求列表和分页信息
     */
    @Operation(summary = "获取预请求分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的预请求列表")
    @PostMapping(value = "/list")
    public Result<PageData<PrepRequestPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PrepRequestQuery> search) {
        // 获取预请求分页
        PageData<PrepRequestPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
