package cn.need.cloud.biz.service.binlocation;

import cn.need.cloud.biz.model.entity.binlocation.BinLocationReserve;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationReserveCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationReserveUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationReserveQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationReserveVO;
import cn.need.cloud.biz.model.vo.page.BinLocationReservePageVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 预留库位 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface BinLocationReserveService extends SuperService<BinLocationReserve> {

    /**
     * 根据参数新增预留库位
     *
     * @param createParam 请求创建参数，包含需要插入的预留库位的相关信息
     * @return 预留库位ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(BinLocationReserveCreateParam createParam);


    /**
     * 根据参数更新预留库位
     *
     * @param updateParam 请求创建参数，包含需要更新的预留库位的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(BinLocationReserveUpdateParam updateParam);

    /**
     * 根据查询条件获取预留库位列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预留库位对象的列表(分页)
     */
    List<BinLocationReservePageVO> listByQuery(BinLocationReserveQuery query);

    /**
     * 根据查询条件获取预留库位列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个预留库位对象的列表(分页)
     */
    PageData<BinLocationReservePageVO> pageByQuery(PageSearch<BinLocationReserveQuery> search);

    /**
     * 根据ID获取预留库位
     *
     * @param id 预留库位ID
     * @return 返回预留库位VO对象
     */
    BinLocationReserveVO detailById(Long id);


}