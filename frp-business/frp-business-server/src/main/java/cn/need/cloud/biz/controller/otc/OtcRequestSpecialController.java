package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.model.param.otc.update.request.OtcRequestCancelParam;
import cn.need.cloud.biz.model.query.RequestListQuery;
import cn.need.cloud.biz.model.vo.base.request.RequestConfirmDetailVO;
import cn.need.cloud.biz.service.otc.request.OtcRequestSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTC请求 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-request/special")
@Tag(name = "OTC请求-Special")
@Slf4j
@Validated
@AllArgsConstructor
public class OtcRequestSpecialController extends AbstractController {

    private final OtcRequestSpecialService otcRequestSpecialService;

    /**
     * <p>
     * 根据数据主键id取消请求
     * </p>
     *
     * @param param 数据主键id
     * @return 受影响行数
     */
    @Operation(summary = "取消请求接口，返回受影响行数", description = "根据数据主键id，从数据库中取消请求")
    @PostMapping(value = "/cancel")
    public Result<Integer> cancel(@RequestBody @Parameter(description = "数据主键id", required = true) OtcRequestCancelParam param) {

        Validate.notNull(param.getId(), "The param value cannot be null.");
        return Result.ok(otcRequestSpecialService.cancel(param));
    }

    @Operation(summary = "取消确认页", description = "根据数据主键id，从数据库中取消请求")
    @PostMapping(value = "/cancel/confirm")
    public Result<List<RequestConfirmDetailVO>> cancelConfirm(@RequestBody @Valid RequestListQuery query) {

        return Result.ok(otcRequestSpecialService.cancelConfirm(query));
    }
}
