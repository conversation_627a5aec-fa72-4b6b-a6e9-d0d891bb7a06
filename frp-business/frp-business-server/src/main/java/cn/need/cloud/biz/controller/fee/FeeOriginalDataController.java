package cn.need.cloud.biz.controller.fee;

import cn.need.cloud.biz.converter.fee.FeeOriginalDataConverter;
import cn.need.cloud.biz.model.entity.fee.FeeOriginalData;
import cn.need.cloud.biz.model.param.fee.build.FeeOriginalDataBuildByIdParam;
import cn.need.cloud.biz.model.query.fee.FeeOriginalDataQuery;
import cn.need.cloud.biz.model.vo.fee.FeeOriginalDataVO;
import cn.need.cloud.biz.model.vo.fee.page.FeeOriginalDataPageVO;
import cn.need.cloud.biz.service.feeoriginalData.FeeOriginalDataService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * 费用原始数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-07
 */
@RestController
@RequestMapping("/api/biz/fee-original-data")
@Tag(name = "费用原始数据表")
public class FeeOriginalDataController extends
        AbstractRestController<FeeOriginalDataService, FeeOriginalData, FeeOriginalDataConverter, FeeOriginalDataVO> {

    /**
     * 重写父类的service字段，使用@Lazy注解解决循环依赖问题
     */
    @Resource
    protected FeeOriginalDataService service;

    // @Operation(summary = "新增费用原始数据表", description =
    // "接收费用原始数据表的传参对象，将该对象持久化到数据库中表")
    // @PostMapping(value = "/insert")
    // public Result<Long> insert(@Valid @RequestBody @Parameter(description =
    // "数据对象", required = true) FeeOriginalDataCreateParam insertParam) {
    // // 返回结果
    // return success(service.insertByParam(insertParam));
    // }
    //
    // @Operation(summary = "修改费用原始数据表", description =
    // "接收费用原始数据表的传参对象，将该对象持久化到数据库中表")
    // @PostMapping(value = "/update")
    // public Result<Integer> update(@Valid @RequestBody @Parameter(description =
    // "数据对象", required = true) FeeOriginalDataUpdateParam updateParam) {
    // // 返回结果
    // return success(service.updateByParam(updateParam));
    // }

    // @Operation(summary = "根据id删除费用原始数据表", description =
    // "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    // @PostMapping(value = "/remove")
    // public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id",
    // required = true) DeletedNoteParam deletedNoteParam) {
    // // 校验参数
    // Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
    // // 执行删除并返回结果
    // return success(service.removeAndNote(deletedNoteParam.getId(),
    // deletedNoteParam.getDeletedNote()));
    // }

    @Operation(summary = "根据id获取费用原始数据表详情", description = "根据数据主键id，从数据库中获取其对应的费用原始数据表详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeOriginalDataVO> detail(
            @PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }

    @Operation(summary = "根据RefNum获取费用原始数据表详情", description = "根据数据RefNum，从数据库中获取其对应的费用原始数据表详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<FeeOriginalDataVO> detail(
            @PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {
        // 返回结果
        return success(service.detailByRefNum(refNum));
    }

    @Operation(summary = "获取费用原始数据表分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的费用原始数据表列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeOriginalDataPageVO>> list(
            @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeOriginalDataQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }

    @Operation(summary = "构建费用原始数据表", description = "构建费用原始数据表")
    @PostMapping(value = "/build")
    public Result<Void> build() {
        // 返回结果

        service.build();

        return success();
    }

    @Operation(summary = "构建 Inbound 费用原始数据表", description = "构建 Inbound 费用原始数据表")
    @PostMapping(value = "/build-inbound")
    public Result<Void> buildInbound() {
        service.buildInbound();
        return success();
    }

    @Operation(summary = "构建 OTC 费用原始数据表", description = "构建 OTC 费用原始数据表")
    @PostMapping(value = "/build-otc")
    public Result<Void> buildOTC() {
        service.buildOTC();
        return success();
    }

    @Operation(summary = "构建 OTB 费用原始数据表", description = "构建 OTB 费用原始数据表")
    @PostMapping(value = "/build-otb")
    public Result<Void> buildOTB() {
        service.buildOTB();
        return success();
    }

    @Operation(summary = "构建 Storage 费用原始数据表", description = "构建 Storage 费用原始数据表")
    @PostMapping(value = "/build-storage")
    public Result<Void> buildStorage() {
        service.buildStorage();
        return success();
    }

    @Operation(summary = "根据请求ID构建 Inbound 费用原始数据表", description = "根据请求ID构建 Inbound 费用原始数据表")
    @PostMapping(value = "/build-inbound-by-id")
    public Result<Void> buildInboundById(
            @Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeOriginalDataBuildByIdParam param) {
        service.buildInboundById(param.getRequestId());
        return success();
    }

    @Operation(summary = "根据请求ID构建 OTC 费用原始数据表", description = "根据请求ID构建 OTC 费用原始数据表")
    @PostMapping(value = "/build-otc-by-id")
    public Result<Void> buildOTCById(
            @Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeOriginalDataBuildByIdParam param) {
        service.buildOTCById(param.getRequestId());
        return success();
    }

    @Operation(summary = "根据请求ID构建 OTB 费用原始数据表", description = "根据请求ID构建 OTB 费用原始数据表")
    @PostMapping(value = "/build-otb-by-id")
    public Result<Void> buildOTBById(
            @Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeOriginalDataBuildByIdParam param) {
        service.buildOTBById(param.getRequestId());
        return success();
    }

    @Operation(summary = "根据请求ID构建 Storage 费用原始数据表", description = "根据请求ID构建 Storage 费用原始数据表")
    @PostMapping(value = "/build-storage-by-id")
    public Result<Void> buildStorageById(
            @Valid @RequestBody @Parameter(description = "构建参数", required = true) FeeOriginalDataBuildByIdParam param) {
        service.buildStorageById(param.getRequestId());
        return success();
    }
}
