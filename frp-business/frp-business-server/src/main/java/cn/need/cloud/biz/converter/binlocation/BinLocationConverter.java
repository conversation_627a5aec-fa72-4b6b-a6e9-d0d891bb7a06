package cn.need.cloud.biz.converter.binlocation;

import cn.need.cloud.biz.client.dto.binlocation.BinLocationDTO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 库位 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class BinLocationConverter extends AbstractModelConverter<BinLocation, BinLocationVO, BinLocationDTO> {

}
