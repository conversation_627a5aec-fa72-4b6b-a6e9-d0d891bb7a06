package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbRoutingInstructionPackageLabelDTO;
import cn.need.cloud.biz.model.entity.otb.OtbRoutingInstructionPackageLabel;
import cn.need.cloud.biz.model.vo.otb.pkg.OtbRoutingInstructionPackageLabelVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb发货指南托盘标签 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbRoutingInstructionPackageLabelConverter extends AbstractModelConverter<OtbRoutingInstructionPackageLabel, OtbRoutingInstructionPackageLabelVO, OtbRoutingInstructionPackageLabelDTO> {

}
