package cn.need.cloud.biz.provider.oco;

import cn.need.cloud.biz.client.api.oco.OcoRequestClient;
import cn.need.cloud.biz.client.api.path.OcoRequestPath;
import cn.need.cloud.biz.client.dto.product.ProductReqDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseFullProductDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.oco.OcoRequestCreateOrUpdateReqDTO;
import cn.need.cloud.biz.client.dto.req.oco.OcoRequestDetailReqDTO;
import cn.need.cloud.biz.client.dto.req.oco.OcoRequestQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.base.RefNumWithRequestRefNumRespDTO;
import cn.need.cloud.biz.client.dto.resp.oco.OcoRequestDetailRespDTO;
import cn.need.cloud.biz.client.dto.resp.oco.OcoRequestPageRespDTO;
import cn.need.cloud.biz.client.dto.resp.oco.OcoRequestRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.oco.OcoRequest;
import cn.need.cloud.biz.model.param.oco.create.OcoRequestCreateParam;
import cn.need.cloud.biz.model.param.oco.create.OcoRequestDetailCreateParam;
import cn.need.cloud.biz.model.param.oco.update.OcoRequestDetailUpdateParam;
import cn.need.cloud.biz.model.param.oco.update.OcoRequestUpdateParam;
import cn.need.cloud.biz.model.query.oco.OcoRequestQuery;
import cn.need.cloud.biz.model.vo.oco.OcoRequestVO;
import cn.need.cloud.biz.model.vo.page.OcoRequestPageVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.oco.OcoRequestService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * OCO请求Provider
 * <p>
 * 提供OCO请求相关的内部服务接口实现，处理来自API Center的请求。
 * 负责租户信息填充、仓库信息处理、产品信息填充等业务逻辑。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@RestController
@RequestMapping(OcoRequestPath.PREFIX)
@Tag(name = "OCO请求Provider")
public class OcoRequestProvider implements OcoRequestClient {

    @Resource
    private OcoRequestService ocoRequestService;

    @Override
    @Operation(summary = "创建或更新OCO请求")
    @PostMapping(value = OcoRequestPath.CREATE_OR_UPDATE)
    @IgnoreAuth
    public Result<RefNumWithRequestRefNumRespDTO> createOrUpdate(@RequestBody OcoRequestCreateOrUpdateReqDTO reqDTO) {
        log.info("OCO createOrUpdate reqDTO: {}", reqDTO);

        // 填充租户信息
        TenantUtil.fillTenant(reqDTO.getLogisticPartner(), reqDTO.getTransactionPartner());
        // 设置租户id到上下文
        TenantContextHolder.setTenantId(reqDTO.getLogisticPartnerId());

        // 填充仓库信息
        WarehouseUtil.fillWarehouseId(List.of(reqDTO.getWarehouseReqDTO()));
        // 填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(reqDTO.getWarehouseId());

        // 填充产品信息
        List<ProductReqDTO> productList = reqDTO.getDetailList()
                .stream()
                .map(OcoRequestDetailReqDTO::getProduct)
                .toList();
        ProductUtil.fillProduct(reqDTO.getTransactionPartner(), productList);

        // 判断是新增还是编辑
        OcoRequest ocoRequest;
        if (ObjectUtil.isEmpty(reqDTO.getRefNum())) {
            // 构建新增参数
            OcoRequestCreateParam createParam = BeanUtil.copyNew(reqDTO, OcoRequestCreateParam.class);
            // 构建参数
            buildCreateParam(reqDTO, createParam);
            // 调用新增方法
            ocoRequest = ocoRequestService.insertByParam(createParam);
        } else {
            // TODO: 获取请求单id - 等待fillRequestId方法实现
            // ocoRequestService.fillRequestId(reqDTO.getTransactionPartner(), List.of(reqDTO.getReqDTO()));
            // 构建编辑参数
            OcoRequestUpdateParam updateParam = BeanUtil.copyNew(reqDTO, OcoRequestUpdateParam.class);
            updateParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
            updateParam.setId(reqDTO.getRequestId());
            // 构建更新参数
            buildUpdateParam(reqDTO, updateParam);
            // 调用更新方法
            ocoRequest = ocoRequestService.updateByParam(updateParam);
        }

        // 构建返回结果
        RefNumWithRequestRefNumRespDTO respDTO = new RefNumWithRequestRefNumRespDTO();
        respDTO.setRefNum(ocoRequest.getRefNum());
        respDTO.setRequestRefNum(ocoRequest.getRequestRefNum());
        return Result.ok(respDTO);
    }

    @Override
    @Operation(summary = "获取OCO请求详情")
    @PostMapping(value = OcoRequestPath.DETAIL)
    @IgnoreAuth
    public Result<OcoRequestRespDTO> detail(@RequestBody BaseDetailQueryReqDTO query) {
        log.info("OCO detail query: {}", query);

        // 填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        // 设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());

        // 填充仓库信息
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        // 填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        // 根据查询类型获取详情
        OcoRequestVO ocoRequestVO;
        if (ObjectUtil.isNotEmpty(query.getRefNum())) {
            ocoRequestVO = ocoRequestService.detailByRefNum(query.getRefNum());
        } else if (ObjectUtil.isNotEmpty(query.getRequestId())) {
            ocoRequestVO = ocoRequestService.detailById(query.getRequestId());
        } else {
            throw new BusinessException("refNum or requestId is required");
        }

        // 构建返回结果
        OcoRequestRespDTO ocoRequestRespDTO = BeanUtil.copyNew(ocoRequestVO, OcoRequestRespDTO.class);

        // 填充详情列表
        List<OcoRequestDetailRespDTO> list = ocoRequestVO.getDetailList().stream()
                .map(item -> {
                    OcoRequestDetailRespDTO ocoRequestDetailRespDTO = BeanUtil.copyNew(item, OcoRequestDetailRespDTO.class);
                    ocoRequestDetailRespDTO.setProduct(BeanUtil.copyNew(item.getBaseProductVO(), BaseFullProductDTO.class));
                    return ocoRequestDetailRespDTO;
                }).toList();

        // 填充请求详情
        ocoRequestRespDTO.setDetailList(list);

        // 构建返回参数
        return Result.ok(ocoRequestRespDTO);
    }

    @Override
    @Operation(summary = "获取OCO请求列表")
    @PostMapping(value = OcoRequestPath.LIST)
    @IgnoreAuth
    public Result<PageData<OcoRequestPageRespDTO>> list(@RequestBody PageSearch<OcoRequestQueryReqDTO> search) {
        log.info("OCO list search: {}", search);

        // 获取查询参数
        OcoRequestQueryReqDTO query = search.getCondition();

        // 填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        // 设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());

        if (ObjectUtil.isEmpty(query.getWarehouseReqDTO())) {
            throw new BusinessException("warehouseReqDTO is required");
        }

        // 填充仓库信息
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        // 填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        // 构建方法入参
        PageSearch<OcoRequestQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, OcoRequestQuery.class));

        // 调用列表方法
        PageData<OcoRequestPageVO> data = ocoRequestService.pageByQuery(pageSearch);

        // 对象转换
        PageData<OcoRequestPageRespDTO> result = PageUtil.convert(data, item -> BeanUtil.copyNew(item, OcoRequestPageRespDTO.class));

        return Result.ok(result);
    }

    @Override
    @Operation(summary = "取消OCO请求")
    @PostMapping(value = OcoRequestPath.CANCEL)
    @IgnoreAuth
    public Result<Boolean> cancel(@RequestBody BaseDetailQueryReqDTO query) {
        log.info("OCO cancel query: {}", query);

        // 填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        // 设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());

        // 填充仓库信息
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        // 填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        try {
            // 实现OCO请求取消逻辑
            boolean result = cancelOcoRequest(query);
            return Result.ok(result);
        } catch (Exception e) {
            log.error("OCO cancel error: {}", e.getMessage(), e);
            return Result.fail("Cancel failed: " + e.getMessage());
        }
    }

    /**
     * 取消OCO请求的具体实现
     * <p>
     * 参考OTC和OTB的取消逻辑，实现OCO请求的取消功能。
     * 包括状态验证、请求状态更新等操作。
     * </p>
     *
     * @param query 取消请求参数
     * @return 取消是否成功
     */
    private boolean cancelOcoRequest(BaseDetailQueryReqDTO query) {
        // 获取请求ID
        Long requestId = query.getRequestId();
        if (ObjectUtil.isEmpty(requestId)) {
            throw new BusinessException("Request ID is required for cancel operation");
        }

        // 获取并校验请求实体
        OcoRequest entity = ocoRequestService.getById(requestId);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("OCO Request not found with ID: " + requestId);
        }

        // 校验取消状态 - 参考OTB的可取消状态
        String currentStatus = entity.getRequestStatus();
        List<String> canCancelStatuses = List.of("New", "Approved", "Processing", "Rejected", "OnHold");

        if (!canCancelStatuses.contains(currentStatus)) {
            throw new BusinessException(String.format(
                    "OCO Request %s cannot be cancelled. Current status: %s, Allowed statuses: %s",
                    entity.getRefNum(), currentStatus, canCancelStatuses
            ));
        }

        // 更新请求状态为已取消
        entity.setRequestStatus("Cancelled");

        // 保存更新
        int updateCount = ocoRequestService.update(entity);
        boolean updateResult = updateCount > 0;

        if (!updateResult) {
            throw new BusinessException("Failed to update OCO Request status to Cancelled");
        }

        // TODO: 这里应该添加审计日志记录
        // OcoRequestAuditLogHelper.recordLog(entity, "Cancelled", query.getNote(), null);

        // TODO: 如果有相关的工单，需要取消工单
        // 目前OCO可能还没有工单系统，所以暂时跳过

        log.info("OCO Request {} cancelled successfully", entity.getRefNum());
        return updateResult;
    }

    /**
     * 构建创建参数
     * <p>
     * 将请求DTO转换为创建参数，设置必要的关联字段。
     * </p>
     *
     * @param reqDTO      请求DTO
     * @param createParam 创建参数
     */
    private void buildCreateParam(OcoRequestCreateOrUpdateReqDTO reqDTO, OcoRequestCreateParam createParam) {
        // 转换详情列表
        List<OcoRequestDetailCreateParam> detailCreateParams = reqDTO.getDetailList().stream()
                .map(detailReqDTO -> BeanUtil.copyNew(detailReqDTO, OcoRequestDetailCreateParam.class))
                .toList();

        // 设置创建参数的特殊字段
        createParam.setDetailList(detailCreateParams);
        createParam.setTransactionPartnerId(reqDTO.getTransactionPartnerId());
    }

    /**
     * 构建更新参数
     * <p>
     * 将请求DTO转换为更新参数，设置必要的关联字段。
     * </p>
     *
     * @param reqDTO      请求DTO
     * @param updateParam 更新参数
     */
    private void buildUpdateParam(OcoRequestCreateOrUpdateReqDTO reqDTO, OcoRequestUpdateParam updateParam) {
        // 转换详情列表
        List<OcoRequestDetailUpdateParam> detailUpdateParams = reqDTO.getDetailList().stream()
                .map(detailReqDTO -> BeanUtil.copyNew(detailReqDTO, OcoRequestDetailUpdateParam.class))
                .toList();

        // 设置更新参数的特殊字段
        updateParam.setDetailList(detailUpdateParams);
    }
}
