package cn.need.cloud.biz.controller.log;

import cn.need.cloud.biz.model.query.log.BinLocationLogQuery;
import cn.need.cloud.biz.service.log.BinLocationLogExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 库位日志导出控制器
 *
 * <AUTHOR> AI
 * @since 2024-11-28
 */
@RestController
@RequestMapping("/api/biz/bin-location-log/export")
@Tag(name = "库位日志-导出")
@Slf4j
@RequiredArgsConstructor
public class BinLocationLogExportController {

    private final BinLocationLogExportService service;

    @Operation(summary = "库位日志-导出", description = "导出库位日志列表")
    @PostMapping("/list")
    public void export(@RequestBody @Validated @Parameter(description = "搜索条件参数", required = true) BinLocationLogQuery query, HttpServletResponse response) {
        // 直接执行导出，不返回Result对象
        service.export(query, response);
    }
} 