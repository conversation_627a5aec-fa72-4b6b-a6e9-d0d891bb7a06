package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlipDetail;
import cn.need.cloud.biz.model.vo.otc.pickingslip.OtcPrepPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预提货单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPrepPickingSlipDetailConverter extends AbstractModelConverter<OtcPrepPickingSlipDetail, OtcPrepPickingSlipDetailVO, OtcPrepPickingSlipDetailDTO> {

}
