package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.cache.bean.ProductCache;
import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.client.constant.ProductStatusFieldConstant;
import cn.need.cloud.biz.client.constant.enums.base.BaseTypeLogEnum;
import cn.need.cloud.biz.client.constant.enums.product.ProductLogStatusEnum;
import cn.need.cloud.biz.client.constant.enums.warehouse.ExtraPackageTypeEnum;
import cn.need.cloud.biz.converter.warehouse.WarehouseProductConverter;
import cn.need.cloud.biz.mapper.warehouse.WarehouseProductMapper;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.entity.product.Product;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseProduct;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseProductCreateParam;
import cn.need.cloud.biz.model.param.warehouse.importparam.WarehouseProductImportParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseProductCreateOrUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseProductQuery;
import cn.need.cloud.biz.model.vo.log.ModificationLogVO;
import cn.need.cloud.biz.model.vo.page.WarehouseProductPageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductListVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseProductVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseVO;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.service.warehouse.WarehouseProductService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.util.JsonUtil;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.cloud.biz.util.log.AuditLogHolder;
import cn.need.cloud.biz.util.log.AuditLogUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 产品即发货 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class WarehouseProductServiceImpl extends SuperServiceImpl<WarehouseProductMapper, WarehouseProduct> implements WarehouseProductService {

    /// ///////////////////////////////////////// 公共方法 ////////////////////////////////////////////

    @Resource
    private ProductService productService;

    @Resource
    private WarehouseService warehouseService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(WarehouseProductCreateParam createParam) {
        // 检查传入产品即发货参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取产品即发货转换器实例，用于将产品即发货参数对象转换为实体对象
        WarehouseProductConverter converter = Converters.get(WarehouseProductConverter.class);

        // 将产品即发货参数对象转换为实体对象并初始化
        WarehouseProduct entity = initWarehouseProduct(converter.toEntity(createParam));

        // 插入产品即发货实体对象到数据库
        super.insert(entity);

        // 返回产品即发货ID
        return entity.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int createOrUpdate(WarehouseProductCreateOrUpdateParam param) {
        // 检查传入产品即发货参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(param)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        // 获取产品即发货转换器实例，用于将产品即发货参数对象转换为实体对象
        WarehouseProductConverter converter = Converters.get(WarehouseProductConverter.class);

        // 将产品即发货参数对象转换为实体对象
        WarehouseProduct entity = converter.toEntity(param);

        WarehouseProduct one = getWarehouseProduct(param.getProductId(), param.getWarehouseId());

        if (ObjectUtil.isEmpty(one)) {
            int insert = super.insert(entity);
            // 获取产品refNum 并记录日志
            recordProductLog(entity, Boolean.FALSE);
            return insert;
        }
        // 设置实体对象的ID
        entity.setId(one.getId());
        // 执行更新产品即发货操作
        int update = super.update(entity);
        // 获取产品refNum 并记录日志
        recordProductLog(entity, one.getSlapAndGoFlag());
        return update;
    }

    /**
     * 记录产品日志
     *
     * @param entity 实体对象
     */
    private void recordProductLog(WarehouseProduct entity, Boolean oldStatus) {
        ProductCache productCache = ProductCacheUtil.getById(entity.getProductId());
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(entity.getWarehouseId());
        assert warehouseCache != null;
        String warehouseCacheName = warehouseCache.getName();
        String menmberPath = StringUtil.format("{} in Warehouse:{}", ProductStatusFieldConstant.SLAP_AND_GO_FLAG, warehouseCacheName);
        AuditShowLog auditShowLog = AuditLogUtil.baseLog(entity)
                // 关联表refNum
                .with(AuditShowLog::setRefTableRefNum, productCache.getRefNum())
                // 关联表展示refNum
                .with(AuditShowLog::setRefTableShowRefNum, productCache.getRefNum())
                // 设置日志的状态为即发货状态变更
                .with(AuditShowLog::setEvent, ProductLogStatusEnum.SLAP_AND_GO_STATUS_CHANGE.getStatus())
                // 设置日志类型为状态变更
                .with(AuditShowLog::setType, BaseTypeLogEnum.STATUS.getType())
                // 设置审计日志的描述为即发货状态变更成的具体内容
                .with(AuditShowLog::setDescription, JsonUtil.toJson(new ModificationLogVO(menmberPath, entity.getSlapAndGoFlag().toString(), oldStatus.toString()))).build();
        AuditLogHolder.record(auditShowLog);

    }

    @Override
    public WarehouseProduct getWarehouseProduct(Long productId, Long warehouseId) {
        return lambdaQuery().eq(WarehouseProduct::getProductId, productId).eq(WarehouseProduct::getWarehouseId, warehouseId)
                .one();
    }

    @Override
    public List<WarehouseProductPageVO> listByQuery(WarehouseProductQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<WarehouseProductPageVO> pageByQuery(PageSearch<WarehouseProductQuery> search) {
        Page<WarehouseProduct> page = Conditions.page(search, entityClass);
        List<WarehouseProductPageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public WarehouseProductVO detailById(Long id) {
        WarehouseProduct entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "WarehouseProduct", id));
        }
        return buildWarehouseProductVO(entity);
    }

    @Override
    public List<WarehouseProductListVO> listByProductId(Long productId) {
        if (ObjectUtil.isEmpty(productId)) {
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }
        List<WarehouseProductListVO> voList = mapper.listByProductId(productId);
        // 缓存填充voList仓库信息
        WarehouseCacheUtil.filledWarehouse(voList);
        return voList;
    }

    @Override
    public Map<Long, WarehouseProduct> listByProductIdList(List<Long> productIdList) {
        if (ObjectUtil.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }
        return lambdaQuery()
                .in(WarehouseProduct::getProductId, productIdList)
                .list()
                .stream()
                .collect(Collectors.toMap(IdModel::getId, Function.identity()));
    }

    //////////////////////////////////////////// 私有方法 ////////////////////////////////////////////

    /**
     * 初始化产品即发货对象
     * 此方法用于设置产品即发货对象的必要参数，确保其处于有效状态
     *
     * @param entity 产品即发货对象，不应为空
     * @return 返回初始化后的产品即发货
     * @throws BusinessException 如果传入的产品即发货为空，则抛出此异常
     */
    private WarehouseProduct initWarehouseProduct(WarehouseProduct entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(String.format(ErrorMessages.PARAMETER_EMPTY_WITH_NAME, "WarehouseProduct"));
        }


        // 返回初始化后的配置对象
        return entity;
    }

    /**
     * 构建产品即发货VO对象
     *
     * @param entity 产品即发货对象
     * @return 返回包含详细信息的产品即发货VO对象
     */
    private WarehouseProductVO buildWarehouseProductVO(WarehouseProduct entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的产品即发货VO对象
        return Converters.get(WarehouseProductConverter.class).toVO(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchImport(List<WarehouseProductImportParam> importList) {
        if (ObjectUtil.isEmpty(importList)) {
            return 0;
        }

        // 收集所有需要查询的数据
        List<String> warehouseCodes = importList.stream()
                .map(WarehouseProductImportParam::getWarehouseCode)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询仓库信息
        Map<String, Long> warehouseCodeToIdMap = new HashMap<>();
        for (String code : warehouseCodes) {
            try {
                WarehouseVO warehouse = warehouseService.getByCode(code);
                if (warehouse != null) {
                    warehouseCodeToIdMap.put(code, warehouse.getId());
                }
            } catch (Exception e) {
                log.warn("仓库编码 {} 不存在", code);
                throw new BusinessException(StringUtil.format("{} warehouse code 不存在", code));
            }
        }

        // 批量查询产品信息
        Map<String, Long> productKeyToIdMap = new HashMap<>();
        for (WarehouseProductImportParam param : importList) {
            String key = param.getTenantId() + "_" + param.getTransactionPartnerId() + "_" + param.getProductSupplierSku();
            if (!productKeyToIdMap.containsKey(key)) {
                try {
                    Product product = productService.lambdaQuery()
                            .eq(Product::getTenantId, param.getTenantId())
                            .eq(Product::getTransactionPartnerId, param.getTransactionPartnerId())
                            .eq(Product::getSupplierSku, param.getProductSupplierSku())
                            .one();
                    if (product != null) {
                        productKeyToIdMap.put(key, product.getId());
                    }
                } catch (Exception e) {
                    log.warn("产品不存在: tenantId={}, transactionPartnerId={}, supplierSku={}",
                            param.getTenantId(), param.getTransactionPartnerId(), param.getProductSupplierSku());
                }
            }
        }

        // 处理导入数据
        List<WarehouseProduct> toInsert = new ArrayList<>();
        List<WarehouseProduct> toUpdate = new ArrayList<>();
        int successCount = 0;

        for (WarehouseProductImportParam param : importList) {
            try {
                // 获取仓库ID
                Long warehouseId = warehouseCodeToIdMap.get(param.getWarehouseCode());
                if (warehouseId == null) {
                    log.warn("仓库编码 {} 不存在，跳过该记录", param.getWarehouseCode());
                    continue;
                }

                // 获取产品ID
                String productKey = param.getTenantId() + "_" + param.getTransactionPartnerId() + "_" + param.getProductSupplierSku();
                Long productId = productKeyToIdMap.get(productKey);
                if (productId == null) {
                    log.warn("产品不存在: tenantId={}, transactionPartnerId={}, supplierSku={}，跳过该记录",
                            param.getTenantId(), param.getTransactionPartnerId(), param.getProductSupplierSku());
                    continue;
                }

                // 转换额外包装类型
                ExtraPackageTypeEnum extraPackageType = ExtraPackageTypeEnum.getByCode(param.getExtraPackageType());

                // 查询现有记录
                WarehouseProduct existing = this.lambdaQuery()
                        .eq(WarehouseProduct::getProductId, productId)
                        .eq(WarehouseProduct::getWarehouseId, warehouseId)
                        .eq(WarehouseProduct::getTenantId, param.getTenantId())
                        .one();

                if (existing != null) {
                    // 更新现有记录，只更新额外包装类型，不修改slapAndGoFlag
                    existing.setExtraPackageType(extraPackageType);
                    toUpdate.add(existing);
                } else {
                    // 创建新记录
                    WarehouseProduct newRecord = new WarehouseProduct();
                    newRecord.setProductId(productId);
                    newRecord.setWarehouseId(warehouseId);
                    newRecord.setTenantId(param.getTenantId());
                    newRecord.setExtraPackageType(extraPackageType);
                    newRecord.setSlapAndGoFlag(false); // 默认为false
                    toInsert.add(newRecord);
                }
                successCount++;
            } catch (Exception e) {
                log.error("处理导入记录失败: {}", param, e);
            }
        }

        // 批量保存
        if (!ObjectUtil.isEmpty(toInsert)) {
            this.insertBatch(toInsert);
        }
        if (!ObjectUtil.isEmpty(toUpdate)) {
            this.updateBatch(toUpdate);
        }

        return successCount;
    }
}
