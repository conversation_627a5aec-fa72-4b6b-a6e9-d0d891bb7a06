package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.converter.warehouse.ContainerTemplateConverter;
import cn.need.cloud.biz.mapper.warehouse.ContainerTemplateMapper;
import cn.need.cloud.biz.model.entity.warehouse.ContainerTemplate;
import cn.need.cloud.biz.model.param.warehouse.create.ContainerTemplateCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.ContainerTemplateUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.ContainerTemplateQuery;
import cn.need.cloud.biz.model.vo.page.ContainerTemplatePageVO;
import cn.need.cloud.biz.model.vo.warehouse.ContainerTemplateVO;
import cn.need.cloud.biz.service.warehouse.ContainerTemplateService;
import cn.need.cloud.dict.client.api.NumberGenerateClient;
import cn.need.cloud.dict.client.constant.enums.RefNumTypeEnum;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import cn.need.framework.common.support.util.ApiUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 集装箱模板配置表 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Service
public class ContainerTemplateServiceImpl extends SuperServiceImpl<ContainerTemplateMapper, ContainerTemplate> implements ContainerTemplateService {


    @Resource
    private NumberGenerateClient numberGenerateClient;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(ContainerTemplateCreateParam createParam) {
        // 检查传入集装箱模板配置表参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 将集装箱模板配置表参数对象转换为实体对象并初始化
        ContainerTemplate entity = initContainerTemplate(createParam);

        // 插入集装箱模板配置表实体对象到数据库
        super.insert(entity);

        // 返回集装箱模板配置表ID
        return entity.getId();
    }


    /**
     * 初始化集装箱模板配置表对象
     * 此方法用于设置集装箱模板配置表对象的必要参数，确保其处于有效状态
     *
     * @param createParam 集装箱模板配置表 新增对象，不应为空
     * @throws BusinessException 如果传入的集装箱模板配置表为空，则抛出此异常
     */
    private ContainerTemplate initContainerTemplate(ContainerTemplateCreateParam createParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(createParam)) {
            throw new BusinessException("ContainerTemplateCreateParam cannot be empty");
        }

        // 获取集装箱模板配置表转换器实例，用于将集装箱模板配置表参数对象转换为实体对象
        ContainerTemplateConverter converter = Converters.get(ContainerTemplateConverter.class);

        // 将集装箱模板配置表参数对象转换为实体对象并初始化
        ContainerTemplate entity = converter.toEntity(createParam);

        // 生成RefNum
        entity.setRefNum(ApiUtil.getResultData(numberGenerateClient.generateNumber(RefNumTypeEnum.CONTAINER_TEMPLATE.getCode())));


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(ContainerTemplateUpdateParam updateParam) {
        // 检查传入集装箱模板配置表参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            throw new BusinessException("Parameter cannot be empty");
        }

        // 将集装箱模板配置表参数对象转换为实体对象
        ContainerTemplate entity = initContainerTemplate(updateParam);

        // 执行更新集装箱模板配置表操作
        return super.update(entity);

    }

    /**
     * 初始化集装箱模板配置表对象
     * 此方法用于设置集装箱模板配置表对象的必要参数，确保其处于有效状态
     *
     * @param updateParam 集装箱模板配置表 修改对象，不应为空
     * @throws BusinessException 如果传入的集装箱模板配置表为空，则抛出此异常
     */
    private ContainerTemplate initContainerTemplate(ContainerTemplateUpdateParam updateParam) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(updateParam)) {
            throw new BusinessException("ContainerTemplateUpdateParam cannot be empty");
        }

        // 获取集装箱模板配置表转换器实例，用于将集装箱模板配置表参数对象转换为实体对象
        ContainerTemplateConverter converter = Converters.get(ContainerTemplateConverter.class);

        // 将集装箱模板配置表参数对象转换为实体对象并初始化
        ContainerTemplate entity = converter.toEntity(updateParam);


        // 返回初始化后的配置对象
        return entity;
    }


    @Override
    public List<ContainerTemplatePageVO> listByQuery(ContainerTemplateQuery query) {
        if (ObjectUtil.isEmpty(query)) {
            return Collections.emptyList();
        }
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<ContainerTemplatePageVO> pageByQuery(PageSearch<ContainerTemplateQuery> search) {
        Page<ContainerTemplate> page = Conditions.page(search, entityClass);
        List<ContainerTemplatePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public ContainerTemplateVO detailById(Long id) {
        ContainerTemplate entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("id: " + id + " not found in ContainerTemplate");
        }
        return buildContainerTemplateVO(entity);
    }

    @Override
    public ContainerTemplateVO detailByRefNum(String refNum) {
        ContainerTemplate entity = getByRefNum(refNum);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("RefNum: " + refNum + " not found in ContainerTemplate");
        }
        return buildContainerTemplateVO(entity);
    }


    /**
     * 构建集装箱模板配置表VO对象
     *
     * @param entity 集装箱模板配置表对象
     * @return 返回包含详细信息的集装箱模板配置表VO对象
     */
    private ContainerTemplateVO buildContainerTemplateVO(ContainerTemplate entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的集装箱模板配置表VO对象
        return Converters.get(ContainerTemplateConverter.class).toVO(entity);
    }

}
