package cn.need.cloud.biz.service.transfer;

import cn.need.cloud.biz.client.constant.enums.transfer.AuditResultType;
import cn.need.cloud.biz.model.entity.TransferOwnerShipRequest;
import cn.need.cloud.biz.model.param.TransferOwnerShipRequestCreateParam;
import cn.need.cloud.biz.model.query.TransferOwnerShipRequestQuery;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestPageVO;
import cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 货权转移 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-29
 */
public interface TransferOwnerShipRequestService extends SuperService<TransferOwnerShipRequest> {

    /**
     * 根据参数新增货权转移
     *
     * @param createParam 请求创建参数，包含需要插入的货权转移的相关信息
     * @return 货权转移ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    TransferOwnerShipRequest insertByParam(TransferOwnerShipRequestCreateParam createParam);

    /**
     * 审核
     *
     * @param request 请求
     * @param audit   审核类型
     */
    void audit(TransferOwnerShipRequest request, AuditResultType audit);

    /**
     * 创建并审核
     *
     * @param input 创建参数
     * @param audit 审核类型
     * @return /
     */
    TransferOwnerShipRequest createWithAudit(TransferOwnerShipRequestCreateParam input,
                                             AuditResultType audit);

    /**
     * 批量创建并审核
     *
     * @param inputList 批量创建参数列表
     * @param audit     审核类型
     * @return 批量创建结果列表
     */
    List<TransferOwnerShipRequest> batchCreateWithAudit(List<TransferOwnerShipRequestCreateParam> inputList,
                                                        AuditResultType audit);

    /**
     * 根据查询条件获取货权转移列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个货权转移对象的列表(分页)
     */
    List<TransferOwnerShipRequestPageVO> listByQuery(TransferOwnerShipRequestQuery query);

    /**
     * 根据查询条件获取货权转移列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个货权转移对象的列表(分页)
     */
    PageData<TransferOwnerShipRequestPageVO> pageByQuery(PageSearch<TransferOwnerShipRequestQuery> search);

    /**
     * 根据ID获取货权转移
     *
     * @param id 货权转移ID
     * @return 返回货权转移VO对象
     */
    TransferOwnerShipRequestVO detailById(Long id);

    /**
     * 根据货权转移唯一编码获取货权转移
     *
     * @param refNum 货权转移唯一编码
     * @return 返回货权转移VO对象
     */
    TransferOwnerShipRequestVO detailByRefNum(String refNum);


}