package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.client.constant.RedisConstant;
import cn.need.cloud.biz.converter.otb.OtbPickingSlipConverter;
import cn.need.cloud.biz.model.entity.otb.OtbPickingSlip;
import cn.need.cloud.biz.model.param.base.update.PickingSlipCancelUpdateParam;
import cn.need.cloud.biz.model.param.otb.update.pickingslip.OtbPickingSlipUnpickCreateParam;
import cn.need.cloud.biz.model.vo.base.UnpickVO;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPickingSlipVO;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipService;
import cn.need.cloud.biz.service.otb.pickingslip.OtbPickingSlipSpecialService;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import cn.need.framework.common.support.redis.RedissonKit;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <p>
 * OTC拣货单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-picking-slip/special")
@Tag(name = "OTB拣货单-Special")
@Validated
public class OtbPickingSlipSpecialController extends AbstractRestController<OtbPickingSlipService, OtbPickingSlip, OtbPickingSlipConverter, OtbPickingSlipVO> {

    @Resource
    private OtbPickingSlipSpecialService specialService;

    @Operation(summary = "Unpick", description = "Unpick")
    @PostMapping(value = "/unpick")
    public Result<Boolean> unpick(@RequestBody @Valid OtbPickingSlipUnpickCreateParam query) {
        RedissonKit.getInstance().lock(
                RedisConstant.OTC_PUTAWAY_SLIP_CREATE_LOCK_PREFIX + query.getWorkorderId(),
                lock -> specialService.unpick(query)
        );
        return success(true);
    }

    @Operation(summary = "可Unpick列表", description = "Unpick列表")
    @GetMapping(value = "/unpick/list/{workorderId}")
    public Result<UnpickVO> unpickByWorkorderId(@PathVariable(name = "workorderId") Long workorderId) {

        return success(specialService.unpickByWorkorderId(workorderId));
    }

    @Operation(summary = "Batch Cancel", description = "Batch Cancel")
    @PostMapping(value = "/batch-cancel")
    public Result<Boolean> batchCancel(@RequestBody @Valid PickingSlipCancelUpdateParam param) {
        return success(specialService.batchCancel(param));
    }
}
