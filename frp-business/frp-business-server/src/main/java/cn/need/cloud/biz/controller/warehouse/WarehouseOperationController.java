package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.WarehouseOperationConverter;
import cn.need.cloud.biz.model.entity.warehouse.WarehouseOperation;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseOperationCreateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseOperationQuery;
import cn.need.cloud.biz.model.vo.page.WarehouseOperationPageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationRemoveVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseOperationVO;
import cn.need.cloud.biz.service.warehouse.WarehouseOperationService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * 仓库分配 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/warehouse-operation")
@Tag(name = "仓库分配")
public class WarehouseOperationController extends AbstractRestController<WarehouseOperationService, WarehouseOperation, WarehouseOperationConverter, WarehouseOperationVO> {

    @Operation(summary = "新增仓库分配", description = "接收仓库分配的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) WarehouseOperationCreateParam insertParam) {

        // 返回结果
        service.insertByParam(insertParam);
        return success();
    }

    @Operation(summary = "根据id删除仓库分配", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody WarehouseOperationRemoveVO warehouseOperationRemoveVO) {

        // 执行删除并返回结果
        service.removeByOperationId(warehouseOperationRemoveVO);
        return success();
    }

    @Operation(summary = "获取仓库分配分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库分配列表")
    @PostMapping(value = "/list")
    public Result<PageData<WarehouseOperationPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<WarehouseOperationQuery> search) {

        // 获取仓库分配分页
        PageData<WarehouseOperationPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
