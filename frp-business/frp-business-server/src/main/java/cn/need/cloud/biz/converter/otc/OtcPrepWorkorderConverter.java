package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcPrepWorkorderDTO;
import cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcPrepWorkorderVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC预提工单 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcPrepWorkorderConverter extends AbstractModelConverter<OtcPrepWorkorder, OtcPrepWorkorderVO, OtcPrepWorkorderDTO> {

}
