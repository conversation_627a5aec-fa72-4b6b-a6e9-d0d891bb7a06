package cn.need.cloud.biz.converter.setting;

import cn.need.cloud.biz.client.dto.SequenceDTO;
import cn.need.cloud.biz.model.entity.setting.Sequence;
import cn.need.cloud.biz.model.vo.setting.SequenceVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 全局序列号ref 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class SequenceConverter extends AbstractModelConverter<Sequence, SequenceVO, SequenceDTO> {

}
