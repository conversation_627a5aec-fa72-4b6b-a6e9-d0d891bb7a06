package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.PalletEmptyProfileConverter;
import cn.need.cloud.biz.model.entity.warehouse.PalletEmptyProfile;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletEmptyProfileUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletEmptyProfileCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletEmptyProfileQuery;
import cn.need.cloud.biz.model.vo.page.PalletEmptyProfilePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import cn.need.cloud.biz.service.warehouse.PalletEmptyProfileService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 托盘信息 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/pallet-empty-profile")
@Tag(name = "托盘信息")
public class PalletEmptyProfileController extends AbstractRestController<PalletEmptyProfileService, PalletEmptyProfile, PalletEmptyProfileConverter, PalletEmptyProfileVO> {

    @Operation(summary = "新增托盘信息", description = "接收托盘信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PalletEmptyProfileCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改托盘信息", description = "接收托盘信息的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PalletEmptyProfileUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除托盘信息", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取托盘信息详情", description = "根据数据主键id，从数据库中获取其对应的托盘信息详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PalletEmptyProfileVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取托盘信息详情
        PalletEmptyProfileVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取托盘信息详情", description = "根据数据RefNum，从数据库中获取其对应的托盘信息详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<PalletEmptyProfileVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取托盘信息详情
        PalletEmptyProfileVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取托盘信息分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的托盘信息列表")
    @PostMapping(value = "/list")
    public Result<PageData<PalletEmptyProfilePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PalletEmptyProfileQuery> search) {

        // 获取托盘信息分页
        PageData<PalletEmptyProfilePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
