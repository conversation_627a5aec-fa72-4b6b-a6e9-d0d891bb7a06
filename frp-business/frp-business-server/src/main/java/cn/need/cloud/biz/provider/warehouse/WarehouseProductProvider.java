package cn.need.cloud.biz.provider.warehouse;

import cn.need.cloud.biz.client.api.path.WarehouseProductPath;
import cn.need.cloud.biz.client.api.warehouse.WarehouseProductClient;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseProductWithTransactionPartnerReqDTO;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseProductCreateOrUpdateParam;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.warehouse.WarehouseProductService;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 租户feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(WarehouseProductPath.PREFIX)
public class WarehouseProductProvider implements WarehouseProductClient {

    @Resource
    private WarehouseProductService warehouseProductService;


    @Override
    @PostMapping(WarehouseProductPath.MARK_PRODUCT_AS_SLAP_AND_GO)
    @IgnoreAuth
    public Result<Boolean> markProductAsSlapAndGo(BaseWarehouseProductWithTransactionPartnerReqDTO condition) {
        // 填充租户id
        TenantUtil.fillTenant(condition.getTransactionPartner(), condition.getLogisticPartner());
        // 把租户id设置到上下文
        TenantContextHolder.setTenantId(condition.getLogisticPartnerId());
        WarehouseUtil.fillWarehouseId(List.of(condition.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(condition.getWarehouseId());

        // 填充产品信息
        ProductUtil.fillProduct(condition.getTransactionPartner(), condition.getProduct());

        WarehouseProductCreateOrUpdateParam param = new WarehouseProductCreateOrUpdateParam();
        param.setProductId(condition.getProductId());
        param.setWarehouseId(condition.getWarehouseId());
        param.setSlapAndGoFlag(true);
        warehouseProductService.createOrUpdate(param);
        return Result.ok(Boolean.TRUE);
    }


}
