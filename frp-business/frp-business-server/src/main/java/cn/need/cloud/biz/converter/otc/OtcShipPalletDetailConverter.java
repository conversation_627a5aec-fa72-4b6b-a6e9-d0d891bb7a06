package cn.need.cloud.biz.converter.otc;

import cn.need.cloud.biz.client.dto.otc.OtcShipPalletDetailDTO;
import cn.need.cloud.biz.model.entity.otc.OtcShipPalletDetail;
import cn.need.cloud.biz.model.vo.otc.OtcShipPalletDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTC运输托盘详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtcShipPalletDetailConverter extends AbstractModelConverter<OtcShipPalletDetail, OtcShipPalletDetailVO, OtcShipPalletDetailDTO> {

}
