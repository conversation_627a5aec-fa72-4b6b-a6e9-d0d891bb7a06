package cn.need.cloud.biz.model.bo.otb;

import cn.need.cloud.biz.model.vo.otb.pkg.OtbPackageVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * otb小件转大件打托上下文
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "otb小件转大件打托上下文 对象")
public class OtbBuildPalletContext implements Serializable {
    /**
     * 托盘-长
     */
    @Schema(description = "托盘-长")
    private BigDecimal palletSizeLength;

    /**
     * 托盘-宽
     */
    @Schema(description = "托盘-宽")
    private BigDecimal palletSizeWidth;

    /**
     * 托盘-高
     */
    @Schema(description = "托盘-高")
    private BigDecimal palletSizeHeight;

    /**
     * 托盘-重量
     */
    @Schema(description = "托盘-重量")
    private BigDecimal palletSizeWeight;


    /**
     * otb工单id
     */
    @Schema(description = "otb工单id")
    private Long otbWorkorderId;

    /**
     * otb发货单单id
     */
    @Schema(description = "otb发货单单id")
    private Long otbShipmentId;

    /**
     * 仓库id
     */
    @Schema(description = "仓库id")
    private Long warehouseId;

    /**
     * 打托状态
     */
    @Schema(description = "打托状态")
    private String palletStatus;

    /**
     * 打托包裹
     */
    @Schema(description = "打托包裹")
    private List<OtbPackageVO> otbPackageList;

    /**
     * 空托盘配置id
     */
    @Schema(description = "空托盘配置id")
    private Long palletEmptyProfileId;
}
