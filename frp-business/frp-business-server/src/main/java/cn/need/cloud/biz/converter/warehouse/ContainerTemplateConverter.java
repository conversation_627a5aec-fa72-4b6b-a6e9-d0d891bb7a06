package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.warehouse.ContainerTemplateDTO;
import cn.need.cloud.biz.model.entity.warehouse.ContainerTemplate;
import cn.need.cloud.biz.model.vo.warehouse.ContainerTemplateVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 集装箱模板配置表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class ContainerTemplateConverter extends AbstractModelConverter<ContainerTemplate, ContainerTemplateVO, ContainerTemplateDTO> {

}
