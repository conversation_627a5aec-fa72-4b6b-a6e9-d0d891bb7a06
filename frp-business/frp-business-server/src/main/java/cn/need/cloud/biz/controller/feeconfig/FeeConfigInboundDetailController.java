package cn.need.cloud.biz.controller.feeconfig;


import cn.need.cloud.biz.converter.feeconfig.FeeConfigInboundDetailConverter;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigInboundDetail;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigInboundDetailQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigInboundDetailVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigInboundDetailPageVO;
import cn.need.cloud.biz.service.feeconfig.FeeConfigInboundDetailService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * 仓库报价费用配置inbound详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
@RestController
@RequestMapping("/api/biz/fee-config-inbound-detail")
@Tag(name = "仓库报价费用配置inbound详情")
public class FeeConfigInboundDetailController extends AbstractRestController<FeeConfigInboundDetailService, FeeConfigInboundDetail, FeeConfigInboundDetailConverter, FeeConfigInboundDetailVO> {


    @Operation(summary = "根据id获取仓库报价费用配置inbound详情详情", description = "根据数据主键id，从数据库中获取其对应的仓库报价费用配置inbound详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<FeeConfigInboundDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 返回结果
        return success(service.detailById(id));
    }


    @Operation(summary = "获取仓库报价费用配置inbound详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的仓库报价费用配置inbound详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<FeeConfigInboundDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<FeeConfigInboundDetailQuery> search) {

        // 获取仓库报价费用配置inbound详情分页
        PageData<FeeConfigInboundDetailPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
