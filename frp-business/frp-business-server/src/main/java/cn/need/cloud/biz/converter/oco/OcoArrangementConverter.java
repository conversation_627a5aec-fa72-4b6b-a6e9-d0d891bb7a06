package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoArrangementDTO;
import cn.need.cloud.biz.model.entity.oco.OcoArrangement;
import cn.need.cloud.biz.model.vo.oco.OcoArrangementVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO安排表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoArrangementConverter extends AbstractModelConverter<OcoArrangement, OcoArrangementVO, OcoArrangementDTO> {

}



