package cn.need.cloud.biz.service.inventory.impl;

import cn.need.cloud.biz.model.bo.product.ProductComponentDicBO;
import cn.need.cloud.biz.model.bo.product.ProductGroupDicBO;
import cn.need.cloud.biz.model.entity.binlocation.BinLocation;
import cn.need.cloud.biz.model.entity.product.ProductComponent;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.query.binlocation.BinLocationQuery;
import cn.need.cloud.biz.model.query.inventory.InventoryQuery;
import cn.need.cloud.biz.model.query.product.ProductQuery;
import cn.need.cloud.biz.model.vo.inventory.*;
import cn.need.cloud.biz.model.vo.product.ProductVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.inbound.InboundWorkorderDetailService;
import cn.need.cloud.biz.service.inventory.InventoryLockedService;
import cn.need.cloud.biz.service.inventory.InventoryReserveService;
import cn.need.cloud.biz.service.inventory.InventoryService;
import cn.need.cloud.biz.service.product.ProductComponentService;
import cn.need.cloud.biz.service.product.ProductGroupService;
import cn.need.cloud.biz.service.product.ProductService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.util.ProductCacheUtil;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.model.IdModel;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.apache.commons.lang3.SerializationUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 库存服务实现类
 * </p>
 * <p>
 * 该类实现了库存管理的核心业务逻辑，包括库存查询、库存统计、库存分配等功能。
 * 提供了对产品库存的全面管理，支持复杂的库存结构，如组合产品库存、多箱产品库存等。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 查询产品在库存量（包括实际库存、锁定库存、预定库存等）
 * 2. 支持复杂产品结构的库存查询（组合产品、组件产品等）
 * 3. 库存分配和管理
 * 4. 多仓库库存查询
 * 5. 库存数据的分页查询
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-22
 */
@Service
public class InventoryServiceImpl implements InventoryService {

    /**
     * 库位详情服务
     * <p>
     * 负责管理库位详细信息，包括库位中存放的产品、数量等。
     * 提供查询库位中正库存数量的功能。
     * </p>
     */
    @Resource
    private BinLocationDetailService binLocationDetailService;

    /**
     * 库存锁定服务
     * <p>
     * 负责管理库存锁定信息，包括锁定的产品、数量、原因等。
     * 库存锁定用于标记某些库存已被预留但尚未实际出库的情况。
     * </p>
     */
    @Resource
    private InventoryLockedService inventoryLockedService;

    /**
     * 库存预定服务
     * <p>
     * 负责管理库存预定信息，包括预定的产品、数量、原因等。
     * 库存预定用于标记某些库存已被预留但尚未实际锁定的情况。
     * </p>
     */
    @Resource
    private InventoryReserveService inventoryReserveService;

    /**
     * 入库工单详情服务
     * <p>
     * 负责管理入库工单的详细信息，包括入库的产品、数量、状态等。
     * 提供查询未完成入库工单的功能，用于计算预期入库数量。
     * </p>
     */
    @Resource
    private InboundWorkorderDetailService inBoundworkorderDetailService;

    /**
     * 产品组件服务
     * <p>
     * 负责管理产品组件信息，包括组件的创建、更新和查询。
     * 提供查询产品组件关系的功能，用于库存计算。
     * </p>
     */
    @Resource
    private ProductComponentService productComponentService;

    /**
     * 产品组服务
     * <p>
     * 负责管理产品组信息，包括产品组的创建、更新和查询。
     * 提供查询产品组关系的功能，用于库存计算。
     * </p>
     */
    @Resource
    private ProductGroupService productGroupService;

    /**
     * 产品服务
     * <p>
     * 负责管理产品信息，包括产品的创建、更新和查询。
     * 提供产品基本信息的查询功能，用于库存管理。
     * </p>
     */
    @Resource
    private ProductService productService;

    /**
     * 库位服务
     * <p>
     * 负责管理库位信息，包括库位的创建、更新和查询。
     * 提供根据条件查询库位的功能，用于库存管理。
     * </p>
     */
    @Resource
    private BinLocationService binLocationService;

    /**
     * 仓库服务
     * <p>
     * 负责管理仓库信息，包括仓库的创建、更新和查询。
     * 提供查询所有仓库的功能，用于多仓库库存查询。
     * </p>
     */
    @Resource
    private WarehouseService warehouseService;

    /**
     * 将组件列表转换为对应的 InventoryVO 列表，每个组件都是一个 InventoryVO
     *
     * @param componentList 组件列表
     * @param inventoryMap  用于查找组件对应的库存信息
     * @return InventoryVO 列表，每个元素对应一个组件的库存信息
     */
    private static List<InventoryVO> convertToInventoryComponents(List<ProductComponent> componentList,
                                                                  Map<Long, InventoryVO> inventoryMap) {
        return componentList.stream()
                .filter(ObjectUtil::isNotEmpty)
                .map(component -> toInventoryComponent(component, inventoryMap))
                .filter(ObjectUtil::isNotEmpty)
                .collect(Collectors.toList());
    }

    /**
     * 将单个 ProductComponent 转换为相应的 InventoryVO，并设置组件数量
     *
     * @param component    组件信息
     * @param inventoryMap 用于查找组件对应的库存信息
     * @return 转换后的 InventoryVO 对象（如果无对应库存信息则返回null）
     */
    private static InventoryVO toInventoryComponent(ProductComponent component,
                                                    Map<Long, InventoryVO> inventoryMap) {
        // 根据组件的产品ID获取库存信息
        InventoryVO componentInventory = inventoryMap.get(component.getComponentProductId());
        if (ObjectUtil.isEmpty(componentInventory)) {
            return null;
        }

        // 复制一份库存信息，并为其设置组件数量
        //todo: 待优化
        //CopyNew 第一层 深Copy, 第二层后面是 浅Copy

        /* 优化建议: 使用SerializationUtils.clone()进行深复制很消耗资源，尤其是对于大型对象。
         * 当对象包含大量嵌套对象时，序列化和反序列化的开销很高。
         * 建议使用BeanUtil.copyNew()进行浅复制，然后有选择地复制必要的字段。
         * 对于嵌套对象，可以采用浅复制加必要字段的方式，并清空组件列表避免循环引用。
         */
        InventoryVO result = SerializationUtils.clone(componentInventory);
        result.setInventoryId(UUID.randomUUID().toString());
        // 为了 解决 Combo 的 Component 也存在 Groups ，导致 Json 序列化 有问题
        if (ObjectUtil.isNotEmpty(result.getGroupList())) {
            result.getGroupList().forEach(x -> x.setInventoryId(UUID.randomUUID().toString()));
        }
        result.setComponentQty(component.getComponentQty());
        return result;
    }

    /**
     * 根据产品ID列表获取在库库存信息
     * <p>
     * 该方法综合查询指定产品的各类库存信息，包括：
     * - 实际库存（库位详情）
     * - 锁定库存（已锁定但未出库的库存）
     * - 预定库存（已预定但未锁定的库存）
     * - 预期入库（未完成的入库工单）
     * </p>
     * <p>
     * 返回的结果中包含每个产品的完整库存情况，并填充产品和仓库信息。
     * </p>
     *
     * @param productIdList 产品ID列表
     * @return 在库库存信息列表，每个元素对应一个产品的完整库存情况
     * <p>
     * TODO: 当产品数量很多时，多次stream操作可能会影响性能
     * 优化建议：使用Map结构预先分组，减少重复遍历
     * <p>
     * 示例优化代码：
     * <pre>
     * public List<InventoryInStockVO> getInventoryInStock(List<Long> productIdList) {
     *     // 去重处理
     *     productIdList = productIdList.stream().distinct().collect(Collectors.toList());
     *
     *     // 获取各类库存信息
     *     List<InventoryBinLocationDetailVO> binLocationDetails =
     *             binLocationDetailService.listInventoryPositiveInStockQtyByProductIds(productIdList);
     *     List<InventoryInventoryLockedVO> inventoryLockeds =
     *             inventoryLockedService.listInventoryLockeByProductIds(productIdList);
     *     List<InventoryInventoryReserveVO> inventoryReserves =
     *             inventoryReserveService.listInventoryNoFinishByProductIds(productIdList);
     *     List<InventoryInboundWorkorderDetailVO> inboundWorkorderDetails =
     *             inBoundworkorderDetailService.listInventoryNoFinishByProductIds(productIdList);
     *
     *     // 预先分组，避免重复遍历
     *     Map<Long, List<InventoryBinLocationDetailVO>> binLocationDetailMap = binLocationDetails.stream()
     *             .collect(Collectors.groupingBy(InventoryBinLocationDetailVO::getProductId));
     *     Map<Long, List<InventoryInventoryLockedVO>> inventoryLockedMap = inventoryLockeds.stream()
     *             .collect(Collectors.groupingBy(InventoryInventoryLockedVO::getProductId));
     *     Map<Long, List<InventoryInventoryReserveVO>> inventoryReserveMap = inventoryReserves.stream()
     *             .collect(Collectors.groupingBy(InventoryInventoryReserveVO::getProductId));
     *     Map<Long, List<InventoryInboundWorkorderDetailVO>> inboundWorkorderDetailMap = inboundWorkorderDetails.stream()
     *             .collect(Collectors.groupingBy(InventoryInboundWorkorderDetailVO::getProductId));
     *
     *     // 构建结果
     *     Long warehouseId = WarehouseContextHolder.getWarehouseId();
     *     List<InventoryInStockVO> result = new ArrayList<>(productIdList.size());
     *
     *     for (Long productId : productIdList) {
     *         InventoryInStockVO inventoryInStockVO = new InventoryInStockVO();
     *         inventoryInStockVO.setProductId(productId);
     *         inventoryInStockVO.setInventoryReserveList(inventoryReserveMap.getOrDefault(productId, Collections.emptyList()));
     *         inventoryInStockVO.setInventoryLockedList(inventoryLockedMap.getOrDefault(productId, Collections.emptyList()));
     *         inventoryInStockVO.setBinLocationDetailList(binLocationDetailMap.getOrDefault(productId, Collections.emptyList()));
     *         inventoryInStockVO.setInboundWorkorderDetailList(inboundWorkorderDetailMap.getOrDefault(productId, Collections.emptyList()));
     *         inventoryInStockVO.setWarehouseId(warehouseId);
     *         result.add(inventoryInStockVO);
     *     }
     *
     *     // 填充产品和仓库信息
     *     ProductCacheUtil.filledProduct(result);
     *     WarehouseCacheUtil.filledWarehouse(result);
     *
     *     return result;
     * }
     * </pre>
     */
    @Override
    public List<InventoryInStockVO> getInventoryInStock(List<Long> productIdList) {

        // 1. 对产品ID列表进行去重处理，避免重复查询和数据冗余
        productIdList = productIdList.stream().distinct().collect(Collectors.toList());

        // 2. 获取库位详情（正库存数量），用于表示各产品当前在库位上的实际库存情况
        List<InventoryBinLocationDetailVO> binLocationDetails =
                binLocationDetailService.listInventoryPositiveInStockQtyByProductIds(productIdList);

        // 3. 获取库存锁定信息，用于表示哪些库存已被锁定（未完成的锁定单）
        List<InventoryInventoryLockedVO> inventoryLockeds =
                inventoryLockedService.listInventoryLockeByProductIds(productIdList);

        // 4. 获取库存预定信息，用于表示哪些库存已被预定但未完成
        List<InventoryInventoryReserveVO> inventoryReserves =
                inventoryReserveService.listInventoryNoFinishByProductIds(productIdList);

        // 5. 获取未完成的入库工单详情，用于表示预计即将入库但尚未完成的库存
        List<InventoryInboundWorkorderDetailVO> inboundWorkorderDetails =
                inBoundworkorderDetailService.listInventoryNoFinishByProductIds(productIdList);

        // 6. 将上述信息整合到最终返回的结果中
        /* 优化建议: 当产品数量很多时，每次循环中都对四个列表进行stream过滤操作，
         * 时间复杂度为O(n*m)，其中n是产品数量，m是各类库存记录数量。
         *
         * 建议先对各类库存数据按productId分组，构建映射关系，然后遍历产品ID时直接从Map中获取对应数据，
         * 避免重复遍历和过滤。这样可以将时间复杂度降低到O(n+m)。
         *
         * 另外，可以预分配结果集大小，减少动态扩容开销：
         * List<InventoryInStockVO> result = new ArrayList<>(productIdList.size());
         */
        List<InventoryInStockVO> result = new ArrayList<>();

        for (Long productId : productIdList) {
            InventoryInStockVO inventoryInStockVO = new InventoryInStockVO();
            inventoryInStockVO.setProductId(productId);
            inventoryInStockVO.setInventoryReserveList(inventoryReserves.stream().filter(x -> x.getProductId().equals(productId)).collect(Collectors.toList()));
            inventoryInStockVO.setInventoryLockedList(inventoryLockeds.stream().filter(x -> x.getProductId().equals(productId)).collect(Collectors.toList()));
            inventoryInStockVO.setBinLocationDetailList(binLocationDetails.stream().filter(x -> x.getProductId().equals(productId)).collect(Collectors.toList()));
            inventoryInStockVO.setInboundWorkorderDetailList(inboundWorkorderDetails.stream().filter(x -> x.getProductId().equals(productId)).collect(Collectors.toList()));
            inventoryInStockVO.setWarehouseId(WarehouseContextHolder.getWarehouseId());
            result.add(inventoryInStockVO);
        }

        // 填充 ProductVO 信息，
        ProductCacheUtil.filledProduct(result);

        // 填充 WarehouseVO 信息，
        WarehouseCacheUtil.filledWarehouse(result);

        return result;
    }

    /**
     * 根据 ProductId 列表获取产品库存信息
     *
     * @param productIdList 产品ID列表
     * @return 产品库存信息列表
     */
    @Override
    public List<InventoryVO> getInventory(List<Long> productIdList) {
        Map<Long, InventoryVO> inventoryMap = getInventoryReturnAll(productIdList);
        //按照 productIdList 的顺序构建最终的 inventories 列表
        return productIdList.stream()
                .map(inventoryMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 根据 ProductId 列表获取产品库存信息
     *
     * @param productIdList 产品ID列表
     * @return 产品库存信息列表
     */
    @Override
    public Map<Long, InventoryVO> getInventoryReturnAll(List<Long> productIdList) {

        //为空返回 空数组
        if (ObjectUtil.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }

        // 1. 获取指定产品ID下的组件映射关系（装配品 -> 组件列表）
        List<ProductComponentDicBO> components =
                productComponentService.getListComponentDicByAssemblyProductIdList(productIdList);

        // 2. 将所有组件信息拉平并取得所有涉及到的组件和装配品的ID
        Set<Long> allComponentProductIds = components.stream()
                .filter(group -> ObjectUtil.isNotEmpty(group.getComponents()))
                .flatMap(dicBO -> dicBO.getComponents().stream())
                .flatMap(component -> Stream.of(component.getComponentProductId(), component.getAssemblyProductId()))
                .collect(Collectors.toSet());
        allComponentProductIds.addAll(productIdList);

        // 3. 获取产品组信息（根据产品ID获取对应的组字典信息）
        List<ProductGroupDicBO> groups = productGroupService.getDicListByProductId(allComponentProductIds.stream().toList());

        // 4. 收集所有涉及的产品ID（包括组父产品和组内产品）
        Set<Long> allProductIdSet = groups.stream()
                .filter(group -> ObjectUtil.isNotEmpty(group.getGroups()))
                .flatMap(group -> Stream.concat(Stream.of(group.getParentProductId()), group.getGroups().stream()))
                .collect(Collectors.toSet());
        allProductIdSet.addAll(allComponentProductIds);

        // 5. 获取所有相关产品ID的 inStock 信息，并转换成 InventoryVO 列表
        List<InventoryInStockVO> inventoryInStockList = getInventoryInStock(allProductIdSet.stream().toList());
        List<InventoryVO> inventories = BeanUtil.copyNew(inventoryInStockList, InventoryVO.class);

        // 6. 将 InventoryVO 按 ProductId 进行映射，方便后续快速查找
        Map<Long, InventoryVO> inventoryMap = ObjectUtil.toMap(inventories, InventoryVO::getProductId);

        // 7. 为所有的产品分配组信息（groupList）
        assignGroups(groups, inventoryMap);

        // 8. 为装配品分配组件信息（componentList）
        assignComponents(components, inventoryMap);

        return inventoryMap;
    }

    /**
     * 获取指定产品在所有仓库的库存信息
     *
     * @param productId 产品ID
     * @return 所有仓库中该产品的库存信息列表
     */
    @Override
    public List<InventoryVO> listWarehouseInventoryListByProductId(Long productId) {

        // 1. 获取当前仓库ID（请求上下文中可能保存有当前仓库ID）。
        Long currentWarehouseId = WarehouseContextHolder.getWarehouseId();

        List<InventoryVO> allInventory = new ArrayList<>();
        List<Warehouse> warehouseList = warehouseService.list();

        // 2. 遍历系统中所有仓库，为每个仓库设置上下文后获取该产品在此仓库的库存信息。
        warehouseList.forEach(item -> {
            // 设置仓库上下文为当前迭代仓库
            WarehouseContextHolder.setWarehouseId(item.getId());
            // 获取该产品在当前仓库下的库存
            List<InventoryVO> inventory = getInventory(Collections.singletonList(productId));
            // 合并结果
            allInventory.addAll(inventory);
        });

        // 3. 还原上下文中的仓库ID
        WarehouseContextHolder.setWarehouseId(currentWarehouseId);

        return allInventory;
    }

    /**
     * 分页查询库存信息
     * <p>
     * 该方法实现了库存信息的分页查询功能，包括以下步骤：
     * 1. 将库存查询条件转换为产品查询条件
     * 2. 分页查询产品信息
     * 3. 获取产品对应的库存信息
     * 4. 根据库位条件过滤库存信息（如果有）
     * 5. 返回带分页信息的库存数据
     * </p>
     * <p>
     * 该方法的特点是先分页查询产品，然后再获取这些产品的库存信息，
     * 这样可以避免先查询所有库存再分页的性能问题。
     * </p>
     *
     * @param search 包含查询条件和分页信息的对象
     * @return 带分页信息的库存数据
     * <p>
     * TODO: 前端可能会传递空的binTypeList数组，需要增强判断逻辑
     * 优化建议：增加对binLocationQuery的有效性检查，避免不必要的查询
     * <p>
     * 示例优化代码：
     * <pre>
     * public PageData<InventoryVO> pageByQuery(PageSearch<InventoryQuery> search) {
     *     // 将库存查询条件转换为产品查询条件
     *     PageSearch<ProductQuery> productPageSearch = new PageSearch<>();
     *     productPageSearch.setCurrent(search.getCurrent());
     *     productPageSearch.setSize(search.getSize());
     *     productPageSearch.setSorts(search.getSorts());
     *     productPageSearch.setCondition(search.getCondition().getProductQuery());
     *
     *     // 分页查询产品信息
     *     PageData<ProductVO> productPageData = productService.pageByQuery(productPageSearch);
     *     List<Long> productIds = productPageData.getRecords().stream()
     *             .map(ProductVO::getId)
     *             .toList();
     *
     *     // 无产品则返回空结果
     *     if (ObjectUtil.isEmpty(productIds)) {
     *         return new PageData<>(Collections.emptyList(), productPageData.getTotal(),
     *                 productPageData.getCurrent(), productPageData.getSize());
     *     }
     *
     *     // 获取这些产品对应的库存信息
     *     List<InventoryVO> inventories = getInventory(productIds);
     *
     *     // 如果有有效的BinLocation条件，则过滤库存数据
     *     BinLocationQuery binLocationQuery = search.getCondition().getBinLocationQuery();
     *     if (isValidBinLocationQuery(binLocationQuery)) {
     *         // 查询匹配条件的库位列表
     *         List<BinLocation> binLocations = binLocationService.listByCondition(binLocationQuery);
     *         if (!binLocations.isEmpty()) {
     *             List<Long> binLocationIds = binLocations.stream().map(IdModel::getId).toList();
     *             assignInventoryBinLocation(inventories, binLocationIds);
     *         }
     *     }
     *
     *     // 确保库存数据的顺序与产品数据一致
     *     List<InventoryVO> sortedInventories = sortInventoriesByProductOrder(inventories, productIds);
     *
     *     // 返回分页数据
     *     return new PageData<>(sortedInventories, productPageData.getTotal(),
     *             productPageData.getCurrent(), productPageData.getSize());
     * }
     *
     * // 检查BinLocationQuery是否有效
     * private boolean isValidBinLocationQuery(BinLocationQuery query) {
     *     if (ObjectUtil.isEmpty(query)) {
     *         return false;
     *     }
     *
     *     // 检查是否有实际的查询条件
     *     return ObjectUtil.isNotEmpty(query.getBinTypeList()) ||
     *            ObjectUtil.isNotEmpty(query.getBinCode()) ||
     *            ObjectUtil.isNotEmpty(query.getZoneId());
     * }
     *
     * // 根据产品ID的顺序对库存数据进行排序
     * private List<InventoryVO> sortInventoriesByProductOrder(List<InventoryVO> inventories, List<Long> productIds) {
     *     Map<Long, InventoryVO> inventoryMap = inventories.stream()
     *             .collect(Collectors.toMap(InventoryVO::getProductId, Function.identity(), (a, b) -> a));
     *
     *     return productIds.stream()
     *             .map(inventoryMap::get)
     *             .filter(Objects::nonNull)
     *             .collect(Collectors.toList());
     * }
     * </pre>
     */
    @Override
    public PageData<InventoryVO> pageByQuery(PageSearch<InventoryQuery> search) {

        // 1. 将 InventoryQuery 中的 ProductQuery 提取出来，用于分页获取产品信息。
        PageSearch<ProductQuery> productPageSearch = new PageSearch<>();
        productPageSearch.setCurrent(search.getCurrent());
        productPageSearch.setSize(search.getSize());
        productPageSearch.setSorts(search.getSorts());
        productPageSearch.setCondition(search.getCondition().getProductQuery());

        // 2. 分页查询产品信息
        PageData<ProductVO> productPageData = productService.pageByQuery(productPageSearch);
        List<Long> productIds = productPageData.getRecords().stream()
                .map(ProductVO::getId)
                .toList();

        // 3. 无产品则返回空结果
        if (ObjectUtil.isEmpty(productIds)) {
            return new PageData<>(Collections.emptyList(), productPageData.getTotal(), productPageData.getCurrent(), productPageData.getSize());
        }

        // 4. 获取这些产品对应的库存信息
        List<InventoryVO> inventories = getInventory(productIds);

        // 5. 如果有 BinLocation 条件，则对库存数据中的 BinLocation 进行过滤。
        BinLocationQuery binLocationQuery = search.getCondition().getBinLocationQuery();
        //todo:前端有可能对于binTypeList [] 数组的时候会传递，也就是这个对象里面有数据的，但是其实没筛选条件

        /* 优化建议: 前端可能会传递空的binTypeList数组，导致系统进行不必要的库位查询和过滤操作。
         * 当binLocationQuery对象存在但其中没有实际的过滤条件时，仍会触发库位查询。
         *
         * 建议增加对binLocationQuery的有效性检查方法，只有当存在实际的过滤条件时才进行库位查询：
         *
         * private boolean isValidBinLocationQuery(BinLocationQuery query) {
         *     if (ObjectUtil.isEmpty(query)) {
         *         return false;
         *     }
         *     // 检查是否有实际的查询条件
         *     return (ObjectUtil.isNotEmpty(query.getBinTypeList()) && !query.getBinTypeList().isEmpty()) ||
         *            ObjectUtil.isNotEmpty(query.getLocationName()) ||
         *            ObjectUtil.isNotEmpty(query.getType());
         * }
         */
        if (ObjectUtil.isNotEmpty(binLocationQuery)) {
            // 查询匹配条件的库位列表
            List<BinLocation> binLocations = binLocationService.listByCondition(binLocationQuery);
            List<Long> binLocationIds = binLocations.stream().map(IdModel::getId).toList();
            assignInventoryBinLocation(inventories, binLocationIds);
        }
        // 6. 最终返回分页数据。
        //todo:这个顺序必须要和product的顺序一样才行

        /* 优化建议: 确保返回的库存数据顺序与产品数据一致，避免前端排序问题。
         * 可以添加一个排序方法：
         *
         * private List<InventoryVO> sortInventoriesByProductOrder(List<InventoryVO> inventories, List<Long> productIds) {
         *     Map<Long, InventoryVO> inventoryMap = inventories.stream()
         *             .collect(Collectors.toMap(InventoryVO::getProductId, Function.identity(), (a, b) -> a));
         *
         *     return productIds.stream()
         *             .map(inventoryMap::get)
         *             .filter(Objects::nonNull)
         *             .collect(Collectors.toList());
         * }
         */
        return new PageData<>(inventories, productPageData.getTotal(), productPageData.getCurrent(), productPageData.getSize());
    }

    /**
     * 根据库位ID列表过滤库存数据的库位明细
     * <p>
     * 该方法是对递归方法的包装，初始化一个空的已处理集合，然后调用递归方法进行实际的过滤操作。
     * </p>
     *
     * @param inventories       要过滤的库存数据列表
     * @param binLocationIdList 要保留的库位ID列表，只有在这个列表中的库位才会被保留
     */
    private void assignInventoryBinLocation(Iterable<InventoryVO> inventories,
                                            List<Long> binLocationIdList) {
        assignInventoryBinLocation(inventories, binLocationIdList, new HashSet<>());
    }

    /**
     * 根据指定的库位ID列表对库存数据的库位明细进行过滤
     * <p>
     * 该方法递归遍历库存数据结构，包括主库存、组库存和组件库存，
     * 并对每个库存项的库位明细进行过滤，只保留指定库位ID列表中的库位。
     * 使用集合记录已处理过的库存ID，避免重复处理。
     * </p>
     *
     * @param inventories             要处理的库存列表
     * @param binLocationIdList       要筛选的库位ID列表，只有在这个列表中的库位才会被保留
     * @param allocateInventoryIdList 已处理过的库存ID集合，用于避免重复处理
     *                                <p>
     *                                                                                                                                                                                                                                                                                        TODO: 当库存结构很复杂或库位很多时，递归过滤可能会影响性能
     *                                                                                                                                                                                                                                                                                        优化建议：使用集合操作代替重复遍历，并考虑在高层次就过滤掉不需要的库存项
     *                                <p>
     *                                                                                                                                                                                                                                                                                        示例优化代码：
     *                                                                                                                                                                                                                                                                                        <pre>
     *                                                                                                                                                                                                                                                                                        private void assignInventoryBinLocation(Iterable<InventoryVO> inventories,
     *                                                                                                                                                                                                                                                                                                                              List<Long> binLocationIdList,
     *                                                                                                                                                                                                                                                                                                                              Set<String> allocateInventoryIdList) {
     *                                                                                                                                                                                                                                                                                            // 将库位ID列表转换为集合，提高查找效率
     *                                                                                                                                                                                                                                                                                            Set<Long> binLocationIdSet = new HashSet<>(binLocationIdList);
     *
     *                                                                                                                                                                                                                                                                                            for (InventoryVO inventory : inventories) {
     *                                                                                                                                                                                                                                                                                                // 如果已处理过该库存项，则跳过
     *                                                                                                                                                                                                                                                                                                if (!allocateInventoryIdList.add(inventory.getInventoryId())) {
     *                                                                                                                                                                                                                                                                                                    continue;
     *                                                                                                                                                                                                                                                                                                }
     *
     *                                                                                                                                                                                                                                                                                                // 过滤库位明细，只保留指定库位ID列表中的库位
     *                                                                                                                                                                                                                                                                                                List<InventoryBinLocationDetailVO> filteredDetails = new ArrayList<>();
     *                                                                                                                                                                                                                                                                                                for (InventoryBinLocationDetailVO detail : inventory.getBinLocationDetailList()) {
     *                                                                                                                                                                                                                                                                                                    if (binLocationIdSet.contains(detail.getBinLocationId())) {
     *                                                                                                                                                                                                                                                                                                        filteredDetails.add(detail);
     *                                                                                                                                                                                                                                                                                                    }
     *                                                                                                                                                                                                                                                                                                }
     *                                                                                                                                                                                                                                                                                                inventory.setBinLocationDetailList(filteredDetails);
     *
     *                                                                                                                                                                                                                                                                                                // 如果过滤后没有库位明细，则不需要处理子库存
     *                                                                                                                                                                                                                                                                                                if (filteredDetails.isEmpty()) {
     *                                                                                                                                                                                                                                                                                                    // 清空子库存列表，避免不必要的递归
     *                                                                                                                                                                                                                                                                                                    inventory.setGroupList(Collections.emptyList());
     *                                                                                                                                                                                                                                                                                                    inventory.setComponentList(Collections.emptyList());
     *                                                                                                                                                                                                                                                                                                    continue;
     *                                                                                                                                                                                                                                                                                                }
     *
     *                                                                                                                                                                                                                                                                                                // 递归处理组库存
     *                                                                                                                                                                                                                                                                                                if (ObjectUtil.isNotEmpty(inventory.getGroupList())) {
     *                                                                                                                                                                                                                                                                                                    assignInventoryBinLocation(inventory.getGroupList(), binLocationIdList, allocateInventoryIdList);
     *                                                                                                                                                                                                                                                                                                }
     *
     *                                                                                                                                                                                                                                                                                                // 递归处理组件库存
     *                                                                                                                                                                                                                                                                                                if (ObjectUtil.isNotEmpty(inventory.getComponentList())) {
     *                                                                                                                                                                                                                                                                                                    assignInventoryBinLocation(inventory.getComponentList(), binLocationIdList, allocateInventoryIdList);
     *                                                                                                                                                                                                                                                                                                }
     *                                                                                                                                                                                                                                                                                            }
     *                                                                                                                                                                                                                                                                                        }
     *                                                                                                                                                                                                                                                                                        </pre>
     */
    private void assignInventoryBinLocation(Iterable<InventoryVO> inventories,
                                            List<Long> binLocationIdList,
                                            Set<String> allocateInventoryIdList) {

        /* 优化建议: 当库存结构很复杂或库位很多时，递归遍历和重复过滤会导致性能下降。
         * 在当前实现中，即使库存项没有匹配的库位，仍然会递归处理其子库存，造成不必要的计算。
         *
         * 主要优化点：
         * 1. 将库位ID列表转换为集合，提高查找效率：
         *    Set<Long> binLocationIdSet = new HashSet<>(binLocationIdList);
         *
         * 2. 当过滤后的库位明细为空时，直接清空子库存列表，避免不必要的递归：
         *    if (filteredDetails.isEmpty()) {
         *        inventory.setGroupList(Collections.emptyList());
         *        inventory.setComponentList(Collections.emptyList());
         *        continue;
         *    }
         *
         * 3. 使用集合操作代替流式过滤，减少中间对象创建
         */

        for (InventoryVO inventory : inventories) {
            // 如果当前库存项的 productId 已在列表中，则跳过
            if (!allocateInventoryIdList.add(inventory.getInventoryId())) {
                continue;
            }

            // 过滤当前库存项的 BinLocationDetailList
            List<InventoryBinLocationDetailVO> filteredBinLocationDetails = inventory.getBinLocationDetailList()
                    .stream()
                    .filter(x -> binLocationIdList.contains(x.getBinLocationId()))
                    .collect(Collectors.toList());
            inventory.setBinLocationDetailList(filteredBinLocationDetails);

            // TODO: 如果过滤后没有库位明细，则不需要处理子库存，可以直接清空子库存列表并跳过

            // 对组列表和组件列表递归调用该方法，确保所有嵌套层级的 BinLocation 都得到过滤
            if (ObjectUtil.isNotEmpty(inventory.getGroupList())) {
                assignInventoryBinLocation(inventory.getGroupList(), binLocationIdList, allocateInventoryIdList);
            }
            if (ObjectUtil.isNotEmpty(inventory.getComponentList())) {
                assignInventoryBinLocation(inventory.getComponentList(), binLocationIdList, allocateInventoryIdList);
            }
        }
    }

    /**
     * 给所有的产品分配组信息
     *
     * @param groups       产品组信息列表
     * @param inventoryMap 产品ID -> InventoryVO 的映射关系
     */
    private void assignGroups(List<ProductGroupDicBO> groups, Map<Long, InventoryVO> inventoryMap) {
        // Set<Long> assignedGroups = new HashSet<>();

        // 1. 构建 productId -> groupDic 的映射
        Map<Long, ProductGroupDicBO> groupDicMap = ObjectUtil.toMap(groups, ProductGroupDicBO::getCurrentProductId);

        // 遍历 productIds 为每个产品分配 group 信息
        for (Map.Entry<Long, InventoryVO> entry : inventoryMap.entrySet()) {
            Long productId = entry.getKey();
            InventoryVO inventory = entry.getValue();

            inventory.setInventoryId(UUID.randomUUID().toString());

            // 获取当前产品的组信息
            ProductGroupDicBO groupDic = groupDicMap.get(productId);
            if (ObjectUtil.isEmpty(groupDic) || ObjectUtil.isEmpty(groupDic.getGroups())) {
                inventory.setGroupList(Collections.emptyList());
                continue;
            }
            // 收集该组中的所有产品的库存信息
            List<InventoryVO> groupInventories = groupDic.getGroups().stream()
                    .map(inventoryMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (ObjectUtil.isEmpty(groupInventories)) {
                inventory.setGroupList(Collections.emptyList());
                continue;
            }
            // 设置当前产品的组列表
            List<InventoryVO> currentGroupInventoryList = BeanUtil.copyNew(groupInventories, InventoryVO.class);
            currentGroupInventoryList.forEach(x -> {
                x.setInventoryId(UUID.randomUUID().toString());
                if (ObjectUtil.isNotEmpty(x.getGroupList())) {
                    x.setGroupList(Collections.emptyList());
                }
            });
            inventory.setGroupList(currentGroupInventoryList);
        }
    }

    /**
     * 为装配品（assemblyProductId）分配其组件列表信息
     *
     * @param componentDicBOList 装配品与组件列表的映射（包含 assemblyProductId 与其对应的组件列表）
     * @param inventoryMap       产品ID与库存信息映射
     */
    private void assignComponents(List<ProductComponentDicBO> componentDicBOList, Map<Long, InventoryVO> inventoryMap) {

        Map<Long, ProductComponentDicBO> assemblyMap = ObjectUtil.toMap(componentDicBOList, ProductComponentDicBO::getAssemblyProductId);

        // // 跟踪已处理的装配品ID，避免重复处理
        // Set<Long> assignedSet = new HashSet<>();

        // 使用 for 循环遍历 inventoryMap
        for (Map.Entry<Long, InventoryVO> entry : inventoryMap.entrySet()) {
            Long assemblyProductId = entry.getKey();
            InventoryVO inventory = entry.getValue();

            if (ObjectUtil.isEmpty(inventory)) {
                continue;
            }
            inventory.setInventoryId(UUID.randomUUID().toString());
            /* 优化建议: 当前实现中存在重复逻辑，对主库存和组库存的处理逻辑几乎相同。
             * 建议提取公共逻辑到单独的方法，减少代码重复。
             * 另外，每次循环都会生成新的UUID，导致不必要的对象创建。
             *
             * 可以创建一个处理组库存的方法：
             * private void processGroupInventoryComponents(List<InventoryVO> groupList, Long parentProductId,
             *                                           Map<Long, ProductComponentDicBO> assemblyMap,
             *                                           Map<Long, InventoryVO> inventoryMap,
             *                                           Set<String> processedIds) {
             *     // 处理逻辑...
             * }
             */
            //todo: 优化这个
            if (ObjectUtil.isNotEmpty(inventory.getGroupList())) {

                for (InventoryVO groupInventory : inventory.getGroupList()) {

                    groupInventory.setInventoryId(UUID.randomUUID().toString());

                    //设置Assembly Component 的Qty 为O=0
                    groupInventory.setComponentQty(0);

                    // 从映射中获取对应的组件列表
                    ProductComponentDicBO productComponentDicBO = assemblyMap.get(assemblyProductId);

                    //todo: check Why 可能为Null
                    /* 优化建议: 当productComponentDicBO为空时，可能是因为该产品不是装配品或者没有组件信息。
                     * 建议在方法开始就对assemblyMap进行检查，并在注释中说明可能为空的原因。
                     * 另外，可以将判空逻辑和空列表设置逻辑合并：
                     * if (ObjectUtil.isEmpty(productComponentDicBO) || ObjectUtil.isEmpty(productComponentDicBO.getComponents())) {
                     *     groupInventory.setComponentList(Collections.emptyList());
                     *     continue;
                     * }
                     */
                    if (ObjectUtil.isEmpty(productComponentDicBO)) {
                        groupInventory.setComponentList(Collections.emptyList());
                        continue;
                    }

                    List<ProductComponent> components = productComponentDicBO.getComponents();

                    // 没有组件则设置空列表
                    if (ObjectUtil.isEmpty(components)) {
                        groupInventory.setComponentList(Collections.emptyList());
                        continue;
                    }
                    // 将组件信息转换为对应的 InventoryVO 列表
                    List<InventoryVO> inventoryComponents = convertToInventoryComponents(components, inventoryMap);
                    // 为装配品设置组件列表
                    groupInventory.setComponentList(inventoryComponents);
                }
            }

            //设置Assembly Component 的Qty 为O=0
            inventory.setComponentQty(0);

            // 从映射中获取对应的组件列表
            ProductComponentDicBO productComponentDicBO = assemblyMap.get(assemblyProductId);

            //todo: check Why 可能为Null
            if (ObjectUtil.isEmpty(productComponentDicBO)) {
                inventory.setComponentList(Collections.emptyList());
                continue;
            }

            List<ProductComponent> components = productComponentDicBO.getComponents();

            // 没有组件则设置空列表
            if (ObjectUtil.isEmpty(components)) {
                inventory.setComponentList(Collections.emptyList());
                continue;
            }
            // 将组件信息转换为对应的 InventoryVO 列表
            List<InventoryVO> inventoryComponents = convertToInventoryComponents(components, inventoryMap);
            // 为装配品设置组件列表
            inventory.setComponentList(inventoryComponents);
        }
    }
}