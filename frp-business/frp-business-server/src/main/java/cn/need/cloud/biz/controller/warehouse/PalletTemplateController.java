package cn.need.cloud.biz.controller.warehouse;

import cn.need.cloud.biz.converter.warehouse.PalletTemplateConverter;
import cn.need.cloud.biz.model.entity.warehouse.PalletTemplate;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletTemplateUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletTemplateCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletTemplateQuery;
import cn.need.cloud.biz.model.vo.page.PalletTemplatePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletTemplateVO;
import cn.need.cloud.biz.service.warehouse.PalletTemplateService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 打托模板 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/pallet-template")
@Tag(name = "打托模板")
public class PalletTemplateController extends AbstractRestController<PalletTemplateService, PalletTemplate, PalletTemplateConverter, PalletTemplateVO> {

    @Operation(summary = "新增打托模板", description = "接收打托模板的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PalletTemplateCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改打托模板", description = "接收打托模板的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) PalletTemplateUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除打托模板", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Boolean> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.remove(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取打托模板详情", description = "根据数据主键id，从数据库中获取其对应的打托模板详情")
    @GetMapping(value = "/detail/{id}")
    public Result<PalletTemplateVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取打托模板详情
        PalletTemplateVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取打托模板详情", description = "根据数据RefNum，从数据库中获取其对应的打托模板详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<PalletTemplateVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取打托模板详情
        PalletTemplateVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取打托模板分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的打托模板列表")
    @PostMapping(value = "/list")
    public Result<PageData<PalletTemplatePageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<PalletTemplateQuery> search) {

        // 获取打托模板分页
        PageData<PalletTemplatePageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "设置默认", description = "设置默认")
    @GetMapping(value = "/set-default/{palletTemplateId}")
    public Result<Integer> setDefault(@PathVariable("palletTemplateId") @Parameter(description = "打托模板id", required = true) Long palletTemplateId) {

        // 返回结果
        return success(service.setDefault(palletTemplateId));
    }
}
