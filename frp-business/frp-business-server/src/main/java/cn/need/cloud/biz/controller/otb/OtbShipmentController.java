package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.converter.otb.OtbShipmentConverter;
import cn.need.cloud.biz.model.entity.otb.OtbShipment;
import cn.need.cloud.biz.model.param.otb.create.shipment.OtbShipmentCreateParam;
import cn.need.cloud.biz.model.param.otb.update.pallet.OtbBuildPalletParam;
import cn.need.cloud.biz.model.query.base.PrintQuery;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otb.pallet.OtbBuildPalletVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbMarkPalletReadyToShipVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbSignedBolFileVO;
import cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO;
import cn.need.cloud.biz.service.otb.shipment.*;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * OTB装运 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-shipment")
@Tag(name = "OTB装运")
public class OtbShipmentController extends AbstractRestController<OtbShipmentService, OtbShipment, OtbShipmentConverter, OtbShipmentVO> {

    @Resource
    private OtbBuildShipmentLtlService otbBuildShipmentLtlService;
    @Resource
    private OtbBuildShipmentSmallParcelService otbBuildShipmentSmallParcelService;
    @Resource
    private OtbShipmentLtlService otbShipmentLtlService;
    @Resource
    private OtbShipmentSmallParcelService otbShipmentSmallParcelService;

    @Operation(summary = "根据id获取OTB装运详情", description = "根据数据主键id，从数据库中获取其对应的OTB装运详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtbShipmentVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取OTB装运详情
        OtbShipmentVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTB装运详情", description = "根据数据RefNum，从数据库中获取其对应的OTB装运详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtbShipmentVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTB装运详情
        OtbShipmentVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取OTB装运分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTB装运列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtbShipmentPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtbShipmentQuery> search) {

        // 获取OTB装运分页
        PageData<OtbShipmentPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "otb大件构建发货单", description = "otb大件构建发货单")
    @PostMapping(value = "/build")
    public Result<OtbShipmentVO> buildShipmentBig(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtbShipmentCreateParam otbShipmentCreateParam) {

        // 返回结果
        return success(otbBuildShipmentLtlService.buildShipmentLtl(otbShipmentCreateParam));
    }

    @Operation(summary = "otb小件构建发货单", description = "otb小件件构建发货单")
    @PostMapping(value = "/build-small-parcel")
    public Result<OtbShipmentVO> buildSmallParcel(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtbShipmentCreateParam param) {

        // 返回结果
        return success(otbBuildShipmentSmallParcelService.buildSmallParcel(param));
    }

    @Operation(summary = "打印shipment", description = "打印shipment")
    @PostMapping(value = "/markPrinted")
    public Result<Boolean> markPrinted(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        service.markPrinted(printQuery);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "processing", description = "processing")
    @PostMapping(value = "/processing")
    public Result<Boolean> processing(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        otbShipmentLtlService.printedBolFile(printQuery);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "processedLtl", description = "processedLtl")
    @PostMapping(value = "/processedLtl")
    public Result<Boolean> processedLtl(@RequestBody @Parameter(description = "搜索条件参数", required = true) OtbSignedBolFileVO otbSignedBolFileVO) {

        otbShipmentLtlService.processed(otbSignedBolFileVO);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "大件ReadyToShip", description = "大件ReadyToShip")
    @PostMapping(value = "/markPalletReadyToShip")
    public Result<Boolean> markPalletReadyToShip(@RequestBody @Parameter(description = "发货单id以及打托单id", required = true) OtbMarkPalletReadyToShipVO param) {

        otbShipmentLtlService.markPalletReadyToShip(param.getOtbPalletId(), param.getOtbShipmentId());
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "打印Pol文件", description = "打印Pol文件")
    @PostMapping(value = "/printedBolFile")
    public Result<Boolean> printedBolFile(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        otbShipmentLtlService.printedBolFile(printQuery);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "打印PalletFile文件", description = "打印PalletFile文件")
    @PostMapping(value = "/printedPalletFile")
    public Result<Boolean> printedPalletFile(@RequestBody @Parameter(description = "搜索条件参数", required = true) PrintQuery printQuery) {

        otbShipmentLtlService.printedPalletFile(printQuery);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "大件拆托", description = "大件拆托")
    @GetMapping(value = "/splitPallet/{otbShipmentId}")
    public Result<Boolean> splitPallet(@PathVariable("otbShipmentId") @Parameter(description = "发货单id", required = true) Long otbShipmentId) {

        otbShipmentLtlService.splitPallet(otbShipmentId);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "渠道确认", description = "渠道确认")
    @PostMapping(value = "/waitChannelConfirm")
    public Result<Boolean> waitChannelConfirm(@RequestBody @Parameter(description = "搜索条件参数", required = true) List<Long> idList) {

        //  等待渠道确认
        service.waitChannelConfirm(idList);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "小件发货ship", description = "小件发货ship")
    @GetMapping(value = "processed/{id}")
    public Result<Boolean> processedSmallParcel(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        otbShipmentSmallParcelService.processed(id);
        // 返回结果
        return success(Boolean.TRUE);
    }

    @Operation(summary = "小件转大件打托", description = "小件转大件打托")
    @PostMapping(value = "createPallet")
    public Result<OtbBuildPalletVO> createPallet(@RequestBody @Parameter(description = "查询条件", required = true) OtbBuildPalletParam param) {

        // 返回结果
        return success(otbShipmentSmallParcelService.createPallet(param));
    }

    @Operation(summary = "下拉列表Pro", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValuePro(@RequestBody @Parameter(description = "查询条件", required = true) OtbShipmentQuery query) {

        return success(service.distinctValuePro(query));
    }

    @Operation(summary = "Dashboard CountPreDay", description = "下拉列表")
    @PostMapping(value = "/count-pre-day")
    public Result<List<DropProVO>> countPreDay(@RequestBody @Parameter(description = "查询条件", required = true) OtbShipmentQuery query) {

        return success(service.countPreDay(query));
    }

}
