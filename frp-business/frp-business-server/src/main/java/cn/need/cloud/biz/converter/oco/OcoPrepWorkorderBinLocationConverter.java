package cn.need.cloud.biz.converter.oco;

import cn.need.cloud.biz.client.dto.oco.OcoPrepWorkorderBinLocationDTO;
import cn.need.cloud.biz.model.entity.oco.OcoPrepWorkorderBinLocation;
import cn.need.cloud.biz.model.vo.oco.OcoPrepWorkorderBinLocationVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OCO预处理工单库位表 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
public class OcoPrepWorkorderBinLocationConverter extends AbstractModelConverter<OcoPrepWorkorderBinLocation, OcoPrepWorkorderBinLocationVO, OcoPrepWorkorderBinLocationDTO> {

}



