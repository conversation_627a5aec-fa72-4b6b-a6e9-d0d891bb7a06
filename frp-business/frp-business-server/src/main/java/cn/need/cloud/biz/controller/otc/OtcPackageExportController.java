package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.model.query.otc.pkg.OtcPackageListQuery;
import cn.need.cloud.biz.service.otc.pkg.OtcPackageExportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;

/**
 * 包裹导出
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@RestController
@RequestMapping("/api/biz/otc-package/export")
@Tag(name = "OTC包裹-导出")
@Slf4j
@RequiredArgsConstructor
public class OtcPackageExportController {

    private final OtcPackageExportService service;

    @Operation(summary = "包裹-export", description = "导出列表")
    @PostMapping("/list")
    public void export(@RequestBody @Validated @Parameter(description = "搜索条件参数", required = true) OtcPackageListQuery query, HttpServletResponse response) {
        // 直接执行导出，不返回Result对象
        service.export(query, response);
    }
}
