package cn.need.cloud.biz.converter.product;

import cn.need.cloud.biz.client.dto.product.ProductGroupDTO;
import cn.need.cloud.biz.model.entity.product.ProductGroup;
import cn.need.cloud.biz.model.vo.product.ProductGroupVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 产品同类 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class ProductGroupConverter extends AbstractModelConverter<ProductGroup, ProductGroupVO, ProductGroupDTO> {

}
