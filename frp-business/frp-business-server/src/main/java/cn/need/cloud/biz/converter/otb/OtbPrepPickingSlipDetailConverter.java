package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPrepPickingSlipDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepPickingSlipDetail;
import cn.need.cloud.biz.model.vo.otb.pickingslip.OtbPrepPickingSlipDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * otb预拣货单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPrepPickingSlipDetailConverter extends AbstractModelConverter<OtbPrepPickingSlipDetail, OtbPrepPickingSlipDetailVO, OtbPrepPickingSlipDetailDTO> {

}
