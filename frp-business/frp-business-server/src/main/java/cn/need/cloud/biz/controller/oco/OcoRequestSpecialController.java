package cn.need.cloud.biz.controller.oco;

import cn.need.cloud.biz.model.param.oco.OcoRequestCancelParam;
import cn.need.cloud.biz.service.oco.request.OcoRequestSpecialService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <p>
 * OCO请求特殊操作控制器
 * </p>
 * <p>
 * 提供OCO请求的特殊操作接口，如取消、回滚等。
 * 参考OTB和OTC的Special Controller实现。
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-01
 */
@RestController
@RequestMapping("/api/biz/oco-request/special")
@Tag(name = "OCO请求特殊操作")
@Slf4j
public class OcoRequestSpecialController extends AbstractController {
    
    @Resource
    private OcoRequestSpecialService ocoRequestSpecialService;

    /**
     * <p>
     * 根据数据主键id取消OCO请求
     * </p>
     *
     * @param param 取消参数，包含请求ID和取消原因
     * @return 受影响行数
     */
    @Operation(summary = "取消OCO请求接口，返回受影响行数", description = "根据数据主键id，从数据库中取消OCO请求")
    @PostMapping(value = "/cancel")
    public Result<Integer> cancel(@RequestBody @Parameter(description = "取消参数", required = true) OcoRequestCancelParam param) {
        log.info("OCO Request Special Controller - cancel: {}", param);
        
        Validate.notNull(param.getId(), "The id value cannot be null.");
        Validate.notBlank(param.getNote(), "The note value cannot be blank.");
        
        int result = ocoRequestSpecialService.cancel(param);
        return Result.ok(result);
    }
}
