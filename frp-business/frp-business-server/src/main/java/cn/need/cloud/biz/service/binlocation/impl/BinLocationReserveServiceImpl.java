package cn.need.cloud.biz.service.binlocation.impl;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.converter.binlocation.BinLocationReserveConverter;
import cn.need.cloud.biz.mapper.binlocation.BinLocationReserveMapper;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationReserve;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationReserveCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationReserveUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationReserveQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationReserveVO;
import cn.need.cloud.biz.model.vo.page.BinLocationReservePageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationReserveService;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.base.SuperServiceImpl;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.mybatis.utils.Conditions;
import cn.need.framework.common.support.convert.Converters;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <p>
 * 预留库位 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@Service
public class BinLocationReserveServiceImpl extends SuperServiceImpl<BinLocationReserveMapper, BinLocationReserve> implements BinLocationReserveService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long insertByParam(BinLocationReserveCreateParam createParam) {
        // 检查传入预留库位参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(createParam)) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取预留库位转换器实例，用于将预留库位参数对象转换为实体对象
        BinLocationReserveConverter converter = Converters.get(BinLocationReserveConverter.class);

        // 将预留库位参数对象转换为实体对象并初始化
        BinLocationReserve entity = initBinLocationReserve(converter.toEntity(createParam));

        // 插入预留库位实体对象到数据库
        super.insert(entity);

        // 返回预留库位ID
        return entity.getId();
    }


    /**
     * 初始化预留库位对象
     * 此方法用于设置预留库位对象的必要参数，确保其处于有效状态
     *
     * @param entity 预留库位对象，不应为空
     * @return 返回初始化后的预留库位
     * @throws BusinessException 如果传入的预留库位为空，则抛出此异常
     */
    private BinLocationReserve initBinLocationReserve(BinLocationReserve entity) {

        // 检查传入的配置对象是否为空
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException("BinLocationReserve cannot be empty");
        }


        // 返回初始化后的配置对象
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateByParam(BinLocationReserveUpdateParam updateParam) {
        // 检查传入预留库位参数是否为空，如果为空则抛出异常
        if (ObjectUtil.isEmpty(updateParam) || ObjectUtil.isEmpty(updateParam.getId())) {
            // throw new BusinessException("Parameter cannot be empty");
            throw new BusinessException(ErrorMessages.PARAMETER_EMPTY);
        }

        // 获取预留库位转换器实例，用于将预留库位参数对象转换为实体对象
        BinLocationReserveConverter converter = Converters.get(BinLocationReserveConverter.class);

        // 将预留库位参数对象转换为实体对象
        BinLocationReserve entity = converter.toEntity(updateParam);

        // 执行更新预留库位操作
        return super.update(entity);

    }

    @Override
    public List<BinLocationReservePageVO> listByQuery(BinLocationReserveQuery query) {
        return mapper.listByQuery(query);
    }

    @Override
    public PageData<BinLocationReservePageVO> pageByQuery(PageSearch<BinLocationReserveQuery> search) {
        /* 优化建议: 当前实现存在以下问题：
         * 1. 没有对查询条件进行验证，可能导致无效查询
         * 2. 没有对查询结果进行缓存，可能导致重复查询
         * 3. 对于大量数据，可能存在性能问题
         *
         * 优化建议：
         * 1. 添加查询条件验证，避免无效查询
         * 2. 对于频繁查询的条件，考虑缓存查询结果
         * 3. 添加索引以提高查询性能
         * 4. 对于大量数据，考虑使用分批查询或异步处理
         *
         * 示例优化代码：
         * // 验证查询条件
         * if (search == null || search.getCondition() == null) {
         *     return new PageData<>(Collections.emptyList(), 0L, 1, 10);
         * }
         *
         * // 尝试从缓存中获取
         * String cacheKey = generateCacheKey(search);
         * PageData<BinLocationReservePageVO> cachedResult = cacheService.get(cacheKey);
         * if (cachedResult != null) {
         *     return cachedResult;
         * }
         *
         * // 执行查询
         * Page<BinLocationReserve> page = Conditions.page(search, entityClass);
         * List<BinLocationReservePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
         * PageData<BinLocationReservePageVO> result = new PageData<>(dataList, page);
         *
         * // 将结果存入缓存
         * cacheService.put(cacheKey, result, CACHE_EXPIRY_TIME);
         *
         * return result;
         */
        Page<BinLocationReserve> page = Conditions.page(search, entityClass);
        List<BinLocationReservePageVO> dataList = mapper.listByQuery(search.getCondition(), page);
        return new PageData<>(dataList, page);
    }

    @Override
    public BinLocationReserveVO detailById(Long id) {
        BinLocationReserve entity = getById(id);
        //  获取不到返回异常信息
        if (ObjectUtil.isEmpty(entity)) {
            // throw new BusinessException("id: " + id + " not found in BinLocationReserve");
            throw new BusinessException(String.format(ErrorMessages.ENTITY_NOT_FOUND, "BinLocationReserve", id));
        }
        return buildBinLocationReserveVO(entity);
    }


    /**
     * 构建预留库位VO对象
     *
     * @param entity 预留库位对象
     * @return 返回包含详细信息的预留库位VO对象
     */
    private BinLocationReserveVO buildBinLocationReserveVO(BinLocationReserve entity) {
        if (ObjectUtil.isEmpty(entity)) {
            return null;
        }
        // 返回包含详细信息的预留库位VO对象
        return Converters.get(BinLocationReserveConverter.class).toVO(entity);
    }

}
