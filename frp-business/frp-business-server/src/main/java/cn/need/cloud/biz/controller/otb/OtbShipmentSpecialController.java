package cn.need.cloud.biz.controller.otb;

import cn.need.cloud.biz.model.param.otb.update.shipment.OtbShipmentBatchRollbackUpdateParam;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentSpecialService;
import cn.need.framework.common.support.api.Result;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <p>
 * OTB装运 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otb-shipment/special")
@Tag(name = "OTB装运-Special")
@AllArgsConstructor
public class OtbShipmentSpecialController {

    private final OtbShipmentSpecialService otbShipmentSpecialService;


    @Operation(summary = "Rollback", description = "批量Rollback")
    @PostMapping(value = "/batch-rollback")
    public Result<Boolean> rollback(@RequestBody @Valid OtbShipmentBatchRollbackUpdateParam param) {

        // 返回结果
        return Result.ok(otbShipmentSpecialService.batchRollback(param));
    }

    @Operation(summary = "Cancel", description = "批量Cancel")
    @PostMapping(value = "/batch-cancel")
    public Result<Boolean> cancel(@RequestBody @Valid OtbShipmentBatchRollbackUpdateParam param) {

        // 返回结果
        return Result.ok(otbShipmentSpecialService.batchRollback(param));
    }
}
