package cn.need.cloud.biz.converter.log;

import cn.need.cloud.biz.client.dto.log.AuditShowLogDTO;
import cn.need.cloud.biz.model.entity.log.AuditShowLog;
import cn.need.cloud.biz.model.vo.log.AuditShowLogVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-27
 */
public class AuditShowLogConverter extends AbstractModelConverter<AuditShowLog, AuditShowLogVO, AuditShowLogDTO> {

}
