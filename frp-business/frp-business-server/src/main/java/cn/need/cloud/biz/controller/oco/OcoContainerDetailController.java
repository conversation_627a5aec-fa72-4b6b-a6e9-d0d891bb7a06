package cn.need.cloud.biz.controller.oco;

import cn.need.cloud.biz.converter.oco.OcoContainerDetailConverter;
import cn.need.cloud.biz.model.entity.oco.OcoContainerDetail;
import cn.need.cloud.biz.model.vo.oco.OcoContainerDetailVO;
import cn.need.cloud.biz.service.oco.OcoContainerDetailService;
import cn.need.cloud.biz.model.query.oco.OcoContainerDetailQuery;
import cn.need.cloud.biz.model.param.oco.create.OcoContainerDetailCreateParam;
import cn.need.cloud.biz.model.param.oco.update.OcoContainerDetailUpdateParam;
import cn.need.cloud.biz.model.vo.page.OcoContainerDetailPageVO;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.core.text.JsonUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.IdCondition;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.cloud.upms.cache.util.UserCacheUtil;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * OCO集装箱明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@RequestMapping("/api/biz/oco-container-detail")
@Tag(name = "OCO集装箱明细表")
public class OcoContainerDetailController extends AbstractRestController<OcoContainerDetailService, OcoContainerDetail, OcoContainerDetailConverter, OcoContainerDetailVO> {

    @Operation(summary = "新增OCO集装箱明细表", description = "接收OCO集装箱明细表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoContainerDetailCreateParam insertParam) {
        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改OCO集装箱明细表", description = "接收OCO集装箱明细表的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) OcoContainerDetailUpdateParam updateParam) {
        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除OCO集装箱明细表", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {
        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取OCO集装箱明细表详情", description = "根据数据主键id，从数据库中获取其对应的OCO集装箱明细表详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OcoContainerDetailVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {
        // 返回结果
        return success(service.detailById(id));
    }




    @Operation(summary = "获取OCO集装箱明细表分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OCO集装箱明细表列表")
    @PostMapping(value = "/list")
    public Result<PageData<OcoContainerDetailPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OcoContainerDetailQuery> search) {
        // 返回结果
        return success(service.pageByQuery(search));
    }
}



