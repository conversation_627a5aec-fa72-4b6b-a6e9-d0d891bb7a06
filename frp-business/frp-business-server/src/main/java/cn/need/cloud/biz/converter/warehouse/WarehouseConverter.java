package cn.need.cloud.biz.converter.warehouse;

import cn.need.cloud.biz.client.dto.warehouse.WarehouseDTO;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库基础信息 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class WarehouseConverter extends AbstractModelConverter<Warehouse, WarehouseVO, WarehouseDTO> {

}
