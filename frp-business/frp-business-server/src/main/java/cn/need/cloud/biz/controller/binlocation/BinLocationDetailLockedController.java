package cn.need.cloud.biz.controller.binlocation;

import cn.need.cloud.biz.converter.binlocation.BinLocationDetailLockedConverter;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetailLocked;
import cn.need.cloud.biz.model.param.binlocation.create.BinLocationDetailLockedCreateParam;
import cn.need.cloud.biz.model.param.binlocation.update.BinLocationDetailLockedUpdateParam;
import cn.need.cloud.biz.model.query.binlocation.BinLocationDetailLockedQuery;
import cn.need.cloud.biz.model.vo.binlocation.BinLocationDetailLockedVO;
import cn.need.cloud.biz.model.vo.page.BinLocationDetailLockedPageVO;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailLockedService;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <p>
 * 锁定 库位详情 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/bin-location-detail-locked")
@Tag(name = "锁定 库位详情")
public class BinLocationDetailLockedController extends AbstractRestController<BinLocationDetailLockedService, BinLocationDetailLocked, BinLocationDetailLockedConverter, BinLocationDetailLockedVO> {

    @Operation(summary = "新增锁定 库位详情", description = "接收锁定 库位详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/insert")
    public Result<Long> insert(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationDetailLockedCreateParam insertParam) {

        // 返回结果
        return success(service.insertByParam(insertParam));
    }

    @Operation(summary = "修改锁定 库位详情", description = "接收锁定 库位详情的传参对象，将该对象持久化到数据库中表")
    @PostMapping(value = "/update")
    public Result<Integer> update(@Valid @RequestBody @Parameter(description = "数据对象", required = true) BinLocationDetailLockedUpdateParam updateParam) {

        // 返回结果
        return success(service.updateByParam(updateParam));
    }

    @Operation(summary = "根据id删除锁定 库位详情", description = "根据数据主键id，从数据库中删除其对应的数据，并记录删除原因")
    @PostMapping(value = "/remove")
    public Result<Integer> remove(@RequestBody @Parameter(description = "数据主键id", required = true) DeletedNoteParam deletedNoteParam) {

        // 校验参数
        Validate.notNull(deletedNoteParam.getId(), "The id value cannot be null.");
        // 执行删除并返回结果
        return success(service.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote()));
    }

    @Operation(summary = "根据id获取锁定 库位详情详情", description = "根据数据主键id，从数据库中获取其对应的锁定 库位详情详情")
    @GetMapping(value = "/detail/{id}")
    public Result<BinLocationDetailLockedVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        // 获取锁定 库位详情详情
        BinLocationDetailLockedVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取锁定 库位详情详情", description = "根据数据RefNum，从数据库中获取其对应的锁定 库位详情详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<BinLocationDetailLockedVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取锁定 库位详情详情
        BinLocationDetailLockedVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }


    @Operation(summary = "获取锁定 库位详情分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的锁定 库位详情列表")
    @PostMapping(value = "/list")
    public Result<PageData<BinLocationDetailLockedPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<BinLocationDetailLockedQuery> search) {

        // 获取锁定 库位详情分页
        PageData<BinLocationDetailLockedPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }
}
