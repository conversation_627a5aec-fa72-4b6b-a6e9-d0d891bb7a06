package cn.need.cloud.biz.service.warehouse.impl;

import cn.need.cloud.biz.cache.WarehouseCacheRepertory;
import cn.need.cloud.biz.model.entity.binlocation.BinLocationDetail;
import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.service.binlocation.BinLocationDetailService;
import cn.need.cloud.biz.service.binlocation.BinLocationService;
import cn.need.cloud.biz.service.warehouse.WarehouseOperationService;
import cn.need.cloud.biz.service.warehouse.WarehouseService;
import cn.need.cloud.biz.service.warehouse.WarehouseSpecialService;
import cn.need.framework.common.core.constant.DataState;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.core.lang.Validate;
import cn.need.framework.common.support.api.DeletedNoteParam;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 仓库基础信息 service 实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
@Service
public class WarehouseSpecialServiceImpl implements WarehouseSpecialService {
    @Resource
    private WarehouseService warehouseService;
    @Resource
    private WarehouseCacheRepertory warehouseCacheRepertory;
    @Resource
    private BinLocationService binLocationService;
    @Resource
    private BinLocationDetailService binLocationDetailService;
    @Resource
    private WarehouseOperationService warehouseOperationService;

    /**
     * 删除仓库
     * 该方法用于删除仓库，传入仓库di
     *
     * @param deletedNoteParam 仓库id及删除备注
     * @return 影响行数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer removeWarehouse(DeletedNoteParam deletedNoteParam) {
        //过滤
        WarehouseContextHolder.setIgnore(Boolean.TRUE);
        //数据初始化
        Warehouse warehouse = warehouseService.getById(deletedNoteParam.getId());
        //数据校验
        dataValid(warehouse);
        //删除仓库
        int removed = warehouseService.removeAndNote(deletedNoteParam.getId(), deletedNoteParam.getDeletedNote());
        //删除该仓库下库位
        binLocationService.removeBinByWarehouseId(deletedNoteParam.getId());
        //删除缓存
        warehouseCacheRepertory.delWarehouse(StringUtil.toString(deletedNoteParam.getId()));
        //返回影响行数
        return removed;
    }

    /**
     * 数据校验
     * 该方法用于校验行数据是否可删，传入 仓库id
     *
     * @param warehouse 仓库信息
     */
    private void dataValid(Warehouse warehouse) {
        boolean exist = warehouseOperationService.exist(warehouse.getId());
        //校验仓库是否已分配操作人
        Validate.isTrue(!exist, warehouse.getName() + " has been assigned an operator and cannot be deleted");
        //校验仓库库位是否有库存
        List<BinLocationDetail> binLocationDetails = binLocationDetailService.listByWarehouseId(warehouse.getId());
        if (ObjectUtil.isEmpty(binLocationDetails)) {
            return;
        }
        //过滤库存为0的库位详情
        List<BinLocationDetail> list = binLocationDetails
                .stream()
                .filter(binLocationDetail -> ObjectUtil.notEqual(binLocationDetail.getInStockQty(), DataState.DISABLED))
                .toList();
        //校验库位上是否存在产品，若存在则不允许删除
        Validate.isTrue(ObjectUtil.isEmpty(list), "There is also a stock of products in " + warehouse.getName());
    }
}
