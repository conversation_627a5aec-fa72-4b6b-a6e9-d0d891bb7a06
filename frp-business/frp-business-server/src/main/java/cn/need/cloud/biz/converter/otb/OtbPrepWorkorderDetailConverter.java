package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbPrepWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbPrepWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB预提工单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbPrepWorkorderDetailConverter extends AbstractModelConverter<OtbPrepWorkorderDetail, OtbPrepWorkorderDetailVO, OtbPrepWorkorderDetailDTO> {

}
