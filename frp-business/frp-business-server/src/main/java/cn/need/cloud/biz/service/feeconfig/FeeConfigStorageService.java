package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigStorage;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigStorageCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigStorageUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigStorageQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigStorageVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigStoragePageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置storage service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigStorageService extends
        SuperService<FeeConfigStorage>,
        RefNumService<FeeConfigStorage, FeeConfigStorageService>,
        RefNumWithNameService<FeeConfigStorage, FeeConfigStorageService>,
        FeeConfigService<FeeConfigStorage, FeeConfigStorageService> {

    /**
     * 根据参数新增仓库报价费用配置storage
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置storage的相关信息
     * @return 仓库报价费用配置storageID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigStorageCreateParam createParam);


    /**
     * 根据参数更新仓库报价费用配置storage
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置storage的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigStorageUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库报价费用配置storage列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置storage对象的列表(分页)
     */
    List<FeeConfigStoragePageVO> listByQuery(FeeConfigStorageQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置storage列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置storage对象的列表(分页)
     */
    PageData<FeeConfigStoragePageVO> pageByQuery(PageSearch<FeeConfigStorageQuery> search);

    /**
     * 根据ID获取仓库报价费用配置storage
     *
     * @param id 仓库报价费用配置storageID
     * @return 返回仓库报价费用配置storageVO对象
     */
    FeeConfigStorageVO detailById(Long id);


    /**
     * 根据仓库报价费用配置storage唯一编码获取仓库报价费用配置storage
     *
     * @param refNum 仓库报价费用配置storage唯一编码
     * @return 返回仓库报价费用配置storageVO对象
     */
    FeeConfigStorageVO detailByRefNum(String refNum);

    /**
     * 根据报价ID获取仓库报价费用配置storage列表
     *
     * @param quoteId 报价ID
     * @return 返回仓库报价费用配置storageVO对象列表
     */
    List<FeeConfigStorageVO> listDetailByQuoteId(Long quoteId);
}