package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.PalletEmptyProfile;
import cn.need.cloud.biz.model.param.otb.update.pallet.PalletEmptyProfileUpdateParam;
import cn.need.cloud.biz.model.param.warehouse.create.PalletEmptyProfileCreateParam;
import cn.need.cloud.biz.model.query.warehouse.PalletEmptyProfileQuery;
import cn.need.cloud.biz.model.vo.page.PalletEmptyProfilePageVO;
import cn.need.cloud.biz.model.vo.warehouse.PalletEmptyProfileVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 托盘信息 service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
public interface PalletEmptyProfileService extends SuperService<PalletEmptyProfile> {

    /**
     * 根据参数新增托盘信息
     *
     * @param createParam 请求创建参数，包含需要插入的托盘信息的相关信息
     * @return 托盘信息ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(PalletEmptyProfileCreateParam createParam);


    /**
     * 根据参数更新托盘信息
     *
     * @param updateParam 请求创建参数，包含需要更新的托盘信息的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(PalletEmptyProfileUpdateParam updateParam);

    /**
     * 根据查询条件获取托盘信息列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个托盘信息对象的列表(分页)
     */
    List<PalletEmptyProfilePageVO> listByQuery(PalletEmptyProfileQuery query);

    /**
     * 根据查询条件获取托盘信息列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个托盘信息对象的列表(分页)
     */
    PageData<PalletEmptyProfilePageVO> pageByQuery(PageSearch<PalletEmptyProfileQuery> search);

    /**
     * 根据ID获取托盘信息
     *
     * @param id 托盘信息ID
     * @return 返回托盘信息VO对象
     */
    PalletEmptyProfileVO detailById(Long id);

    /**
     * 根据托盘信息唯一编码获取托盘信息
     *
     * @param refNum 托盘信息唯一编码
     * @return 返回托盘信息VO对象
     */
    PalletEmptyProfileVO detailByRefNum(String refNum);

    /**
     * 更新使用状态
     *
     * @param id 托盘信息ID
     */
    void updateInUser(Long id);
}