package cn.need.cloud.biz.service.feeconfig;


import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtb;
import cn.need.cloud.biz.model.param.feeconfig.create.FeeConfigOtbCreateParam;
import cn.need.cloud.biz.model.param.feeconfig.update.FeeConfigOtbUpdateParam;
import cn.need.cloud.biz.model.query.feeconfig.FeeConfigOtbQuery;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbVO;
import cn.need.cloud.biz.model.vo.feeconfig.page.FeeConfigOtbPageVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;

/**
 * <p>
 * 仓库报价费用配置Otb service 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public interface FeeConfigOtbService extends
        SuperService<FeeConfigOtb>,
        RefNumService<FeeConfigOtb, FeeConfigOtbService>,
        RefNumWithNameService<FeeConfigOtb, FeeConfigOtbService>,
        FeeConfigService<FeeConfigOtb, FeeConfigOtbService> {

    /**
     * 根据参数新增仓库报价费用配置Otb
     *
     * @param createParam 请求创建参数，包含需要插入的仓库报价费用配置Otb的相关信息
     * @return 仓库报价费用配置OtbID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(FeeConfigOtbCreateParam createParam);


    /**
     * 根据参数更新仓库报价费用配置Otb
     *
     * @param updateParam 请求创建参数，包含需要更新的仓库报价费用配置Otb的相关信息
     * @return 影响数据的行数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(FeeConfigOtbUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库报价费用配置Otb列表
     *
     * @param query 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置Otb对象的列表(分页)
     */
    List<FeeConfigOtbPageVO> listByQuery(FeeConfigOtbQuery query);

    /**
     * 根据查询条件获取仓库报价费用配置Otb列表(分页)
     *
     * @param search 查询条件对象，包含了用于筛选预备请求的各种条件
     * @return 返回一个仓库报价费用配置Otb对象的列表(分页)
     */
    PageData<FeeConfigOtbPageVO> pageByQuery(PageSearch<FeeConfigOtbQuery> search);

    /**
     * 根据ID获取仓库报价费用配置Otb
     *
     * @param id 仓库报价费用配置OtbID
     * @return 返回仓库报价费用配置OtbVO对象
     */
    FeeConfigOtbVO detailById(Long id);


    /**
     * 根据仓库报价费用配置Otb唯一编码获取仓库报价费用配置Otb
     *
     * @param refNum 仓库报价费用配置Otb唯一编码
     * @return 返回仓库报价费用配置OtbVO对象
     */
    FeeConfigOtbVO detailByRefNum(String refNum);

    /**
     * 根据报价ID获取仓库报价费用配置otb列表
     *
     * @param quoteId 报价ID
     * @return 返回仓库报价费用配置otbVO对象列表
     */
    List<FeeConfigOtbVO> listDetailByQuoteId(Long quoteId);
}