package cn.need.cloud.biz.service.feeconfig;

import cn.need.cloud.biz.client.constant.ErrorMessages;
import cn.need.cloud.biz.model.entity.base.FeeConfigModel;
import cn.need.cloud.biz.model.entity.base.SwitchActiveModel;
import cn.need.cloud.biz.model.vo.base.FeeConfigRefNumVO;
import cn.need.cloud.biz.model.vo.feeconfig.QuoteVO;
import cn.need.cloud.biz.service.base.RefNumService;
import cn.need.cloud.biz.service.base.RefNumWithNameService;
import cn.need.cloud.biz.service.base.SwitchActiveService;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.exception.unchecked.BusinessException;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.core.lang.StringUtil;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.support.util.SpringUtil;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * RefNumService
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-06
 */
public interface FeeConfigService<T extends FeeConfigModel & SwitchActiveModel, S extends SuperService<T>> extends
        RefNumService<T, S>,
        RefNumWithNameService<T, S>,
        SwitchActiveService<T, S> {

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteId 需要查询的主键ID列表
     */
    @SuppressWarnings("unchecked")
    default void removeQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return;
        }
        S service = (S) this;
        // Use string-based field names to avoid lambda method reference parsing issues
        service.update()
                .eq("quote_id", quoteId)
                .set("quote_id", null)
                .update();
    }


    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteId 需要查询的主键ID列表
     * @return FeeConfigRefNumVO列表
     */
    default List<T> feeConfigByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return Collections.emptyList();
        }

        return feeConfigByQuoteIds(Collections.singletonList(quoteId));
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteIds 需要查询的主键ID列表
     * @return FeeConfigRefNumVO列表
     */
    @SuppressWarnings("unchecked")
    default List<T> feeConfigByQuoteIds(List<Long> quoteIds) {
        if (ObjectUtil.isEmpty(quoteIds)) {
            return Collections.emptyList();
        }
        // 确保S是当前接口的实现类，此处转换安全
        S service = (S) this;

        return service.query()
                .in("quote_id", quoteIds)
                .list();
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteIds 需要查询的主键ID列表
     * @return FeeConfigRefNumVO列表
     */
    @SuppressWarnings("unchecked")
    default List<FeeConfigRefNumVO> feeConfigRefNumByQuoteIds(List<Long> quoteIds) {
        if (ObjectUtil.isEmpty(quoteIds)) {
            return Collections.emptyList();
        }
        // 确保S是当前接口的实现类，此处转换安全
        S service = (S) this;

        return service.query()
                .select("quote_id", "ref_num", "id", "name")
                .in("quote_id", quoteIds)
                .list()
                .stream()
                .map(obj -> BeanUtil.copyNew(obj, FeeConfigRefNumVO.class))
                .toList();
    }

    /**
     * 根据给定的ID查询RefNumModel数据，
     *
     * @param quoteId 需要查询的主键ID
     * @return FeeConfigRefNumVO
     */
    default List<FeeConfigRefNumVO> feeConfigRefNumByQuoteId(Long quoteId) {
        if (ObjectUtil.isEmpty(quoteId)) {
            return Collections.emptyList();
        }

        List<Long> quoteIds = Collections.singletonList(quoteId);
        return feeConfigRefNumByQuoteIds(quoteIds);
    }


    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteIdModels 需要查询的主键ID列表
     * @return FeeConfigRefNumVO列表
     */
    default List<FeeConfigRefNumVO> feeConfigRefNumByFeeConfigModels(List<? extends FeeConfigModel> quoteIdModels) {
        if (ObjectUtil.isEmpty(quoteIdModels)) {
            return Collections.emptyList();
        }

        return feeConfigRefNumByQuoteIds(quoteIdModels.stream()
                .map(FeeConfigModel::getQuoteId)
                .toList());
    }

    /**
     * 根据给定的ID列表查询RefNumModel数据，
     *
     * @param quoteIdModel 需要查询的主键ID
     * @return FeeConfigRefNumVO
     */
    default List<FeeConfigRefNumVO> feeConfigRefNumByFeeConfigModel(FeeConfigModel quoteIdModel) {
        if (ObjectUtil.isEmpty(quoteIdModel)) {
            return Collections.emptyList();
        }

        List<Long> quoteIds = Collections.singletonList(quoteIdModel.getQuoteId());
        return feeConfigRefNumByQuoteIds(quoteIds);
    }

    @SuppressWarnings("unchecked")
    default void checkHasQuote(Long id) {
        S service = (S) this;
        T entity = service.getById(id);
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(StringUtil.format(ErrorMessages.ENTITY_NOT_FOUND, entity.getClass().getName(), entity.getId()));
        }
        checkHasQuote(entity);
    }

    default void checkHasQuote(T entity) {
        if (ObjectUtil.isEmpty(entity)) {
            throw new BusinessException(StringUtil.format(ErrorMessages.ENTITY_NOT_FOUND, entity.getClass().getName(), entity.getId()));
        }
        if (ObjectUtil.isNotEmpty(entity.getQuoteId())) {
            QuoteService quoteService = SpringUtil.getBean(QuoteService.class);
            if (ObjectUtil.isEmpty(quoteService)) {
                throw new BusinessException("quoteService is null");
            }
            final QuoteVO quote = quoteService.detailById(entity.getQuoteId());
            throw new BusinessException(StringUtil.format(
                    "{}: has Quote {}, Can not {}",
                    entity.refNumLog(),
                    quote.getShowString(),
                    "do this"
            ));
        }
    }
}
