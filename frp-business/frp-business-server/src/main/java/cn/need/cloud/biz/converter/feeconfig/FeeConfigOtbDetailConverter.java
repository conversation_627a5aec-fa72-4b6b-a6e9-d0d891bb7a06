package cn.need.cloud.biz.converter.feeconfig;


import cn.need.cloud.biz.client.dto.feeconfig.FeeConfigOtbDetailDTO;
import cn.need.cloud.biz.model.entity.feeconfig.FeeConfigOtbDetail;
import cn.need.cloud.biz.model.vo.feeconfig.FeeConfigOtbDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 仓库报价费用配置otb详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-14
 */
public class FeeConfigOtbDetailConverter extends AbstractModelConverter<FeeConfigOtbDetail, FeeConfigOtbDetailVO, FeeConfigOtbDetailDTO> {

}
