package cn.need.cloud.biz.converter.inbound;

import cn.need.cloud.biz.client.dto.inbound.InboundPalletDetailDTO;
import cn.need.cloud.biz.model.entity.inbound.InboundPalletDetail;
import cn.need.cloud.biz.model.vo.inbound.pallet.InboundPalletDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * 入库单打托详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class InboundPalletDetailConverter extends AbstractModelConverter<InboundPalletDetail, InboundPalletDetailVO, InboundPalletDetailDTO> {

}
