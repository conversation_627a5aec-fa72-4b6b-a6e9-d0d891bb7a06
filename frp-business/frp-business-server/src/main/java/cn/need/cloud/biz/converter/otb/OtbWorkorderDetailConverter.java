package cn.need.cloud.biz.converter.otb;

import cn.need.cloud.biz.client.dto.otb.OtbWorkorderDetailDTO;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.model.vo.otb.workorder.OtbWorkorderDetailVO;
import cn.need.framework.common.support.convert.AbstractModelConverter;

/**
 * <p>
 * OTB工单详情 对象转换器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
public class OtbWorkorderDetailConverter extends AbstractModelConverter<OtbWorkorderDetail, OtbWorkorderDetailVO, OtbWorkorderDetailDTO> {

}
