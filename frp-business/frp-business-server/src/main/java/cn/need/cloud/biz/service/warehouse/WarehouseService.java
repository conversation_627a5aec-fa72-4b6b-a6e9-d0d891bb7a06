package cn.need.cloud.biz.service.warehouse;

import cn.need.cloud.biz.model.entity.warehouse.Warehouse;
import cn.need.cloud.biz.model.param.warehouse.create.WarehouseCreateParam;
import cn.need.cloud.biz.model.param.warehouse.update.WarehouseUpdateParam;
import cn.need.cloud.biz.model.query.warehouse.WarehouseQuery;
import cn.need.cloud.biz.model.vo.base.BaseWarehouseVO;
import cn.need.cloud.biz.model.vo.page.WarehousePageVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseDropVO;
import cn.need.cloud.biz.model.vo.warehouse.WarehouseVO;
import cn.need.framework.common.mybatis.base.SuperService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仓库管理服务接口
 * </p>
 * <p>
 * 该接口提供仓库管理的核心业务功能，包括仓库的创建、查询、更新及状态管理等。
 * 仓库是物流系统的核心实体，代表实际的物理仓储空间，负责存储和管理货物。
 * </p>
 * <p>
 * 主要功能包括：
 * 1. 仓库基础信息管理（创建、更新、查询）
 * 2. 仓库状态管理（启用、禁用）
 * 3. 仓库数据查询（分页查询、详情查询、下拉列表等）
 * 4. 仓库信息缓存管理
 * 5. 仓库映射关系查询（编码与名称、ID与基础信息等）
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface WarehouseService extends SuperService<Warehouse> {

    /**
     * 根据参数新增仓库
     * <p>
     * 创建新的仓库记录，包括仓库的基本信息、属性等。
     * 在创建过程中会进行数据验证、编号生成、关联信息校验等操作。
     * 创建成功后会初始化仓库的相关资源，如默认库位等。
     * </p>
     *
     * @param createParam 请求创建参数，包含需要插入的仓库基础信息的相关信息
     * @return 创建成功的仓库ID
     * @throws IllegalArgumentException 如果传入的createParam为空，则抛出此异常
     */
    Long insertByParam(WarehouseCreateParam createParam);


    /**
     * 根据参数更新仓库
     * <p>
     * 更新已有仓库的信息，可以修改仓库的基本属性、状态等。
     * 在更新过程中会进行数据验证、状态校验、权限验证等操作。
     * 更新成功后会刷新仓库的缓存信息。
     * </p>
     *
     * @param updateParam 请求更新参数，包含需要更新的仓库基础信息的相关信息
     * @return 更新影响的记录数
     * @throws IllegalArgumentException 如果传入的updateParam为空，则抛出此异常
     */
    int updateByParam(WarehouseUpdateParam updateParam);

    /**
     * 根据查询条件获取仓库列表
     * <p>
     * 根据指定的查询条件查询仓库列表，支持多种筛选条件组合。
     * 返回符合条件的仓库视图对象列表，不包含分页信息。
     * </p>
     *
     * @param query 查询条件对象，包含了用于筛选仓库的各种条件
     * @return 返回符合条件的仓库视图对象列表
     */
    List<WarehousePageVO> listByQuery(WarehouseQuery query);

    /**
     * 根据查询条件获取仓库列表(分页)
     * <p>
     * 根据指定的查询条件和分页参数查询仓库列表。
     * 返回的结果包含分页信息和仓库视图对象列表。
     * </p>
     *
     * @param search 查询条件和分页参数对象
     * @return 返回带分页信息的仓库视图对象列表
     */
    PageData<WarehousePageVO> pageByQuery(PageSearch<WarehouseQuery> search);

    /**
     * 根据ID获取仓库详情
     * <p>
     * 根据仓库ID查询仓库的详细信息，包括基本属性、状态等。
     * 返回结果为仓库视图对象，包含完整的仓库信息。
     * </p>
     *
     * @param id 仓库ID
     * @return 返回仓库视图对象
     */
    WarehouseVO detailById(Long id);

    /**
     * 根据唯一编码获取仓库详情
     * <p>
     * 根据仓库的唯一引用编号查询仓库的详细信息。
     * 返回结果为仓库视图对象，包含完整的仓库信息。
     * </p>
     *
     * @param refNum 仓库唯一编码
     * @return 返回仓库视图对象
     */
    WarehouseVO detailByRefNum(String refNum);

    /**
     * 切换仓库的启用状态
     * <p>
     * 根据仓库ID切换仓库的启用/禁用状态。
     * 已启用的仓库将被禁用，已禁用的仓库将被启用。
     * 状态变更会影响仓库的可用性和相关业务操作。
     * </p>
     *
     * @param id 仓库ID
     * @return 更新影响的记录数
     */
    Integer switchActive(Long id);

    /**
     * 获取仓库下拉列表
     * <p>
     * 查询仓库的下拉列表数据，用于前端选择框展示。
     * 可根据当前用户权限和指定ID过滤可见的仓库列表。
     * </p>
     *
     * @param id 指定的过滤ID，可以为null
     * @return 仓库下拉列表数据
     */
    List<WarehouseDropVO> dropList(Long id);


    /**
     * 获取仓库ID与编码名称的映射关系
     * <p>
     * 批量查询指定ID列表的仓库编码和名称，并以ID为键构建映射。
     * 此方法用于高效获取多个仓库的编码名称信息，避免多次数据库查询。
     * </p>
     *
     * @param warehouseIds 仓库ID集合
     * @return 以仓库ID为键，编码名称为值的映射
     */
    Map<Long, String> codeRelationByIds(List<Long> warehouseIds);

    /**
     * 根据仓库ID获取基础仓库信息
     * <p>
     * 查询指定仓库ID的基础信息，包括仓库名称、编码、状态等核心属性。
     * 返回的基础信息不包含详细属性和关联关系，用于轻量级的信息展示。
     * </p>
     *
     * @param warehouseId 仓库ID
     * @return 仓库基础信息视图对象
     */
    BaseWarehouseVO baseWarehouseById(Long warehouseId);

    /**
     * 根据仓库ID列表获取基础仓库信息映射
     * <p>
     * 批量查询指定ID列表的仓库基础信息，并以ID为键构建映射。
     * 此方法用于高效获取多个仓库的基础信息，避免多次数据库查询。
     * </p>
     *
     * @param warehouseIds 仓库ID集合
     * @return 以仓库ID为键，仓库基础信息为值的映射
     */
    Map<Long, BaseWarehouseVO> baseWarehouseByIds(List<Long> warehouseIds);

    /**
     * 初始化仓库信息缓存
     * <p>
     * 将仓库信息加载到缓存中，提高系统性能和响应速度。
     * 此方法通常在系统启动时或缓存失效时调用，确保缓存数据的最新性。
     * </p>
     */
    void initWarehouseCache();


    /**
     * 根据仓库编码获取仓库详情
     * <p>
     * 通过仓库的唯一编码查询仓库的详细信息。
     * 编码是仓库的业务标识，可用于外部系统集成和数据交换。
     * </p>
     *
     * @param code 仓库编码
     * @return 返回仓库视图对象
     */
    WarehouseVO getByCode(String code);
}