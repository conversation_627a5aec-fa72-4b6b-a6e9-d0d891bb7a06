package cn.need.cloud.biz.controller.otc;

import cn.need.cloud.biz.converter.otc.OtcWorkorderConverter;
import cn.need.cloud.biz.model.entity.otc.OtcWorkorder;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderBinLocationQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkOrderListQuery;
import cn.need.cloud.biz.model.query.otc.workorder.OtcWorkorderQuery;
import cn.need.cloud.biz.model.vo.base.DropProVO;
import cn.need.cloud.biz.model.vo.otc.workorder.OtcWorkorderVO;
import cn.need.cloud.biz.model.vo.page.OtcWorkorderBinLocationPageVO;
import cn.need.cloud.biz.model.vo.page.OtcWorkorderPageVO;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderBinLocationService;
import cn.need.cloud.biz.service.otc.workorder.OtcWorkorderService;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.common.support.base.AbstractRestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <p>
 * OTC工单 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-28
 */
@RestController
@RequestMapping("/api/biz/otc-workorder")
@Tag(name = "OTC工单")
@Validated
public class OtcWorkorderController extends AbstractRestController<OtcWorkorderService, OtcWorkorder, OtcWorkorderConverter, OtcWorkorderVO> {

    @Resource
    private OtcWorkorderBinLocationService otcWorkorderBinLocationService;

    @Operation(summary = "根据id获取OTC工单详情", description = "根据数据主键id，从数据库中获取其对应的OTC工单详情")
    @GetMapping(value = "/detail/{id}")
    public Result<OtcWorkorderVO> detail(@PathVariable("id") @Parameter(description = "数据主键id", required = true) Long id) {

        log.info("====> /api/biz/otc-workorder/detail, id={}", id);
        // 获取OTC工单详情
        OtcWorkorderVO detailVo = service.detailById(id);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "根据RefNum获取OTC工单详情", description = "根据数据RefNum，从数据库中获取其对应的OTC工单详情")
    @GetMapping(value = "/detail-by-ref-num/{refNum}")
    public Result<OtcWorkorderVO> detail(@PathVariable("refNum") @Parameter(description = "数据主键id", required = true) String refNum) {

        // 获取OTC工单详情
        OtcWorkorderVO detailVo = service.detailByRefNum(refNum);
        // 返回结果
        return success(detailVo);
    }

    @Operation(summary = "获取OTC工单分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC工单列表")
    @PostMapping(value = "/list")
    public Result<PageData<OtcWorkorderPageVO>> list(@RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcWorkOrderListQuery> search) {

        // 获取OTC工单分页
        PageData<OtcWorkorderPageVO> resultPage = service.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "count 数量", description = "根据传入的搜索条件参数，获取过滤构建拣货单数量")
    @PostMapping(value = "/count")
    public Result<Long> count(@RequestBody @Parameter(description = "工单列表搜索条件参数", required = true) OtcWorkOrderListQuery query) {

        // 返回结果
        return success(service.filterBuildPickingSlipCount(query).getFilterSum());
    }

    @Operation(summary = "获取OTC工单仓储位置分页列表", description = "根据传入的搜索条件参数，从数据库中获取分页后的OTC工单仓储位置列表")
    @PostMapping(value = "/bin-location/list")
    public Result<PageData<OtcWorkorderBinLocationPageVO>> binLocationList(
            @Valid @RequestBody @Parameter(description = "搜索条件参数", required = true) PageSearch<OtcWorkOrderBinLocationQuery> search) {

        // 获取OTC工单仓储位置分页
        PageData<OtcWorkorderBinLocationPageVO> resultPage = otcWorkorderBinLocationService.pageByQuery(search);
        // 返回结果
        return success(resultPage);
    }

    @Operation(summary = "下拉列表Pro", description = "下拉列表")
    @PostMapping(value = "/distinct-value")
    public Result<List<DropProVO>> distinctValuePro(@RequestBody @Parameter(description = "查询条件", required = true) OtcWorkorderQuery query) {

        return success(service.distinctValuePro(query));
    }

}
