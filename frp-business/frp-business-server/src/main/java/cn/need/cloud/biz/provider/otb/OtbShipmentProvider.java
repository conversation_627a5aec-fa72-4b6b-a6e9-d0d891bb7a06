package cn.need.cloud.biz.provider.otb;

import cn.need.cloud.biz.cache.bean.WarehouseCache;
import cn.need.cloud.biz.client.api.otb.OtbShipmentClient;
import cn.need.cloud.biz.client.api.path.OtbShipmentPath;
import cn.need.cloud.biz.client.constant.enums.base.PrepWorkOrderTypeEnum;
import cn.need.cloud.biz.client.constant.enums.base.RequestStatusEnum;
import cn.need.cloud.biz.client.constant.enums.inventory.WorkOrderPrepStatusEnum;
import cn.need.cloud.biz.client.constant.enums.otb.OtbPrepWorkOrderEnum;
import cn.need.cloud.biz.client.dto.req.base.BaseProductDTO;
import cn.need.cloud.biz.client.dto.req.base.BaseWarehouseDTO;
import cn.need.cloud.biz.client.dto.req.inbound.BaseDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentDetailQueryReqDTO;
import cn.need.cloud.biz.client.dto.req.otb.OtbShipmentQueryReqDTO;
import cn.need.cloud.biz.client.dto.resp.otb.*;
import cn.need.cloud.biz.client.dto.resp.otc.RequestActualProductRespDTO;
import cn.need.cloud.biz.client.dto.resp.otc.RequestProductRespDTO;
import cn.need.cloud.biz.client.util.PageUtil;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbPrepWorkorderDetail;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorder;
import cn.need.cloud.biz.model.entity.otb.OtbWorkorderDetail;
import cn.need.cloud.biz.model.query.otb.shipment.OtbShipmentQuery;
import cn.need.cloud.biz.model.vo.otb.request.OtbRequestVO;
import cn.need.cloud.biz.model.vo.otb.shipment.OtbShipmentVO;
import cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO;
import cn.need.cloud.biz.provider.base.ProductUtil;
import cn.need.cloud.biz.provider.base.TenantUtil;
import cn.need.cloud.biz.provider.base.WarehouseUtil;
import cn.need.cloud.biz.service.otb.request.OtbRequestService;
import cn.need.cloud.biz.service.otb.shipment.OtbShipmentService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbPrepWorkorderService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderDetailService;
import cn.need.cloud.biz.service.otb.workorder.OtbWorkorderService;
import cn.need.cloud.biz.util.WarehouseCacheUtil;
import cn.need.framework.common.annotation.auth.IgnoreAuth;
import cn.need.framework.common.core.bean.BeanUtil;
import cn.need.framework.common.core.lang.ObjectUtil;
import cn.need.framework.common.mybatis.page.PageData;
import cn.need.framework.common.mybatis.page.PageSearch;
import cn.need.framework.common.support.api.Result;
import cn.need.framework.starter.tenant.core.context.TenantContextHolder;
import cn.need.framework.starter.warehouse.core.context.WarehouseContextHolder;
import org.jetbrains.annotations.NotNull;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Stream;

/**
 * 发货单feign接口实现类
 *
 * <AUTHOR>
 */
@RestController
@Validated
@RequestMapping(OtbShipmentPath.PREFIX)
public class OtbShipmentProvider implements OtbShipmentClient {
    @Resource
    private OtbShipmentService otbShipmentService;
    @Resource
    private OtbRequestService otbRequestService;
    @Resource
    private OtbWorkorderService otbWorkorderService;
    @Resource
    private OtbWorkorderDetailService otbWorkorderDetailService;
    @Resource
    private OtbPrepWorkorderService otbPrepWorkorderService;
    @Resource
    private OtbPrepWorkorderDetailService otbPrepWorkorderDetailService;

    /**
     * 构建实际产品信息
     *
     * @param otbWorkorderDetailList otb工单详情列表
     * @param warehouseCache         仓库缓存
     * @return 实际产品信息列表
     */
    private static @NotNull Stream<RequestProductRespDTO> buildRespDTOStream(List<OtbWorkorderDetail> otbWorkorderDetailList, WarehouseCache warehouseCache) {
        //判空
        if (ObjectUtil.isEmpty(otbWorkorderDetailList)) {
            return Stream.empty();
        }
        //获取产品id
        List<Long> productIdList = otbWorkorderDetailList.stream()
                .map(OtbWorkorderDetail::getProductId)
                .toList();
        //根据产品id映射产品信息
        Map<Long, BaseProductDTO> productMap = ProductUtil.getByProductId(productIdList);
        //返回结果
        return otbWorkorderDetailList.stream()
                .map(item -> {
                    RequestProductRespDTO respDTO = new RequestProductRespDTO();
                    //填充工单详情ID
                    respDTO.setId(item.getId());
                    //填充产品信息
                    respDTO.setBaseProductDTO(productMap.get(item.getProductId()));
                    //填充仓库信息
                    respDTO.setBaseWarehouseDTO(BeanUtil.copyNew(warehouseCache, BaseWarehouseDTO.class));
                    //填充prepWorkOrder类型
                    respDTO.setPrepWorkorderType(PrepWorkOrderTypeEnum.NONE.getStatus());
                    //填充数量
                    respDTO.setQty(item.getQty());
                    return respDTO;
                });
    }

    /**
     * 构建实际产品信息
     *
     * @param otbPrepWorkorderDetailList otb预处理工单详情列表
     * @param warehouseCache             仓库缓存
     * @return 实际产品信息列表
     */
    private static @NotNull Stream<RequestProductRespDTO> buildRespByPrepWorderDetailStream(
            List<OtbPrepWorkorderDetail> otbPrepWorkorderDetailList,
            WarehouseCache warehouseCache,
            List<OtbPrepWorkorder> otbPrepWorkorderList) {
        //根据预处理工单产品id映射预处理工单类
        Map<Long, OtbPrepWorkorder> prepWorkOrderMap = ObjectUtil.toMap(otbPrepWorkorderList, OtbPrepWorkorder::getId);
        //获取预处理工单产品id，预处理工单详情产品id
        List<Long> productIdList = getProductIdList(otbPrepWorkorderDetailList, otbPrepWorkorderList);
        //根据产品id映射产品信息
        Map<Long, BaseProductDTO> productMap = ProductUtil.getByProductId(productIdList);
        //获取预处理工单id，预处理工单详情映射
        Map<Long, List<OtbPrepWorkorderDetail>> detailMap = ObjectUtil.toMapList(otbPrepWorkorderDetailList, OtbPrepWorkorderDetail::getOtbPrepWorkorderId);
        //返回构建结果
        return prepWorkOrderMap.values()
                .stream()
                .map(item -> buildRequestProductRespDTO(warehouseCache, item, productMap, detailMap));
    }

    /**
     * 构建返回对象
     *
     * @param warehouseCache   仓库缓存
     * @param otbPrepWorkorder 预处理工单
     * @param productMap       产品信息
     * @param detailMap        预处理工单详情映射
     * @return 返回对象
     */
    private static @NotNull RequestProductRespDTO buildRequestProductRespDTO(
            WarehouseCache warehouseCache,
            OtbPrepWorkorder otbPrepWorkorder,
            Map<Long, BaseProductDTO> productMap,
            Map<Long, List<OtbPrepWorkorderDetail>> detailMap) {
        //构建返回对象
        RequestProductRespDTO respDTO = new RequestProductRespDTO();
        //填充工单详情ID
        respDTO.setId(otbPrepWorkorder.getOtbWorkorderDetailId());
        //填充产品信息
        Long productId = Objects.requireNonNull(otbPrepWorkorder).getProductId();
        respDTO.setBaseProductDTO(productMap.get(productId));
        //填充仓库信息
        BaseWarehouseDTO baseWarehouseDTO = BeanUtil.copyNew(warehouseCache, BaseWarehouseDTO.class);
        respDTO.setBaseWarehouseDTO(baseWarehouseDTO);
        //统计产品数量
        respDTO.setQty(otbPrepWorkorder.getQty());
        //填充prepWorkOrder类型
        respDTO.setPrepWorkorderType(otbPrepWorkorder.getPrepWorkorderType());
        //构建返回详情
        //遍历预处理工单
        List<OtbPrepWorkorderDetail> list = detailMap.get(otbPrepWorkorder.getId());
        //构建返回产品详情
        List<RequestActualProductRespDTO> actualProductRespList = list.stream()
                .map(item -> {
                    RequestActualProductRespDTO dto = BeanUtil.copyNew(item, RequestActualProductRespDTO.class);
                    //填充预工单详情ID
                    dto.setId(item.getId());
                    dto.setBaseProductDTO(productMap.get(item.getProductId()));
                    dto.setLineNum(String.valueOf(item.getLineNum()));
                    //处理 ParentLineNum
                    if (ObjectUtil.isNotEmpty(item.getParentId())) {
                        //说明有 parent
                        Optional<OtbPrepWorkorderDetail> first = list.stream()
                                .filter(x -> x.getId().equals(item.getParentId()))
                                .findFirst();
                        dto.setLineNumParent(String.valueOf(first.map(OtbPrepWorkorderDetail::getLineNum).orElse(null)));
                    }
                    //返回结果
                    return dto;
                }).toList();
        respDTO.setRequestActualProductRespList(actualProductRespList);
        return respDTO;
    }

    /**
     * 预处理工单id，预处理工单类型映射
     *
     * @param otbPrepWorkorderDetailList 预处理工单详情列表
     * @param otbPrepWorkorderList       预处理工单列表
     * @return 预处理工单id，预处理工单类型映射
     */
    private static @NotNull List<Long> getProductIdList(List<OtbPrepWorkorderDetail> otbPrepWorkorderDetailList, List<OtbPrepWorkorder> otbPrepWorkorderList) {
        //获取prep 工单详情产品id
        Stream<Long> productPrepWorkOrderDetailStream = otbPrepWorkorderDetailList.stream()
                .map(OtbPrepWorkorderDetail::getProductId);
        //prep工单产品id
        Stream<Long> productPrepWorkOrderStream = otbPrepWorkorderList.stream()
                .map(OtbPrepWorkorder::getProductId);
        //合并产品id流获取产品id
        return Stream.concat(productPrepWorkOrderDetailStream, productPrepWorkOrderStream).toList();
    }

    @Override
    @PostMapping(value = OtbShipmentPath.LIST)
    @IgnoreAuth
    public Result<PageData<OtbShipmentPageRespDTO>> list(@RequestBody PageSearch<OtbShipmentQueryReqDTO> search) {
        //获取传参
        OtbShipmentQueryReqDTO query = search.getCondition();
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());


        //构建参数
        PageSearch<OtbShipmentQuery> pageSearch = PageUtil.convert(search, item -> BeanUtil.copyNew(item, OtbShipmentQuery.class));

        //调用列表方法
        PageData<OtbShipmentPageVO> data = otbShipmentService.pageByQuery(pageSearch);

        //转换对象
        PageData<OtbShipmentPageRespDTO> pageData = PageUtil.convert(data, item -> {
            //构建返回对象
            OtbShipmentPageRespDTO respDTO = BeanUtil.copyNew(item, OtbShipmentPageRespDTO.class);
            //填充仓库信息
            respDTO.setBaseWarehouseDTO(BeanUtil.copyNew(item.getBaseWarehouseVO(), BaseWarehouseDTO.class));
            //填充ri信息
            respDTO.setOtbRoutingInstructionDTO(BeanUtil.copyNew(item.getOtbRoutingInstructionVO(), OtbRoutingInstructionRespDTO.class));
            //返回结果
            return respDTO;
        });
        //返回结果
        return Result.ok(pageData);
    }

    @Override
    @PostMapping(value = OtbShipmentPath.DETAIL)
    @IgnoreAuth
    public Result<OtbShipmentRespDTO> detail(@RequestBody OtbShipmentDetailQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //调用详情方法
        OtbShipmentVO otbShipmentVO = otbShipmentService.detailByRefNum(query.getRefNum());
        //转换对象
        OtbShipmentRespDTO respDTO = BeanUtil.copyNew(otbShipmentVO, OtbShipmentRespDTO.class);
        respDTO.setBaseWarehouseDTO(BeanUtil.copyNew(otbShipmentVO.getBaseWarehouseVO(), BaseWarehouseDTO.class));
        respDTO.setRoutingInstructionList(BeanUtil.copyNew(otbShipmentVO.getRoutingInstructionList(), OtbRoutingInstructionRespDTO.class));
        respDTO.setOtbPalletList(BeanUtil.copyNew(otbShipmentVO.getOtbPalletList(), OtbPalletRespDTO.class));
        respDTO.setOtbPackageList(BeanUtil.copyNew(otbShipmentVO.getOtbPalletList(), OtbPackageRespDTO.class));
        //返回结果
        return Result.ok(respDTO);
    }

    @Override
    @PostMapping(OtbShipmentPath.REQUEST_ACTUAL_PRODUCT)
    @IgnoreAuth
    public Result<List<RequestProductRespDTO>> requestActualProduct(@RequestBody BaseDetailQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取请求id
        otbRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));

        //获取请求详情，检查状态
        OtbRequestVO otbRequestVO = otbRequestService.detailById(query.getRequestId());
        //如果没有都处理完，不返回结果
        if (!RequestStatusEnum.getHasShipProcessed().contains(otbRequestVO.getOtbRequestStatus())) {
            return Result.ok(Collections.emptyList());
        }

        //获取otb工单id
        List<OtbWorkorder> otbWorkorderList = otbWorkorderService.listByRequestIds(List.of(query.getRequestId()));
        List<Long> otbWorkOrderIdList = otbWorkorderList.stream()
                .map(OtbWorkorder::getId)
                .toList();
        //获取工单详情
        List<OtbWorkorderDetail> otbWorkorderDetailList = otbWorkorderDetailService.listByWorkorderIds(otbWorkOrderIdList);
        //获取仓库缓存
        Long warehouseId = otbWorkorderDetailList.stream()
                .findFirst()
                .map(OtbWorkorderDetail::getWarehouseId)
                .orElse(null);
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(warehouseId);
        //获取预配工单id
        List<OtbPrepWorkorder> otbPrepWorkorderList = otbPrepWorkorderService.listByWorkOrderIdList(otbWorkOrderIdList);
        //判空
        if (ObjectUtil.isEmpty(otbPrepWorkorderList)) {
            List<RequestProductRespDTO> list = buildRespDTOStream(otbWorkorderDetailList, warehouseCache).toList();
            //返回结果
            return Result.ok(list);
        }
        List<Long> otbPrepWorkOrderIdList = otbPrepWorkorderList.stream()
                .map(OtbPrepWorkorder::getId)
                .toList();
        //获取预配工单详情
        List<OtbPrepWorkorderDetail> otbPrepWorkorderDetailList = otbPrepWorkorderDetailService.listByPrepWorkOrderIds(otbPrepWorkOrderIdList);
        //获取需要做的工单详情id
        List<Long> workOrderDetailIdList = otbPrepWorkorderList.stream()
                .map(OtbPrepWorkorder::getOtbWorkorderDetailId)
                .toList();
        //过滤需要做的工单详情
        List<OtbWorkorderDetail> workOrderDetailList = otbWorkorderDetailList.stream()
                .filter(item -> !workOrderDetailIdList.contains(item.getId()))
                .toList();
        //获取构建流
        Stream<RequestProductRespDTO> respStream = buildRespDTOStream(workOrderDetailList, warehouseCache);
        //获取通过prepWorkOrder构建的流
        Stream<RequestProductRespDTO> stream = buildRespByPrepWorderDetailStream(otbPrepWorkorderDetailList, warehouseCache, otbPrepWorkorderList);
        //合并流
        List<RequestProductRespDTO> list = Stream.concat(respStream, stream).toList();
        //返回结果
        return Result.ok(list);
    }

    @Override
    @PostMapping(OtbShipmentPath.REQUEST_PREP_ACTUAL_PRODUCT)
    @IgnoreAuth
    public Result<List<RequestProductRespDTO>> requestPrepActualProduct(@RequestBody BaseDetailQueryReqDTO query) {
        //填充租户信息
        TenantUtil.fillTenant(query.getLogisticPartner());
        //设置租户id到上下文
        TenantContextHolder.setTenantId(query.getLogisticPartnerId());
        //填充仓库
        WarehouseUtil.fillWarehouseId(List.of(query.getWarehouseReqDTO()));
        //填充仓库id到上下文
        WarehouseContextHolder.setWarehouseId(query.getWarehouseId());

        //获取请求id
        otbRequestService.fillRequestId(query.getTransactionPartner(), List.of(query.getReqDTO()));

        // //获取请求详情，检查状态
        // OtbRequestVO otbRequestVO = otbRequestService.detailById(query.getRequestId());
        // //如果没有都处理完，不返回结果
        // if (!RequestStatusEnum.getHasShipProcessed().contains(otbRequestVO.getOtbRequestStatus())) {
        //     return Result.ok(Collections.emptyList());
        // }

        //获取otb工单id
        List<OtbWorkorder> otbWorkorderList = otbWorkorderService.listByRequestIds(List.of(query.getRequestId()));

        //过滤只有WorkorderPrepStatus为Processed的工单
        List<Long> otbWorkOrderIdList = otbWorkorderList.stream()
                .filter(item -> item.getWorkorderPrepStatus().equals(WorkOrderPrepStatusEnum.PROCESSED.getStatus()))
                .map(OtbWorkorder::getId)
                .toList();

        if (ObjectUtil.isEmpty(otbWorkOrderIdList)) {
            return Result.ok(Collections.emptyList());
        }

        //获取工单详情
        List<OtbWorkorderDetail> otbWorkorderDetailList = otbWorkorderDetailService.listByWorkorderIds(otbWorkOrderIdList);
        //获取仓库缓存
        Long warehouseId = otbWorkorderDetailList.stream()
                .findFirst()
                .map(OtbWorkorderDetail::getWarehouseId)
                .orElse(null);
        WarehouseCache warehouseCache = WarehouseCacheUtil.getById(warehouseId);
        //获取预配工单id
        List<OtbPrepWorkorder> otbPrepWorkorderList = otbPrepWorkorderService.listByWorkOrderIdList(otbWorkOrderIdList);
        // //判空
        // if (ObjectUtil.isEmpty(otbPrepWorkorderList)) {
        //     List<RequestProductRespDTO> list = buildRespDTOStream(otbWorkorderDetailList, warehouseCache).toList();
        //     //返回结果
        //     return Result.ok(list);
        // }

        //过滤只有PrepWorkorderStatus为Processed的预配工单
        List<OtbPrepWorkorder> processedPrepWorkorderList = otbPrepWorkorderList.stream()
                .filter(item -> OtbPrepWorkOrderEnum.PROCESSED.getStatus().equals(item.getOtbPrepWorkorderStatus()))
                .toList();

        //如果没有Processed状态的预配工单，返回空列表
        if (ObjectUtil.isEmpty(processedPrepWorkorderList)) {
            return Result.ok(Collections.emptyList());
        }

        List<Long> otbPrepWorkOrderIdList = processedPrepWorkorderList.stream()
                .map(OtbPrepWorkorder::getId)
                .toList();
        //获取预配工单详情
        List<OtbPrepWorkorderDetail> otbPrepWorkorderDetailList = otbPrepWorkorderDetailService.listByPrepWorkOrderIds(otbPrepWorkOrderIdList);
        //获取需要做的工单详情id
        List<Long> workOrderDetailIdList = processedPrepWorkorderList.stream()
                .map(OtbPrepWorkorder::getOtbWorkorderDetailId)
                .toList();
        //过滤需要做的工单详情
        List<OtbWorkorderDetail> workOrderDetailList = otbWorkorderDetailList.stream()
                .filter(item -> !workOrderDetailIdList.contains(item.getId()))
                .toList();
        // //获取构建流
        // Stream<RequestProductRespDTO> respStream = buildRespDTOStream(workOrderDetailList, warehouseCache);
        // //获取通过prepWorkOrder构建的流
        Stream<RequestProductRespDTO> stream = buildRespByPrepWorderDetailStream(otbPrepWorkorderDetailList, warehouseCache, processedPrepWorkorderList);
        // //合并流
        // List<RequestProductRespDTO> list = Stream.concat(respStream, stream).toList();

        // 只返回通过prepWorkOrder构建的工单详情
        //返回结果
        return Result.ok(stream.toList());
    }
}
