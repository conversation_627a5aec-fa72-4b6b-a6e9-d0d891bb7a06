<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPutawaySlipMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.note,
        ${dbTableAlias}.picking_slip_id,
        ${dbTableAlias}.putaway_slip_status,
        ${dbTableAlias}.putaway_slip_type,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.warehouse_id,
        ${dbTableAlias}.print_status,
        ${dbTableAlias}.workorder_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otb.page.OtbPutawaySlipPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ops"/>
            </include>
        FROM
        otc_putaway_slip ops
        WHERE
            ops.remove_flag = 0
            <include refid="Base_Where_List">
                <property name="dbTableAlias" value="ops"/>
                <property name="qoTableAlias" value="qoops"/>
            </include>
            <include refid="cn.need.cloud.biz.mapper.otb.OtbWorkorderMapper.Exists_InnerTable">
                <property name="InnerQoTableAlias" value="qoops.workorderQuery"/>
                <property name="InnerTableAlias" value="t"/>
                <property name="JoinCondition" value="t.id = ops.workorder_id"/>
            </include>
            <include refid="cn.need.cloud.biz.mapper.otb.OtbPickingSlipMapper.Exists_InnerTable">
                <property name="InnerQoTableAlias" value="qoops.pickingSlipQuery"/>
                <property name="InnerTableAlias" value="t"/>
                <property name="JoinCondition" value="t.id = ops.picking_slip_id"/>
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ops"/>
                <property name="qoTableAlias" value="qoops"/>
            </include>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.remove_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.removeFlag"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.deleted_note"/>
            <property name="qoColumnName" value="${qoTableAlias}.deletedNote"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.note"/>
            <property name="qoColumnName" value="${qoTableAlias}.note"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.picking_slip_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.pickingSlipId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.putaway_slip_status"/>
            <property name="qoColumnName" value="${qoTableAlias}.putawaySlipStatus"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.print_status"/>
            <property name="qoColumnName" value="${qoTableAlias}.printStatus"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.putaway_slip_type"/>
            <property name="qoColumnName" value="${qoTableAlias}.putawaySlipType"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.refNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.warehouse_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.warehouseId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.workorder_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.workorderId"/>
        </include>

    </sql>

</mapper>