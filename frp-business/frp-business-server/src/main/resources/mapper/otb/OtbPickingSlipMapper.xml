<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPickingSlipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPickingSlip">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="note" property="note" />
        <result column="otb_picking_slip_status" property="otbPickingSlipStatus" />
        <result column="print_status" property="printStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="build_from_type" property="buildFromType" />
        <result column="pick_from_type" property="pickFromType" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.assigned_user_id,
        t.pick_to_station,
        t.note,
        t.otb_picking_slip_status,
        t.print_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.otb_workorder_id,
        t.build_from_type,
        t.pick_from_type,
        t.process_type,
        t.picking_slip_product_type,
        t.ship_type,
        t.routing_instruction_file_file_extension,
        t.routing_instruction_file_file_data,
        t.routing_instruction_file_file_type,
        t.routing_instruction_file_paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_picking_slip t
        WHERE
            t.remove_flag = 0
            <if test="wk != null">
                AND EXISTS (
                    SELECT
                    t2.id
                    FROM otb_workorder t2
                    WHERE t2.remove_flag = 0
                    AND t.otb_workorder_id = t2.id
                    <include refid="Workorder_Where_List"/>
                )
            </if>
            <if test="qo != null">
                <include refid="Base_Where_List" />
            </if>
            <include refid="Base_Order_By_List" />
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otb_picking_slip t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 工单查询条件 -->
    <sql id="Workorder_Where_List">
        <if test="wk.refNum != null and wk.refNum != ''">
            AND t2.ref_num = #{wk.refNum}
        </if>
        <if test="wk.refNumList != null and wk.refNumList.size > 0 ">
            AND t2.ref_num in
            <foreach collection="wk.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="wk.requestSnapshotRequestRefNumList != null and wk.requestSnapshotRequestRefNumList.size > 0 ">
            AND t2.request_snapshot_request_ref_num IN
            <foreach collection="wk.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="wk.requestSnapshotShipWindowStartStart != null">
            AND t2.request_snapshot_ship_window_start &gt;= #{wk.requestSnapshotShipWindowStartStart}
        </if>
        <if test="wk.requestSnapshotShipWindowStartEnd != null">
            AND t2.request_snapshot_ship_window_start &lt; #{wk.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="wk.requestSnapshotShipWindowEndStart != null">
            AND t2.request_snapshot_ship_window_end &gt;= #{wk.requestSnapshotShipWindowEndStart}
        </if>
        <if test="wk.requestSnapshotShipWindowEndEnd != null">
            AND t2.request_snapshot_ship_window_end &lt; #{wk.requestSnapshotShipWindowEndEnd}
        </if>
    </sql>
    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickToStation != null and qo.pickToStation != ''">
            AND t.pick_to_station = #{qo.pickToStation}
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPickingSlipStatus != null and qo.otbPickingSlipStatus != ''">
            AND t.otb_picking_slip_status = #{qo.otbPickingSlipStatus}
        </if>
        <if test="qo.otbPickingSlipStatusList != null and qo.otbPickingSlipStatusList.size > 0 ">
            AND t.otb_picking_slip_status in
            <foreach collection="qo.otbPickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--出库工单id-->
        <if test="qo.otbWorkorderIdList != null and qo.otbWorkorderIdList.size > 0 ">
            AND t.otb_workorder_id in
            <foreach collection="qo.otbWorkorderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--拣货产品类型-->
        <if test="qo.pickingSlipProductTypeList != null and qo.pickingSlipProductTypeList.size > 0 ">
            AND t.picking_slip_product_type in
            <foreach collection="qo.pickingSlipProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbWorkorderId != null">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
        <if test="qo.buildFromType != null and qo.buildFromType != ''">
            AND t.build_from_type = #{qo.buildFromType}
        </if>
        <if test="qo.buildFromTypeList != null and qo.buildFromTypeList.size > 0 ">
            AND t.build_from_type in
            <foreach collection="qo.buildFromTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickFromType != null and qo.pickFromType != ''">
            AND t.pick_from_type = #{qo.pickFromType}
        </if>
        <if test="qo.pickFromTypeList != null and qo.pickFromTypeList.size > 0 ">
            AND t.pick_from_type in
            <foreach collection="qo.pickFromTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.shipType != null">
            AND t.ship_type = #{qo.shipType}
        </if>
        <if test="qo.shipTypeList != null and qo.shipTypeList.size > 0 ">
            AND t.ship_type in
            <foreach collection="qo.shipTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>

        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otb_picking_slip t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otb.OtbPickingSlipMapper.Base_Where_List">

            </include>
            )
        </if>
    </sql>

</mapper>