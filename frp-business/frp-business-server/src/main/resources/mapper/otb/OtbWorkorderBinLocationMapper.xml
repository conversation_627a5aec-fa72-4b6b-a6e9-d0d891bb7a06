<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbWorkorderBinLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbWorkorderBinLocation">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="otb_workorder_detail_id" property="otbWorkorderDetailId" />
        <result column="otb_picking_slip_id" property="otbPickingSlipId" />
        <result column="otb_picking_slip_detail_id" property="otbPickingSlipDetailId" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="qty" property="qty" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="product_version_id" property="productVersionId" />
        <result column="bin_location_detail_locked_id" property="binLocationDetailLockedId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otb_workorder_id,
        t.otb_workorder_detail_id,
        t.otb_picking_slip_id,
        t.otb_picking_slip_detail_id,
        t.bin_location_detail_id,
        t.bin_location_id,
        t.qty,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.product_id,
        t.hazmat_version_ref_num,
        t.bin_location_detail_locked_id,
        t.product_version_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otb.page.OtbWorkorderBinLocationPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_workorder_bin_location t
        WHERE
            t.remove_flag = 0
            <if test="qo.otbWorkorderId != null">
                AND t.otb_workorder_id = #{qo.otbWorkorderId}
            </if>
            <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbWorkorderId != null">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
    </sql>

</mapper>