<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbRoutingInstructionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="carrier_contact_name" property="carrierContactName"/>
        <result column="carrier_contact_phone" property="carrierContactPhone"/>
        <result column="carrier_contact_email" property="carrierContactEmail"/>
        <result column="receiving_contact_name" property="receivingContactName"/>
        <result column="receiving_contact_phone" property="receivingContactPhone"/>
        <result column="receiving_contact_email" property="receivingContactEmail"/>
        <result column="shipper_contact_name" property="shipperContactName"/>
        <result column="shipper_contact_phone" property="shipperContactPhone"/>
        <result column="shipper_contact_email" property="shipperContactEmail"/>
        <result column="ship_from_address_name" property="shipFromAddressName"/>
        <result column="ship_from_address_company" property="shipFromAddressCompany"/>
        <result column="ship_from_address_country" property="shipFromAddressCountry"/>
        <result column="ship_from_address_state" property="shipFromAddressState"/>
        <result column="ship_from_address_city" property="shipFromAddressCity"/>
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode"/>
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1"/>
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2"/>
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3"/>
        <result column="ship_from_address_email" property="shipFromAddressEmail"/>
        <result column="ship_from_address_phone" property="shipFromAddressPhone"/>
        <result column="ship_from_address_note" property="shipFromAddressNote"/>
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential"/>
        <result column="ship_to_address_name" property="shipToAddressName"/>
        <result column="ship_to_address_company" property="shipToAddressCompany"/>
        <result column="ship_to_address_country" property="shipToAddressCountry"/>
        <result column="ship_to_address_state" property="shipToAddressState"/>
        <result column="ship_to_address_city" property="shipToAddressCity"/>
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode"/>
        <result column="ship_to_address_addr1" property="shipToAddressAddr1"/>
        <result column="ship_to_address_addr2" property="shipToAddressAddr2"/>
        <result column="ship_to_address_addr3" property="shipToAddressAddr3"/>
        <result column="ship_to_address_email" property="shipToAddressEmail"/>
        <result column="ship_to_address_phone" property="shipToAddressPhone"/>
        <result column="ship_to_address_note" property="shipToAddressNote"/>
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="bol_file_file_data" property="otbBolFileFileData"/>
        <result column="bol_file_file_extension" property="otbBolFileFileExtension"/>
        <result column="bol_file_file_type" property="otbBolFileFileType"/>
        <result column="carrier_code" property="carrierCode"/>
        <result column="carrier_name" property="carrierName"/>
        <result column="carton_qty" property="cartonQty"/>
        <result column="confirmed_shipment_type" property="confirmedShipmentType"/>
        <result column="note" property="note"/>
        <result column="pallet_qty" property="palletQty"/>
        <result column="otb_request_id" property="otbRequestId"/>
        <result column="request_pickup_date" property="requestPickupDate"/>
        <result column="schedule_pickup_date" property="schedulePickupDate"/>
        <result column="otb_shipment_id" property="otbShipmentId"/>
        <result column="otb_shipment_ref_num" property="otbShipmentRefNum"/>
        <result column="stacked_pallet_qty" property="stackedPalletQty"/>
        <result column="ref_num" property="refNum"/>
        <result column="bol_num" property="bolNum"/>
        <result column="ship_carrier" property="shipCarrier"/>
        <result column="ship_method" property="shipMethod"/>
        <result column="ship_api_profile_ref_num" property="shipApiProfileRefNum"/>
        <result column="otb_routing_instruction_status" property="otbRoutingInstructionStatus"/>
        <result column="otb_pallet_file_file_data" property="otbPalletFileFileData"/>
        <result column="otb_pallet_file_file_extension" property="otbPalletFileFileExtension"/>
        <result column="otb_pallet_file_file_type" property="otbPalletFileFileType"/>
        <result column="otb_bol_file_paper_type" property="otbBolFilePaperType"/>
        <result column="otb_pallet_file_paper_type" property="otbPalletFilePaperType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t
        .
        id
        ,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.carrier_contact_name,
        t.carrier_contact_phone,
        t.carrier_contact_email,
        t.receiving_contact_name,
        t.receiving_contact_phone,
        t.receiving_contact_email,
        t.shipper_contact_name,
        t.shipper_contact_phone,
        t.shipper_contact_email,
        t.ship_from_address_name,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_state,
        t.ship_from_address_city,
        t.ship_from_address_zip_code,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_email,
        t.ship_from_address_phone,
        t.ship_from_address_note,
        t.ship_from_address_is_residential,
        t.ship_to_address_name,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_state,
        t.ship_to_address_city,
        t.ship_to_address_zip_code,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_email,
        t.ship_to_address_phone,
        t.ship_to_address_note,
        t.ship_to_address_is_residential,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.bol_file_file_data as otbBolFileFileData,
        t.bol_file_file_extension as otbBolFileFileExtension,
        t.bol_file_file_type as otbBolFileFileType,
        t.carrier_code,
        t.carrier_name,
        t.carton_qty,
        t.confirmed_shipment_type,
        t.note,
        t.pallet_qty,
        t.otb_request_id,
        t.request_pickup_date,
        t.schedule_pickup_date,
        t.otb_shipment_id,
        t.otb_shipment_ref_num,
        t.stacked_pallet_qty,
        t.ref_num,
        t.bol_num,
        t.ship_carrier,
        t.ship_method,
        t.ship_api_profile_ref_num,
        t.otb_routing_instruction_status,
        t.otb_pallet_file_file_data,
        t.otb_pallet_file_file_extension,
        t.otb_pallet_file_file_type,
        t.otb_bol_file_paper_type,
        t.otb_pallet_file_paper_type
    </sql>
    <update id="updateRequestPickupDate"
            parameterType="cn.need.cloud.biz.model.entity.otb.OtbRoutingInstruction">
        UPDATE otb_routing_instruction
        SET request_pickup_date = #{entity.requestPickupDate},
            version             = #{entity.version} + 1
        WHERE id = #{entity.id}
          AND version = #{entity.version}
          AND remove_flag = 0
    </update>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otb.page.OtbRoutingInstructionPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        otb_routing_instruction t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>
    <select id="dropProList" resultType="java.util.Map">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otb_routing_instruction t
        WHERE
        t.remove_flag=0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <select id="getDetailById" resultMap="GetLabelList"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>,
        pa.line_num as paLineNum,
        pa.package_ssccnum as paPackageSsccnum,
        pa.label_type as paLabelType,
        pa.paper_type as paPaperType,
        pa.raw_data_type as paRawDataType,
        pa.label_ref_num as paLabelRefNum,
        pa.label_raw_data as paLabelRawData,
        pa.file_id_raw_data_type as paFileIdRawDataType,
        pl.line_num as plLineNum,
        pl.pallet_ssccnum as plPalletSsccnum,
        pl.label_type as plLabelType,
        pl.paper_type as plPaperType,
        pl.raw_data_type as plRawDataType,
        pl.label_ref_num as plLabelRefNum,
        pl.label_raw_data as plLabelRawData,
        pl.file_id_raw_data_type as plFileIdRawDataType
        from
        otb_routing_instruction t
        left join otb_routing_instruction_package_label pa on pa.otb_routing_instruction_id = t.id
        left join otb_routing_instruction_pallet_label pl on pl.otb_routing_instruction_id = t.id
        where t.id = #{id}
        and t.remove_flag = 0
        and COALESCE(pa.remove_flag, 0) = 0
        and COALESCE(pl.remove_flag, 0) = 0;
    </select>

    <resultMap id="GetLabelList" type="cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionVO" autoMapping="true">
        <id column="id" property="id"/>
        <collection property="routingInstructionPackageLabelList"
                    ofType="cn.need.cloud.biz.model.vo.otb.pkg.OtbRoutingInstructionPackageLabelVO">
            <id property="lineNum" column="paLineNum"/>
            <result property="packageSsccNum" column="paPackageSsccnum"/>
            <result property="labelType" column="paLabelType"/>
            <result property="labelRefNum" column="paLabelRefNum"/>
            <result property="paperType" column="paPaperType"/>
            <result property="rawDataType" column="paRawDataType"/>
            <result property="fileIdRawDataType" column="paFileIdRawDataType"/>
            <result property="labelRawData" column="paLabelRawData"/>
        </collection>
        <collection property="routingInstructionPalletLabelList"
                    ofType="cn.need.cloud.biz.model.vo.otb.ri.OtbRoutingInstructionPalletLabelVO">
            <id property="lineNum" column="plLineNum"/>
            <result property="palletSsccNum" column="plPalletSsccnum"/>
            <result property="labelType" column="plLabelType"/>
            <result property="labelRefNum" column="plLabelRefNum"/>
            <result property="paperType" column="plPaperType"/>
            <result property="rawDataType" column="plRawDataType"/>
            <result property="fileIdRawDataType" column="plFileIdRawDataType"/>
            <result property="labelRawData" column="plLabelRawData"/>
        </collection>

    </resultMap>

    <resultMap id="drop" type="cn.need.cloud.biz.model.vo.base.DropVO">
        <id column="name" property="columnName"/>
        <collection property="dropDetailVO" ofType="cn.need.cloud.biz.model.vo.base.DropDetailVO" autoMapping="true"/>
    </resultMap>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.carrierContactName != null and qo.carrierContactName != ''">
            AND t.carrier_contact_name = #{qo.carrierContactName}
        </if>
        <if test="qo.carrierContactPhone != null and qo.carrierContactPhone != ''">
            AND t.carrier_contact_phone = #{qo.carrierContactPhone}
        </if>
        <if test="qo.carrierContactEmail != null and qo.carrierContactEmail != ''">
            AND t.carrier_contact_email = #{qo.carrierContactEmail}
        </if>
        <if test="qo.receivingContactName != null and qo.receivingContactName != ''">
            AND t.receiving_contact_name = #{qo.receivingContactName}
        </if>
        <if test="qo.receivingContactPhone != null and qo.receivingContactPhone != ''">
            AND t.receiving_contact_phone = #{qo.receivingContactPhone}
        </if>
        <if test="qo.receivingContactEmail != null and qo.receivingContactEmail != ''">
            AND t.receiving_contact_email = #{qo.receivingContactEmail}
        </if>
        <if test="qo.shipperContactName != null and qo.shipperContactName != ''">
            AND t.shipper_contact_name = #{qo.shipperContactName}
        </if>
        <if test="qo.shipperContactPhone != null and qo.shipperContactPhone != ''">
            AND t.shipper_contact_phone = #{qo.shipperContactPhone}
        </if>
        <if test="qo.shipperContactEmail != null and qo.shipperContactEmail != ''">
            AND t.shipper_contact_email = #{qo.shipperContactEmail}
        </if>
        <if test="qo.shipFromAddressName != null and qo.shipFromAddressName != ''">
            AND t.ship_from_address_name = #{qo.shipFromAddressName}
        </if>
        <if test="qo.shipFromAddressCompany != null and qo.shipFromAddressCompany != ''">
            AND t.ship_from_address_company = #{qo.shipFromAddressCompany}
        </if>
        <if test="qo.shipFromAddressCountry != null and qo.shipFromAddressCountry != ''">
            AND t.ship_from_address_country = #{qo.shipFromAddressCountry}
        </if>
        <if test="qo.shipFromAddressState != null and qo.shipFromAddressState != ''">
            AND t.ship_from_address_state = #{qo.shipFromAddressState}
        </if>
        <if test="qo.shipFromAddressCity != null and qo.shipFromAddressCity != ''">
            AND t.ship_from_address_city = #{qo.shipFromAddressCity}
        </if>
        <if test="qo.shipFromAddressZipCode != null and qo.shipFromAddressZipCode != ''">
            AND t.ship_from_address_zip_code = #{qo.shipFromAddressZipCode}
        </if>
        <if test="qo.shipFromAddressAddr1 != null and qo.shipFromAddressAddr1 != ''">
            AND t.ship_from_address_addr1 = #{qo.shipFromAddressAddr1}
        </if>
        <if test="qo.shipFromAddressAddr2 != null and qo.shipFromAddressAddr2 != ''">
            AND t.ship_from_address_addr2 = #{qo.shipFromAddressAddr2}
        </if>
        <if test="qo.shipFromAddressAddr3 != null and qo.shipFromAddressAddr3 != ''">
            AND t.ship_from_address_addr3 = #{qo.shipFromAddressAddr3}
        </if>
        <if test="qo.shipFromAddressEmail != null and qo.shipFromAddressEmail != ''">
            AND t.ship_from_address_email = #{qo.shipFromAddressEmail}
        </if>
        <if test="qo.shipFromAddressPhone != null and qo.shipFromAddressPhone != ''">
            AND t.ship_from_address_phone = #{qo.shipFromAddressPhone}
        </if>
        <if test="qo.shipFromAddressNote != null and qo.shipFromAddressNote != ''">
            AND t.ship_from_address_note = #{qo.shipFromAddressNote}
        </if>
        <if test="qo.shipFromAddressIsResidential != null">
            AND t.ship_from_address_is_residential = #{qo.shipFromAddressIsResidential}
        </if>
        <if test="qo.shipToAddressName != null and qo.shipToAddressName != ''">
            AND t.ship_to_address_name = #{qo.shipToAddressName}
        </if>
        <if test="qo.shipToAddressCompany != null and qo.shipToAddressCompany != ''">
            AND t.ship_to_address_company = #{qo.shipToAddressCompany}
        </if>
        <if test="qo.shipToAddressCountry != null and qo.shipToAddressCountry != ''">
            AND t.ship_to_address_country = #{qo.shipToAddressCountry}
        </if>
        <if test="qo.shipToAddressState != null and qo.shipToAddressState != ''">
            AND t.ship_to_address_state = #{qo.shipToAddressState}
        </if>
        <if test="qo.shipToAddressCity != null and qo.shipToAddressCity != ''">
            AND t.ship_to_address_city = #{qo.shipToAddressCity}
        </if>
        <if test="qo.shipToAddressZipCode != null and qo.shipToAddressZipCode != ''">
            AND t.ship_to_address_zip_code = #{qo.shipToAddressZipCode}
        </if>
        <if test="qo.shipToAddressAddr1 != null and qo.shipToAddressAddr1 != ''">
            AND t.ship_to_address_addr1 = #{qo.shipToAddressAddr1}
        </if>
        <if test="qo.shipToAddressAddr2 != null and qo.shipToAddressAddr2 != ''">
            AND t.ship_to_address_addr2 = #{qo.shipToAddressAddr2}
        </if>
        <if test="qo.shipToAddressAddr3 != null and qo.shipToAddressAddr3 != ''">
            AND t.ship_to_address_addr3 = #{qo.shipToAddressAddr3}
        </if>
        <if test="qo.shipToAddressEmail != null and qo.shipToAddressEmail != ''">
            AND t.ship_to_address_email = #{qo.shipToAddressEmail}
        </if>
        <if test="qo.shipToAddressPhone != null and qo.shipToAddressPhone != ''">
            AND t.ship_to_address_phone = #{qo.shipToAddressPhone}
        </if>
        <if test="qo.shipToAddressNote != null and qo.shipToAddressNote != ''">
            AND t.ship_to_address_note = #{qo.shipToAddressNote}
        </if>
        <if test="qo.shipToAddressIsResidential != null">
            AND t.ship_to_address_is_residential = #{qo.shipToAddressIsResidential}
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.bolFileFileData != null and qo.bolFileFileData != ''">
            AND t.bol_file_file_data = #{qo.bolFileFileData}
        </if>
        <if test="qo.bolFileFileExtension != null and qo.bolFileFileExtension != ''">
            AND t.bol_file_file_extension = #{qo.bolFileFileExtension}
        </if>
        <if test="qo.bolFileFileType != null and qo.bolFileFileType != ''">
            AND t.bol_file_file_type = #{qo.bolFileFileType}
        </if>
        <if test="qo.bolFileFileTypeList != null and qo.bolFileFileTypeList.size > 0 ">
            AND t.bol_file_file_type in
            <foreach collection="qo.bolFileFileTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.carrierCode != null and qo.carrierCode != ''">
            AND t.carrier_code = #{qo.carrierCode}
        </if>
        <if test="qo.carrierName != null and qo.carrierName != ''">
            AND t.carrier_name = #{qo.carrierName}
        </if>
        <if test="qo.cartonQty != null">
            AND t.carton_qty = #{qo.cartonQty}
        </if>
        <if test="qo.confirmedShipmentType != null and qo.confirmedShipmentType != ''">
            AND t.confirmed_shipment_type = #{qo.confirmedShipmentType}
        </if>
        <if test="qo.confirmedShipmentTypeList != null and qo.confirmedShipmentTypeList.size > 0 ">
            AND t.confirmed_shipment_type in
            <foreach collection="qo.confirmedShipmentTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.palletQty != null">
            AND t.pallet_qty = #{qo.palletQty}
        </if>
        <if test="qo.otbRequestId != null">
            AND t.otb_request_id = #{qo.otbRequestId}
        </if>
        <if test="qo.requestPickupDateStart != null">
            AND t.request_pickup_date &gt;= #{qo.requestPickupDateStart}
        </if>
        <if test="qo.requestPickupDateEnd != null">
            AND t.request_pickup_date &lt; #{qo.requestPickupDateEnd}
        </if>
        <if test="qo.schedulePickupDateStart != null">
            AND t.schedule_pickup_date &gt;= #{qo.schedulePickupDateStart}
        </if>
        <if test="qo.schedulePickupDateEnd != null">
            AND t.schedule_pickup_date &lt; #{qo.schedulePickupDateEnd}
        </if>
        <if test="qo.otbShipmentId != null">
            AND t.otb_shipment_id = #{qo.otbShipmentId}
        </if>
        <if test="qo.otbShipmentRefNum != null and qo.otbShipmentRefNum != ''">
            AND t.otb_shipment_ref_num = #{qo.otbShipmentRefNum}
        </if>
        <if test="qo.otbShipmentRefNumList != null and qo.otbShipmentRefNumList.size > 0 ">
            AND t.otb_shipment_ref_num in
            <foreach collection="qo.otbShipmentRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.stackedPalletQty != null">
            AND t.stacked_pallet_qty = #{qo.stackedPalletQty}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.bolNum != null and qo.bolNum != ''">
            AND t.bol_num = #{qo.bolNum}
        </if>
        <if test="qo.bolNumList != null and qo.bolNumList.size > 0 ">
            AND t.bol_num in
            <foreach collection="qo.bolNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipMethod != null and qo.shipMethod != ''">
            AND t.ship_method = #{qo.shipMethod}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipApiProfileRefNum != null and qo.shipApiProfileRefNum != ''">
            AND t.ship_api_profile_ref_num = #{qo.shipApiProfileRefNum}
        </if>
        <if test="qo.shipApiProfileRefNumList != null and qo.shipApiProfileRefNumList.size > 0 ">
            AND t.ship_api_profile_ref_num in
            <foreach collection="qo.shipApiProfileRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbRoutingInstructionStatus != null and qo.otbRoutingInstructionStatus != ''">
            AND t.otb_routing_instruction_status = #{qo.otbRoutingInstructionStatus}
        </if>
        <if test="qo.otbRoutingInstructionStatusList != null and qo.otbRoutingInstructionStatusList.size > 0 ">
            AND t.otb_routing_instruction_status in
            <foreach collection="qo.otbRoutingInstructionStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPalletFileFileData != null and qo.otbPalletFileFileData != ''">
            AND t.otb_pallet_file_file_data = #{qo.otbPalletFileFileData}
        </if>
        <if test="qo.otbPalletFileFileExtension != null and qo.otbPalletFileFileExtension != ''">
            AND t.otb_pallet_file_file_extension = #{qo.otbPalletFileFileExtension}
        </if>
        <if test="qo.otbPalletFileFileType != null and qo.otbPalletFileFileType != ''">
            AND t.otb_pallet_file_file_type = #{qo.otbPalletFileFileType}
        </if>
        <if test="qo.otbPalletFileFileTypeList != null and qo.otbPalletFileFileTypeList.size > 0 ">
            AND t.otb_pallet_file_file_type in
            <foreach collection="qo.otbPalletFileFileTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbBolFilePaperType != null and qo.otbBolFilePaperType != ''">
            AND t.otb_bol_file_paper_type = #{qo.otbBolFilePaperType}
        </if>
        <if test="qo.otbBolFilePaperTypeList != null and qo.otbBolFilePaperTypeList.size > 0 ">
            AND t.otb_bol_file_paper_type in
            <foreach collection="qo.otbBolFilePaperTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPalletFilePaperType != null and qo.otbPalletFilePaperType != ''">
            AND t.otb_pallet_file_paper_type = #{qo.otbPalletFilePaperType}
        </if>
        <if test="qo.otbPalletFilePaperTypeList != null and qo.otbPalletFilePaperTypeList.size > 0 ">
            AND t.otb_pallet_file_paper_type in
            <foreach collection="qo.otbPalletFilePaperTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>