<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbShipmentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="otb_request_id" property="otbRequestId" />
        <result column="otb_workorder_id" property="otbWorkorderId" />
        <result column="otb_shipment_status" property="otbShipmentStatus" />
        <result column="otb_shipment_type" property="otbShipmentType" />
        <result column="otb_picking_slip_id" property="otbPickingSlipId" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="bol_num" property="bolNum" />
        <result column="print_status" property="printStatus" />
        <result column="signed_bol_file_file_data" property="otbSignedBolFileFileData" />
        <result column="signed_bol_file_file_extension" property="otbSignedBolFileFileExtension" />
        <result column="signed_bol_file_file_type" property="otbSignedBolFileFileType" />
        <result column="package_count" property="packageCount" />
        <result column="pallet_count" property="palletCount" />
        <result column="total_weight_unit" property="totalWeightUnit" />
        <result column="total_weight_value" property="totalWeightValue" />
        <result column="volume_unit" property="volumeUnit" />
        <result column="volume_value" property="volumeValue" />
        <result column="signed_bol_file_paper_type" property="otbSignedBolFilePaperType" />
        <result column="pallet_file_status" property="palletFileStatus" />
        <result column="process_type" property="processType" />
        <result column="routing_instruction_schedule_pickup_date" property="routingInstructionSchedulePickupDate"/>
        <result column="routing_instruction_request_pickup_date" property="routingInstructionRequestPickupDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otb_request_id,
        t.otb_workorder_id,
        t.otb_shipment_status,
        t.otb_shipment_type,
        t.otb_picking_slip_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.bol_num,
        t.print_status,
        t.signed_bol_file_file_data as otbSignedBolFileFileData,
        t.signed_bol_file_file_extension as otbSignedBolFileFileExtension,
        t.signed_bol_file_file_type as otbSignedBolFileFileType,
        t.package_count,
        t.pallet_count,
        t.package_count,
        t.pallet_count,
        t.total_weight_unit,
        t.total_weight_value,
        t.volume_unit,
        t.volume_value,
        t.signed_bol_file_paper_type,
        t.pallet_file_status,
        t.process_type,
        t.routing_instruction_schedule_pickup_date,
        t.routing_instruction_request_pickup_date
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbShipmentPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otb_shipment t
        LEFT JOIN
            otb_routing_instruction ori ON  ori.otb_shipment_id=t.id
        WHERE
            t.remove_flag = 0
            <if test="qo.carrierCodeList!= null and qo.carrierCodeList.size > 0 ">
                and ori.carrier_code in
                <foreach collection="qo.carrierCodeList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.carrierNameList!= null and qo.carrierNameList.size > 0 ">
                and ori.carrier_name in
                <foreach collection="qo.carrierNameList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.schedulePickupDateStart!= null ">
                and ori.schedule_pickup_date &gt; #{qo.schedulePickupDateStart}
            </if>
            <if test="qo.schedulePickupDateEnd!= null">
                and ori.schedule_pickup_date &lt; #{qo.schedulePickupDateEnd}
            </if>
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="dropProList" resultType="hashMap" >
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otb_shipment t
        WHERE
        t.remove_flag=0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">
                
            </when>
            <otherwise>
                ORDER BY t.routing_instruction_schedule_pickup_date DESC,t.update_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipmentIdList != null and qo.shipmentIdList.size > 0 ">
            AND t.id in
            <foreach collection="qo.shipmentIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--请求单id-->
        <if test="qo.otbRequestId != null">
            AND t.otb_request_id = #{qo.otbRequestId}
        </if>
        <if test="qo.otbRequestIdList != null and qo.otbRequestIdList.size > 0 ">
            AND t.otb_request_id in <foreach collection="qo.otbRequestIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--工单单id-->
        <if test="qo.otbWorkorderId != null">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
        <if test="qo.otbWorkorderIdList != null and qo.otbWorkorderIdList.size > 0 ">
            AND t.otb_workorder_id in <foreach collection="qo.otbWorkorderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbShipmentStatus != null and qo.otbShipmentStatus != ''">
            AND t.otb_shipment_status = #{qo.otbShipmentStatus}
        </if>
        <if test="qo.otbShipmentStatusList != null and qo.otbShipmentStatusList.size > 0 ">
            AND t.otb_shipment_status in
            <foreach collection="qo.otbShipmentStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbShipmentType != null and qo.otbShipmentType != ''">
            AND t.otb_shipment_type = #{qo.otbShipmentType}
        </if>
        <if test="qo.otbShipmentTypeList != null and qo.otbShipmentTypeList.size > 0 ">
            AND t.otb_shipment_type in
            <foreach collection="qo.otbShipmentTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPickingSlipId != null">
            AND t.otb_picking_slip_id = #{qo.otbPickingSlipId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.bolNum != null and qo.bolNum != ''">
            AND t.bol_num = #{qo.bolNum}
        </if>
        <if test="qo.bolNumList != null and qo.bolNumList.size > 0 ">
            AND t.bol_num in
            <foreach collection="qo.bolNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.signedBolFileFileData != null and qo.signedBolFileFileData != ''">
            AND t.signed_bol_file_file_data = #{qo.signedBolFileFileData}
        </if>
        <if test="qo.signedBolFileFileExtension != null and qo.signedBolFileFileExtension != ''">
            AND t.signed_bol_file_file_extension = #{qo.signedBolFileFileExtension}
        </if>
        <if test="qo.signedBolFileFileType != null and qo.signedBolFileFileType != ''">
            AND t.signed_bol_file_file_type = #{qo.signedBolFileFileType}
        </if>
        <if test="qo.signedBolFileFileTypeList != null and qo.signedBolFileFileTypeList.size > 0 ">
            AND t.signed_bol_file_file_type in
            <foreach collection="qo.signedBolFileFileTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.packageCount != null">
            AND t.package_count = #{qo.packageCount}
        </if>
        <if test="qo.palletCount != null">
            AND t.pallet_count = #{qo.palletCount}
        </if>
        <if test="qo.totalWeightUnit != null and qo.totalWeightUnit != ''">
            AND t.total_weight_unit = #{qo.totalWeightUnit}
        </if>
        <if test="qo.totalWeightValue != null">
            AND t.total_weight_value = #{qo.totalWeightValue}
        </if>
        <if test="qo.volumeUnit != null and qo.volumeUnit != ''">
            AND t.volume_unit = #{qo.volumeUnit}
        </if>
        <if test="qo.volumeValue != null">
            AND t.volume_value = #{qo.volumeValue}
        </if>
        <if test="qo.signedBolFilePaperType != null and qo.signedBolFilePaperType != ''">
            AND t.signed_bol_file_paper_type = #{qo.signedBolFilePaperType}
        </if>
        <if test="qo.signedBolFilePaperTypeList != null and qo.signedBolFilePaperTypeList.size > 0 ">
            AND t.signed_bol_file_paper_type in
            <foreach collection="qo.signedBolFilePaperTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.palletFileStatus != null and qo.palletFileStatus != ''">
            AND t.pallet_file_status = #{qo.palletFileStatus}
        </if>
        <if test="qo.palletFileStatusList != null and qo.palletFileStatusList.size > 0 ">
            AND t.pallet_file_status in
            <foreach collection="qo.palletFileStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
    </sql>

</mapper>