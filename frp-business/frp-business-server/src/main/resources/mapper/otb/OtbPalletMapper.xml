<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otb.OtbPalletMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbPallet" autoMapping="true"/>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.sscc_num,
        t.pallet_size_length,
        t.pallet_size_width,
        t.pallet_size_height,
        t.pallet_size_weight,
        t.pallet_size_weight_unit,
        t.pallet_size_dimension_unit,
        t.otb_request_id,
        t.otb_workorder_id,
        t.otb_shipment_id,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.short_ssccnum,
        t.otb_pallet_status,
        t.order_num,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_city,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_email,
        t.ship_from_address_is_residential,
        t.ship_from_address_name,
        t.ship_from_address_note,
        t.ship_from_address_phone,
        t.ship_from_address_state,
        t.ship_from_address_zip_code,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_city,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_email,
        t.ship_to_address_is_residential,
        t.ship_to_address_name,
        t.ship_to_address_note,
        t.ship_to_address_phone,
        t.ship_to_address_state,
        t.ship_to_address_zip_code,
        t.pallet_empty_profile_id,
        t.process_type,
        t.pallet_type as otbPalletType,
        t.otb_picking_slip_id
    </sql>
    <update id="updateBatchWithNull">
        <foreach collection="otbPalletList" item="item" separator=";">
            UPDATE
                otb_pallet
            set
                otb_pallet_status = #{item.otbPalletStatus},
                otb_shipment_id = #{item.otbShipmentId},
                update_by= #{item.updateBy},
                update_time= #{item.updateTime}
            where
                id = #{item.id}
        </foreach>
    </update>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbPalletPageVO">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM
            otb_pallet t
        left join
            otb_package p on p.otb_pallet_id = t.id
        WHERE
            t.remove_flag = 0
            <if test="qo.packageType != null and qo.packageType != ''">
                and p.otb_package_type = #{qo.packageType}
            </if>
            <if test="qo.workOrderIdList!=null and qo.workOrderIdList.size() > 0">
                and t.otb_workorder_id in
                <foreach collection="qo.workOrderIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.requestIdList!=null and qo.requestIdList.size() > 0">
                and t.otb_request_id in
                <foreach collection="qo.requestIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="qo.otbPalletIdList!=null and qo.otbPalletIdList.size() > 0">
                and t.id in
                <foreach collection="qo.otbPalletIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <!-- 拣货单id -->
        <if test="qo.otbPickingSlipId!= null">
            AND t.otb_picking_slip_id = #{qo.otbPickingSlipId}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.ssccnumList != null and qo.ssccnumList.size > 0 ">
            AND t.sscc_num in
            <foreach collection="qo.ssccnumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shortSsccnumList != null and qo.shortSsccnumList.size > 0 ">
            AND t.short_ssccnum in
            <foreach collection="qo.shortSsccnumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otbPalletStatus != null and qo.otbPalletStatus != ''">
            AND t.otb_pallet_status = #{qo.otbPalletStatus}
        </if>
        <if test="qo.otbPalletStatusList != null and qo.otbPalletStatusList.size > 0 ">
            AND t.otb_pallet_status in
            <foreach collection="qo.otbPalletStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.palletEmptyProfileId != null">
            AND t.pallet_empty_profile_id = #{qo.palletEmptyProfileId}
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.otbWorkorderId != null and qo.otbWorkorderId != ''">
            AND t.otb_workorder_id = #{qo.otbWorkorderId}
        </if>
    </sql>



</mapper>