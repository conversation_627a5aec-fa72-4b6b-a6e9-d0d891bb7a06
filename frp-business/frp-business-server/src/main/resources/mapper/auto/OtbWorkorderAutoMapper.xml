<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.auto.OtbWorkorderAutoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otb.OtbWorkorder">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="note" property="note" />
        <result column="otb_picking_slip_id" property="otbPickingSlipId" />
        <result column="otb_request_id" property="otbRequestId" />
        <result column="otb_request_shipment_status" property="otbRequestShipmentStatus" />
        <result column="otb_workorder_status" property="otbWorkorderStatus" />
        <result column="otb_workorder_type" property="otbWorkorderType" />
        <result column="process_type" property="processType" />
        <result column="ref_num" property="refNum" />
        <result column="request_snapshot_channel" property="requestSnapshotChannel" />
        <result column="request_snapshot_insurance_amount_amount" property="requestSnapshotInsuranceAmountAmount" />
        <result column="request_snapshot_insurance_amount_currency" property="requestSnapshotInsuranceAmountCurrency" />
        <result column="request_snapshot_note" property="requestSnapshotNote" />
        <result column="request_snapshot_order_num" property="requestSnapshotOrderNum" />
        <result column="request_snapshot_ref_num" property="requestSnapshotRefNum" />
        <result column="request_snapshot_request_ref_num" property="requestSnapshotRequestRefNum" />
        <result column="request_snapshot_ship_window_end" property="requestSnapshotShipWindowEnd" />
        <result column="request_snapshot_ship_window_start" property="requestSnapshotShipWindowStart" />
        <result column="request_snapshot_signature_type" property="requestSnapshotSignatureType" />
        <result column="request_snapshot_transaction_partner_id" property="requestSnapshotTransactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="workorder_prep_status" property="workorderPrepStatus" />
        <result column="workorder_product_type" property="workorderProductType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.detail_product_type,
        ${dbTableAlias}.note,
        ${dbTableAlias}.otb_picking_slip_id,
        ${dbTableAlias}.otb_request_id,
        ${dbTableAlias}.otb_request_shipment_status,
        ${dbTableAlias}.otb_workorder_status,
        ${dbTableAlias}.otb_workorder_type,
        ${dbTableAlias}.process_type,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.request_snapshot_channel,
        ${dbTableAlias}.request_snapshot_insurance_amount_amount,
        ${dbTableAlias}.request_snapshot_insurance_amount_currency,
        ${dbTableAlias}.request_snapshot_note,
        ${dbTableAlias}.request_snapshot_order_num,
        ${dbTableAlias}.request_snapshot_ref_num,
        ${dbTableAlias}.request_snapshot_request_ref_num,
        ${dbTableAlias}.request_snapshot_ship_window_end,
        ${dbTableAlias}.request_snapshot_ship_window_start,
        ${dbTableAlias}.request_snapshot_signature_type,
        ${dbTableAlias}.request_snapshot_transaction_partner_id,
        ${dbTableAlias}.warehouse_id,
        ${dbTableAlias}.workorder_prep_status,
        ${dbTableAlias}.workorder_product_type
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ow"/>
            </include>
        FROM
        otb_workorder ow
        WHERE
            ow.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ow"/>
                    <property name="qoTableAlias" value="qoow"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ow"/>
                <property name="qoTableAlias" value="qoow"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoow.timeZone != null and qoow.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoow.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            otb_workorder ow
        WHERE
            ow.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ow"/>
                    <property name="qoTableAlias" value="qoow"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoow.timeZone != null and qoow.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoow.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.detailProductType != null and ${qoTableAlias}.detailProductType != ''">
            AND ${dbTableAlias}.detail_product_type = #{${qoTableAlias}.detailProductType}
        </if>
    <if test="${qoTableAlias}.detailProductTypeList != null and ${qoTableAlias}.detailProductTypeList.size > 0 ">
        AND ${dbTableAlias}.detail_product_type in
        <foreach collection="${qoTableAlias}.detailProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.otbPickingSlipId != null">
            AND ${dbTableAlias}.otb_picking_slip_id = #{${qoTableAlias}.otbPickingSlipId}
        </if>
    <if test="${qoTableAlias}.otbPickingSlipIdList != null and ${qoTableAlias}.otbPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.otb_picking_slip_id in
        <foreach collection="${qoTableAlias}.otbPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbRequestId != null">
            AND ${dbTableAlias}.otb_request_id = #{${qoTableAlias}.otbRequestId}
        </if>
    <if test="${qoTableAlias}.otbRequestIdList != null and ${qoTableAlias}.otbRequestIdList.size > 0 ">
        AND ${dbTableAlias}.otb_request_id in
        <foreach collection="${qoTableAlias}.otbRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbRequestShipmentStatus != null and ${qoTableAlias}.otbRequestShipmentStatus != ''">
            AND ${dbTableAlias}.otb_request_shipment_status = #{${qoTableAlias}.otbRequestShipmentStatus}
        </if>
    <if test="${qoTableAlias}.otbRequestShipmentStatusList != null and ${qoTableAlias}.otbRequestShipmentStatusList.size > 0 ">
        AND ${dbTableAlias}.otb_request_shipment_status in
        <foreach collection="${qoTableAlias}.otbRequestShipmentStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbWorkorderStatus != null and ${qoTableAlias}.otbWorkorderStatus != ''">
            AND ${dbTableAlias}.otb_workorder_status = #{${qoTableAlias}.otbWorkorderStatus}
        </if>
    <if test="${qoTableAlias}.otbWorkorderStatusList != null and ${qoTableAlias}.otbWorkorderStatusList.size > 0 ">
        AND ${dbTableAlias}.otb_workorder_status in
        <foreach collection="${qoTableAlias}.otbWorkorderStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.otbWorkorderType != null and ${qoTableAlias}.otbWorkorderType != ''">
            AND ${dbTableAlias}.otb_workorder_type = #{${qoTableAlias}.otbWorkorderType}
        </if>
    <if test="${qoTableAlias}.otbWorkorderTypeList != null and ${qoTableAlias}.otbWorkorderTypeList.size > 0 ">
        AND ${dbTableAlias}.otb_workorder_type in
        <foreach collection="${qoTableAlias}.otbWorkorderTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.processType != null and ${qoTableAlias}.processType != ''">
            AND ${dbTableAlias}.process_type = #{${qoTableAlias}.processType}
        </if>
    <if test="${qoTableAlias}.processTypeList != null and ${qoTableAlias}.processTypeList.size > 0 ">
        AND ${dbTableAlias}.process_type in
        <foreach collection="${qoTableAlias}.processTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotChannel != null and ${qoTableAlias}.requestSnapshotChannel != ''">
            AND ${dbTableAlias}.request_snapshot_channel = #{${qoTableAlias}.requestSnapshotChannel}
        </if>
    <if test="${qoTableAlias}.requestSnapshotChannelList != null and ${qoTableAlias}.requestSnapshotChannelList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_channel in
        <foreach collection="${qoTableAlias}.requestSnapshotChannelList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotInsuranceAmountAmount != null">
            AND ${dbTableAlias}.request_snapshot_insurance_amount_amount = #{${qoTableAlias}.requestSnapshotInsuranceAmountAmount}
        </if>
        <if test="${qoTableAlias}.requestSnapshotInsuranceAmountCurrency != null and ${qoTableAlias}.requestSnapshotInsuranceAmountCurrency != ''">
            AND ${dbTableAlias}.request_snapshot_insurance_amount_currency = #{${qoTableAlias}.requestSnapshotInsuranceAmountCurrency}
        </if>
        <if test="${qoTableAlias}.requestSnapshotNote != null and ${qoTableAlias}.requestSnapshotNote != ''">
            AND ${dbTableAlias}.request_snapshot_note = #{${qoTableAlias}.requestSnapshotNote}
        </if>
        <if test="${qoTableAlias}.requestSnapshotOrderNum != null and ${qoTableAlias}.requestSnapshotOrderNum != ''">
            AND ${dbTableAlias}.request_snapshot_order_num = #{${qoTableAlias}.requestSnapshotOrderNum}
        </if>
        <if test="${qoTableAlias}.requestSnapshotRefNum != null and ${qoTableAlias}.requestSnapshotRefNum != ''">
            AND ${dbTableAlias}.request_snapshot_ref_num = #{${qoTableAlias}.requestSnapshotRefNum}
        </if>
    <if test="${qoTableAlias}.requestSnapshotRefNumList != null and ${qoTableAlias}.requestSnapshotRefNumList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_ref_num in
        <foreach collection="${qoTableAlias}.requestSnapshotRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotRequestRefNum != null and ${qoTableAlias}.requestSnapshotRequestRefNum != ''">
            AND ${dbTableAlias}.request_snapshot_request_ref_num = #{${qoTableAlias}.requestSnapshotRequestRefNum}
        </if>
    <if test="${qoTableAlias}.requestSnapshotRequestRefNumList != null and ${qoTableAlias}.requestSnapshotRequestRefNumList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_request_ref_num in
        <foreach collection="${qoTableAlias}.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowEndStart != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_end  &gt;= #{${qoTableAlias}.requestSnapshotShipWindowEndStart}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowEndEnd != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_end  &lt; #{${qoTableAlias}.requestSnapshotShipWindowEndEnd}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowStartStart != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_start  &gt;= #{${qoTableAlias}.requestSnapshotShipWindowStartStart}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowStartEnd != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_start  &lt; #{${qoTableAlias}.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="${qoTableAlias}.requestSnapshotSignatureType != null and ${qoTableAlias}.requestSnapshotSignatureType != ''">
            AND ${dbTableAlias}.request_snapshot_signature_type = #{${qoTableAlias}.requestSnapshotSignatureType}
        </if>
    <if test="${qoTableAlias}.requestSnapshotSignatureTypeList != null and ${qoTableAlias}.requestSnapshotSignatureTypeList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_signature_type in
        <foreach collection="${qoTableAlias}.requestSnapshotSignatureTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotTransactionPartnerId != null">
            AND ${dbTableAlias}.request_snapshot_transaction_partner_id = #{${qoTableAlias}.requestSnapshotTransactionPartnerId}
        </if>
    <if test="${qoTableAlias}.requestSnapshotTransactionPartnerIdList != null and ${qoTableAlias}.requestSnapshotTransactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_transaction_partner_id in
        <foreach collection="${qoTableAlias}.requestSnapshotTransactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.workorderPrepStatus != null and ${qoTableAlias}.workorderPrepStatus != ''">
            AND ${dbTableAlias}.workorder_prep_status = #{${qoTableAlias}.workorderPrepStatus}
        </if>
    <if test="${qoTableAlias}.workorderPrepStatusList != null and ${qoTableAlias}.workorderPrepStatusList.size > 0 ">
        AND ${dbTableAlias}.workorder_prep_status in
        <foreach collection="${qoTableAlias}.workorderPrepStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.workorderProductType != null and ${qoTableAlias}.workorderProductType != ''">
            AND ${dbTableAlias}.workorder_product_type = #{${qoTableAlias}.workorderProductType}
        </if>
    <if test="${qoTableAlias}.workorderProductTypeList != null and ${qoTableAlias}.workorderProductTypeList.size > 0 ">
        AND ${dbTableAlias}.workorder_product_type in
        <foreach collection="${qoTableAlias}.workorderProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OtbWorkorderPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ow"/>
           </include>
        FROM
            otb_workorder ow
        WHERE
            ow.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="ow" />
                <property name="qoTableAlias" value="qoow" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="ow" />
                <property name="qoTableAlias" value="qoow" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ow"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM otb_workorder ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.auto.OtbWorkorderAutoMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>