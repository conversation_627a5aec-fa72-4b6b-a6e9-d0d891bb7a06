<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.binlocation.BinLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.vo.page.BinLocationPageVO">
        <id column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="location_name" property="locationName" />
        <result column="lrow" property="lrow" />
        <result column="ldepth" property="ldepth" />
        <result column="llevel" property="llevel" />
        <result column="lsplit" property="lsplit" />
        <result column="note" property="note" />
        <result column="active_flag" property="activeFlag" />
        <result column="ref_num" property="refNum" />
        <result column="bin_type" property="binType" />
        <result column="type" property="type" />
        <result column="warehouse_zone_type" property="warehouseZoneType" />
        <result column="default_flag" property="defaultFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.location_name,
        t.lrow,
        t.ldepth,
        t.llevel,
        t.lsplit,
        t.note,
        t.active_flag,
        t.ref_num,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.bin_type,
        t.type,
        t.warehouse_zone_type,
        t.default_flag
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultMap="BaseResultMap">
        SELECT
            distinct
            <include refid="Base_Column_List" />
        FROM
            bin_location t
        left join
            bin_location_detail as bld on bld.bin_location_id=t.id and bld.remove_flag=0
        WHERE
            t.remove_flag = 0
            <if test="qo.productVersionQuery!=null">
                and EXISTS( select id
                            from product_version pv
                            where pv.id=bld.product_version_id
                            and pv.remove_flag=0
                            <include refid="Base_Product_Version_Where_List"/>
                           )
                and bld.in_stock_qty &gt; 0
            </if>
            <if test="qo.productVersionIdList!= null and qo.productVersionIdList.size()>0">
                and bld.in_stock_qty &gt; 0
            </if>
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <sql id="Base_Product_Version_Where_List">
        <if test="qo.productVersionQuery.supplierSkuList != null and qo.productVersionQuery.supplierSkuList.size > 0 ">
            AND pv.supplier_sku in
            <foreach collection="qo.productVersionQuery.supplierSkuList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productVersionQuery.refNumList != null and qo.productVersionQuery.refNumList.size > 0 ">
            AND pv.ref_num in
            <foreach collection="qo.productVersionQuery.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productVersionQuery.upcList != null and qo.productVersionQuery.upcList.size > 0 ">
            AND pv.upc in
            <foreach collection="qo.productVersionQuery.upcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        bin_location t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>


    <select id="listWithDetailByQuery" resultType="cn.need.cloud.biz.model.vo.binlocation.BinLocationVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            bin_location t
        WHERE
            EXISTS(
                SELECT
                    t2.bin_location_id id
                FROM bin_location_detail t2
                WHERE
                    t2.product_id IN
                    <foreach collection="qo.productIds" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
            )
            AND t.remove_flag = 0
            <include refid="Bin_Location_With_Detail_Where_List" />
    </select>
    <select id="list" resultType="java.lang.Long">
            select id
            from bin_location bl
            where bl.remove_flag=0 and bl.default_flag=true
            <include refid="Bin_Location_With_Detail_Where_List"/>

    </select>


    <sql id="Bin_Location_With_Detail_Where_List">
        <if test="qo.warehouseZoneTypeList != null and qo.warehouseZoneTypeList.size > 0 ">
            AND b.warehouse_zone_type in
            <foreach collection="qo.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.binTypeList != null and qo.binTypeList.size > 0 ">
            AND b.bin_type in
            <foreach collection="qo.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lRowList != null and qo.lRowList.size > 0 ">
            AND b.lrow in
            <foreach collection="qo.lRowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lDepthList != null and qo.lDepthList.size > 0 ">
            AND b.ldepth in
            <foreach collection="qo.lDepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lLevelList != null and qo.lLevelList.size > 0 ">
            AND b.llevel in
            <foreach collection="qo.lLevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lSplitsList != null and qo.lSplitsList.size > 0 ">
            AND b.lsplit in
            <foreach collection="qo.lSplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>


    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.locationName != null and qo.locationName != ''">
            AND t.location_name like concat('%', #{qo.locationName},'%')
        </if>
        <if test="qo.lrow != null and qo.lrow != ''">
            AND t.lrow = #{qo.lrow}
        </if>
        <if test="qo.ldepth != null and qo.ldepth != ''">
            AND t.ldepth = #{qo.ldepth}
        </if>
        <if test="qo.llevel != null and qo.llevel != ''">
            AND t.llevel = #{qo.llevel}
        </if>
        <if test="qo.lsplit != null and qo.lsplit != ''">
            AND t.lsplit = #{qo.lsplit}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.activeFlag != null">
            AND t.active_flag = #{qo.activeFlag}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.binType != null">
            AND t.bin_type = #{qo.binType}
        </if>
        <if test="qo.binTypeList != null and qo.binTypeList.size > 0 ">
            AND t.bin_type in
            <foreach collection="qo.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.type != null and qo.type != ''">
            AND t.type = #{qo.type}
        </if>
        <if test="qo.typeList != null and qo.typeList.size > 0 ">
            AND t.type in
            <foreach collection="qo.typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.warehouseZoneType != null and qo.warehouseZoneType != ''">
            AND t.warehouse_zone_type = #{qo.warehouseZoneType}
        </if>
        <if test="qo.warehouseZoneTypeList != null and qo.warehouseZoneTypeList.size > 0 ">
            AND t.warehouse_zone_type in
            <foreach collection="qo.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.defaultFlag != null">
            AND t.default_flag = #{qo.defaultFlag}
        </if>
        <!--库存数量-->
        <if test="qo.inStockQty != null">
            AND bld.in_stock_qty &gt; #{qo.inStockQty}
        </if>
        <!--库位名称-->
        <if test="qo.locationNameList != null and qo.locationNameList.size > 0 ">
            AND t.location_name in
            <foreach collection="qo.locationNameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--库位名称模糊查-->
        <if test="qo.locationNameLikeList != null and qo.locationNameLikeList.size > 0 ">
            AND
            <foreach collection="qo.locationNameLikeList" item="item" separator="or" open="(" close=")">
                t.location_name like concat('%', #{item},'%')
            </foreach>
        </if>
        <!--版本产品id-->
        <if test="qo.productVersionIdList != null and qo.productVersionIdList.size > 0 ">
            AND bld.product_version_id in
            <foreach collection="qo.productVersionIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>