<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductScanMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductScan">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="scan_num" property="scanNum"/>
        <result column="product_id" property="productId"/>
        <result column="version" property="version"/>
        <result column="product_type" property="productType"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t
        .
        id
        ,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ref_id,
        t.scan_num,
        t.line_num,
        t.default_flag,
        t.product_id,
        t.transaction_partner_id,
        t.product_attribute,
        t.product_type,
        t.version,
        t.tenant_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.page.ProductScanPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_scan t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        order by
        FIELD(product_attribute, 'product', 'multibox'),
        line_num
    </select>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.scanNum != null and qo.scanNum != ''">
            AND t.scan_num = #{qo.scanNum}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <!--        <if test="qo.version != null">-->
        <!--            AND t.version  = #{qo.version}-->
        <!--        </if>-->
        <if test="qo.productType != null and qo.productType != ''">
            AND t.product_type = #{qo.productType}
        </if>
        <if test="qo.productTypeList != null and qo.productTypeList.size > 0 ">
            AND t.product_type in
            <foreach collection="qo.productTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <!--        <if test="qo.tenantId != null">-->
        <!--            AND t.tenant_id = #{qo.tenantId}-->
        <!--        </if>-->
    </sql>

</mapper>