<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductComponentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductComponent">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="assembly_product_id" property="assemblyProductId"/>
        <result column="component_product_id" property="componentProductId"/>
        <result column="assembly_instruction_note" property="assemblyInstructionNote"/>
        <result column="component_qty" property="componentQty"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="component_version_int" property="componentVersionInt"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.assembly_product_id,
        t.component_product_id,
        t.assembly_instruction_note,
        t.component_qty,
        t.version,
        t.deleted_note,
        t.component_version_int
    </sql>


    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.page.ProductComponentPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_component t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
    </select>
    <select id="componentList" resultType="cn.need.cloud.biz.model.vo.product.ComponentProductListVO"
            parameterType="java.lang.Long">
        SELECT t.id,
               t.assembly_product_id,
               t.component_product_id,
               t.assembly_instruction_note,
               t.component_qty,
               t.component_version_int,
               p.ref_num,
               p.supplier_sku,
               p.upc,
               p.transaction_partner_id,
               p.create_time
        From product_component t
                 INNER JOIN product p ON t.component_product_id = p.id
        WHERE t.assembly_product_id = #{productId}
          AND t.remove_flag = 0
          AND p.remove_flag = 0
    </select>

    <select id="assemblyList" resultType="cn.need.cloud.biz.model.vo.product.AssemblyProductListVO"
            parameterType="java.lang.Long">
        SELECT DISTINCT t.assembly_product_id,
                        p.ref_num,
                        p.supplier_sku,
                        p.upc,
                        p.transaction_partner_id,
                        p.title
        From product_component t
                 INNER JOIN product p ON t.assembly_product_id = p.id
        WHERE t.component_product_id = #{productId}
          AND t.remove_flag = 0
          AND p.remove_flag = 0
    </select>

    <select id="getAssemblyProductIdByComponent" resultType="java.lang.Long"
            parameterType="java.lang.Long">
        SELECT assembly_product_id
        FROM product_component
        WHERE component_product_id = #{componentProductId}
          and remove_flag = 0
    </select>

    <select id="getOldVersionInt" resultType="java.lang.Integer" parameterType="java.lang.Long">
        SELECT MAX(t.component_version_int)
        FROM product_component t
        WHERE t.assembly_product_id = #{productId}
    </select>
    <select id="findListByAssemblyProductIdAndVersionInt"
            resultType="cn.need.cloud.biz.model.entity.product.ProductComponent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_component t
        WHERE
        t.assembly_product_id = #{productId}
        AND t.component_version_int = #{versionInt}
    </select>


    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.assemblyProductId != null">
            AND t.assembly_product_id = #{qo.assemblyProductId}
        </if>
        <if test="qo.componentProductId != null">
            AND t.component_product_id = #{qo.componentProductId}
        </if>
        <if test="qo.assemblyInstructionNote != null and qo.assemblyInstructionNote != ''">
            AND t.assembly_instruction_note = #{qo.assemblyInstructionNote}
        </if>
        <if test="qo.componentQty != null">
            AND t.component_qty = #{qo.componentQty}
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.componentVersionInt != null">
            AND t.component_version_int = #{qo.componentVersionInt}
        </if>
    </sql>

</mapper>