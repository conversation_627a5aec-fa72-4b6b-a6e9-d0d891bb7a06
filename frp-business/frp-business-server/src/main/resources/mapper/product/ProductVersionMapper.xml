<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.product.ProductVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.product.ProductVersion">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="product_id" property="productId"/>
        <result column="supplier_sku" property="supplierSku"/>
        <result column="ref_num" property="refNum"/>
        <result column="upc" property="upc"/>
        <result column="title" property="title"/>
        <result column="net_length" property="netLength"/>
        <result column="net_width" property="netWidth"/>
        <result column="net_height" property="netHeight"/>
        <result column="net_weight" property="netWeight"/>
        <result column="ship_length" property="shipLength"/>
        <result column="ship_width" property="shipWidth"/>
        <result column="ship_height" property="shipHeight"/>
        <result column="ship_weight" property="shipWeight"/>
        <result column="carton_length" property="cartonLength"/>
        <result column="carton_width" property="cartonWidth"/>
        <result column="carton_height" property="cartonHeight"/>
        <result column="carton_weight" property="cartonWeight"/>
        <result column="pcs_per_carton" property="pcsPerCarton"/>
        <result column="product_version_int" property="productVersionInt"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="carton_dimension_unit" property="cartonDimensionUnit"/>
        <result column="carton_weight_unit" property="cartonWeightUnit"/>
        <result column="net_dimension_unit" property="netDimensionUnit"/>
        <result column="net_weight_unit" property="netWeightUnit"/>
        <result column="ship_dimension_unit" property="shipDimensionUnit"/>
        <result column="ship_weight_unit" property="shipWeightUnit"/>
        <result column="transaction_partner_id" property="transactionPartnerId"/>
        <result column="product_remeasure_type" property="productRemeasureType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.product_id,
        t.supplier_sku,
        t.ref_num,
        t.upc,
        t.title,
        t.net_length,
        t.net_width,
        t.net_height,
        t.net_weight,
        t.ship_length,
        t.ship_width,
        t.ship_height,
        t.ship_weight,
        t.carton_length,
        t.carton_width,
        t.carton_height,
        t.carton_weight,
        t.pcs_per_carton,
        t.product_version_int,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.carton_dimension_unit,
        t.carton_weight_unit,
        t.net_dimension_unit,
        t.net_weight_unit,
        t.ship_dimension_unit,
        t.ship_weight_unit,
        t.transaction_partner_id,
        t.product_type,
        t.product_remeasure_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.product.ProductVersionVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_version t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <select id="gretLatestProductVersionByProductIdList"
            resultType="cn.need.cloud.biz.model.vo.product.ProductVersionVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        product_version t
        INNER JOIN (
        SELECT
        product_id,
        MAX(product_version_int) AS max_version
        FROM product_version
        WHERE
        remove_flag = 0
        AND product_id IN
        <foreach collection="productIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY product_id
        ) t2 ON t.product_id = t2.product_id
        AND t.product_version_int = t2.max_version
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.supplierSku != null and qo.supplierSku != ''">
            AND t.supplier_sku = #{qo.supplierSku}
        </if>
        <if test="qo.supplierSkuList != null and qo.supplierSkuList.size > 0 ">
            AND t.supplier_sku in
            <foreach collection="qo.supplierSkuList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.upc != null and qo.upc != ''">
            AND t.upc = #{qo.upc}
        </if>
        <if test="qo.upcList != null and qo.upcList.size > 0 ">
            AND t.upc in
            <foreach collection="qo.upcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.title != null and qo.title != ''">
            AND t.title = #{qo.title}
        </if>
        <if test="qo.netLength != null">
            AND t.net_length = #{qo.netLength}
        </if>
        <if test="qo.netWidth != null">
            AND t.net_width = #{qo.netWidth}
        </if>
        <if test="qo.netHeight != null">
            AND t.net_height = #{qo.netHeight}
        </if>
        <if test="qo.netWeight != null">
            AND t.net_weight = #{qo.netWeight}
        </if>
        <if test="qo.shipLength != null">
            AND t.ship_length = #{qo.shipLength}
        </if>
        <if test="qo.shipWidth != null">
            AND t.ship_width = #{qo.shipWidth}
        </if>
        <if test="qo.shipHeight != null">
            AND t.ship_height = #{qo.shipHeight}
        </if>
        <if test="qo.shipWeight != null">
            AND t.ship_weight = #{qo.shipWeight}
        </if>
        <if test="qo.cartonLength != null">
            AND t.carton_length = #{qo.cartonLength}
        </if>
        <if test="qo.cartonWidth != null">
            AND t.carton_width = #{qo.cartonWidth}
        </if>
        <if test="qo.cartonHeight != null">
            AND t.carton_height = #{qo.cartonHeight}
        </if>
        <if test="qo.cartonWeight != null">
            AND t.carton_weight = #{qo.cartonWeight}
        </if>
        <if test="qo.pcsPerCarton != null">
            AND t.pcs_per_carton = #{qo.pcsPerCarton}
        </if>
        <if test="qo.productVersionInt != null">
            AND t.product_version_int = #{qo.productVersionInt}
        </if>
        <if test="qo.productVersionIntList!= null and qo.productVersionIntList.size > 0 ">
            AND t.product_version_int in
            <foreach collection="qo.productVersionIntList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.cartonDimensionUnit != null and qo.cartonDimensionUnit != ''">
            AND t.carton_dimension_unit = #{qo.cartonDimensionUnit}
        </if>
        <if test="qo.cartonWeightUnit != null and qo.cartonWeightUnit != ''">
            AND t.carton_weight_unit = #{qo.cartonWeightUnit}
        </if>
        <if test="qo.netDimensionUnit != null and qo.netDimensionUnit != ''">
            AND t.net_dimension_unit = #{qo.netDimensionUnit}
        </if>
        <if test="qo.netWeightUnit != null and qo.netWeightUnit != ''">
            AND t.net_weight_unit = #{qo.netWeightUnit}
        </if>
        <if test="qo.shipDimensionUnit != null and qo.shipDimensionUnit != ''">
            AND t.ship_dimension_unit = #{qo.shipDimensionUnit}
        </if>
        <if test="qo.shipWeightUnit != null and qo.shipWeightUnit != ''">
            AND t.ship_weight_unit = #{qo.shipWeightUnit}
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productType != null and qo.productType != ''">
            AND t.product_type = #{qo.productType}
        </if>
        <if test="qo.productRemeasureType != null and qo.productRemeasureType != ''">
            AND t.product_remeasure_type = #{qo.productRemeasureType}
        </if>
        <if test="qo.productRemeasureTypeList != null and qo.productRemeasureTypeList.size > 0 ">
            AND t.product_remeasure_type in
            <foreach collection="qo.productRemeasureTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>