<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.warehouse.WarehouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.warehouse.Warehouse">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="time_zone" property="timeZone" />
        <result column="address_name" property="addressName" />
        <result column="address_company" property="addressCompany" />
        <result column="address_country" property="addressCountry" />
        <result column="address_state" property="addressState" />
        <result column="address_city" property="addressCity" />
        <result column="address_zip_code" property="addressZipCode" />
        <result column="address_addr1" property="addressAddr1" />
        <result column="address_addr2" property="addressAddr2" />
        <result column="address_addr3" property="addressAddr3" />
        <result column="address_email" property="addressEmail" />
        <result column="address_phone" property="addressPhone" />
        <result column="address_note" property="addressNote" />
        <result column="ref_num" property="refNum" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="address_is_residential" property="addressIsResidential" />
        <result column="amazon_ship_profile_ship_api_ref_num" property="amazonShipProfileShipApiRefNum" />
        <result column="fedex_ship_profile_ship_api_ref_num" property="fedexShipProfileShipApiRefNum" />
        <result column="upsship_profile_ship_api_ref_num" property="upsshipProfileShipApiRefNum" />
        <result column="active_flag" property="activeFlag" />
        <result column="amazon_ship_pallet_profile_ship_api_ref_num" property="amazonShipPalletProfileShipApiRefNum" />
        <result column="amazon_warehouse_code" property="amazonWarehouseCode" />
        <result column="ssccprefix" property="ssccprefix" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.code,
        t.name,
        t.time_zone,
        t.address_name,
        t.address_company,
        t.address_country,
        t.address_state,
        t.address_city,
        t.address_zip_code,
        t.address_addr1,
        t.address_addr2,
        t.address_addr3,
        t.address_email,
        t.address_phone,
        t.address_note,
        t.ref_num,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.address_is_residential,
        t.amazon_ship_profile_ship_api_ref_num,
        t.fedex_ship_profile_ship_api_ref_num,
        t.upsship_profile_ship_api_ref_num,
        t.active_flag,
        t.amazon_ship_pallet_profile_ship_api_ref_num,
        t.amazon_warehouse_code,
        t.ssccprefix,
        t.usps_ship_profile_ship_api_ref_num,
        t.ontrac_ship_profile_ship_api_ref_num
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.WarehousePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            warehouse t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.code != null and qo.code != ''">
            AND t.code = #{qo.code}
        </if>
        <if test="qo.codeList != null and qo.codeList.size > 0 ">
            AND t.code in
            <foreach collection="qo.codeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.name != null and qo.name != ''">
            AND t.name = #{qo.name}
        </if>
        <!--仓库名称-->
        <if test="qo.nameList != null and qo.nameList.size > 0 ">
            AND t.name in
            <foreach collection="qo.nameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.timeZone != null">
            AND t.time_zone = #{qo.timeZone}
        </if>
        <if test="qo.addressName != null and qo.addressName != ''">
            AND t.address_name = #{qo.addressName}
        </if>
        <if test="qo.addressCompany != null and qo.addressCompany != ''">
            AND t.address_company = #{qo.addressCompany}
        </if>
        <if test="qo.addressCountry != null and qo.addressCountry != ''">
            AND t.address_country = #{qo.addressCountry}
        </if>
        <if test="qo.addressState != null and qo.addressState != ''">
            AND t.address_state = #{qo.addressState}
        </if>
        <if test="qo.addressCity != null and qo.addressCity != ''">
            AND t.address_city = #{qo.addressCity}
        </if>
        <if test="qo.addressZipCode != null and qo.addressZipCode != ''">
            AND t.address_zip_code = #{qo.addressZipCode}
        </if>
        <if test="qo.addressAddr1 != null and qo.addressAddr1 != ''">
            AND t.address_addr1 = #{qo.addressAddr1}
        </if>
        <if test="qo.addressAddr2 != null and qo.addressAddr2 != ''">
            AND t.address_addr2 = #{qo.addressAddr2}
        </if>
        <if test="qo.addressAddr3 != null and qo.addressAddr3 != ''">
            AND t.address_addr3 = #{qo.addressAddr3}
        </if>
        <if test="qo.addressEmail != null and qo.addressEmail != ''">
            AND t.address_email = #{qo.addressEmail}
        </if>
        <if test="qo.addressPhone != null and qo.addressPhone != ''">
            AND t.address_phone = #{qo.addressPhone}
        </if>
        <if test="qo.addressNote != null and qo.addressNote != ''">
            AND t.address_note = #{qo.addressNote}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.addressIsResidential != null">
            AND t.address_is_residential = #{qo.addressIsResidential}
        </if>
        <if test="qo.amazonShipProfileShipApiRefNum != null and qo.amazonShipProfileShipApiRefNum != ''">
            AND t.amazon_ship_profile_ship_api_ref_num = #{qo.amazonShipProfileShipApiRefNum}
        </if>
        <if test="qo.amazonShipProfileShipApiRefNumList != null and qo.amazonShipProfileShipApiRefNumList.size > 0 ">
            AND t.amazon_ship_profile_ship_api_ref_num in
            <foreach collection="qo.amazonShipProfileShipApiRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fedexShipProfileShipApiRefNum != null and qo.fedexShipProfileShipApiRefNum != ''">
            AND t.fedex_ship_profile_ship_api_ref_num = #{qo.fedexShipProfileShipApiRefNum}
        </if>
        <if test="qo.fedexShipProfileShipApiRefNumList != null and qo.fedexShipProfileShipApiRefNumList.size > 0 ">
            AND t.fedex_ship_profile_ship_api_ref_num in
            <foreach collection="qo.fedexShipProfileShipApiRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.upsshipProfileShipApiRefNum != null and qo.upsshipProfileShipApiRefNum != ''">
            AND t.upsship_profile_ship_api_ref_num = #{qo.upsshipProfileShipApiRefNum}
        </if>
        <if test="qo.upsshipProfileShipApiRefNumList != null and qo.upsshipProfileShipApiRefNumList.size > 0 ">
            AND t.upsship_profile_ship_api_ref_num in
            <foreach collection="qo.upsshipProfileShipApiRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.activeFlag != null">
            AND t.active_flag = #{qo.activeFlag}
        </if>
        <if test="qo.amazonShipPalletProfileShipApiRefNum != null and qo.amazonShipPalletProfileShipApiRefNum != ''">
            AND t.amazon_ship_pallet_profile_ship_api_ref_num = #{qo.amazonShipPalletProfileShipApiRefNum}
        </if>
        <if test="qo.amazonShipPalletProfileShipApiRefNumList != null and qo.amazonShipPalletProfileShipApiRefNumList.size > 0 ">
            AND t.amazon_ship_pallet_profile_ship_api_ref_num in
            <foreach collection="qo.amazonShipPalletProfileShipApiRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.amazonWarehouseCode != null and qo.amazonWarehouseCode != ''">
            AND t.amazon_warehouse_code = #{qo.amazonWarehouseCode}
        </if>
        <if test="qo.ssccprefix != null and qo.ssccprefix != ''">
            AND t.ssccprefix = #{qo.ssccprefix}
        </if>
    </sql>

</mapper>