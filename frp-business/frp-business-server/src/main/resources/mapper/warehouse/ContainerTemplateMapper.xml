<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.warehouse.ContainerTemplateMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.ContainerTemplate">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="container_height" property="containerHeight" />
        <result column="container_length" property="containerLength" />
        <result column="container_width" property="containerWidth" />
        <result column="deleted_note" property="deletedNote" />
        <result column="dimension_unit" property="dimensionUnit" />
        <result column="max_weight" property="maxWeight" />
        <result column="name" property="name" />
        <result column="note" property="note" />
        <result column="ref_num" property="refNum" />
        <result column="volume" property="volume" />
        <result column="volume_unit" property="volumeUnit" />
        <result column="weight_unit" property="weightUnit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.container_height,
        ${dbTableAlias}.container_length,
        ${dbTableAlias}.container_width,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.dimension_unit,
        ${dbTableAlias}.max_weight,
        ${dbTableAlias}.name,
        ${dbTableAlias}.note,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.volume,
        ${dbTableAlias}.volume_unit,
        ${dbTableAlias}.weight_unit
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.ContainerTemplatePageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ct"/>
            </include>
        FROM
        container_template ct
        WHERE
            ct.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ct"/>
                    <property name="qoTableAlias" value="qoct"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ct"/>
                <property name="qoTableAlias" value="qoct"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoct.timeZone != null and qoct.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoct.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            container_template ct
        WHERE
            ct.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ct"/>
                    <property name="qoTableAlias" value="qoct"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoct.timeZone != null and qoct.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoct.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.containerHeight != null">
            AND ${dbTableAlias}.container_height = #{${qoTableAlias}.containerHeight}
        </if>
        <if test="${qoTableAlias}.containerLength != null">
            AND ${dbTableAlias}.container_length = #{${qoTableAlias}.containerLength}
        </if>
        <if test="${qoTableAlias}.containerWidth != null">
            AND ${dbTableAlias}.container_width = #{${qoTableAlias}.containerWidth}
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.dimensionUnit != null and ${qoTableAlias}.dimensionUnit != ''">
            AND ${dbTableAlias}.dimension_unit = #{${qoTableAlias}.dimensionUnit}
        </if>
        <if test="${qoTableAlias}.maxWeight != null">
            AND ${dbTableAlias}.max_weight = #{${qoTableAlias}.maxWeight}
        </if>
        <if test="${qoTableAlias}.name != null and ${qoTableAlias}.name != ''">
            AND ${dbTableAlias}.name = #{${qoTableAlias}.name}
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.volume != null">
            AND ${dbTableAlias}.volume = #{${qoTableAlias}.volume}
        </if>
        <if test="${qoTableAlias}.volumeUnit != null and ${qoTableAlias}.volumeUnit != ''">
            AND ${dbTableAlias}.volume_unit = #{${qoTableAlias}.volumeUnit}
        </if>
        <if test="${qoTableAlias}.weightUnit != null and ${qoTableAlias}.weightUnit != ''">
            AND ${dbTableAlias}.weight_unit = #{${qoTableAlias}.weightUnit}
        </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.ContainerTemplatePageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ct"/>
           </include>
        FROM
            container_template ct
        WHERE
            ct.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="ct" />
                <property name="qoTableAlias" value="qoct" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="ct" />
                <property name="qoTableAlias" value="qoct" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ct"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM container_template ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.ContainerTemplateMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
