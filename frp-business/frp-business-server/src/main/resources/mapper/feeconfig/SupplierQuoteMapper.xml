<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.feeconfig.SupplierQuoteMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.feeconfig.SupplierQuote">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="active_flag" property="activeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="end_time" property="endTime" />
        <result column="name" property="name" />
        <result column="note" property="note" />
        <result column="quote_id" property="quoteId" />
        <result column="ref_num" property="refNum" />
        <result column="start_time" property="startTime" />
        <result column="supplier_id" property="supplierId" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.active_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.end_time,
        ${dbTableAlias}.name,
        ${dbTableAlias}.note,
        ${dbTableAlias}.quote_id,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.start_time,
        ${dbTableAlias}.supplier_id,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.feeconfig.page.SupplierQuotePageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="sq"/>
            </include>
        FROM
        supplier_quote sq
        WHERE
            sq.remove_flag = 0
            <if test="qosq != null">
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="sq"/>
                    <property name="qoTableAlias" value="qosq"/>
                </include>
            </if>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="sq"/>
                <property name="qoTableAlias" value="qosq"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            <include refid="mapper.DropProList_Select_Columns">
                <property name="dbTableAlias" value="sq"/>
                <property name="qoTableAlias" value="qosq"/>
            </include>
        FROM supplier_quote sq
        WHERE sq.remove_flag = 0
            <if test="qosq != null">
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="sq"/>
                    <property name="qoTableAlias" value="qosq"/>
                </include>
            </if>
        GROUP BY
            <include refid="mapper.DropProList_GroupBy_Columns">
                <property name="dbTableAlias" value="sq"/>
                <property name="qoTableAlias" value="qosq"/>
            </include>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.remove_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.removeFlag"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.active_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.activeFlag"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.deleted_note"/>
            <property name="qoColumnName" value="${qoTableAlias}.deletedNote"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.end_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.endTime"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.name"/>
            <property name="qoColumnName" value="${qoTableAlias}.name"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.note"/>
            <property name="qoColumnName" value="${qoTableAlias}.note"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.quote_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.quoteId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.refNum"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.start_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.startTime"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.supplier_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.supplierId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.warehouse_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.warehouseId"/>
        </include>

    </sql>

</mapper>