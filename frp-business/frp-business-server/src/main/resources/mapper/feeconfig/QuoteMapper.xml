<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.feeconfig.QuoteMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.feeconfig.Quote">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="active_flag" property="activeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="name" property="name" />
        <result column="note" property="note" />
        <result column="ref_num" property="refNum" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        q.id,
        q.create_by,
        q.create_time,
        q.update_by,
        q.update_time,
        q.remove_flag,
        q.version,
        q.tenant_id,
        q.active_flag,
        q.deleted_note,
        q.name,
        q.note,
        q.ref_num,
        q.warehouse_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.feeconfig.page.QuotePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
        quote q
        WHERE
            q.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY q.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qoq.createTimeStart != null">
            AND q.create_time &gt;= #{qoq.createTimeStart}
        </if>
        <if test="qoq.createTimeEnd != null">
            AND q.create_time &lt; #{qoq.createTimeEnd}
        </if>
        <if test="qoq.updateTimeStart != null">
            AND q.update_time &gt;= #{qoq.updateTimeStart}
        </if>
        <if test="qoq.updateTimeEnd != null">
            AND q.update_time &lt; #{qoq.updateTimeEnd}
        </if>
        <if test="qoq.createBy != null">
            AND q.create_by = #{qoq.createBy}
        </if>
        <if test="qoq.createByList != null and qoq.createByList.size > 0 ">
            AND q.create_by in
            <foreach collection="qoq.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qoq.updateBy != null">
            AND q.update_by = #{qoq.updateBy}
        </if>
        <if test="qoq.updateByList != null and qoq.updateByList.size > 0 ">
            AND q.update_by in
            <foreach collection="qoq.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
            <if test="qoq.activeFlag != null">
                AND q.active_flag = #{qoq.activeFlag}
        </if>
            <if test="qoq.deletedNote != null and qoq.deletedNote != ''">
                AND q.deleted_note = #{qoq.deletedNote}
        </if>
            <if test="qoq.name != null and qoq.name != ''">
                AND q.name = #{qoq.name}
        </if>
            <if test="qoq.note != null and qoq.note != ''">
                AND q.note = #{qoq.note}
        </if>
            <if test="qoq.refNum != null and qoq.refNum != ''">
                AND q.ref_num = #{qoq.refNum}
        </if>
            <if test="qoq.refNumList != null and qoq.refNumList.size > 0 ">
            AND q.ref_num in
                <foreach collection="qoq.refNumList" item="item" separator="," open="("
                         close=")">
                #{item}
            </foreach>
        </if>
            <if test="qoq.warehouseId != null">
                AND q.warehouse_id = #{qoq.warehouseId}
        </if>
            <if test="qoq.warehouseIdList != null and qoq.warehouseIdList.size > 0 ">
            AND q.warehouse_id in
                <foreach collection="qoq.warehouseIdList" item="item" separator="," open="("
                         close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="dropProList" resultType="hashMap">
        SELECT
            <include refid="mapper.DropProList_Select_Columns">
                <property name="dbTableAlias" value="q"/>
                <property name="qoTableAlias" value="qoq"/>
            </include>
        FROM quote q
        WHERE q.remove_flag = 0
            <include refid="Base_Where_List" />
        GROUP BY
            <include refid="mapper.DropProList_GroupBy_Columns">
                <property name="dbTableAlias" value="q"/>
                <property name="qoTableAlias" value="qoq"/>
            </include>
    </select>

</mapper>