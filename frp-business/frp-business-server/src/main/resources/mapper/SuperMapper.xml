<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="mapper">

    <!-- 定义可复用的条件片段：处理字段值存在性/空值/列表查询 -->
    <sql id="valueConditions">
        <!-- 单值查询：当参数字段非空时 -->
        <if test="${qoColumnName} != null">
            AND ${dbColumnName} = #{${qoColumnName}}
        </if>

        <!-- > -->
        <if test="${qoColumnName}Gt != null">
            AND ${dbColumnName} &gt; #{${qoColumnName}Gt}
        </if>

        <!-- >= -->
        <if test="${qoColumnName}Ge != null">
            AND ${dbColumnName} &gt;= #{${qoColumnName}Ge}
        </if>

        <!--LT -->
        <if test="${qoColumnName}Lt != null">
            AND ${dbColumnName} &lt; #{${qoColumnName}Lt}
        </if>

        <!--LE -->
        <if test="${qoColumnName}Le != null">
            AND ${dbColumnName} &lt;= #{${qoColumnName}Le}
        </if>

        <!--LIKE -->
        <if test="${qoColumnName}Like != null">
            AND ${dbColumnName} like CONCAT('%', #{${qoColumnName}Like}, '%')
        </if>

        <!--LIKE_LEFT -->
        <if test="${qoColumnName}LikeLeft != null">
            AND ${dbColumnName} like CONCAT(#{${qoColumnName}LikeLeft}, '%')
        </if>

        <!--LIKE_RIGHT -->
        <if test="${qoColumnName}LikeRight != null">
            AND ${dbColumnName} like CONCAT('%', #{${qoColumnName}LikeRight})
        </if>

        <!-- 列表查询：当参数列表非空时 -->
        <if test="${qoColumnName}List != null and ${qoColumnName}List.size > 0 ">
            AND ${dbColumnName} in
            <foreach
                    collection="${qoColumnName}List"
                    item="item"
                    separator=","
                    open="("
                    close=")">
                #{item}
            </foreach>
        </if>

        <!--NotIn-->
        <if test="${qoColumnName}NiList != null and ${qoColumnName}NiList.size > 0 ">
            AND ${dbColumnName} not in
            <foreach
                    collection="${qoColumnName}List"
                    item="item"
                    separator=","
                    open="("
                    close=")">
                #{item}
            </foreach>
        </if>

        <!-- 复合存在性查询：处理空值/非空/空字符串等特殊条件 -->
        <if test="${qoColumnName}ValueTypeList != null and ${qoColumnName}ValueTypeList.size > 0 ">
            AND
            (
            <foreach collection="${qoColumnName}ValueTypeList" item="type" separator=" OR ">
                <choose>
                    <!-- NULL判断 -->
                    <when test="type == 'Null'">
                        ${dbColumnName} IS NULL
                    </when>
                    <!-- 非NULL判断 -->
                    <when test="type == 'NotNull'">
                        ${dbColumnName} IS NOT NULL
                    </when>
                    <!-- 非空字符串判断 -->
                    <when test="type == 'Empty'">
                        (${dbColumnName} IS NOT NULL AND ${dbColumnName} = '')
                    </when>
                    <!-- 非空且非空字符串判断 -->
                    <when test="type == 'NotEmpty'">
                        (${dbColumnName} IS NOT NULL AND ${dbColumnName} != '')
                    </when>
                    <!-- 默认处理：忽略无效类型 -->
                    <otherwise>
                        <!-- 避免无效类型影响查询结果，采用恒真条件 -->
                        1=1
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>

    </sql>

    <!-- 定义可复用的条件片段：处理字段值存在性/空值/列表查询 -->
    <sql id="valueOrConditions">
        <!-- 单值查询：当参数字段非空时 -->
        <if test="${qoColumnName} != null">
            OR ${dbColumnName} = #{${qoColumnName}}
        </if>

        <!-- > -->
        <if test="${qoColumnName}Gt != null">
            OR ${dbColumnName} &gt; #{${qoColumnName}Gt}
        </if>

        <!-- >= -->
        <if test="${qoColumnName}Ge != null">
            OR ${dbColumnName} &gt;= #{${qoColumnName}Ge}
        </if>

        <!--LT -->
        <if test="${qoColumnName}Lt != null">
            OR ${dbColumnName} &lt; #{${qoColumnName}Lt}
        </if>

        <!--LE -->
        <if test="${qoColumnName}Le != null">
            OR ${dbColumnName} &lt;= #{${qoColumnName}Le}
        </if>

        <!--LIKE -->
        <if test="${qoColumnName}Like != null">
            OR ${dbColumnName} like CONCAT('%', #{${qoColumnName}Like}, '%')
        </if>

        <!--LIKE_LEFT -->
        <if test="${qoColumnName}LikeLeft != null">
            OR ${dbColumnName} like CONCAT(#{${qoColumnName}LikeLeft}, '%')
        </if>

        <!--LIKE_RIGHT -->
        <if test="${qoColumnName}LikeRight != null">
            OR ${dbColumnName} like CONCAT('%', #{${qoColumnName}LikeRight})
        </if>

        <!-- 列表查询：当参数列表非空时 -->
        <if test="${qoColumnName}List != null and ${qoColumnName}List.size > 0 ">
            OR ${dbColumnName} in
            <foreach
                    collection="${qoColumnName}List"
                    item="item"
                    separator=","
                    open="("
                    close=")">
                #{item}
            </foreach>
        </if>

        <!--NotIn-->
        <if test="${qoColumnName}NiList != null and ${qoColumnName}NiList.size > 0 ">
            OR ${dbColumnName} not in
            <foreach
                    collection="${qoColumnName}List"
                    item="item"
                    separator=","
                    open="("
                    close=")">
                #{item}
            </foreach>
        </if>

        <!-- 复合存在性查询：处理空值/非空/空字符串等特殊条件 -->
        <if test="${qoColumnName}ValueTypeList != null and ${qoColumnName}ValueTypeList.size > 0 ">
            AND
            (
            <foreach collection="${qoColumnName}ValueTypeList" item="type" separator=" OR ">
                <choose>
                    <!-- NULL判断 -->
                    <when test="type == 'Null'">
                        ${dbColumnName} IS NULL
                    </when>
                    <!-- 非NULL判断 -->
                    <when test="type == 'NotNull'">
                        ${dbColumnName} IS NOT NULL
                    </when>
                    <!-- 非空字符串判断 -->
                    <when test="type == 'Empty'">
                        (${dbColumnName} IS NOT NULL AND ${dbColumnName} = '')
                    </when>
                    <!-- 非空且非空字符串判断 -->
                    <when test="type == 'NotEmpty'">
                        (${dbColumnName} IS NOT NULL AND ${dbColumnName} != '')
                    </when>
                    <!-- 默认处理：忽略无效类型 -->
                    <otherwise>
                        <!-- 避免无效类型影响查询结果，采用恒真条件 -->
                        1=2
                    </otherwise>
                </choose>
            </foreach>
            )
        </if>

    </sql>

    <sql id="valueConditionsPro">
        <if test="${qoTableAlias} != null and ${qoTableAlias}.andConditions != null and ${qoTableAlias}.andConditions.size > 0">
            AND
            <foreach collection="${qoTableAlias}.andConditions" item="condition" separator=" AND ">
                <if test="${qoTableAlias}.availableFields != null and ${qoTableAlias}.availableFields.contains(condition.field)">
                <choose>
                    <!-- 等值查询 -->
                    <when test="condition.keyword == 'EQ' || condition.keyword == ''">
                        fci.${condition.field} = #{condition.value}
                    </when>
                    <!-- 不等于查询 -->
                    <when test="condition.keyword == 'NE'">
                        fci.${condition.field} != #{condition.value}
                    </when>
                    <!-- 大于查询 -->
                    <when test="condition.keyword == 'GT'">
                        fci.${condition.field} &gt; #{condition.value}
                    </when>
                    <!-- 大于等于查询 -->
                    <when test="condition.keyword == 'GE'">
                        fci.${condition.field} &gt;= #{condition.value}
                    </when>
                    <!-- 小于查询 -->
                    <when test="condition.keyword == 'LT'">
                        fci.${condition.field} &lt; #{condition.value}
                    </when>
                    <!-- 小于等于查询 -->
                    <when test="condition.keyword == 'LE'">
                        fci.${condition.field} &lt;= #{condition.value}
                    </when>
                    <!-- 模糊查询 -->
                    <when test="condition.keyword == 'LIKE'">
                        fci.${condition.field} LIKE CONCAT('%', #{condition.value}, '%')
                    </when>
                    <!-- 左模糊查询 -->
                    <when test="condition.keyword == 'LIKE_LEFT'">
                        fci.${condition.field} LIKE CONCAT(#{condition.value}, '%')
                    </when>
                    <!-- 右模糊查询 -->
                    <when test="condition.keyword == 'LIKE_RIGHT'">
                        fci.${condition.field} LIKE CONCAT('%', #{condition.value})
                    </when>
                    <!-- IN查询 -->
                    <when test="condition.keyword == 'IN' and condition.values != null and condition.values.size > 0">
                        fci.${condition.field} IN
                        <foreach collection="condition.values" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </when>
                    <!-- NOT IN查询 -->
                    <when test="condition.keyword == 'NOT_IN' and condition.values != null and condition.values.size > 0">
                        fci.${condition.field} NOT IN
                        <foreach collection="condition.values" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </when>
                    <!-- IS EMPTY查询 -->
                    <when test="condition.keyword == 'IS_EMPTY'">
                        fci.${condition.field} IS NOT NULL AND fci.${condition.field} = ''
                    </when>
                    <!-- IS NOT EMPTY查询 -->
                    <when test="condition.keyword == 'IS_NOT_EMPTY'">
                        fci.${condition.field} IS NOT NULL AND fci.${condition.field} != ''
                    </when>
                    <!-- IS NULL查询 -->
                    <when test="condition.keyword == 'IS_NULL'">
                        fci.${condition.field} IS NULL
                    </when>
                    <!-- IS NOT NULL查询 -->
                    <when test="condition.keyword == 'IS_NOT_NULL'">
                        fci.${condition.field} IS NOT NULL
                    </when>
                    <!-- BETWEEN查询 -->
                    <when test="condition.keyword == 'BETWEEN' and condition.values != null and condition.values.size == 2">
                        fci.${condition.field} BETWEEN #{condition.value[0]} AND #{condition.value[1]}
                    </when>
                </choose>
                </if>
                <if test="${qoTableAlias}.availableFields == null || !${qoTableAlias}.availableFields.contains(condition.field)">
                1=2
                </if>
            </foreach>
        </if>
    </sql>

    <sql id="valueOrConditionsPro">
        <if test="${qoTableAlias} != null and ${qoTableAlias}.andConditions != null and ${qoTableAlias}.andConditions.size > 0">
            AND
            <foreach collection="${qoTableAlias}.orConditions" item="condition" separator=" OR ">
                <if test="${qoTableAlias}.availableFields != null and ${qoTableAlias}.availableFields.contains(condition.field)">
                <choose>
                    <!-- 等值查询 -->
                    <when test="condition.keyword == 'EQ' || condition.keyword == ''">
                        fci.${condition.field} = #{condition.value}
                    </when>
                    <!-- 不等于查询 -->
                    <when test="condition.keyword == 'NE'">
                        fci.${condition.field} != #{condition.value}
                    </when>
                    <!-- 大于查询 -->
                    <when test="condition.keyword == 'GT'">
                        fci.${condition.field} &gt; #{condition.value}
                    </when>
                    <!-- 大于等于查询 -->
                    <when test="condition.keyword == 'GE'">
                        fci.${condition.field} &gt;= #{condition.value}
                    </when>
                    <!-- 小于查询 -->
                    <when test="condition.keyword == 'LT'">
                        fci.${condition.field} &lt; #{condition.value}
                    </when>
                    <!-- 小于等于查询 -->
                    <when test="condition.keyword == 'LE'">
                        fci.${condition.field} &lt;= #{condition.value}
                    </when>
                    <!-- 模糊查询 -->
                    <when test="condition.keyword == 'LIKE'">
                        fci.${condition.field} LIKE CONCAT('%', #{condition.value}, '%')
                    </when>
                    <!-- 左模糊查询 -->
                    <when test="condition.keyword == 'LIKE_LEFT'">
                        fci.${condition.field} LIKE CONCAT(#{condition.value}, '%')
                    </when>
                    <!-- 右模糊查询 -->
                    <when test="condition.keyword == 'LIKE_RIGHT'">
                        fci.${condition.field} LIKE CONCAT('%', #{condition.value})
                    </when>
                    <!-- IN查询 -->
                    <when test="condition.keyword == 'IN' and condition.values != null and condition.values.size > 0">
                        fci.${condition.field} IN
                        <foreach collection="condition.values" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </when>
                    <!-- NOT IN查询 -->
                    <when test="condition.keyword == 'NOT_IN' and condition.values != null and condition.values.size > 0">
                        fci.${condition.field} NOT IN
                        <foreach collection="condition.values" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    </when>
                    <!-- IS EMPTY查询 -->
                    <when test="condition.keyword == 'IS_EMPTY'">
                        fci.${condition.field} IS NOT NULL AND fci.${condition.field} = ''
                    </when>
                    <!-- IS NOT EMPTY查询 -->
                    <when test="condition.keyword == 'IS_NOT_EMPTY'">
                        fci.${condition.field} IS NOT NULL AND fci.${condition.field} != ''
                    </when>
                    <!-- IS NULL查询 -->
                    <when test="condition.keyword == 'IS_NULL'">
                        fci.${condition.field} IS NULL
                    </when>
                    <!-- IS NOT NULL查询 -->
                    <when test="condition.keyword == 'IS_NOT_NULL'">
                        fci.${condition.field} IS NOT NULL
                    </when>
                    <!-- BETWEEN查询 -->
                    <when test="condition.keyword == 'BETWEEN' and condition.values != null and condition.values.size == 2">
                        fci.${condition.field} BETWEEN #{condition.value[0]} AND #{condition.value[1]}
                    </when>
                </choose>
                </if>
                <if test="${qoTableAlias}.availableFields == null || !${qoTableAlias}.availableFields.contains(condition.field)">
                    1=1
                </if>
            </foreach>
        </if>
    </sql>


    <!-- 公共 SELECT 片段：统计数量 + 维度列 -->
    <sql id="DropProList_Select_Columns">
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="${qoTableAlias}.timeZone != null and ${qoTableAlias}.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(${dbTableAlias}.${item.name}, '+00:00', #{${qoTableAlias}.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </sql>

    <!-- 公共 GROUP BY 片段：维度列 -->
    <sql id="DropProList_GroupBy_Columns">
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="${qoTableAlias}.timeZone != null and ${qoTableAlias}.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(${dbTableAlias}.${item.name}, '+00:00', #{${qoTableAlias}.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </sql>

</mapper>