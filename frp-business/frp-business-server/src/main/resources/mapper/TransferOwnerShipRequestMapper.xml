<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.TransferOwnerShipRequestMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.from_partner_id,
        ${dbTableAlias}.note,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.ref_num_type,
        ${dbTableAlias}.request_ref_num,
        ${dbTableAlias}.request_status,
        ${dbTableAlias}.to_partner_id,
        ${dbTableAlias}.transfer_owner_ship_type,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="tosr"/>
            </include>
        FROM
        transfer_owner_ship_request tosr
        WHERE
            tosr.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="tosr"/>
                    <property name="qoTableAlias" value="qotosr"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="tosr"/>
                <property name="qoTableAlias" value="qotosr"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qotosr.timeZone != null and qotosr.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qotosr.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            transfer_owner_ship_request tosr
        WHERE
            tosr.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="tosr"/>
                    <property name="qoTableAlias" value="qotosr"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qotosr.timeZone != null and qotosr.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qotosr.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
    <if test="${qoTableAlias} != null">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.remove_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.removeFlag"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.deleted_note"/>
            <property name="qoColumnName" value="${qoTableAlias}.deletedNote"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.from_partner_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.fromPartnerId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.note"/>
            <property name="qoColumnName" value="${qoTableAlias}.note"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.refNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.ref_num_type"/>
            <property name="qoColumnName" value="${qoTableAlias}.refNumType"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.request_ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.requestRefNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.request_status"/>
            <property name="qoColumnName" value="${qoTableAlias}.requestStatus"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.to_partner_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.toPartnerId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.transfer_owner_ship_type"/>
            <property name="qoColumnName" value="${qoTableAlias}.transferOwnerShipType"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.warehouse_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.warehouseId"/>
        </include>

    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.transfer.TransferOwnerShipRequestPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="tosr"/>
           </include>
        FROM
            transfer_owner_ship_request tosr
        WHERE
            tosr.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="tosr" />
                <property name="qoTableAlias" value="qotosr" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="tosr" />
                <property name="qoTableAlias" value="qotosr" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="tosr"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM transfer_owner_ship_request ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.TransferOwnerShipRequestMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>