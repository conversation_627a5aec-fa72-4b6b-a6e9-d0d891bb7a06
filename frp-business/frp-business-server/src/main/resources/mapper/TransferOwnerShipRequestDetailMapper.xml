<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.TransferOwnerShipRequestDetailMapper">

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.from_product_id,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.note,
        ${dbTableAlias}.to_product_id,
        ${dbTableAlias}.transfer_qty,
        ${dbTableAlias}.warehouse_id
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
    <if test="${qoTableAlias} != null">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.remove_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.removeFlag"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.deleted_note"/>
            <property name="qoColumnName" value="${qoTableAlias}.deletedNote"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.from_product_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.fromProductId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.line_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.lineNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.note"/>
            <property name="qoColumnName" value="${qoTableAlias}.note"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.to_product_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.toProductId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.transfer_qty"/>
            <property name="qoColumnName" value="${qoTableAlias}.transferQty"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.warehouse_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.warehouseId"/>
        </include>

    </if>
    </sql>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM transfer_owner_ship_request_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.TransferOwnerShipRequestDetailMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>