<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoWorkorderMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoWorkorder">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />  
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="oco_arrangement_id" property="ocoArrangementId" />
        <result column="oco_request_id" property="ocoRequestId" />
        <result column="oco_workorder_status" property="ocoWorkorderStatus" />
        <result column="ref_num" property="refNum" />
        <result column="request_snapshot_channel" property="requestSnapshotChannel" />
        <result column="request_snapshot_note" property="requestSnapshotNote" />
        <result column="request_snapshot_order_num" property="requestSnapshotOrderNum" />
        <result column="request_snapshot_ref_num" property="requestSnapshotRefNum" />
        <result column="request_snapshot_request_ref_num" property="requestSnapshotRequestRefNum" />
        <result column="request_snapshot_ship_from_address_addr1" property="requestSnapshotShipFromAddressAddr1" />
        <result column="request_snapshot_ship_from_address_addr2" property="requestSnapshotShipFromAddressAddr2" />
        <result column="request_snapshot_ship_from_address_addr3" property="requestSnapshotShipFromAddressAddr3" />
        <result column="request_snapshot_ship_from_address_city" property="requestSnapshotShipFromAddressCity" />
        <result column="request_snapshot_ship_from_address_company" property="requestSnapshotShipFromAddressCompany" />
        <result column="request_snapshot_ship_from_address_country" property="requestSnapshotShipFromAddressCountry" />
        <result column="request_snapshot_ship_from_address_email" property="requestSnapshotShipFromAddressEmail" />
        <result column="request_snapshot_ship_from_address_is_residential" property="requestSnapshotShipFromAddressIsResidential" />
        <result column="request_snapshot_ship_from_address_name" property="requestSnapshotShipFromAddressName" />
        <result column="request_snapshot_ship_from_address_note" property="requestSnapshotShipFromAddressNote" />
        <result column="request_snapshot_ship_from_address_phone" property="requestSnapshotShipFromAddressPhone" />
        <result column="request_snapshot_ship_from_address_state" property="requestSnapshotShipFromAddressState" />
        <result column="request_snapshot_ship_from_address_zip_code" property="requestSnapshotShipFromAddressZipCode" />
        <result column="request_snapshot_ship_to_address_addr1" property="requestSnapshotShipToAddressAddr1" />
        <result column="request_snapshot_ship_to_address_addr2" property="requestSnapshotShipToAddressAddr2" />
        <result column="request_snapshot_ship_to_address_addr3" property="requestSnapshotShipToAddressAddr3" />
        <result column="request_snapshot_ship_to_address_city" property="requestSnapshotShipToAddressCity" />
        <result column="request_snapshot_ship_to_address_company" property="requestSnapshotShipToAddressCompany" />
        <result column="request_snapshot_ship_to_address_country" property="requestSnapshotShipToAddressCountry" />
        <result column="request_snapshot_ship_to_address_email" property="requestSnapshotShipToAddressEmail" />
        <result column="request_snapshot_ship_to_address_is_residential" property="requestSnapshotShipToAddressIsResidential" />
        <result column="request_snapshot_ship_to_address_name" property="requestSnapshotShipToAddressName" />
        <result column="request_snapshot_ship_to_address_note" property="requestSnapshotShipToAddressNote" />
        <result column="request_snapshot_ship_to_address_phone" property="requestSnapshotShipToAddressPhone" />
        <result column="request_snapshot_ship_to_address_state" property="requestSnapshotShipToAddressState" />
        <result column="request_snapshot_ship_to_address_zip_code" property="requestSnapshotShipToAddressZipCode" />
        <result column="request_snapshot_ship_window_end" property="requestSnapshotShipWindowEnd" />
        <result column="request_snapshot_ship_window_start" property="requestSnapshotShipWindowStart" />
        <result column="request_snapshot_transaction_partner_id" property="requestSnapshotTransactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="workorder_prep_status" property="workorderPrepStatus" />
        <result column="workorder_type" property="workorderType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.oco_arrangement_id,
        ${dbTableAlias}.oco_request_id,
        ${dbTableAlias}.oco_workorder_status,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.request_snapshot_channel,
        ${dbTableAlias}.request_snapshot_note,
        ${dbTableAlias}.request_snapshot_order_num,
        ${dbTableAlias}.request_snapshot_ref_num,
        ${dbTableAlias}.request_snapshot_request_ref_num,
        ${dbTableAlias}.request_snapshot_ship_from_address_addr1,
        ${dbTableAlias}.request_snapshot_ship_from_address_addr2,
        ${dbTableAlias}.request_snapshot_ship_from_address_addr3,
        ${dbTableAlias}.request_snapshot_ship_from_address_city,
        ${dbTableAlias}.request_snapshot_ship_from_address_company,
        ${dbTableAlias}.request_snapshot_ship_from_address_country,
        ${dbTableAlias}.request_snapshot_ship_from_address_email,
        ${dbTableAlias}.request_snapshot_ship_from_address_is_residential,
        ${dbTableAlias}.request_snapshot_ship_from_address_name,
        ${dbTableAlias}.request_snapshot_ship_from_address_note,
        ${dbTableAlias}.request_snapshot_ship_from_address_phone,
        ${dbTableAlias}.request_snapshot_ship_from_address_state,
        ${dbTableAlias}.request_snapshot_ship_from_address_zip_code,
        ${dbTableAlias}.request_snapshot_ship_to_address_addr1,
        ${dbTableAlias}.request_snapshot_ship_to_address_addr2,
        ${dbTableAlias}.request_snapshot_ship_to_address_addr3,
        ${dbTableAlias}.request_snapshot_ship_to_address_city,
        ${dbTableAlias}.request_snapshot_ship_to_address_company,
        ${dbTableAlias}.request_snapshot_ship_to_address_country,
        ${dbTableAlias}.request_snapshot_ship_to_address_email,
        ${dbTableAlias}.request_snapshot_ship_to_address_is_residential,
        ${dbTableAlias}.request_snapshot_ship_to_address_name,
        ${dbTableAlias}.request_snapshot_ship_to_address_note,
        ${dbTableAlias}.request_snapshot_ship_to_address_phone,
        ${dbTableAlias}.request_snapshot_ship_to_address_state,
        ${dbTableAlias}.request_snapshot_ship_to_address_zip_code,
        ${dbTableAlias}.request_snapshot_ship_window_end,
        ${dbTableAlias}.request_snapshot_ship_window_start,
        ${dbTableAlias}.request_snapshot_transaction_partner_id,
        ${dbTableAlias}.warehouse_id,
        ${dbTableAlias}.workorder_prep_status,
        ${dbTableAlias}.workorder_type
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ow"/>
            </include>
        FROM
        oco_workorder ow
        WHERE
            ow.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ow"/>
                    <property name="qoTableAlias" value="qoow"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ow"/>
                <property name="qoTableAlias" value="qoow"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoow.timeZone != null and qoow.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoow.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_workorder ow
        WHERE
            ow.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ow"/>
                    <property name="qoTableAlias" value="qoow"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoow.timeZone != null and qoow.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoow.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.ocoArrangementId != null">
            AND ${dbTableAlias}.oco_arrangement_id = #{${qoTableAlias}.ocoArrangementId}
        </if>
    <if test="${qoTableAlias}.ocoArrangementIdList != null and ${qoTableAlias}.ocoArrangementIdList.size > 0 ">
        AND ${dbTableAlias}.oco_arrangement_id in
        <foreach collection="${qoTableAlias}.ocoArrangementIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoRequestId != null">
            AND ${dbTableAlias}.oco_request_id = #{${qoTableAlias}.ocoRequestId}
        </if>
    <if test="${qoTableAlias}.ocoRequestIdList != null and ${qoTableAlias}.ocoRequestIdList.size > 0 ">
        AND ${dbTableAlias}.oco_request_id in
        <foreach collection="${qoTableAlias}.ocoRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderStatus != null and ${qoTableAlias}.ocoWorkorderStatus != ''">
            AND ${dbTableAlias}.oco_workorder_status = #{${qoTableAlias}.ocoWorkorderStatus}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderStatusList != null and ${qoTableAlias}.ocoWorkorderStatusList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_status in
        <foreach collection="${qoTableAlias}.ocoWorkorderStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotChannel != null and ${qoTableAlias}.requestSnapshotChannel != ''">
            AND ${dbTableAlias}.request_snapshot_channel = #{${qoTableAlias}.requestSnapshotChannel}
        </if>
    <if test="${qoTableAlias}.requestSnapshotChannelList != null and ${qoTableAlias}.requestSnapshotChannelList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_channel in
        <foreach collection="${qoTableAlias}.requestSnapshotChannelList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotNote != null and ${qoTableAlias}.requestSnapshotNote != ''">
            AND ${dbTableAlias}.request_snapshot_note = #{${qoTableAlias}.requestSnapshotNote}
        </if>
        <if test="${qoTableAlias}.requestSnapshotOrderNum != null and ${qoTableAlias}.requestSnapshotOrderNum != ''">
            AND ${dbTableAlias}.request_snapshot_order_num = #{${qoTableAlias}.requestSnapshotOrderNum}
        </if>
        <if test="${qoTableAlias}.requestSnapshotRefNum != null and ${qoTableAlias}.requestSnapshotRefNum != ''">
            AND ${dbTableAlias}.request_snapshot_ref_num = #{${qoTableAlias}.requestSnapshotRefNum}
        </if>
    <if test="${qoTableAlias}.requestSnapshotRefNumList != null and ${qoTableAlias}.requestSnapshotRefNumList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_ref_num in
        <foreach collection="${qoTableAlias}.requestSnapshotRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotRequestRefNum != null and ${qoTableAlias}.requestSnapshotRequestRefNum != ''">
            AND ${dbTableAlias}.request_snapshot_request_ref_num = #{${qoTableAlias}.requestSnapshotRequestRefNum}
        </if>
    <if test="${qoTableAlias}.requestSnapshotRequestRefNumList != null and ${qoTableAlias}.requestSnapshotRequestRefNumList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_request_ref_num in
        <foreach collection="${qoTableAlias}.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressAddr1 != null and ${qoTableAlias}.requestSnapshotShipFromAddressAddr1 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_addr1 = #{${qoTableAlias}.requestSnapshotShipFromAddressAddr1}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressAddr2 != null and ${qoTableAlias}.requestSnapshotShipFromAddressAddr2 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_addr2 = #{${qoTableAlias}.requestSnapshotShipFromAddressAddr2}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressAddr3 != null and ${qoTableAlias}.requestSnapshotShipFromAddressAddr3 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_addr3 = #{${qoTableAlias}.requestSnapshotShipFromAddressAddr3}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressCity != null and ${qoTableAlias}.requestSnapshotShipFromAddressCity != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_city = #{${qoTableAlias}.requestSnapshotShipFromAddressCity}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressCompany != null and ${qoTableAlias}.requestSnapshotShipFromAddressCompany != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_company = #{${qoTableAlias}.requestSnapshotShipFromAddressCompany}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressCountry != null and ${qoTableAlias}.requestSnapshotShipFromAddressCountry != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_country = #{${qoTableAlias}.requestSnapshotShipFromAddressCountry}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressEmail != null and ${qoTableAlias}.requestSnapshotShipFromAddressEmail != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_email = #{${qoTableAlias}.requestSnapshotShipFromAddressEmail}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressIsResidential != null">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_is_residential = #{${qoTableAlias}.requestSnapshotShipFromAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressName != null and ${qoTableAlias}.requestSnapshotShipFromAddressName != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_name = #{${qoTableAlias}.requestSnapshotShipFromAddressName}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressNote != null and ${qoTableAlias}.requestSnapshotShipFromAddressNote != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_note = #{${qoTableAlias}.requestSnapshotShipFromAddressNote}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressPhone != null and ${qoTableAlias}.requestSnapshotShipFromAddressPhone != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_phone = #{${qoTableAlias}.requestSnapshotShipFromAddressPhone}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressState != null and ${qoTableAlias}.requestSnapshotShipFromAddressState != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_state = #{${qoTableAlias}.requestSnapshotShipFromAddressState}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipFromAddressZipCode != null and ${qoTableAlias}.requestSnapshotShipFromAddressZipCode != ''">
            AND ${dbTableAlias}.request_snapshot_ship_from_address_zip_code = #{${qoTableAlias}.requestSnapshotShipFromAddressZipCode}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressAddr1 != null and ${qoTableAlias}.requestSnapshotShipToAddressAddr1 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_addr1 = #{${qoTableAlias}.requestSnapshotShipToAddressAddr1}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressAddr2 != null and ${qoTableAlias}.requestSnapshotShipToAddressAddr2 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_addr2 = #{${qoTableAlias}.requestSnapshotShipToAddressAddr2}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressAddr3 != null and ${qoTableAlias}.requestSnapshotShipToAddressAddr3 != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_addr3 = #{${qoTableAlias}.requestSnapshotShipToAddressAddr3}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressCity != null and ${qoTableAlias}.requestSnapshotShipToAddressCity != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_city = #{${qoTableAlias}.requestSnapshotShipToAddressCity}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressCompany != null and ${qoTableAlias}.requestSnapshotShipToAddressCompany != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_company = #{${qoTableAlias}.requestSnapshotShipToAddressCompany}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressCountry != null and ${qoTableAlias}.requestSnapshotShipToAddressCountry != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_country = #{${qoTableAlias}.requestSnapshotShipToAddressCountry}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressEmail != null and ${qoTableAlias}.requestSnapshotShipToAddressEmail != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_email = #{${qoTableAlias}.requestSnapshotShipToAddressEmail}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressIsResidential != null">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_is_residential = #{${qoTableAlias}.requestSnapshotShipToAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressName != null and ${qoTableAlias}.requestSnapshotShipToAddressName != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_name = #{${qoTableAlias}.requestSnapshotShipToAddressName}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressNote != null and ${qoTableAlias}.requestSnapshotShipToAddressNote != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_note = #{${qoTableAlias}.requestSnapshotShipToAddressNote}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressPhone != null and ${qoTableAlias}.requestSnapshotShipToAddressPhone != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_phone = #{${qoTableAlias}.requestSnapshotShipToAddressPhone}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressState != null and ${qoTableAlias}.requestSnapshotShipToAddressState != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_state = #{${qoTableAlias}.requestSnapshotShipToAddressState}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipToAddressZipCode != null and ${qoTableAlias}.requestSnapshotShipToAddressZipCode != ''">
            AND ${dbTableAlias}.request_snapshot_ship_to_address_zip_code = #{${qoTableAlias}.requestSnapshotShipToAddressZipCode}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowEndStart != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_end  &gt;= #{${qoTableAlias}.requestSnapshotShipWindowEndStart}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowEndEnd != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_end  &lt; #{${qoTableAlias}.requestSnapshotShipWindowEndEnd}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowStartStart != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_start  &gt;= #{${qoTableAlias}.requestSnapshotShipWindowStartStart}
        </if>
        <if test="${qoTableAlias}.requestSnapshotShipWindowStartEnd != null">
            AND ${dbTableAlias}.request_snapshot_ship_window_start  &lt; #{${qoTableAlias}.requestSnapshotShipWindowStartEnd}
        </if>
        <if test="${qoTableAlias}.requestSnapshotTransactionPartnerId != null">
            AND ${dbTableAlias}.request_snapshot_transaction_partner_id = #{${qoTableAlias}.requestSnapshotTransactionPartnerId}
        </if>
    <if test="${qoTableAlias}.requestSnapshotTransactionPartnerIdList != null and ${qoTableAlias}.requestSnapshotTransactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.request_snapshot_transaction_partner_id in
        <foreach collection="${qoTableAlias}.requestSnapshotTransactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.workorderPrepStatus != null and ${qoTableAlias}.workorderPrepStatus != ''">
            AND ${dbTableAlias}.workorder_prep_status = #{${qoTableAlias}.workorderPrepStatus}
        </if>
    <if test="${qoTableAlias}.workorderPrepStatusList != null and ${qoTableAlias}.workorderPrepStatusList.size > 0 ">
        AND ${dbTableAlias}.workorder_prep_status in
        <foreach collection="${qoTableAlias}.workorderPrepStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.workorderType != null and ${qoTableAlias}.workorderType != ''">
            AND ${dbTableAlias}.workorder_type = #{${qoTableAlias}.workorderType}
        </if>
    <if test="${qoTableAlias}.workorderTypeList != null and ${qoTableAlias}.workorderTypeList.size > 0 ">
        AND ${dbTableAlias}.workorder_type in
        <foreach collection="${qoTableAlias}.workorderTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ow"/>
           </include>
        FROM
            oco_workorder ow
        WHERE
            ow.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="ow" />
                <property name="qoTableAlias" value="qoow" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="ow" />
                <property name="qoTableAlias" value="qoow" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ow"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_workorder ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoWorkorderMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
