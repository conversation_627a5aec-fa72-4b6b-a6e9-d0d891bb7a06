<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPackageLabelMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPackageLabel">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="file_id_raw_data_type" property="fileIdRawDataType" />
        <result column="label_raw_data" property="labelRawData" />
        <result column="label_ref_num" property="labelRefNum" />
        <result column="label_type" property="labelType" />
        <result column="line_num" property="lineNum" />
        <result column="oco_package_id" property="ocoPackageId" />
        <result column="paper_type" property="paperType" />
        <result column="print_status" property="printStatus" />
        <result column="raw_data_type" property="rawDataType" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.file_id_raw_data_type,
        ${dbTableAlias}.label_raw_data,
        ${dbTableAlias}.label_ref_num,
        ${dbTableAlias}.label_type,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.oco_package_id,
        ${dbTableAlias}.paper_type,
        ${dbTableAlias}.print_status,
        ${dbTableAlias}.raw_data_type,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPackageLabelPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opl"/>
            </include>
        FROM
        oco_package_label opl
        WHERE
            opl.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opl"/>
                    <property name="qoTableAlias" value="qoopl"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opl"/>
                <property name="qoTableAlias" value="qoopl"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopl.timeZone != null and qoopl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopl.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_package_label opl
        WHERE
            opl.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opl"/>
                    <property name="qoTableAlias" value="qoopl"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopl.timeZone != null and qoopl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopl.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.fileIdRawDataType != null and ${qoTableAlias}.fileIdRawDataType != ''">
            AND ${dbTableAlias}.file_id_raw_data_type = #{${qoTableAlias}.fileIdRawDataType}
        </if>
    <if test="${qoTableAlias}.fileIdRawDataTypeList != null and ${qoTableAlias}.fileIdRawDataTypeList.size > 0 ">
        AND ${dbTableAlias}.file_id_raw_data_type in
        <foreach collection="${qoTableAlias}.fileIdRawDataTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.labelRawData != null and ${qoTableAlias}.labelRawData != ''">
            AND ${dbTableAlias}.label_raw_data = #{${qoTableAlias}.labelRawData}
        </if>
        <if test="${qoTableAlias}.labelRefNum != null and ${qoTableAlias}.labelRefNum != ''">
            AND ${dbTableAlias}.label_ref_num = #{${qoTableAlias}.labelRefNum}
        </if>
    <if test="${qoTableAlias}.labelRefNumList != null and ${qoTableAlias}.labelRefNumList.size > 0 ">
        AND ${dbTableAlias}.label_ref_num in
        <foreach collection="${qoTableAlias}.labelRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.labelType != null and ${qoTableAlias}.labelType != ''">
            AND ${dbTableAlias}.label_type = #{${qoTableAlias}.labelType}
        </if>
    <if test="${qoTableAlias}.labelTypeList != null and ${qoTableAlias}.labelTypeList.size > 0 ">
        AND ${dbTableAlias}.label_type in
        <foreach collection="${qoTableAlias}.labelTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lineNum != null">
            AND ${dbTableAlias}.line_num = #{${qoTableAlias}.lineNum}
        </if>
        <if test="${qoTableAlias}.ocoPackageId != null">
            AND ${dbTableAlias}.oco_package_id = #{${qoTableAlias}.ocoPackageId}
        </if>
    <if test="${qoTableAlias}.ocoPackageIdList != null and ${qoTableAlias}.ocoPackageIdList.size > 0 ">
        AND ${dbTableAlias}.oco_package_id in
        <foreach collection="${qoTableAlias}.ocoPackageIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.paperType != null and ${qoTableAlias}.paperType != ''">
            AND ${dbTableAlias}.paper_type = #{${qoTableAlias}.paperType}
        </if>
    <if test="${qoTableAlias}.paperTypeList != null and ${qoTableAlias}.paperTypeList.size > 0 ">
        AND ${dbTableAlias}.paper_type in
        <foreach collection="${qoTableAlias}.paperTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.printStatus != null and ${qoTableAlias}.printStatus != ''">
            AND ${dbTableAlias}.print_status = #{${qoTableAlias}.printStatus}
        </if>
    <if test="${qoTableAlias}.printStatusList != null and ${qoTableAlias}.printStatusList.size > 0 ">
        AND ${dbTableAlias}.print_status in
        <foreach collection="${qoTableAlias}.printStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.rawDataType != null and ${qoTableAlias}.rawDataType != ''">
            AND ${dbTableAlias}.raw_data_type = #{${qoTableAlias}.rawDataType}
        </if>
    <if test="${qoTableAlias}.rawDataTypeList != null and ${qoTableAlias}.rawDataTypeList.size > 0 ">
        AND ${dbTableAlias}.raw_data_type in
        <foreach collection="${qoTableAlias}.rawDataTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPackageLabelPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opl"/>
           </include>
        FROM
            oco_package_label opl
        WHERE
            opl.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="opl" />
                <property name="qoTableAlias" value="qoopl" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="opl" />
                <property name="qoTableAlias" value="qoopl" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opl"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_package_label ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPackageLabelMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
