<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoWorkorderBinLocationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoWorkorderBinLocation">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_detail_locked_id" property="binLocationDetailLockedId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="oco_picking_slip_detail_id" property="ocoPickingSlipDetailId" />
        <result column="oco_picking_slip_id" property="ocoPickingSlipId" />
        <result column="oco_workorder_detail_id" property="ocoWorkorderDetailId" />
        <result column="oco_workorder_id" property="ocoWorkorderId" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
        <result column="qty" property="qty" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.bin_location_detail_id,
        ${dbTableAlias}.bin_location_detail_locked_id,
        ${dbTableAlias}.bin_location_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.hazmat_version_ref_num,
        ${dbTableAlias}.oco_picking_slip_detail_id,
        ${dbTableAlias}.oco_picking_slip_id,
        ${dbTableAlias}.oco_workorder_detail_id,
        ${dbTableAlias}.oco_workorder_id,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.product_version_id,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderBinLocationPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="owbl"/>
            </include>
        FROM
        oco_workorder_bin_location owbl
        WHERE
            owbl.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="owbl"/>
                    <property name="qoTableAlias" value="qoowbl"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="owbl"/>
                <property name="qoTableAlias" value="qoowbl"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoowbl.timeZone != null and qoowbl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoowbl.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_workorder_bin_location owbl
        WHERE
            owbl.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="owbl"/>
                    <property name="qoTableAlias" value="qoowbl"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoowbl.timeZone != null and qoowbl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoowbl.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.binLocationDetailId != null">
            AND ${dbTableAlias}.bin_location_detail_id = #{${qoTableAlias}.binLocationDetailId}
        </if>
    <if test="${qoTableAlias}.binLocationDetailIdList != null and ${qoTableAlias}.binLocationDetailIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_detail_id in
        <foreach collection="${qoTableAlias}.binLocationDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationDetailLockedId != null">
            AND ${dbTableAlias}.bin_location_detail_locked_id = #{${qoTableAlias}.binLocationDetailLockedId}
        </if>
    <if test="${qoTableAlias}.binLocationDetailLockedIdList != null and ${qoTableAlias}.binLocationDetailLockedIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_detail_locked_id in
        <foreach collection="${qoTableAlias}.binLocationDetailLockedIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationId != null">
            AND ${dbTableAlias}.bin_location_id = #{${qoTableAlias}.binLocationId}
        </if>
    <if test="${qoTableAlias}.binLocationIdList != null and ${qoTableAlias}.binLocationIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_id in
        <foreach collection="${qoTableAlias}.binLocationIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.hazmatVersionRefNum != null and ${qoTableAlias}.hazmatVersionRefNum != ''">
            AND ${dbTableAlias}.hazmat_version_ref_num = #{${qoTableAlias}.hazmatVersionRefNum}
        </if>
    <if test="${qoTableAlias}.hazmatVersionRefNumList != null and ${qoTableAlias}.hazmatVersionRefNumList.size > 0 ">
        AND ${dbTableAlias}.hazmat_version_ref_num in
        <foreach collection="${qoTableAlias}.hazmatVersionRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPickingSlipDetailId != null">
            AND ${dbTableAlias}.oco_picking_slip_detail_id = #{${qoTableAlias}.ocoPickingSlipDetailId}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipDetailIdList != null and ${qoTableAlias}.ocoPickingSlipDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_detail_id in
        <foreach collection="${qoTableAlias}.ocoPickingSlipDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPickingSlipId != null">
            AND ${dbTableAlias}.oco_picking_slip_id = #{${qoTableAlias}.ocoPickingSlipId}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipIdList != null and ${qoTableAlias}.ocoPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_id in
        <foreach collection="${qoTableAlias}.ocoPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderDetailId != null">
            AND ${dbTableAlias}.oco_workorder_detail_id = #{${qoTableAlias}.ocoWorkorderDetailId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderDetailIdList != null and ${qoTableAlias}.ocoWorkorderDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_detail_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderId != null">
            AND ${dbTableAlias}.oco_workorder_id = #{${qoTableAlias}.ocoWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderIdList != null and ${qoTableAlias}.ocoWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productVersionId != null">
            AND ${dbTableAlias}.product_version_id = #{${qoTableAlias}.productVersionId}
        </if>
    <if test="${qoTableAlias}.productVersionIdList != null and ${qoTableAlias}.productVersionIdList.size > 0 ">
        AND ${dbTableAlias}.product_version_id in
        <foreach collection="${qoTableAlias}.productVersionIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderBinLocationPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="owbl"/>
           </include>
        FROM
            oco_workorder_bin_location owbl
        WHERE
            owbl.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="owbl" />
                <property name="qoTableAlias" value="qoowbl" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="owbl" />
                <property name="qoTableAlias" value="qoowbl" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="owbl"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_workorder_bin_location ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoWorkorderBinLocationMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
