<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPrepPickingSlipMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPrepPickingSlip">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="allocate_putaway_qty" property="allocatePutawayQty" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="description" property="description" />
        <result column="has_cus_ship_require" property="hasCusShipRequire" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="locked_before" property="lockedBefore" />
        <result column="oco_picking_slip_id" property="ocoPickingSlipId" />
        <result column="on_site_pack_flag" property="onSitePackFlag" />
        <result column="order_type" property="orderType" />
        <result column="prep_picking_slip_product_type" property="prepPickingSlipProductType" />
        <result column="prep_picking_slip_status" property="prepPickingSlipStatus" />
        <result column="prep_picking_slip_type" property="prepPickingSlipType" />
        <result column="prep_picking_slip_version_int" property="prepPickingSlipVersionInt" />
        <result column="product_barcode" property="productBarcode" />
        <result column="product_channel_sku" property="productChannelSku" />
        <result column="product_id" property="productId" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="qty" property="qty" />
        <result column="ref_num" property="refNum" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.allocate_putaway_qty,
        ${dbTableAlias}.assigned_user_id,
        ${dbTableAlias}.bin_location_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.description,
        ${dbTableAlias}.has_cus_ship_require,
        ${dbTableAlias}.hazmat_version_ref_num,
        ${dbTableAlias}.locked_before,
        ${dbTableAlias}.oco_picking_slip_id,
        ${dbTableAlias}.on_site_pack_flag,
        ${dbTableAlias}.order_type,
        ${dbTableAlias}.prep_picking_slip_product_type,
        ${dbTableAlias}.prep_picking_slip_status,
        ${dbTableAlias}.prep_picking_slip_type,
        ${dbTableAlias}.prep_picking_slip_version_int,
        ${dbTableAlias}.product_barcode,
        ${dbTableAlias}.product_channel_sku,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.putaway_qty,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.transaction_partner_id,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opps"/>
            </include>
        FROM
        oco_prep_picking_slip opps
        WHERE
            opps.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opps"/>
                    <property name="qoTableAlias" value="qoopps"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opps"/>
                <property name="qoTableAlias" value="qoopps"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopps.timeZone != null and qoopps.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopps.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_prep_picking_slip opps
        WHERE
            opps.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opps"/>
                    <property name="qoTableAlias" value="qoopps"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopps.timeZone != null and qoopps.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopps.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.allocatePutawayQty != null">
            AND ${dbTableAlias}.allocate_putaway_qty = #{${qoTableAlias}.allocatePutawayQty}
        </if>
        <if test="${qoTableAlias}.assignedUserId != null">
            AND ${dbTableAlias}.assigned_user_id = #{${qoTableAlias}.assignedUserId}
        </if>
    <if test="${qoTableAlias}.assignedUserIdList != null and ${qoTableAlias}.assignedUserIdList.size > 0 ">
        AND ${dbTableAlias}.assigned_user_id in
        <foreach collection="${qoTableAlias}.assignedUserIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationId != null">
            AND ${dbTableAlias}.bin_location_id = #{${qoTableAlias}.binLocationId}
        </if>
    <if test="${qoTableAlias}.binLocationIdList != null and ${qoTableAlias}.binLocationIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_id in
        <foreach collection="${qoTableAlias}.binLocationIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.description != null and ${qoTableAlias}.description != ''">
            AND ${dbTableAlias}.description = #{${qoTableAlias}.description}
        </if>
        <if test="${qoTableAlias}.hasCusShipRequire != null">
            AND ${dbTableAlias}.has_cus_ship_require = #{${qoTableAlias}.hasCusShipRequire}
        </if>
        <if test="${qoTableAlias}.hazmatVersionRefNum != null and ${qoTableAlias}.hazmatVersionRefNum != ''">
            AND ${dbTableAlias}.hazmat_version_ref_num = #{${qoTableAlias}.hazmatVersionRefNum}
        </if>
    <if test="${qoTableAlias}.hazmatVersionRefNumList != null and ${qoTableAlias}.hazmatVersionRefNumList.size > 0 ">
        AND ${dbTableAlias}.hazmat_version_ref_num in
        <foreach collection="${qoTableAlias}.hazmatVersionRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lockedBefore != null and ${qoTableAlias}.lockedBefore != ''">
            AND ${dbTableAlias}.locked_before = #{${qoTableAlias}.lockedBefore}
        </if>
        <if test="${qoTableAlias}.ocoPickingSlipId != null">
            AND ${dbTableAlias}.oco_picking_slip_id = #{${qoTableAlias}.ocoPickingSlipId}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipIdList != null and ${qoTableAlias}.ocoPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_id in
        <foreach collection="${qoTableAlias}.ocoPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.onSitePackFlag != null">
            AND ${dbTableAlias}.on_site_pack_flag = #{${qoTableAlias}.onSitePackFlag}
        </if>
        <if test="${qoTableAlias}.orderType != null and ${qoTableAlias}.orderType != ''">
            AND ${dbTableAlias}.order_type = #{${qoTableAlias}.orderType}
        </if>
    <if test="${qoTableAlias}.orderTypeList != null and ${qoTableAlias}.orderTypeList.size > 0 ">
        AND ${dbTableAlias}.order_type in
        <foreach collection="${qoTableAlias}.orderTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepPickingSlipProductType != null and ${qoTableAlias}.prepPickingSlipProductType != ''">
            AND ${dbTableAlias}.prep_picking_slip_product_type = #{${qoTableAlias}.prepPickingSlipProductType}
        </if>
    <if test="${qoTableAlias}.prepPickingSlipProductTypeList != null and ${qoTableAlias}.prepPickingSlipProductTypeList.size > 0 ">
        AND ${dbTableAlias}.prep_picking_slip_product_type in
        <foreach collection="${qoTableAlias}.prepPickingSlipProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepPickingSlipStatus != null and ${qoTableAlias}.prepPickingSlipStatus != ''">
            AND ${dbTableAlias}.prep_picking_slip_status = #{${qoTableAlias}.prepPickingSlipStatus}
        </if>
    <if test="${qoTableAlias}.prepPickingSlipStatusList != null and ${qoTableAlias}.prepPickingSlipStatusList.size > 0 ">
        AND ${dbTableAlias}.prep_picking_slip_status in
        <foreach collection="${qoTableAlias}.prepPickingSlipStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepPickingSlipType != null and ${qoTableAlias}.prepPickingSlipType != ''">
            AND ${dbTableAlias}.prep_picking_slip_type = #{${qoTableAlias}.prepPickingSlipType}
        </if>
    <if test="${qoTableAlias}.prepPickingSlipTypeList != null and ${qoTableAlias}.prepPickingSlipTypeList.size > 0 ">
        AND ${dbTableAlias}.prep_picking_slip_type in
        <foreach collection="${qoTableAlias}.prepPickingSlipTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepPickingSlipVersionInt != null">
            AND ${dbTableAlias}.prep_picking_slip_version_int = #{${qoTableAlias}.prepPickingSlipVersionInt}
        </if>
        <if test="${qoTableAlias}.productBarcode != null and ${qoTableAlias}.productBarcode != ''">
            AND ${dbTableAlias}.product_barcode = #{${qoTableAlias}.productBarcode}
        </if>
    <if test="${qoTableAlias}.productBarcodeList != null and ${qoTableAlias}.productBarcodeList.size > 0 ">
        AND ${dbTableAlias}.product_barcode in
        <foreach collection="${qoTableAlias}.productBarcodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productChannelSku != null and ${qoTableAlias}.productChannelSku != ''">
            AND ${dbTableAlias}.product_channel_sku = #{${qoTableAlias}.productChannelSku}
        </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.putawayQty != null">
            AND ${dbTableAlias}.putaway_qty = #{${qoTableAlias}.putawayQty}
        </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.transactionPartnerId != null">
            AND ${dbTableAlias}.transaction_partner_id = #{${qoTableAlias}.transactionPartnerId}
        </if>
    <if test="${qoTableAlias}.transactionPartnerIdList != null and ${qoTableAlias}.transactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.transaction_partner_id in
        <foreach collection="${qoTableAlias}.transactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepPickingSlipPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opps"/>
           </include>
        FROM
            oco_prep_picking_slip opps
        WHERE
            opps.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="opps" />
                <property name="qoTableAlias" value="qoopps" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="opps" />
                <property name="qoTableAlias" value="qoopps" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opps"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_prep_picking_slip ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPrepPickingSlipMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
