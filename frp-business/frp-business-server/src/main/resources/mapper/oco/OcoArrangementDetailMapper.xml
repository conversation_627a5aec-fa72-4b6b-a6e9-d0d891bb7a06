<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoArrangementDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoArrangementDetail">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="allocated_qty" property="allocatedQty" />
        <result column="deleted_note" property="deletedNote" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="line_num" property="lineNum" />
        <result column="note" property="note" />
        <result column="oco_arrangement_id" property="ocoArrangementId" />
        <result column="oco_request_detail_id" property="ocoRequestDetailId" />
        <result column="oco_request_id" property="ocoRequestId" />
        <result column="product_barcode" property="productBarcode" />
        <result column="product_channel_sku" property="productChannelSku" />
        <result column="product_id" property="productId" />
        <result column="qty" property="qty" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.allocated_qty,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.hazmat_version_ref_num,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.note,
        ${dbTableAlias}.oco_arrangement_id,
        ${dbTableAlias}.oco_request_detail_id,
        ${dbTableAlias}.oco_request_id,
        ${dbTableAlias}.product_barcode,
        ${dbTableAlias}.product_channel_sku,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoArrangementDetailPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oad"/>
            </include>
        FROM
        oco_arrangement_detail oad
        WHERE
            oad.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oad"/>
                    <property name="qoTableAlias" value="qooad"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oad"/>
                <property name="qoTableAlias" value="qooad"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooad.timeZone != null and qooad.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooad.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_arrangement_detail oad
        WHERE
            oad.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oad"/>
                    <property name="qoTableAlias" value="qooad"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooad.timeZone != null and qooad.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooad.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.allocatedQty != null">
            AND ${dbTableAlias}.allocated_qty = #{${qoTableAlias}.allocatedQty}
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.hazmatVersionRefNum != null and ${qoTableAlias}.hazmatVersionRefNum != ''">
            AND ${dbTableAlias}.hazmat_version_ref_num = #{${qoTableAlias}.hazmatVersionRefNum}
        </if>
    <if test="${qoTableAlias}.hazmatVersionRefNumList != null and ${qoTableAlias}.hazmatVersionRefNumList.size > 0 ">
        AND ${dbTableAlias}.hazmat_version_ref_num in
        <foreach collection="${qoTableAlias}.hazmatVersionRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lineNum != null">
            AND ${dbTableAlias}.line_num = #{${qoTableAlias}.lineNum}
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.ocoArrangementId != null">
            AND ${dbTableAlias}.oco_arrangement_id = #{${qoTableAlias}.ocoArrangementId}
        </if>
    <if test="${qoTableAlias}.ocoArrangementIdList != null and ${qoTableAlias}.ocoArrangementIdList.size > 0 ">
        AND ${dbTableAlias}.oco_arrangement_id in
        <foreach collection="${qoTableAlias}.ocoArrangementIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoRequestDetailId != null">
            AND ${dbTableAlias}.oco_request_detail_id = #{${qoTableAlias}.ocoRequestDetailId}
        </if>
    <if test="${qoTableAlias}.ocoRequestDetailIdList != null and ${qoTableAlias}.ocoRequestDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_request_detail_id in
        <foreach collection="${qoTableAlias}.ocoRequestDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoRequestId != null">
            AND ${dbTableAlias}.oco_request_id = #{${qoTableAlias}.ocoRequestId}
        </if>
    <if test="${qoTableAlias}.ocoRequestIdList != null and ${qoTableAlias}.ocoRequestIdList.size > 0 ">
        AND ${dbTableAlias}.oco_request_id in
        <foreach collection="${qoTableAlias}.ocoRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productBarcode != null and ${qoTableAlias}.productBarcode != ''">
            AND ${dbTableAlias}.product_barcode = #{${qoTableAlias}.productBarcode}
        </if>
    <if test="${qoTableAlias}.productBarcodeList != null and ${qoTableAlias}.productBarcodeList.size > 0 ">
        AND ${dbTableAlias}.product_barcode in
        <foreach collection="${qoTableAlias}.productBarcodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productChannelSku != null and ${qoTableAlias}.productChannelSku != ''">
            AND ${dbTableAlias}.product_channel_sku = #{${qoTableAlias}.productChannelSku}
        </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoArrangementDetailPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oad"/>
           </include>
        FROM
            oco_arrangement_detail oad
        WHERE
            oad.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="oad" />
                <property name="qoTableAlias" value="qooad" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="oad" />
                <property name="qoTableAlias" value="qooad" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oad"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_arrangement_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoArrangementDetailMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>