<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoContainerMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoContainer">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="actual_height" property="actualHeight" />
        <result column="actual_length" property="actualLength" />
        <result column="actual_volume" property="actualVolume" />
        <result column="actual_weight" property="actualWeight" />
        <result column="actual_width" property="actualWidth" />
        <result column="container_num" property="containerNum" />
        <result column="container_template_id" property="containerTemplateId" />
        <result column="container_type" property="containerType" />
        <result column="deleted_note" property="deletedNote" />
        <result column="oco_arrangement_id" property="ocoArrangementId" />
        <result column="oco_container_status" property="ocoContainerStatus" />
        <result column="package_count" property="packageCount" />
        <result column="ref_num" property="refNum" />
        <result column="total_volume" property="totalVolume" />
        <result column="total_weight" property="totalWeight" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.actual_height,
        ${dbTableAlias}.actual_length,
        ${dbTableAlias}.actual_volume,
        ${dbTableAlias}.actual_weight,
        ${dbTableAlias}.actual_width,
        ${dbTableAlias}.container_num,
        ${dbTableAlias}.container_template_id,
        ${dbTableAlias}.container_type,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.oco_arrangement_id,
        ${dbTableAlias}.oco_container_status,
        ${dbTableAlias}.package_count,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.total_volume,
        ${dbTableAlias}.total_weight,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoContainerPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oc"/>
            </include>
        FROM
        oco_container oc
        WHERE
            oc.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oc"/>
                    <property name="qoTableAlias" value="qooc"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oc"/>
                <property name="qoTableAlias" value="qooc"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooc.timeZone != null and qooc.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooc.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_container oc
        WHERE
            oc.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oc"/>
                    <property name="qoTableAlias" value="qooc"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooc.timeZone != null and qooc.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooc.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.actualHeight != null">
            AND ${dbTableAlias}.actual_height = #{${qoTableAlias}.actualHeight}
        </if>
        <if test="${qoTableAlias}.actualLength != null">
            AND ${dbTableAlias}.actual_length = #{${qoTableAlias}.actualLength}
        </if>
        <if test="${qoTableAlias}.actualVolume != null">
            AND ${dbTableAlias}.actual_volume = #{${qoTableAlias}.actualVolume}
        </if>
        <if test="${qoTableAlias}.actualWeight != null">
            AND ${dbTableAlias}.actual_weight = #{${qoTableAlias}.actualWeight}
        </if>
        <if test="${qoTableAlias}.actualWidth != null">
            AND ${dbTableAlias}.actual_width = #{${qoTableAlias}.actualWidth}
        </if>
        <if test="${qoTableAlias}.containerNum != null and ${qoTableAlias}.containerNum != ''">
            AND ${dbTableAlias}.container_num = #{${qoTableAlias}.containerNum}
        </if>
        <if test="${qoTableAlias}.containerTemplateId != null">
            AND ${dbTableAlias}.container_template_id = #{${qoTableAlias}.containerTemplateId}
        </if>
    <if test="${qoTableAlias}.containerTemplateIdList != null and ${qoTableAlias}.containerTemplateIdList.size > 0 ">
        AND ${dbTableAlias}.container_template_id in
        <foreach collection="${qoTableAlias}.containerTemplateIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.containerType != null and ${qoTableAlias}.containerType != ''">
            AND ${dbTableAlias}.container_type = #{${qoTableAlias}.containerType}
        </if>
    <if test="${qoTableAlias}.containerTypeList != null and ${qoTableAlias}.containerTypeList.size > 0 ">
        AND ${dbTableAlias}.container_type in
        <foreach collection="${qoTableAlias}.containerTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.ocoArrangementId != null">
            AND ${dbTableAlias}.oco_arrangement_id = #{${qoTableAlias}.ocoArrangementId}
        </if>
    <if test="${qoTableAlias}.ocoArrangementIdList != null and ${qoTableAlias}.ocoArrangementIdList.size > 0 ">
        AND ${dbTableAlias}.oco_arrangement_id in
        <foreach collection="${qoTableAlias}.ocoArrangementIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoContainerStatus != null and ${qoTableAlias}.ocoContainerStatus != ''">
            AND ${dbTableAlias}.oco_container_status = #{${qoTableAlias}.ocoContainerStatus}
        </if>
    <if test="${qoTableAlias}.ocoContainerStatusList != null and ${qoTableAlias}.ocoContainerStatusList.size > 0 ">
        AND ${dbTableAlias}.oco_container_status in
        <foreach collection="${qoTableAlias}.ocoContainerStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.packageCount != null">
            AND ${dbTableAlias}.package_count = #{${qoTableAlias}.packageCount}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.totalVolume != null">
            AND ${dbTableAlias}.total_volume = #{${qoTableAlias}.totalVolume}
        </if>
        <if test="${qoTableAlias}.totalWeight != null">
            AND ${dbTableAlias}.total_weight = #{${qoTableAlias}.totalWeight}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoContainerPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oc"/>
           </include>
        FROM
            oco_container oc
        WHERE
            oc.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="oc" />
                <property name="qoTableAlias" value="qooc" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="oc" />
                <property name="qoTableAlias" value="qooc" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oc"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_container ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoContainerMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
