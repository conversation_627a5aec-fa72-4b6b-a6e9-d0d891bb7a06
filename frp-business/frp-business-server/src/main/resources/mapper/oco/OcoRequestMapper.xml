<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoRequestMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoRequest">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="channel" property="channel" />
        <result column="deleted_note" property="deletedNote" />
        <result column="fee_calculation_time" property="feeCalculationTime" />
        <result column="fee_status" property="feeStatus" />
        <result column="note" property="note" />
        <result column="order_num" property="orderNum" />
        <result column="process_end_time" property="processEndTime" />
        <result column="process_start_time" property="processStartTime" />
        <result column="ref_num" property="refNum" />
        <result column="request_ref_num" property="requestRefNum" />
        <result column="request_status" property="requestStatus" />
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1" />
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2" />
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3" />
        <result column="ship_from_address_city" property="shipFromAddressCity" />
        <result column="ship_from_address_company" property="shipFromAddressCompany" />
        <result column="ship_from_address_country" property="shipFromAddressCountry" />
        <result column="ship_from_address_email" property="shipFromAddressEmail" />
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential" />
        <result column="ship_from_address_name" property="shipFromAddressName" />
        <result column="ship_from_address_note" property="shipFromAddressNote" />
        <result column="ship_from_address_phone" property="shipFromAddressPhone" />
        <result column="ship_from_address_state" property="shipFromAddressState" />
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode" />
        <result column="ship_to_address_addr1" property="shipToAddressAddr1" />
        <result column="ship_to_address_addr2" property="shipToAddressAddr2" />
        <result column="ship_to_address_addr3" property="shipToAddressAddr3" />
        <result column="ship_to_address_city" property="shipToAddressCity" />
        <result column="ship_to_address_company" property="shipToAddressCompany" />
        <result column="ship_to_address_country" property="shipToAddressCountry" />
        <result column="ship_to_address_email" property="shipToAddressEmail" />
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential" />
        <result column="ship_to_address_name" property="shipToAddressName" />
        <result column="ship_to_address_note" property="shipToAddressNote" />
        <result column="ship_to_address_phone" property="shipToAddressPhone" />
        <result column="ship_to_address_state" property="shipToAddressState" />
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode" />
        <result column="ship_window_end" property="shipWindowEnd" />
        <result column="ship_window_start" property="shipWindowStart" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.channel,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.fee_calculation_time,
        ${dbTableAlias}.fee_status,
        ${dbTableAlias}.note,
        ${dbTableAlias}.order_num,
        ${dbTableAlias}.process_end_time,
        ${dbTableAlias}.process_start_time,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.request_ref_num,
        ${dbTableAlias}.request_status,
        ${dbTableAlias}.ship_from_address_addr1,
        ${dbTableAlias}.ship_from_address_addr2,
        ${dbTableAlias}.ship_from_address_addr3,
        ${dbTableAlias}.ship_from_address_city,
        ${dbTableAlias}.ship_from_address_company,
        ${dbTableAlias}.ship_from_address_country,
        ${dbTableAlias}.ship_from_address_email,
        ${dbTableAlias}.ship_from_address_is_residential,
        ${dbTableAlias}.ship_from_address_name,
        ${dbTableAlias}.ship_from_address_note,
        ${dbTableAlias}.ship_from_address_phone,
        ${dbTableAlias}.ship_from_address_state,
        ${dbTableAlias}.ship_from_address_zip_code,
        ${dbTableAlias}.ship_to_address_addr1,
        ${dbTableAlias}.ship_to_address_addr2,
        ${dbTableAlias}.ship_to_address_addr3,
        ${dbTableAlias}.ship_to_address_city,
        ${dbTableAlias}.ship_to_address_company,
        ${dbTableAlias}.ship_to_address_country,
        ${dbTableAlias}.ship_to_address_email,
        ${dbTableAlias}.ship_to_address_is_residential,
        ${dbTableAlias}.ship_to_address_name,
        ${dbTableAlias}.ship_to_address_note,
        ${dbTableAlias}.ship_to_address_phone,
        ${dbTableAlias}.ship_to_address_state,
        ${dbTableAlias}.ship_to_address_zip_code,
        ${dbTableAlias}.ship_window_end,
        ${dbTableAlias}.ship_window_start,
        ${dbTableAlias}.transaction_partner_id,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoRequestPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="eor"/>
            </include>
        FROM
        oco_request eor
        WHERE
            eor.remove_flag = 0
        <include refid="Base_Where_List">
            <property name="dbTableAlias" value="eor"/>
            <property name="qoTableAlias" value="qoor"/>
        </include>
        <include refid="Base_Order_By_List" >
            <property name="dbTableAlias" value="eor"/>
            <property name="qoTableAlias" value="qoor"/>
        </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoor.timeZone != null and qoor.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoor.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_request or
        WHERE
            or.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="eor"/>
                    <property name="qoTableAlias" value="qoor"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoor.timeZone != null and qoor.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoor.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.channel != null and ${qoTableAlias}.channel != ''">
            AND ${dbTableAlias}.channel = #{${qoTableAlias}.channel}
        </if>
    <if test="${qoTableAlias}.channelList != null and ${qoTableAlias}.channelList.size > 0 ">
        AND ${dbTableAlias}.channel in
        <foreach collection="${qoTableAlias}.channelList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeStart != null">
            AND ${dbTableAlias}.fee_calculation_time  &gt;= #{${qoTableAlias}.feeCalculationTimeStart}
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeEnd != null">
            AND ${dbTableAlias}.fee_calculation_time  &lt; #{${qoTableAlias}.feeCalculationTimeEnd}
        </if>
        <if test="${qoTableAlias}.feeStatus != null and ${qoTableAlias}.feeStatus != ''">
            AND ${dbTableAlias}.fee_status = #{${qoTableAlias}.feeStatus}
        </if>
    <if test="${qoTableAlias}.feeStatusList != null and ${qoTableAlias}.feeStatusList.size > 0 ">
        AND ${dbTableAlias}.fee_status in
        <foreach collection="${qoTableAlias}.feeStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.orderNum != null and ${qoTableAlias}.orderNum != ''">
            AND ${dbTableAlias}.order_num = #{${qoTableAlias}.orderNum}
        </if>
        <if test="${qoTableAlias}.processEndTimeStart != null">
            AND ${dbTableAlias}.process_end_time  &gt;= #{${qoTableAlias}.processEndTimeStart}
        </if>
        <if test="${qoTableAlias}.processEndTimeEnd != null">
            AND ${dbTableAlias}.process_end_time  &lt; #{${qoTableAlias}.processEndTimeEnd}
        </if>
        <if test="${qoTableAlias}.processStartTimeStart != null">
            AND ${dbTableAlias}.process_start_time  &gt;= #{${qoTableAlias}.processStartTimeStart}
        </if>
        <if test="${qoTableAlias}.processStartTimeEnd != null">
            AND ${dbTableAlias}.process_start_time  &lt; #{${qoTableAlias}.processStartTimeEnd}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestRefNum != null and ${qoTableAlias}.requestRefNum != ''">
            AND ${dbTableAlias}.request_ref_num = #{${qoTableAlias}.requestRefNum}
        </if>
    <if test="${qoTableAlias}.requestRefNumList != null and ${qoTableAlias}.requestRefNumList.size > 0 ">
        AND ${dbTableAlias}.request_ref_num in
        <foreach collection="${qoTableAlias}.requestRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestShipmentStatus != null and ${qoTableAlias}.requestShipmentStatus != ''">
            AND ${dbTableAlias}.request_shipment_status = #{${qoTableAlias}.requestShipmentStatus}
        </if>
    <if test="${qoTableAlias}.requestShipmentStatusList != null and ${qoTableAlias}.requestShipmentStatusList.size > 0 ">
        AND ${dbTableAlias}.request_shipment_status in
        <foreach collection="${qoTableAlias}.requestShipmentStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.requestStatus != null and ${qoTableAlias}.requestStatus != ''">
            AND ${dbTableAlias}.request_status = #{${qoTableAlias}.requestStatus}
        </if>
    <if test="${qoTableAlias}.requestStatusList != null and ${qoTableAlias}.requestStatusList.size > 0 ">
        AND ${dbTableAlias}.request_status in
        <foreach collection="${qoTableAlias}.requestStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.shipFromAddressAddr1 != null and ${qoTableAlias}.shipFromAddressAddr1 != ''">
            AND ${dbTableAlias}.ship_from_address_addr1 = #{${qoTableAlias}.shipFromAddressAddr1}
        </if>
        <if test="${qoTableAlias}.shipFromAddressAddr2 != null and ${qoTableAlias}.shipFromAddressAddr2 != ''">
            AND ${dbTableAlias}.ship_from_address_addr2 = #{${qoTableAlias}.shipFromAddressAddr2}
        </if>
        <if test="${qoTableAlias}.shipFromAddressAddr3 != null and ${qoTableAlias}.shipFromAddressAddr3 != ''">
            AND ${dbTableAlias}.ship_from_address_addr3 = #{${qoTableAlias}.shipFromAddressAddr3}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCity != null and ${qoTableAlias}.shipFromAddressCity != ''">
            AND ${dbTableAlias}.ship_from_address_city = #{${qoTableAlias}.shipFromAddressCity}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCompany != null and ${qoTableAlias}.shipFromAddressCompany != ''">
            AND ${dbTableAlias}.ship_from_address_company = #{${qoTableAlias}.shipFromAddressCompany}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCountry != null and ${qoTableAlias}.shipFromAddressCountry != ''">
            AND ${dbTableAlias}.ship_from_address_country = #{${qoTableAlias}.shipFromAddressCountry}
        </if>
        <if test="${qoTableAlias}.shipFromAddressEmail != null and ${qoTableAlias}.shipFromAddressEmail != ''">
            AND ${dbTableAlias}.ship_from_address_email = #{${qoTableAlias}.shipFromAddressEmail}
        </if>
        <if test="${qoTableAlias}.shipFromAddressIsResidential != null">
            AND ${dbTableAlias}.ship_from_address_is_residential = #{${qoTableAlias}.shipFromAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.shipFromAddressName != null and ${qoTableAlias}.shipFromAddressName != ''">
            AND ${dbTableAlias}.ship_from_address_name = #{${qoTableAlias}.shipFromAddressName}
        </if>
        <if test="${qoTableAlias}.shipFromAddressNote != null and ${qoTableAlias}.shipFromAddressNote != ''">
            AND ${dbTableAlias}.ship_from_address_note = #{${qoTableAlias}.shipFromAddressNote}
        </if>
        <if test="${qoTableAlias}.shipFromAddressPhone != null and ${qoTableAlias}.shipFromAddressPhone != ''">
            AND ${dbTableAlias}.ship_from_address_phone = #{${qoTableAlias}.shipFromAddressPhone}
        </if>
        <if test="${qoTableAlias}.shipFromAddressState != null and ${qoTableAlias}.shipFromAddressState != ''">
            AND ${dbTableAlias}.ship_from_address_state = #{${qoTableAlias}.shipFromAddressState}
        </if>
        <if test="${qoTableAlias}.shipFromAddressZipCode != null and ${qoTableAlias}.shipFromAddressZipCode != ''">
            AND ${dbTableAlias}.ship_from_address_zip_code = #{${qoTableAlias}.shipFromAddressZipCode}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr1 != null and ${qoTableAlias}.shipToAddressAddr1 != ''">
            AND ${dbTableAlias}.ship_to_address_addr1 = #{${qoTableAlias}.shipToAddressAddr1}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr2 != null and ${qoTableAlias}.shipToAddressAddr2 != ''">
            AND ${dbTableAlias}.ship_to_address_addr2 = #{${qoTableAlias}.shipToAddressAddr2}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr3 != null and ${qoTableAlias}.shipToAddressAddr3 != ''">
            AND ${dbTableAlias}.ship_to_address_addr3 = #{${qoTableAlias}.shipToAddressAddr3}
        </if>
        <if test="${qoTableAlias}.shipToAddressCity != null and ${qoTableAlias}.shipToAddressCity != ''">
            AND ${dbTableAlias}.ship_to_address_city = #{${qoTableAlias}.shipToAddressCity}
        </if>
        <if test="${qoTableAlias}.shipToAddressCompany != null and ${qoTableAlias}.shipToAddressCompany != ''">
            AND ${dbTableAlias}.ship_to_address_company = #{${qoTableAlias}.shipToAddressCompany}
        </if>
        <if test="${qoTableAlias}.shipToAddressCountry != null and ${qoTableAlias}.shipToAddressCountry != ''">
            AND ${dbTableAlias}.ship_to_address_country = #{${qoTableAlias}.shipToAddressCountry}
        </if>
        <if test="${qoTableAlias}.shipToAddressEmail != null and ${qoTableAlias}.shipToAddressEmail != ''">
            AND ${dbTableAlias}.ship_to_address_email = #{${qoTableAlias}.shipToAddressEmail}
        </if>
        <if test="${qoTableAlias}.shipToAddressIsResidential != null">
            AND ${dbTableAlias}.ship_to_address_is_residential = #{${qoTableAlias}.shipToAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.shipToAddressName != null and ${qoTableAlias}.shipToAddressName != ''">
            AND ${dbTableAlias}.ship_to_address_name = #{${qoTableAlias}.shipToAddressName}
        </if>
        <if test="${qoTableAlias}.shipToAddressNote != null and ${qoTableAlias}.shipToAddressNote != ''">
            AND ${dbTableAlias}.ship_to_address_note = #{${qoTableAlias}.shipToAddressNote}
        </if>
        <if test="${qoTableAlias}.shipToAddressPhone != null and ${qoTableAlias}.shipToAddressPhone != ''">
            AND ${dbTableAlias}.ship_to_address_phone = #{${qoTableAlias}.shipToAddressPhone}
        </if>
        <if test="${qoTableAlias}.shipToAddressState != null and ${qoTableAlias}.shipToAddressState != ''">
            AND ${dbTableAlias}.ship_to_address_state = #{${qoTableAlias}.shipToAddressState}
        </if>
        <if test="${qoTableAlias}.shipToAddressZipCode != null and ${qoTableAlias}.shipToAddressZipCode != ''">
            AND ${dbTableAlias}.ship_to_address_zip_code = #{${qoTableAlias}.shipToAddressZipCode}
        </if>
        <if test="${qoTableAlias}.shipWindowEndStart != null">
            AND ${dbTableAlias}.ship_window_end  &gt;= #{${qoTableAlias}.shipWindowEndStart}
        </if>
        <if test="${qoTableAlias}.shipWindowEndEnd != null">
            AND ${dbTableAlias}.ship_window_end  &lt; #{${qoTableAlias}.shipWindowEndEnd}
        </if>
        <if test="${qoTableAlias}.shipWindowStartStart != null">
            AND ${dbTableAlias}.ship_window_start  &gt;= #{${qoTableAlias}.shipWindowStartStart}
        </if>
        <if test="${qoTableAlias}.shipWindowStartEnd != null">
            AND ${dbTableAlias}.ship_window_start  &lt; #{${qoTableAlias}.shipWindowStartEnd}
        </if>
        <if test="${qoTableAlias}.transactionPartnerId != null">
            AND ${dbTableAlias}.transaction_partner_id = #{${qoTableAlias}.transactionPartnerId}
        </if>
    <if test="${qoTableAlias}.transactionPartnerIdList != null and ${qoTableAlias}.transactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.transaction_partner_id in
        <foreach collection="${qoTableAlias}.transactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoRequestPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="or"/>
           </include>
        FROM
            oco_request or
        WHERE
            or.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="eor" />
                <property name="qoTableAlias" value="qoor" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="eor" />
                <property name="qoTableAlias" value="qoor" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="eor"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_request ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoRequestMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
