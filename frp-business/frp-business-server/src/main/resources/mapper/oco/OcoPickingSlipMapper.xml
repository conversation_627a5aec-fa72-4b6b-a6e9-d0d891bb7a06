<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPickingSlipMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPickingSlip">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="build_from_type" property="buildFromType" />
        <result column="deleted_note" property="deletedNote" />
        <result column="oco_picking_slip_status" property="ocoPickingSlipStatus" />
        <result column="oco_picking_slip_type" property="ocoPickingSlipType" />
        <result column="oco_workorder_id" property="ocoWorkorderId" />
        <result column="pick_from_type" property="pickFromType" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="picking_slip_product_type" property="pickingSlipProductType" />
        <result column="print_status" property="printStatus" />
        <result column="process_type" property="processType" />
        <result column="ref_num" property="refNum" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.assigned_user_id,
        ${dbTableAlias}.build_from_type,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.oco_picking_slip_status,
        ${dbTableAlias}.oco_picking_slip_type,
        ${dbTableAlias}.oco_workorder_id,
        ${dbTableAlias}.pick_from_type,
        ${dbTableAlias}.pick_to_station,
        ${dbTableAlias}.picking_slip_product_type,
        ${dbTableAlias}.print_status,
        ${dbTableAlias}.process_type,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ops"/>
            </include>
        FROM
        oco_picking_slip ops
        WHERE
            ops.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ops"/>
                    <property name="qoTableAlias" value="qoops"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ops"/>
                <property name="qoTableAlias" value="qoops"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoops.timeZone != null and qoops.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoops.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_picking_slip ops
        WHERE
            ops.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ops"/>
                    <property name="qoTableAlias" value="qoops"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoops.timeZone != null and qoops.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoops.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.assignedUserId != null">
            AND ${dbTableAlias}.assigned_user_id = #{${qoTableAlias}.assignedUserId}
        </if>
    <if test="${qoTableAlias}.assignedUserIdList != null and ${qoTableAlias}.assignedUserIdList.size > 0 ">
        AND ${dbTableAlias}.assigned_user_id in
        <foreach collection="${qoTableAlias}.assignedUserIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.buildFromType != null and ${qoTableAlias}.buildFromType != ''">
            AND ${dbTableAlias}.build_from_type = #{${qoTableAlias}.buildFromType}
        </if>
    <if test="${qoTableAlias}.buildFromTypeList != null and ${qoTableAlias}.buildFromTypeList.size > 0 ">
        AND ${dbTableAlias}.build_from_type in
        <foreach collection="${qoTableAlias}.buildFromTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.ocoPickingSlipStatus != null and ${qoTableAlias}.ocoPickingSlipStatus != ''">
            AND ${dbTableAlias}.oco_picking_slip_status = #{${qoTableAlias}.ocoPickingSlipStatus}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipStatusList != null and ${qoTableAlias}.ocoPickingSlipStatusList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_status in
        <foreach collection="${qoTableAlias}.ocoPickingSlipStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPickingSlipType != null and ${qoTableAlias}.ocoPickingSlipType != ''">
            AND ${dbTableAlias}.oco_picking_slip_type = #{${qoTableAlias}.ocoPickingSlipType}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipTypeList != null and ${qoTableAlias}.ocoPickingSlipTypeList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_type in
        <foreach collection="${qoTableAlias}.ocoPickingSlipTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderId != null">
            AND ${dbTableAlias}.oco_workorder_id = #{${qoTableAlias}.ocoWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderIdList != null and ${qoTableAlias}.ocoWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.pickFromType != null and ${qoTableAlias}.pickFromType != ''">
            AND ${dbTableAlias}.pick_from_type = #{${qoTableAlias}.pickFromType}
        </if>
    <if test="${qoTableAlias}.pickFromTypeList != null and ${qoTableAlias}.pickFromTypeList.size > 0 ">
        AND ${dbTableAlias}.pick_from_type in
        <foreach collection="${qoTableAlias}.pickFromTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.pickToStation != null and ${qoTableAlias}.pickToStation != ''">
            AND ${dbTableAlias}.pick_to_station = #{${qoTableAlias}.pickToStation}
        </if>
    <if test="${qoTableAlias}.pickToStationList != null and ${qoTableAlias}.pickToStationList.size > 0 ">
        AND ${dbTableAlias}.pick_to_station in
        <foreach collection="${qoTableAlias}.pickToStationList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.pickingSlipProductType != null and ${qoTableAlias}.pickingSlipProductType != ''">
            AND ${dbTableAlias}.picking_slip_product_type = #{${qoTableAlias}.pickingSlipProductType}
        </if>
    <if test="${qoTableAlias}.pickingSlipProductTypeList != null and ${qoTableAlias}.pickingSlipProductTypeList.size > 0 ">
        AND ${dbTableAlias}.picking_slip_product_type in
        <foreach collection="${qoTableAlias}.pickingSlipProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.printStatus != null and ${qoTableAlias}.printStatus != ''">
            AND ${dbTableAlias}.print_status = #{${qoTableAlias}.printStatus}
        </if>
    <if test="${qoTableAlias}.printStatusList != null and ${qoTableAlias}.printStatusList.size > 0 ">
        AND ${dbTableAlias}.print_status in
        <foreach collection="${qoTableAlias}.printStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.processType != null and ${qoTableAlias}.processType != ''">
            AND ${dbTableAlias}.process_type = #{${qoTableAlias}.processType}
        </if>
    <if test="${qoTableAlias}.processTypeList != null and ${qoTableAlias}.processTypeList.size > 0 ">
        AND ${dbTableAlias}.process_type in
        <foreach collection="${qoTableAlias}.processTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPickingSlipPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ops"/>
           </include>
        FROM
            oco_picking_slip ops
        WHERE
            ops.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="ops" />
                <property name="qoTableAlias" value="qoops" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="ops" />
                <property name="qoTableAlias" value="qoops" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ops"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_picking_slip ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPickingSlipMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
