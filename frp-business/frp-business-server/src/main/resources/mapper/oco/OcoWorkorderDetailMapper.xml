<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoWorkorderDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoWorkorderDetail">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="detail_snapshot_product_barcode" property="detailSnapshotProductBarcode" />
        <result column="detail_snapshot_product_channel_sku" property="detailSnapshotProductChannelSku" />
        <result column="finish_qty" property="finishQty" />
        <result column="finish_reserve_qty" property="finishReserveQty" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="inventory_locked_id" property="inventoryLockedId" />
        <result column="inventory_reserve_id" property="inventoryReserveId" />
        <result column="line_num" property="lineNum" />
        <result column="note" property="note" />
        <result column="oco_workorder_id" property="ocoWorkorderId" />
        <result column="packed_qty" property="packedQty" />
        <result column="picked_qty" property="pickedQty" />
        <result column="product_id" property="productId" />
        <result column="qty" property="qty" />
        <result column="reserve_qty" property="reserveQty" />
        <result column="shipment_qty" property="shipmentQty" />
        <result column="shipped_qty" property="shippedQty" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.detail_snapshot_product_barcode,
        ${dbTableAlias}.detail_snapshot_product_channel_sku,
        ${dbTableAlias}.finish_qty,
        ${dbTableAlias}.finish_reserve_qty,
        ${dbTableAlias}.hazmat_version_ref_num,
        ${dbTableAlias}.inventory_locked_id,
        ${dbTableAlias}.inventory_reserve_id,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.note,
        ${dbTableAlias}.oco_workorder_id,
        ${dbTableAlias}.packed_qty,
        ${dbTableAlias}.picked_qty,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.reserve_qty,
        ${dbTableAlias}.shipment_qty,
        ${dbTableAlias}.shipped_qty,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderDetailPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="owd"/>
            </include>
        FROM
        oco_workorder_detail owd
        WHERE
            owd.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="owd"/>
                    <property name="qoTableAlias" value="qoowd"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="owd"/>
                <property name="qoTableAlias" value="qoowd"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoowd.timeZone != null and qoowd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoowd.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_workorder_detail owd
        WHERE
            owd.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="owd"/>
                    <property name="qoTableAlias" value="qoowd"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoowd.timeZone != null and qoowd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoowd.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.detailSnapshotProductBarcode != null and ${qoTableAlias}.detailSnapshotProductBarcode != ''">
            AND ${dbTableAlias}.detail_snapshot_product_barcode = #{${qoTableAlias}.detailSnapshotProductBarcode}
        </if>
    <if test="${qoTableAlias}.detailSnapshotProductBarcodeList != null and ${qoTableAlias}.detailSnapshotProductBarcodeList.size > 0 ">
        AND ${dbTableAlias}.detail_snapshot_product_barcode in
        <foreach collection="${qoTableAlias}.detailSnapshotProductBarcodeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.detailSnapshotProductChannelSku != null and ${qoTableAlias}.detailSnapshotProductChannelSku != ''">
            AND ${dbTableAlias}.detail_snapshot_product_channel_sku = #{${qoTableAlias}.detailSnapshotProductChannelSku}
        </if>
        <if test="${qoTableAlias}.finishQty != null">
            AND ${dbTableAlias}.finish_qty = #{${qoTableAlias}.finishQty}
        </if>
        <if test="${qoTableAlias}.finishReserveQty != null">
            AND ${dbTableAlias}.finish_reserve_qty = #{${qoTableAlias}.finishReserveQty}
        </if>
        <if test="${qoTableAlias}.hazmatVersionRefNum != null and ${qoTableAlias}.hazmatVersionRefNum != ''">
            AND ${dbTableAlias}.hazmat_version_ref_num = #{${qoTableAlias}.hazmatVersionRefNum}
        </if>
    <if test="${qoTableAlias}.hazmatVersionRefNumList != null and ${qoTableAlias}.hazmatVersionRefNumList.size > 0 ">
        AND ${dbTableAlias}.hazmat_version_ref_num in
        <foreach collection="${qoTableAlias}.hazmatVersionRefNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.inventoryLockedId != null">
            AND ${dbTableAlias}.inventory_locked_id = #{${qoTableAlias}.inventoryLockedId}
        </if>
    <if test="${qoTableAlias}.inventoryLockedIdList != null and ${qoTableAlias}.inventoryLockedIdList.size > 0 ">
        AND ${dbTableAlias}.inventory_locked_id in
        <foreach collection="${qoTableAlias}.inventoryLockedIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.inventoryReserveId != null">
            AND ${dbTableAlias}.inventory_reserve_id = #{${qoTableAlias}.inventoryReserveId}
        </if>
    <if test="${qoTableAlias}.inventoryReserveIdList != null and ${qoTableAlias}.inventoryReserveIdList.size > 0 ">
        AND ${dbTableAlias}.inventory_reserve_id in
        <foreach collection="${qoTableAlias}.inventoryReserveIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lineNum != null">
            AND ${dbTableAlias}.line_num = #{${qoTableAlias}.lineNum}
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.ocoWorkorderId != null">
            AND ${dbTableAlias}.oco_workorder_id = #{${qoTableAlias}.ocoWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderIdList != null and ${qoTableAlias}.ocoWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.packedQty != null">
            AND ${dbTableAlias}.packed_qty = #{${qoTableAlias}.packedQty}
        </if>
        <if test="${qoTableAlias}.pickedQty != null">
            AND ${dbTableAlias}.picked_qty = #{${qoTableAlias}.pickedQty}
        </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.reserveQty != null">
            AND ${dbTableAlias}.reserve_qty = #{${qoTableAlias}.reserveQty}
        </if>
        <if test="${qoTableAlias}.shipmentQty != null">
            AND ${dbTableAlias}.shipment_qty = #{${qoTableAlias}.shipmentQty}
        </if>
        <if test="${qoTableAlias}.shippedQty != null">
            AND ${dbTableAlias}.shipped_qty = #{${qoTableAlias}.shippedQty}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoWorkorderDetailPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="owd"/>
           </include>
        FROM
            oco_workorder_detail owd
        WHERE
            owd.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="owd" />
                <property name="qoTableAlias" value="qoowd" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="owd" />
                <property name="qoTableAlias" value="qoowd" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="owd"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_workorder_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoWorkorderDetailMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
