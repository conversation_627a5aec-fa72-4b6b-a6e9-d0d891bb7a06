<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPrepWorkorderDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPrepWorkorderDetail">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="deleted_note" property="deletedNote" />
        <result column="inventory_locked_id" property="inventoryLockedId" />
        <result column="line_num" property="lineNum" />
        <result column="oco_prep_workorder_id" property="ocoPrepWorkorderId" />
        <result column="parent_id" property="parentId" />
        <result column="picked_qty" property="pickedQty" />
        <result column="prep_workorder_detail_type" property="prepWorkorderDetailType" />
        <result column="prep_workorder_detail_version_int" property="prepWorkorderDetailVersionInt" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="qty" property="qty" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.inventory_locked_id,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.oco_prep_workorder_id,
        ${dbTableAlias}.parent_id,
        ${dbTableAlias}.picked_qty,
        ${dbTableAlias}.prep_workorder_detail_type,
        ${dbTableAlias}.prep_workorder_detail_version_int,
        ${dbTableAlias}.putaway_qty,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepWorkorderDetailPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opwd"/>
            </include>
        FROM
        oco_prep_workorder_detail opwd
        WHERE
            opwd.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opwd"/>
                    <property name="qoTableAlias" value="qoopwd"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opwd"/>
                <property name="qoTableAlias" value="qoopwd"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopwd.timeZone != null and qoopwd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopwd.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_prep_workorder_detail opwd
        WHERE
            opwd.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opwd"/>
                    <property name="qoTableAlias" value="qoopwd"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopwd.timeZone != null and qoopwd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopwd.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.inventoryLockedId != null">
            AND ${dbTableAlias}.inventory_locked_id = #{${qoTableAlias}.inventoryLockedId}
        </if>
    <if test="${qoTableAlias}.inventoryLockedIdList != null and ${qoTableAlias}.inventoryLockedIdList.size > 0 ">
        AND ${dbTableAlias}.inventory_locked_id in
        <foreach collection="${qoTableAlias}.inventoryLockedIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lineNum != null">
            AND ${dbTableAlias}.line_num = #{${qoTableAlias}.lineNum}
        </if>
        <if test="${qoTableAlias}.ocoPrepWorkorderId != null">
            AND ${dbTableAlias}.oco_prep_workorder_id = #{${qoTableAlias}.ocoPrepWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoPrepWorkorderIdList != null and ${qoTableAlias}.ocoPrepWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_prep_workorder_id in
        <foreach collection="${qoTableAlias}.ocoPrepWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.parentId != null">
            AND ${dbTableAlias}.parent_id = #{${qoTableAlias}.parentId}
        </if>
    <if test="${qoTableAlias}.parentIdList != null and ${qoTableAlias}.parentIdList.size > 0 ">
        AND ${dbTableAlias}.parent_id in
        <foreach collection="${qoTableAlias}.parentIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.pickedQty != null">
            AND ${dbTableAlias}.picked_qty = #{${qoTableAlias}.pickedQty}
        </if>
        <if test="${qoTableAlias}.prepWorkorderDetailType != null and ${qoTableAlias}.prepWorkorderDetailType != ''">
            AND ${dbTableAlias}.prep_workorder_detail_type = #{${qoTableAlias}.prepWorkorderDetailType}
        </if>
    <if test="${qoTableAlias}.prepWorkorderDetailTypeList != null and ${qoTableAlias}.prepWorkorderDetailTypeList.size > 0 ">
        AND ${dbTableAlias}.prep_workorder_detail_type in
        <foreach collection="${qoTableAlias}.prepWorkorderDetailTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.prepWorkorderDetailVersionInt != null">
            AND ${dbTableAlias}.prep_workorder_detail_version_int = #{${qoTableAlias}.prepWorkorderDetailVersionInt}
        </if>
        <if test="${qoTableAlias}.putawayQty != null">
            AND ${dbTableAlias}.putaway_qty = #{${qoTableAlias}.putawayQty}
        </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepWorkorderDetailPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opwd"/>
           </include>
        FROM
            oco_prep_workorder_detail opwd
        WHERE
            opwd.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="opwd" />
                <property name="qoTableAlias" value="qoopwd" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="opwd" />
                <property name="qoTableAlias" value="qoopwd" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opwd"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_prep_workorder_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPrepWorkorderDetailMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
