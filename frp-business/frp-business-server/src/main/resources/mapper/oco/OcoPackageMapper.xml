<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPackageMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPackage">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="carton_size_dimension_unit" property="cartonSizeDimensionUnit" />
        <result column="carton_size_height" property="cartonSizeHeight" />
        <result column="carton_size_length" property="cartonSizeLength" />
        <result column="carton_size_weight" property="cartonSizeWeight" />
        <result column="carton_size_weight_unit" property="cartonSizeWeightUnit" />
        <result column="carton_size_width" property="cartonSizeWidth" />
        <result column="deleted_note" property="deletedNote" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="oco_container_id" property="ocoContainerId" />
        <result column="oco_package_status" property="ocoPackageStatus" />
        <result column="oco_package_type" property="ocoPackageType" />
        <result column="oco_picking_slip_id" property="ocoPickingSlipId" />
        <result column="oco_request_id" property="ocoRequestId" />
        <result column="oco_workorder_id" property="ocoWorkorderId" />
        <result column="order_num" property="orderNum" />
        <result column="print_status" property="printStatus" />
        <result column="ref_num" property="refNum" />
        <result column="short_sscc_num" property="shortSsccNum" />
        <result column="sscc_num" property="ssccNum" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.carton_size_dimension_unit,
        ${dbTableAlias}.carton_size_height,
        ${dbTableAlias}.carton_size_length,
        ${dbTableAlias}.carton_size_weight,
        ${dbTableAlias}.carton_size_weight_unit,
        ${dbTableAlias}.carton_size_width,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.detail_product_type,
        ${dbTableAlias}.oco_container_id,
        ${dbTableAlias}.oco_package_status,
        ${dbTableAlias}.oco_package_type,
        ${dbTableAlias}.oco_picking_slip_id,
        ${dbTableAlias}.oco_request_id,
        ${dbTableAlias}.oco_workorder_id,
        ${dbTableAlias}.order_num,
        ${dbTableAlias}.print_status,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.short_sscc_num,
        ${dbTableAlias}.sscc_num,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPackagePageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="op"/>
            </include>
        FROM
        oco_package op
        WHERE
            op.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="op"/>
                    <property name="qoTableAlias" value="qoop"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="op"/>
                <property name="qoTableAlias" value="qoop"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoop.timeZone != null and qoop.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoop.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_package op
        WHERE
            op.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="op"/>
                    <property name="qoTableAlias" value="qoop"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoop.timeZone != null and qoop.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoop.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.cartonSizeDimensionUnit != null and ${qoTableAlias}.cartonSizeDimensionUnit != ''">
            AND ${dbTableAlias}.carton_size_dimension_unit = #{${qoTableAlias}.cartonSizeDimensionUnit}
        </if>
        <if test="${qoTableAlias}.cartonSizeHeight != null">
            AND ${dbTableAlias}.carton_size_height = #{${qoTableAlias}.cartonSizeHeight}
        </if>
        <if test="${qoTableAlias}.cartonSizeLength != null">
            AND ${dbTableAlias}.carton_size_length = #{${qoTableAlias}.cartonSizeLength}
        </if>
        <if test="${qoTableAlias}.cartonSizeWeight != null">
            AND ${dbTableAlias}.carton_size_weight = #{${qoTableAlias}.cartonSizeWeight}
        </if>
        <if test="${qoTableAlias}.cartonSizeWeightUnit != null and ${qoTableAlias}.cartonSizeWeightUnit != ''">
            AND ${dbTableAlias}.carton_size_weight_unit = #{${qoTableAlias}.cartonSizeWeightUnit}
        </if>
        <if test="${qoTableAlias}.cartonSizeWidth != null">
            AND ${dbTableAlias}.carton_size_width = #{${qoTableAlias}.cartonSizeWidth}
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.detailProductType != null and ${qoTableAlias}.detailProductType != ''">
            AND ${dbTableAlias}.detail_product_type = #{${qoTableAlias}.detailProductType}
        </if>
    <if test="${qoTableAlias}.detailProductTypeList != null and ${qoTableAlias}.detailProductTypeList.size > 0 ">
        AND ${dbTableAlias}.detail_product_type in
        <foreach collection="${qoTableAlias}.detailProductTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoContainerId != null">
            AND ${dbTableAlias}.oco_container_id = #{${qoTableAlias}.ocoContainerId}
        </if>
    <if test="${qoTableAlias}.ocoContainerIdList != null and ${qoTableAlias}.ocoContainerIdList.size > 0 ">
        AND ${dbTableAlias}.oco_container_id in
        <foreach collection="${qoTableAlias}.ocoContainerIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPackageStatus != null and ${qoTableAlias}.ocoPackageStatus != ''">
            AND ${dbTableAlias}.oco_package_status = #{${qoTableAlias}.ocoPackageStatus}
        </if>
    <if test="${qoTableAlias}.ocoPackageStatusList != null and ${qoTableAlias}.ocoPackageStatusList.size > 0 ">
        AND ${dbTableAlias}.oco_package_status in
        <foreach collection="${qoTableAlias}.ocoPackageStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPackageType != null and ${qoTableAlias}.ocoPackageType != ''">
            AND ${dbTableAlias}.oco_package_type = #{${qoTableAlias}.ocoPackageType}
        </if>
    <if test="${qoTableAlias}.ocoPackageTypeList != null and ${qoTableAlias}.ocoPackageTypeList.size > 0 ">
        AND ${dbTableAlias}.oco_package_type in
        <foreach collection="${qoTableAlias}.ocoPackageTypeList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPickingSlipId != null">
            AND ${dbTableAlias}.oco_picking_slip_id = #{${qoTableAlias}.ocoPickingSlipId}
        </if>
    <if test="${qoTableAlias}.ocoPickingSlipIdList != null and ${qoTableAlias}.ocoPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.oco_picking_slip_id in
        <foreach collection="${qoTableAlias}.ocoPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoRequestId != null">
            AND ${dbTableAlias}.oco_request_id = #{${qoTableAlias}.ocoRequestId}
        </if>
    <if test="${qoTableAlias}.ocoRequestIdList != null and ${qoTableAlias}.ocoRequestIdList.size > 0 ">
        AND ${dbTableAlias}.oco_request_id in
        <foreach collection="${qoTableAlias}.ocoRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderId != null">
            AND ${dbTableAlias}.oco_workorder_id = #{${qoTableAlias}.ocoWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderIdList != null and ${qoTableAlias}.ocoWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.orderNum != null and ${qoTableAlias}.orderNum != ''">
            AND ${dbTableAlias}.order_num = #{${qoTableAlias}.orderNum}
        </if>
        <if test="${qoTableAlias}.printStatus != null and ${qoTableAlias}.printStatus != ''">
            AND ${dbTableAlias}.print_status = #{${qoTableAlias}.printStatus}
        </if>
    <if test="${qoTableAlias}.printStatusList != null and ${qoTableAlias}.printStatusList.size > 0 ">
        AND ${dbTableAlias}.print_status in
        <foreach collection="${qoTableAlias}.printStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.shortSsccNum != null and ${qoTableAlias}.shortSsccNum != ''">
            AND ${dbTableAlias}.short_sscc_num = #{${qoTableAlias}.shortSsccNum}
        </if>
        <if test="${qoTableAlias}.ssccNum != null and ${qoTableAlias}.ssccNum != ''">
            AND ${dbTableAlias}.sscc_num = #{${qoTableAlias}.ssccNum}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPackagePageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="op"/>
           </include>
        FROM
            oco_package op
        WHERE
            op.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="op" />
                <property name="qoTableAlias" value="qoop" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="op" />
                <property name="qoTableAlias" value="qoop" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="op"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_package ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPackageMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
