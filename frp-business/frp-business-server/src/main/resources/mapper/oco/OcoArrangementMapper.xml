<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoArrangementMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoArrangement">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="container_num" property="containerNum" />
        <result column="container_template_id" property="containerTemplateId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="oco_arrangement_status" property="ocoArrangementStatus" />
        <result column="ref_num" property="refNum" />
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1" />
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2" />
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3" />
        <result column="ship_from_address_city" property="shipFromAddressCity" />
        <result column="ship_from_address_company" property="shipFromAddressCompany" />
        <result column="ship_from_address_country" property="shipFromAddressCountry" />
        <result column="ship_from_address_email" property="shipFromAddressEmail" />
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential" />
        <result column="ship_from_address_name" property="shipFromAddressName" />
        <result column="ship_from_address_note" property="shipFromAddressNote" />
        <result column="ship_from_address_phone" property="shipFromAddressPhone" />
        <result column="ship_from_address_state" property="shipFromAddressState" />
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode" />
        <result column="ship_to_address_addr1" property="shipToAddressAddr1" />
        <result column="ship_to_address_addr2" property="shipToAddressAddr2" />
        <result column="ship_to_address_addr3" property="shipToAddressAddr3" />
        <result column="ship_to_address_city" property="shipToAddressCity" />
        <result column="ship_to_address_company" property="shipToAddressCompany" />
        <result column="ship_to_address_country" property="shipToAddressCountry" />
        <result column="ship_to_address_email" property="shipToAddressEmail" />
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential" />
        <result column="ship_to_address_name" property="shipToAddressName" />
        <result column="ship_to_address_note" property="shipToAddressNote" />
        <result column="ship_to_address_phone" property="shipToAddressPhone" />
        <result column="ship_to_address_state" property="shipToAddressState" />
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.container_num,
        ${dbTableAlias}.container_template_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.oco_arrangement_status,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.ship_from_address_addr1,
        ${dbTableAlias}.ship_from_address_addr2,
        ${dbTableAlias}.ship_from_address_addr3,
        ${dbTableAlias}.ship_from_address_city,
        ${dbTableAlias}.ship_from_address_company,
        ${dbTableAlias}.ship_from_address_country,
        ${dbTableAlias}.ship_from_address_email,
        ${dbTableAlias}.ship_from_address_is_residential,
        ${dbTableAlias}.ship_from_address_name,
        ${dbTableAlias}.ship_from_address_note,
        ${dbTableAlias}.ship_from_address_phone,
        ${dbTableAlias}.ship_from_address_state,
        ${dbTableAlias}.ship_from_address_zip_code,
        ${dbTableAlias}.ship_to_address_addr1,
        ${dbTableAlias}.ship_to_address_addr2,
        ${dbTableAlias}.ship_to_address_addr3,
        ${dbTableAlias}.ship_to_address_city,
        ${dbTableAlias}.ship_to_address_company,
        ${dbTableAlias}.ship_to_address_country,
        ${dbTableAlias}.ship_to_address_email,
        ${dbTableAlias}.ship_to_address_is_residential,
        ${dbTableAlias}.ship_to_address_name,
        ${dbTableAlias}.ship_to_address_note,
        ${dbTableAlias}.ship_to_address_phone,
        ${dbTableAlias}.ship_to_address_state,
        ${dbTableAlias}.ship_to_address_zip_code,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoArrangementPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oa"/>
            </include>
        FROM
        oco_arrangement oa
        WHERE
            oa.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oa"/>
                    <property name="qoTableAlias" value="qooa"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oa"/>
                <property name="qoTableAlias" value="qooa"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooa.timeZone != null and qooa.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooa.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_arrangement oa
        WHERE
            oa.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="oa"/>
                    <property name="qoTableAlias" value="qooa"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qooa.timeZone != null and qooa.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qooa.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.containerNum != null and ${qoTableAlias}.containerNum != ''">
            AND ${dbTableAlias}.container_num = #{${qoTableAlias}.containerNum}
        </if>
        <if test="${qoTableAlias}.containerTemplateId != null">
            AND ${dbTableAlias}.container_template_id = #{${qoTableAlias}.containerTemplateId}
        </if>
    <if test="${qoTableAlias}.containerTemplateIdList != null and ${qoTableAlias}.containerTemplateIdList.size > 0 ">
        AND ${dbTableAlias}.container_template_id in
        <foreach collection="${qoTableAlias}.containerTemplateIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.ocoArrangementStatus != null and ${qoTableAlias}.ocoArrangementStatus != ''">
            AND ${dbTableAlias}.oco_arrangement_status = #{${qoTableAlias}.ocoArrangementStatus}
        </if>
    <if test="${qoTableAlias}.ocoArrangementStatusList != null and ${qoTableAlias}.ocoArrangementStatusList.size > 0 ">
        AND ${dbTableAlias}.oco_arrangement_status in
        <foreach collection="${qoTableAlias}.ocoArrangementStatusList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
    <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
        <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.shipFromAddressAddr1 != null and ${qoTableAlias}.shipFromAddressAddr1 != ''">
            AND ${dbTableAlias}.ship_from_address_addr1 = #{${qoTableAlias}.shipFromAddressAddr1}
        </if>
        <if test="${qoTableAlias}.shipFromAddressAddr2 != null and ${qoTableAlias}.shipFromAddressAddr2 != ''">
            AND ${dbTableAlias}.ship_from_address_addr2 = #{${qoTableAlias}.shipFromAddressAddr2}
        </if>
        <if test="${qoTableAlias}.shipFromAddressAddr3 != null and ${qoTableAlias}.shipFromAddressAddr3 != ''">
            AND ${dbTableAlias}.ship_from_address_addr3 = #{${qoTableAlias}.shipFromAddressAddr3}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCity != null and ${qoTableAlias}.shipFromAddressCity != ''">
            AND ${dbTableAlias}.ship_from_address_city = #{${qoTableAlias}.shipFromAddressCity}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCompany != null and ${qoTableAlias}.shipFromAddressCompany != ''">
            AND ${dbTableAlias}.ship_from_address_company = #{${qoTableAlias}.shipFromAddressCompany}
        </if>
        <if test="${qoTableAlias}.shipFromAddressCountry != null and ${qoTableAlias}.shipFromAddressCountry != ''">
            AND ${dbTableAlias}.ship_from_address_country = #{${qoTableAlias}.shipFromAddressCountry}
        </if>
        <if test="${qoTableAlias}.shipFromAddressEmail != null and ${qoTableAlias}.shipFromAddressEmail != ''">
            AND ${dbTableAlias}.ship_from_address_email = #{${qoTableAlias}.shipFromAddressEmail}
        </if>
        <if test="${qoTableAlias}.shipFromAddressIsResidential != null">
            AND ${dbTableAlias}.ship_from_address_is_residential = #{${qoTableAlias}.shipFromAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.shipFromAddressName != null and ${qoTableAlias}.shipFromAddressName != ''">
            AND ${dbTableAlias}.ship_from_address_name = #{${qoTableAlias}.shipFromAddressName}
        </if>
        <if test="${qoTableAlias}.shipFromAddressNote != null and ${qoTableAlias}.shipFromAddressNote != ''">
            AND ${dbTableAlias}.ship_from_address_note = #{${qoTableAlias}.shipFromAddressNote}
        </if>
        <if test="${qoTableAlias}.shipFromAddressPhone != null and ${qoTableAlias}.shipFromAddressPhone != ''">
            AND ${dbTableAlias}.ship_from_address_phone = #{${qoTableAlias}.shipFromAddressPhone}
        </if>
        <if test="${qoTableAlias}.shipFromAddressState != null and ${qoTableAlias}.shipFromAddressState != ''">
            AND ${dbTableAlias}.ship_from_address_state = #{${qoTableAlias}.shipFromAddressState}
        </if>
        <if test="${qoTableAlias}.shipFromAddressZipCode != null and ${qoTableAlias}.shipFromAddressZipCode != ''">
            AND ${dbTableAlias}.ship_from_address_zip_code = #{${qoTableAlias}.shipFromAddressZipCode}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr1 != null and ${qoTableAlias}.shipToAddressAddr1 != ''">
            AND ${dbTableAlias}.ship_to_address_addr1 = #{${qoTableAlias}.shipToAddressAddr1}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr2 != null and ${qoTableAlias}.shipToAddressAddr2 != ''">
            AND ${dbTableAlias}.ship_to_address_addr2 = #{${qoTableAlias}.shipToAddressAddr2}
        </if>
        <if test="${qoTableAlias}.shipToAddressAddr3 != null and ${qoTableAlias}.shipToAddressAddr3 != ''">
            AND ${dbTableAlias}.ship_to_address_addr3 = #{${qoTableAlias}.shipToAddressAddr3}
        </if>
        <if test="${qoTableAlias}.shipToAddressCity != null and ${qoTableAlias}.shipToAddressCity != ''">
            AND ${dbTableAlias}.ship_to_address_city = #{${qoTableAlias}.shipToAddressCity}
        </if>
        <if test="${qoTableAlias}.shipToAddressCompany != null and ${qoTableAlias}.shipToAddressCompany != ''">
            AND ${dbTableAlias}.ship_to_address_company = #{${qoTableAlias}.shipToAddressCompany}
        </if>
        <if test="${qoTableAlias}.shipToAddressCountry != null and ${qoTableAlias}.shipToAddressCountry != ''">
            AND ${dbTableAlias}.ship_to_address_country = #{${qoTableAlias}.shipToAddressCountry}
        </if>
        <if test="${qoTableAlias}.shipToAddressEmail != null and ${qoTableAlias}.shipToAddressEmail != ''">
            AND ${dbTableAlias}.ship_to_address_email = #{${qoTableAlias}.shipToAddressEmail}
        </if>
        <if test="${qoTableAlias}.shipToAddressIsResidential != null">
            AND ${dbTableAlias}.ship_to_address_is_residential = #{${qoTableAlias}.shipToAddressIsResidential}
        </if>
        <if test="${qoTableAlias}.shipToAddressName != null and ${qoTableAlias}.shipToAddressName != ''">
            AND ${dbTableAlias}.ship_to_address_name = #{${qoTableAlias}.shipToAddressName}
        </if>
        <if test="${qoTableAlias}.shipToAddressNote != null and ${qoTableAlias}.shipToAddressNote != ''">
            AND ${dbTableAlias}.ship_to_address_note = #{${qoTableAlias}.shipToAddressNote}
        </if>
        <if test="${qoTableAlias}.shipToAddressPhone != null and ${qoTableAlias}.shipToAddressPhone != ''">
            AND ${dbTableAlias}.ship_to_address_phone = #{${qoTableAlias}.shipToAddressPhone}
        </if>
        <if test="${qoTableAlias}.shipToAddressState != null and ${qoTableAlias}.shipToAddressState != ''">
            AND ${dbTableAlias}.ship_to_address_state = #{${qoTableAlias}.shipToAddressState}
        </if>
        <if test="${qoTableAlias}.shipToAddressZipCode != null and ${qoTableAlias}.shipToAddressZipCode != ''">
            AND ${dbTableAlias}.ship_to_address_zip_code = #{${qoTableAlias}.shipToAddressZipCode}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoArrangementPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="oa"/>
           </include>
        FROM
            oco_arrangement oa
        WHERE
            oa.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="oa" />
                <property name="qoTableAlias" value="qooa" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="oa" />
                <property name="qoTableAlias" value="qooa" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="oa"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_arrangement ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoArrangementMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
