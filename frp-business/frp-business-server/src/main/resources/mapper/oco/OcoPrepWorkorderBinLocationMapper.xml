<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.oco.OcoPrepWorkorderBinLocationMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.oco.OcoPrepWorkorderBinLocation">
        <result column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_by" property="createBy" />
        <result column="update_by" property="updateBy" />
        <result column="remove_flag" property="removeFlag" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_detail_locked_id" property="binLocationDetailLockedId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="oco_prep_picking_slip_detail_id" property="ocoPrepPickingSlipDetailId" />
        <result column="oco_prep_picking_slip_id" property="ocoPrepPickingSlipId" />
        <result column="oco_prep_workorder_detail_id" property="ocoPrepWorkorderDetailId" />
        <result column="oco_prep_workorder_id" property="ocoPrepWorkorderId" />
        <result column="oco_workorder_detail_id" property="ocoWorkorderDetailId" />
        <result column="oco_workorder_id" property="ocoWorkorderId" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
        <result column="qty" property="qty" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.version,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.bin_location_detail_id,
        ${dbTableAlias}.bin_location_detail_locked_id,
        ${dbTableAlias}.bin_location_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.oco_prep_picking_slip_detail_id,
        ${dbTableAlias}.oco_prep_picking_slip_id,
        ${dbTableAlias}.oco_prep_workorder_detail_id,
        ${dbTableAlias}.oco_prep_workorder_id,
        ${dbTableAlias}.oco_workorder_detail_id,
        ${dbTableAlias}.oco_workorder_id,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.product_version_id,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepWorkorderBinLocationPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opwbl"/>
            </include>
        FROM
        oco_prep_workorder_bin_location opwbl
        WHERE
            opwbl.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opwbl"/>
                    <property name="qoTableAlias" value="qoopwbl"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opwbl"/>
                <property name="qoTableAlias" value="qoopwbl"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopwbl.timeZone != null and qoopwbl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopwbl.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            oco_prep_workorder_bin_location opwbl
        WHERE
            opwbl.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="opwbl"/>
                    <property name="qoTableAlias" value="qoopwbl"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qoopwbl.timeZone != null and qoopwbl.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qoopwbl.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.binLocationDetailId != null">
            AND ${dbTableAlias}.bin_location_detail_id = #{${qoTableAlias}.binLocationDetailId}
        </if>
    <if test="${qoTableAlias}.binLocationDetailIdList != null and ${qoTableAlias}.binLocationDetailIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_detail_id in
        <foreach collection="${qoTableAlias}.binLocationDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationDetailLockedId != null">
            AND ${dbTableAlias}.bin_location_detail_locked_id = #{${qoTableAlias}.binLocationDetailLockedId}
        </if>
    <if test="${qoTableAlias}.binLocationDetailLockedIdList != null and ${qoTableAlias}.binLocationDetailLockedIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_detail_locked_id in
        <foreach collection="${qoTableAlias}.binLocationDetailLockedIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationId != null">
            AND ${dbTableAlias}.bin_location_id = #{${qoTableAlias}.binLocationId}
        </if>
    <if test="${qoTableAlias}.binLocationIdList != null and ${qoTableAlias}.binLocationIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_id in
        <foreach collection="${qoTableAlias}.binLocationIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.ocoPrepPickingSlipDetailId != null">
            AND ${dbTableAlias}.oco_prep_picking_slip_detail_id = #{${qoTableAlias}.ocoPrepPickingSlipDetailId}
        </if>
    <if test="${qoTableAlias}.ocoPrepPickingSlipDetailIdList != null and ${qoTableAlias}.ocoPrepPickingSlipDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_prep_picking_slip_detail_id in
        <foreach collection="${qoTableAlias}.ocoPrepPickingSlipDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPrepPickingSlipId != null">
            AND ${dbTableAlias}.oco_prep_picking_slip_id = #{${qoTableAlias}.ocoPrepPickingSlipId}
        </if>
    <if test="${qoTableAlias}.ocoPrepPickingSlipIdList != null and ${qoTableAlias}.ocoPrepPickingSlipIdList.size > 0 ">
        AND ${dbTableAlias}.oco_prep_picking_slip_id in
        <foreach collection="${qoTableAlias}.ocoPrepPickingSlipIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPrepWorkorderDetailId != null">
            AND ${dbTableAlias}.oco_prep_workorder_detail_id = #{${qoTableAlias}.ocoPrepWorkorderDetailId}
        </if>
    <if test="${qoTableAlias}.ocoPrepWorkorderDetailIdList != null and ${qoTableAlias}.ocoPrepWorkorderDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_prep_workorder_detail_id in
        <foreach collection="${qoTableAlias}.ocoPrepWorkorderDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoPrepWorkorderId != null">
            AND ${dbTableAlias}.oco_prep_workorder_id = #{${qoTableAlias}.ocoPrepWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoPrepWorkorderIdList != null and ${qoTableAlias}.ocoPrepWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_prep_workorder_id in
        <foreach collection="${qoTableAlias}.ocoPrepWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderDetailId != null">
            AND ${dbTableAlias}.oco_workorder_detail_id = #{${qoTableAlias}.ocoWorkorderDetailId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderDetailIdList != null and ${qoTableAlias}.ocoWorkorderDetailIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_detail_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.ocoWorkorderId != null">
            AND ${dbTableAlias}.oco_workorder_id = #{${qoTableAlias}.ocoWorkorderId}
        </if>
    <if test="${qoTableAlias}.ocoWorkorderIdList != null and ${qoTableAlias}.ocoWorkorderIdList.size > 0 ">
        AND ${dbTableAlias}.oco_workorder_id in
        <foreach collection="${qoTableAlias}.ocoWorkorderIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productVersionId != null">
            AND ${dbTableAlias}.product_version_id = #{${qoTableAlias}.productVersionId}
        </if>
    <if test="${qoTableAlias}.productVersionIdList != null and ${qoTableAlias}.productVersionIdList.size > 0 ">
        AND ${dbTableAlias}.product_version_id in
        <foreach collection="${qoTableAlias}.productVersionIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.OcoPrepWorkorderBinLocationPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="opwbl"/>
           </include>
        FROM
            oco_prep_workorder_bin_location opwbl
        WHERE
            opwbl.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="opwbl" />
                <property name="qoTableAlias" value="qoopwbl" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="opwbl" />
                <property name="qoTableAlias" value="qoopwbl" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="opwbl"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM oco_prep_workorder_bin_location ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.oco.OcoPrepWorkorderBinLocationMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>
