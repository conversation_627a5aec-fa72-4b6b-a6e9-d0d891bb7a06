<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.log.AuditShowLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.log.AuditShowLog">
        <result column="id" property="id" />
        <result column="version" property="version" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="ref_table_id" property="refTableId" />
        <result column="ref_table_name" property="refTableName" />
        <result column="ref_table_ref_num" property="refTableRefNum" />
        <result column="ref_table_show_name" property="refTableShowName" />
        <result column="ref_table_show_ref_num" property="refTableShowRefNum" />
        <result column="event" property="event" />
        <result column="show_flag" property="showFlag" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="note" property="note" />
        <result column="description" property="description" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.version,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ref_table_id,
        t.ref_table_name,
        t.ref_table_ref_num,
        t.ref_table_show_name,
        t.ref_table_show_ref_num,
        t.event,
        t.type,
        t.show_flag,
        t.warehouse_id,
        t.note,
        t.description,
        t.tenant_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.log.AuditShowLogPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            audit_show_log t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        audit_show_log t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableId != null">
            AND t.ref_table_id = #{qo.refTableId}
        </if>
        <if test="qo.refTableName != null and qo.refTableName != ''">
            AND t.ref_table_name = #{qo.refTableName}
        </if>
        <if test="qo.refTableRefNum != null and qo.refTableRefNum != ''">
            AND t.ref_table_ref_num = #{qo.refTableRefNum}
        </if>
        <if test="qo.refTableRefNumList != null and qo.refTableRefNumList.size > 0 ">
            AND t.ref_table_ref_num in
            <foreach collection="qo.refTableRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refTableShowName != null and qo.refTableShowName != ''">
            AND t.ref_table_show_name = #{qo.refTableShowName}
        </if>
        <if test="qo.refTableShowRefNum != null and qo.refTableShowRefNum != ''">
            AND t.ref_table_show_ref_num = #{qo.refTableShowRefNum}
        </if>
        <if test="qo.refTableShowRefNumList != null and qo.refTableShowRefNumList.size > 0 ">
            AND t.ref_table_show_ref_num in
            <foreach collection="qo.refTableShowRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.event != null and qo.event != ''">
            AND t.event = #{qo.event}
        </if>
        <if test="qo.eventList != null and qo.eventList.size > 0 ">
            AND t.event in
            <foreach collection="qo.eventList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.type != null and qo.type != ''">
            AND t.type = #{qo.type}
        </if>
        <if test="qo.typeList != null and qo.typeList.size > 0 ">
            AND t.type in
            <foreach collection="qo.typeList" item="type" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.showFlag != null">
            AND t.show_flag = #{qo.showFlag}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.description != null and qo.description != ''">
            AND t.description = #{qo.description}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
    </sql>

</mapper>