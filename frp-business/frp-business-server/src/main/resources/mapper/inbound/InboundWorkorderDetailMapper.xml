<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundWorkorderDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.inbound.InboundWorkorderDetail">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="inbound_request_detail_id" property="inboundRequestDetailId" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="product_id" property="productId" />
        <result column="inbound_workorder_id" property="inboundWorkorderId" />
        <result column="line_num" property="lineNum" />
        <result column="note" property="note" />
        <result column="qty" property="qty" />
        <result column="finish_qty" property="finishQty" />
        <result column="request_detail_snapshot_qty" property="requestDetailSnapshotQty" />
        <result column="request_detail_snapshot_line_num" property="requestDetailSnapshotLineNum" />
        <result column="request_detail_snapshot_note" property="requestDetailSnapshotNote" />
        <result column="request_detail_snapshot_product_id" property="requestDetailSnapshotProductId" />
        <result column="need_receive_qty" property="needReceiveQty" />
        <result column="remeasure_flag" property="remeasureFlag" />
        <result column="product_version_id" property="productVersionId" />
        <result column="request_detail_snapshot_detail_request_ref_num" property="requestDetailSnapshotDetailRequestRefNum" />
        <result column="request_detail_snapshot_detail_type" property="requestDetailSnapshotDetailType" />
        <result column="remeasure_type" property="remeasureType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.inbound_request_detail_id,
        t.version,
        t.tenant_id,
        t.warehouse_id,
        t.product_id,
        t.inbound_workorder_id,
        t.line_num,
        t.note,
        t.qty,
        t.finish_qty,
        t.request_detail_snapshot_qty,
        t.request_detail_snapshot_line_num,
        t.request_detail_snapshot_note,
        t.request_detail_snapshot_product_id,
        t.need_receive_qty,
        t.remeasure_flag,
        t.product_version_id,
        t.request_detail_snapshot_detail_request_ref_num,
        t.request_detail_snapshot_detail_type,
        t.remeasure_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.InboundWorkorderDetailPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            inbound_workorder_detail t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundRequestDetailId != null">
            AND t.inbound_request_detail_id = #{qo.inboundRequestDetailId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.inboundWorkorderId != null">
            AND t.inbound_workorder_id = #{qo.inboundWorkorderId}
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.qty != null">
            AND t.qty = #{qo.qty}
        </if>
        <if test="qo.finishQty != null">
            AND t.finish_qty = #{qo.finishQty}
        </if>
        <if test="qo.requestDetailSnapshotQty != null">
            AND t.request_detail_snapshot_qty = #{qo.requestDetailSnapshotQty}
        </if>
        <if test="qo.requestDetailSnapshotLineNum != null">
            AND t.request_detail_snapshot_line_num = #{qo.requestDetailSnapshotLineNum}
        </if>
        <if test="qo.requestDetailSnapshotNote != null and qo.requestDetailSnapshotNote != ''">
            AND t.request_detail_snapshot_note = #{qo.requestDetailSnapshotNote}
        </if>
        <if test="qo.requestDetailSnapshotProductId != null">
            AND t.request_detail_snapshot_product_id = #{qo.requestDetailSnapshotProductId}
        </if>
        <if test="qo.needReceiveQty != null">
            AND t.need_receive_qty = #{qo.needReceiveQty}
        </if>
        <if test="qo.remeasureFlag != null">
            AND t.remeasure_flag = #{qo.remeasureFlag}
        </if>
        <if test="qo.productVersionId != null">
            AND t.product_version_id = #{qo.productVersionId}
        </if>
        <if test="qo.requestDetailSnapshotDetailRequestRefNum != null and qo.requestDetailSnapshotDetailRequestRefNum != ''">
            AND t.request_detail_snapshot_detail_request_ref_num = #{qo.requestDetailSnapshotDetailRequestRefNum}
        </if>
        <if test="qo.requestDetailSnapshotDetailRequestRefNumList != null and qo.requestDetailSnapshotDetailRequestRefNumList.size > 0 ">
            AND t.request_detail_snapshot_detail_request_ref_num in
            <foreach collection="qo.requestDetailSnapshotDetailRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestDetailSnapshotDetailType != null and qo.requestDetailSnapshotDetailType != ''">
            AND t.request_detail_snapshot_detail_type = #{qo.requestDetailSnapshotDetailType}
        </if>
        <if test="qo.requestDetailSnapshotDetailTypeList != null and qo.requestDetailSnapshotDetailTypeList.size > 0 ">
            AND t.request_detail_snapshot_detail_type in
            <foreach collection="qo.requestDetailSnapshotDetailTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>