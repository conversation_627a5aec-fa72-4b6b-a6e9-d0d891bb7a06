<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundPalletMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.vo.page.InboundPalletPageVO">
        <id column="tid" property="id" />
        <result column="create_by" property="createBy" />
        <result column="update_time" property="updateTime" />
        <result column="create_time" property="createTime" />
        <result column="note" property="note" />
        <result column="pallet_status" property="palletStatus" />
        <result column="print_status" property="printStatus" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="carton_per_layer" property="cartonPerLayer" />
        <result column="layers_count" property="layersCount" />
        <result column="ext_carton" property="extCarton" />
        <result column="pcs_per_carton" property="pcsPerCarton" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id as tid,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.note,
        t.pallet_status,
        t.print_status,
        t.ref_num,
        t.warehouse_id,
        t.bin_location_id,
        t.carton_per_layer,
        t.layers_count,
        t.ext_carton,
        t.pcs_per_carton,
        t.product_id,
        t.product_version_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            inbound_pallet t
        WHERE
            t.remove_flag = 0
            <if test="qo.inboundWorkorderQuery!=null">
                and exists(  select iw.id
                             from inbound_workorder iw
                             iw.id=t.inbound_workorder_id and iw.remove_flag=0
                             <include refid="Base_inbound_workorder_Where_List"/>
                           )
            </if>
            <if test="qo.binLocationQuery!=null">
                and exists(select bl.id
                           from bin_location bl
                           where bl.id=t.bin_location_id and bl.remove_flag=0
                           <include refid="Base_bin_location_Where_List"/>
                           )
            </if>
        <include refid="Base_Where_List" />
    </select>

    <sql id="Base_bin_location_Where_List">
        <if test="qo.binLocationQuery.locationNameList!= null and qo.binLocationQuery.locationNameList!= ''">
            AND bl.location_name in <foreach collection="qo.binLocationQuery.locationNameList" item="locationName" open="(" separator="," close=")">
                #{locationName}
            </foreach>
        </if>
    </sql>

    <sql id="Base_inbound_workorder_Where_List">
        <if test="qo.inboundWorkorderQuery.requestSnapshotRequestRefNumList != null and qo.inboundWorkorderQuery.requestSnapshotRequestRefNumList.size > 0 ">
            AND iw.request_snapshot_request_ref_num in
            <foreach collection="qo.inboundWorkorderQuery.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderQuery.requestSnapshotTrackingNumList != null and qo.inboundWorkorderQuery.requestSnapshotTrackingNumList.size > 0 ">
            AND iw.request_snapshot_tracking_num in
            <foreach collection="qo.inboundWorkorderQuery.requestSnapshotTrackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderQuery.requestSnapshotRefNumList != null and qo.inboundWorkorderQuery.requestSnapshotRefNumList.size > 0 ">
            AND iw.request_snapshot_ref_num in
            <foreach collection="qo.inboundWorkorderQuery.requestSnapshotRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderQuery.refNumList != null and qo.inboundWorkorderQuery.refNumList.size > 0 ">
            AND iw.ref_num in
            <foreach collection="qo.inboundWorkorderQuery.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <!--入库工单id-->
        <if test="qo.inboundWorkOrderIdList != null and qo.inboundWorkOrderIdList.size > 0 ">
            and exists(
                select 1
                from inbound_pallet_detail as ipd
                where ipd.inbound_pallet_id=t.id AND ipd.inbound_workorder_id in <foreach collection="qo.inboundWorkOrderIdList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            )
        </if>
        <if test="qo.inboundWorkOrderId != null">
            and exists(
                select 1
                from inbound_pallet_detail as ipd
                where ipd.inbound_pallet_id=t.id AND ipd.inbound_workorder_id = #{qo.inboundWorkOrderId}
            )
        </if>
        <!--库位id-->
        <if test="qo.binLocationIdList != null and qo.binLocationIdList.size > 0 ">
            AND t.bin_Location_id in
            <foreach collection="qo.binLocationIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.palletStatus != null and qo.palletStatus != ''">
            AND t.pallet_status = #{qo.palletStatus}
        </if>
        <if test="qo.palletStatusList != null and qo.palletStatusList.size > 0 ">
            AND t.pallet_status in
            <foreach collection="qo.palletStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
    </sql>

</mapper>