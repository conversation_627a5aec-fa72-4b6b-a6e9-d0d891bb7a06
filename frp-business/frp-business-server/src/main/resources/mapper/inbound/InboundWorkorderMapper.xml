<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundWorkorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.inbound.InboundWorkorder">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="request_snapshot_transport_method_type" property="transportMethodType" />
        <result column="request_snapshot_transaction_partner_id" property="transactionPartnerId" />
        <result column="inbound_request_id" property="inboundRequestId" />
        <result column="request_snapshot_request_ref_num" property="requestSnapshotRequestRefNum" />
        <result column="request_snapshot_estimate_arrival_date" property="estimateArrivalDate" />
        <result column="request_snapshot_actual_arrival_date" property="actualArrivalDate" />
        <result column="request_snapshot_tracking_num" property="trackingNum" />
        <result column="request_snapshot_from_address_name" property="fromAddressName" />
        <result column="request_snapshot_from_address_company" property="fromAddressCompany" />
        <result column="request_snapshot_from_address_country" property="fromAddressCountry" />
        <result column="request_snapshot_from_address_state" property="fromAddressState" />
        <result column="request_snapshot_from_address_city" property="fromAddressCity" />
        <result column="request_snapshot_from_address_zip_code" property="fromAddressZipCode" />
        <result column="request_snapshot_from_address_addr1" property="fromAddressAddr1" />
        <result column="request_snapshot_from_address_addr2" property="fromAddressAddr2" />
        <result column="request_snapshot_from_address_addr3" property="fromAddressAddr3" />
        <result column="request_snapshot_from_address_email" property="fromAddressEmail" />
        <result column="request_snapshot_from_address_phone" property="fromAddressPhone" />
        <result column="request_snapshot_note" property="requestSnapshotNote" />
        <result column="inbound_workorder_unload_status" property="inboundWorkorderUnloadStatus" />
        <result column="inbound_workorder_putaway_status" property="inboundWorkorderPutawayStatus" />
        <result column="print_status" property="printStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="note" property="note" />
        <result column="request_snapshot_from_address_note" property="fromAddressNote" />
        <result column="request_snapshot_ref_num" property="requestSnapshotRefNum" />
        <result column="inbound_workorder_status" property="inboundWorkorderStatus" />
        <result column="request_snapshot_container_type" property="containerType" />
        <result column="request_snapshot_from_address_is_residential" property="fromAddressIsResidential" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.request_snapshot_transport_method_type,
        t.request_snapshot_transaction_partner_id,
        t.inbound_request_id,
        t.request_snapshot_request_ref_num,
        t.request_snapshot_estimate_arrival_date,
        t.request_snapshot_actual_arrival_date,
        t.request_snapshot_tracking_num,
        t.request_snapshot_from_address_name,
        t.request_snapshot_from_address_company,
        t.request_snapshot_from_address_country,
        t.request_snapshot_from_address_state,
        t.request_snapshot_from_address_city,
        t.request_snapshot_from_address_zip_code,
        t.request_snapshot_from_address_addr1,
        t.request_snapshot_from_address_addr2,
        t.request_snapshot_from_address_addr3,
        t.request_snapshot_from_address_email,
        t.request_snapshot_from_address_phone,
        t.request_snapshot_note,
        t.inbound_workorder_unload_status,
        t.inbound_workorder_putaway_status,
        t.print_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.note,
        t.request_snapshot_from_address_note,
        t.request_snapshot_ref_num,
        t.inbound_workorder_status,
        t.request_snapshot_container_type,
        t.request_snapshot_from_address_is_residential
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.InboundWorkorderPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            inbound_workorder t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        inbound_workorder t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotTransactionPartnerId != null">
            AND t.request_snapshot_transaction_partner_id = #{qo.requestSnapshotTransactionPartnerId}
        </if>
        <if test="qo.requestSnapshotTransactionPartnerIdList != null and qo.requestSnapshotTransactionPartnerIdList.size > 0 ">
            AND t.request_snapshot_transaction_partner_id in
            <foreach collection="qo.requestSnapshotTransactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundRequestId != null">
            AND t.inbound_request_id = #{qo.inboundRequestId}
        </if>
        <if test="qo.requestSnapshotRequestRefNum != null and qo.requestSnapshotRequestRefNum != ''">
            AND t.request_snapshot_request_ref_num = #{qo.requestSnapshotRequestRefNum}
        </if>
        <if test="qo.requestSnapshotRequestRefNumList != null and qo.requestSnapshotRequestRefNumList.size > 0 ">
            AND t.request_snapshot_request_ref_num in
            <foreach collection="qo.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotEstimateArrivalDateStart != null">
            AND t.request_snapshot_estimate_arrival_date  &gt;= #{qo.requestSnapshotEstimateArrivalDateStart}
        </if>
        <if test="qo.requestSnapshotEstimateArrivalDateEnd != null">
            AND t.request_snapshot_estimate_arrival_date  &lt; #{qo.requestSnapshotEstimateArrivalDateEnd}
        </if>
        <if test="qo.requestSnapshotActualArrivalDateStart != null">
            AND t.request_snapshot_actual_arrival_date  &gt;= #{qo.requestSnapshotActualArrivalDateStart}
        </if>
        <if test="qo.requestSnapshotActualArrivalDateEnd != null">
            AND t.request_snapshot_actual_arrival_date  &lt; #{qo.requestSnapshotActualArrivalDateEnd}
        </if>
        <if test="qo.requestSnapshotTrackingNum != null and qo.requestSnapshotTrackingNum != ''">
            AND t.request_snapshot_tracking_num = #{qo.requestSnapshotTrackingNum}
        </if>
        <if test="qo.requestSnapshotTrackingNumList != null and qo.requestSnapshotTrackingNumList.size > 0 ">
            AND t.request_snapshot_tracking_num in
            <foreach collection="qo.requestSnapshotTrackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderUnloadStatus != null and qo.inboundWorkorderUnloadStatus != ''">
            AND t.inbound_workorder_unload_status = #{qo.inboundWorkorderUnloadStatus}
        </if>
        <if test="qo.inboundWorkorderUnloadStatusList != null and qo.inboundWorkorderUnloadStatusList.size > 0 ">
            AND t.inbound_workorder_unload_status in
            <foreach collection="qo.inboundWorkorderUnloadStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotRefNum != null and qo.requestSnapshotRefNum != ''">
            AND t.request_snapshot_ref_num = #{qo.requestSnapshotRefNum}
        </if>
        <if test="qo.requestSnapshotRefNumList != null and qo.requestSnapshotRefNumList.size > 0 ">
            AND t.request_snapshot_ref_num in
            <foreach collection="qo.requestSnapshotRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundWorkorderStatus != null and qo.inboundWorkorderStatus != ''">
            AND t.inbound_workorder_status = #{qo.inboundWorkorderStatus}
        </if>
        <if test="qo.inboundWorkorderStatusList != null and qo.inboundWorkorderStatusList.size > 0 ">
            AND t.inbound_workorder_status in
            <foreach collection="qo.inboundWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>