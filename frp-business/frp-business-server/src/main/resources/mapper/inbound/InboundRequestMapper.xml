<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.inbound.InboundRequestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.inbound.InboundRequest">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="transport_method_type" property="transportMethodType"/>
        <result column="request_ref_num" property="requestRefNum"/>
        <result column="estimate_arrival_date" property="estimateArrivalDate"/>
        <result column="actual_arrival_date" property="actualArrivalDate"/>
        <result column="tracking_num" property="trackingNum"/>
        <result column="from_address_name" property="fromAddressName"/>
        <result column="from_address_company" property="fromAddressCompany"/>
        <result column="from_address_country" property="fromAddressCountry"/>
        <result column="from_address_state" property="fromAddressState"/>
        <result column="from_address_city" property="fromAddressCity"/>
        <result column="from_address_zip_code" property="fromAddressZipCode"/>
        <result column="from_address_addr1" property="fromAddressAddr1"/>
        <result column="from_address_addr2" property="fromAddressAddr2"/>
        <result column="from_address_addr3" property="fromAddressAddr3"/>
        <result column="from_address_email" property="fromAddressEmail"/>
        <result column="from_address_phone" property="fromAddressPhone"/>
        <result column="from_address_note" property="fromAddressNote"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="ref_num" property="refNum"/>
        <result column="transaction_partner_id" property="transactionPartnerId"/>
        <result column="inbound_request_status" property="inboundRequestStatus"/>
        <result column="note" property="note"/>
        <result column="container_type" property="containerType"/>
        <result column="from_address_is_residential" property="fromAddressIsResidential"/>
        <result column="process_start_time" property="processStartTime" />
        <result column="process_end_time" property="processEndTime" />
        <result column="fee_status" property="feeStatus" />
        <result column="fee_calculation_time" property="feeCalculationTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.transport_method_type,
        t.request_ref_num,
        t.estimate_arrival_date,
        t.actual_arrival_date,
        t.tracking_num,
        t.from_address_name,
        t.from_address_company,
        t.from_address_country,
        t.from_address_state,
        t.from_address_city,
        t.from_address_zip_code,
        t.from_address_addr1,
        t.from_address_addr2,
        t.from_address_addr3,
        t.from_address_email,
        t.from_address_phone,
        t.from_address_note,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.transaction_partner_id,
        t.inbound_request_status,
        t.note,
        t.product_type,
        t.process_start_time,
        t.process_end_time,
        t.fee_status,
        t.fee_calculation_time,
        t.container_type,
        t.from_address_is_residential
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <resultMap id="list" type="cn.need.cloud.biz.model.vo.page.InboundRequestPageVO" autoMapping="true">
        <id column="id" property="id"></id>
        <association property="baseWarehouseVO" autoMapping="true">
            <result property="refNum" column="wRefNum"></result>
        </association>
    </resultMap>
    <!-- todo: 这里要去掉Warehouse join -->
    <select id="listByQuery" resultMap="list">
        SELECT
        w.code,
        w.name,
        w.ref_num as wRefNum,
        <include refid="Base_Column_List"/>
        FROM
        inbound_request t
        left join
        warehouse w on t.warehouse_id=w.id
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"/>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        inbound_request t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">

        <if test="qo.id != null">
            AND t.id = #{qo.id}
        </if>
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.idNiList != null and qo.idNiList.size > 0 ">
            AND t.id not in
            <foreach collection="qo.idNiList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <!--仓库id-->
        <if test="qo.warehouseIdList != null and qo.warehouseIdList.size > 0 ">
            AND t.warehouse_id in
            <foreach collection="qo.warehouseIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transportMethodType != null and qo.transportMethodType != ''">
            AND t.transport_method_type = #{qo.transportMethodType}
        </if>
        <if test="qo.transportMethodTypeList != null and qo.transportMethodTypeList.size > 0 ">
            AND t.transport_method_type in
            <foreach collection="qo.transportMethodTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestRefNum != null and qo.requestRefNum != ''">
            AND t.request_ref_num = #{qo.requestRefNum}
        </if>
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND t.request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.estimateArrivalDateStart != null">
            AND t.estimate_arrival_date &gt;= #{qo.estimateArrivalDateStart}
        </if>
        <if test="qo.estimateArrivalDateEnd != null">
            AND t.estimate_arrival_date &lt; #{qo.estimateArrivalDateEnd}
        </if>
        <if test="qo.actualArrivalDateStart != null">
            AND t.actual_arrival_date &gt;= #{qo.actualArrivalDateStart}
        </if>
        <if test="qo.actualArrivalDateEnd != null">
            AND t.actual_arrival_date &lt; #{qo.actualArrivalDateEnd}
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.tracking_num = #{qo.trackingNum}
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.tracking_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fromAddressName != null and qo.fromAddressName != ''">
            AND t.from_address_name = #{qo.fromAddressName}
        </if>
        <if test="qo.fromAddressCompany != null and qo.fromAddressCompany != ''">
            AND t.from_address_company = #{qo.fromAddressCompany}
        </if>
        <if test="qo.fromAddressCountry != null and qo.fromAddressCountry != ''">
            AND t.from_address_country = #{qo.fromAddressCountry}
        </if>
        <if test="qo.fromAddressState != null and qo.fromAddressState != ''">
            AND t.from_address_state = #{qo.fromAddressState}
        </if>
        <if test="qo.fromAddressCity != null and qo.fromAddressCity != ''">
            AND t.from_address_city = #{qo.fromAddressCity}
        </if>
        <if test="qo.fromAddressZipCode != null and qo.fromAddressZipCode != ''">
            AND t.from_address_zip_code = #{qo.fromAddressZipCode}
        </if>
        <if test="qo.fromAddressAddr1 != null and qo.fromAddressAddr1 != ''">
            AND t.from_address_addr1 = #{qo.fromAddressAddr1}
        </if>
        <if test="qo.fromAddressAddr2 != null and qo.fromAddressAddr2 != ''">
            AND t.from_address_addr2 = #{qo.fromAddressAddr2}
        </if>
        <if test="qo.fromAddressAddr3 != null and qo.fromAddressAddr3 != ''">
            AND t.from_address_addr3 = #{qo.fromAddressAddr3}
        </if>
        <if test="qo.fromAddressEmail != null and qo.fromAddressEmail != ''">
            AND t.from_address_email = #{qo.fromAddressEmail}
        </if>
        <if test="qo.fromAddressPhone != null and qo.fromAddressPhone != ''">
            AND t.from_address_phone = #{qo.fromAddressPhone}
        </if>
        <if test="qo.fromAddressNote != null and qo.fromAddressNote != ''">
            AND t.from_address_note = #{qo.fromAddressNote}
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.inboundRequestStatus != null and qo.inboundRequestStatus != ''">
            AND t.inbound_request_status = #{qo.inboundRequestStatus}
        </if>
        <if test="qo.inboundRequestStatusList != null and qo.inboundRequestStatusList.size > 0 ">
            AND t.inbound_request_status in
            <foreach collection="qo.inboundRequestStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.productType != null and qo.productType != ''">
            AND t.product_type = #{qo.productType}
        </if>
        <if test="qo.containerType != null and qo.containerType != ''">
            AND t.container_type = #{qo.containerType}
        </if>
        <if test="qo.containerTypeList != null and qo.containerTypeList.size > 0 ">
            AND t.container_type in
            <foreach collection="qo.containerTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fromAddressIsResidential != null">
            AND t.from_address_is_residential = #{qo.fromAddressIsResidential}
        </if>

        <if test="qo.feeStatus != null and qo.feeStatus != ''">
            AND t.fee_status = #{qo.feeStatus}
        </if>
        <if test="qo.feeStatusList != null and qo.feeStatusList.size > 0 ">
            AND t.fee_status in
            <foreach collection="qo.feeStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>