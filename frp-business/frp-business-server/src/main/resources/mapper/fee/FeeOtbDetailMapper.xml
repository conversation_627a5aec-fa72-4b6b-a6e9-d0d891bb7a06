<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.fee.FeeOtbDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.fee.FeeOtbDetail">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="fee" property="fee" />
        <result column="fee_config_detail_id" property="feeConfigDetailId" />
        <result column="fee_config_id" property="feeConfigId" />
        <result column="header_id" property="headerId" />
        <result column="line_num" property="lineNum" />
        <result column="note" property="note" />
        <result column="ref_num" property="refNum" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.fee,
        ${dbTableAlias}.fee_config_detail_id,
        ${dbTableAlias}.fee_config_id,
        ${dbTableAlias}.header_id,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.note,
        ${dbTableAlias}.description,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.fee.page.FeeOtbDetailPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="fod"/>
            </include>
        FROM
        fee_otb_detail fod
        WHERE
            fod.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="fod"/>
                    <property name="qoTableAlias" value="qofod"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="fod"/>
                <property name="qoTableAlias" value="qofod"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            <include refid="mapper.DropProList_Select_Columns">
                <property name="dbTableAlias" value="fod"/>
                <property name="qoTableAlias" value="qofod"/>
            </include>
        FROM fee_otb_detail fod
        WHERE fod.remove_flag = 0
            <include refid="Base_Where_List">
                <property name="dbTableAlias" value="fod"/>
                <property name="qoTableAlias" value="qofod"/>
            </include>
        GROUP BY
            <include refid="mapper.DropProList_GroupBy_Columns">
                <property name="dbTableAlias" value="fod"/>
                <property name="qoTableAlias" value="qofod"/>
            </include>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
    <if test="${qoTableAlias} != null">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.id"/>
            <property name="qoColumnName" value="${qoTableAlias}.id"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.createBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.create_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.createTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_by"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateBy"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.update_time"/>
            <property name="qoColumnName" value="${qoTableAlias}.updateTime"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.remove_flag"/>
            <property name="qoColumnName" value="${qoTableAlias}.removeFlag"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.version"/>
            <property name="qoColumnName" value="${qoTableAlias}.version"/>
        </include>
        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.tenant_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.tenantId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.deleted_note"/>
            <property name="qoColumnName" value="${qoTableAlias}.deletedNote"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.fee"/>
            <property name="qoColumnName" value="${qoTableAlias}.fee"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.fee_config_detail_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.feeConfigDetailId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.fee_config_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.feeConfigId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.header_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.headerId"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.line_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.lineNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.note"/>
            <property name="qoColumnName" value="${qoTableAlias}.note"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.ref_num"/>
            <property name="qoColumnName" value="${qoTableAlias}.refNum"/>
        </include>

        <include refid="mapper.valueConditions">
            <property name="dbColumnName" value="${dbTableAlias}.warehouse_id"/>
            <property name="qoColumnName" value="${qoTableAlias}.warehouseId"/>
        </include>

    </if>
    </sql>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM fee_otb_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
                WHERE ${InnerTableAlias}.remove_flag = 0
                    <include refid="Base_Where_List">
                        <property name="dbTableAlias" value="${InnerTableAlias}"/>
                        <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
                    </include>
            )
        </if>
    </sql>
</mapper>