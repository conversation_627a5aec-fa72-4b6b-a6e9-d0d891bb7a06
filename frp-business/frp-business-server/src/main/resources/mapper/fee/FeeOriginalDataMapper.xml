<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.fee.FeeOriginalDataMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.fee.FeeOriginalData">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="deleted_note" property="deletedNote" />
        <result column="extra_data" property="extraData" />
        <result column="fee_calculation_time" property="feeCalculationTime" />
        <result column="fee_model_type" property="feeModelType" />
        <result column="fee_original_status" property="feeOriginalStatus" />
        <result column="note" property="note" />
        <result column="process_end_time" property="processEndTime" />
        <result column="process_start_time" property="processStartTime" />
        <result column="ref_num" property="refNum" />
        <result column="snapshot_ref_num" property="snapshotRefNum" />
        <result column="snapshot_request_id" property="snapshotRequestId" />
        <result column="snapshot_request_ref_num" property="snapshotRequestRefNum" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.deleted_note,
        ${dbTableAlias}.extra_data,
        ${dbTableAlias}.fee_calculation_time,
        ${dbTableAlias}.fee_model_type,
        ${dbTableAlias}.fee_original_status,
        ${dbTableAlias}.note,
        ${dbTableAlias}.process_end_time,
        ${dbTableAlias}.process_start_time,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.snapshot_ref_num,
        ${dbTableAlias}.snapshot_request_id,
        ${dbTableAlias}.snapshot_request_ref_num,
        ${dbTableAlias}.transaction_partner_id,
        ${dbTableAlias}.warehouse_id
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.fee.page.FeeOriginalDataPageVO">
        SELECT
        <include refid="Base_Column_List">
            <property name="dbTableAlias" value="fod"/>
        </include>
        FROM
        fee_original_data fod
        WHERE
        fod.remove_flag = 0
        <include refid="Base_Where_List">
            <property name="dbTableAlias" value="fod"/>
            <property name="qoTableAlias" value="qofod"/>
        </include>
        <include refid="Base_Order_By_List" >
            <property name="dbTableAlias" value="fod"/>
            <property name="qoTableAlias" value="qofod"/>
        </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qofod.timeZone != null and qofod.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qofod.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        fee_original_data fod
        WHERE
        fod.remove_flag = 0
        <include refid="Base_Where_List">
            <property name="dbTableAlias" value="fod"/>
            <property name="qoTableAlias" value="qofod"/>
        </include>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qofod.timeZone != null and qofod.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qofod.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.deletedNote != null and ${qoTableAlias}.deletedNote != ''">
            AND ${dbTableAlias}.deleted_note = #{${qoTableAlias}.deletedNote}
        </if>
        <if test="${qoTableAlias}.extraData != null and ${qoTableAlias}.extraData != ''">
            AND ${dbTableAlias}.extra_data = #{${qoTableAlias}.extraData}
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeStart != null">
            AND ${dbTableAlias}.fee_calculation_time  &gt;= #{${qoTableAlias}.feeCalculationTimeStart}
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeEnd != null">
            AND ${dbTableAlias}.fee_calculation_time  &lt; #{${qoTableAlias}.feeCalculationTimeEnd}
        </if>
        <if test="${qoTableAlias}.feeModelType != null and ${qoTableAlias}.feeModelType != ''">
            AND ${dbTableAlias}.fee_model_type = #{${qoTableAlias}.feeModelType}
        </if>
        <if test="${qoTableAlias}.feeModelTypeList != null and ${qoTableAlias}.feeModelTypeList.size > 0 ">
            AND ${dbTableAlias}.fee_model_type in
            <foreach collection="${qoTableAlias}.feeModelTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.feeOriginalStatus != null and ${qoTableAlias}.feeOriginalStatus != ''">
            AND ${dbTableAlias}.fee_original_status = #{${qoTableAlias}.feeOriginalStatus}
        </if>
        <if test="${qoTableAlias}.feeOriginalStatusList != null and ${qoTableAlias}.feeOriginalStatusList.size > 0 ">
            AND ${dbTableAlias}.fee_original_status in
            <foreach collection="${qoTableAlias}.feeOriginalStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.processEndTimeStart != null">
            AND ${dbTableAlias}.process_end_time  &gt;= #{${qoTableAlias}.processEndTimeStart}
        </if>
        <if test="${qoTableAlias}.processEndTimeEnd != null">
            AND ${dbTableAlias}.process_end_time  &lt; #{${qoTableAlias}.processEndTimeEnd}
        </if>
        <if test="${qoTableAlias}.processStartTimeStart != null">
            AND ${dbTableAlias}.process_start_time  &gt;= #{${qoTableAlias}.processStartTimeStart}
        </if>
        <if test="${qoTableAlias}.processStartTimeEnd != null">
            AND ${dbTableAlias}.process_start_time  &lt; #{${qoTableAlias}.processStartTimeEnd}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
        <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
            AND ${dbTableAlias}.ref_num in
            <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.snapshotRefNum != null and ${qoTableAlias}.snapshotRefNum != ''">
            AND ${dbTableAlias}.snapshot_ref_num = #{${qoTableAlias}.snapshotRefNum}
        </if>
        <if test="${qoTableAlias}.snapshotRefNumList != null and ${qoTableAlias}.snapshotRefNumList.size > 0 ">
            AND ${dbTableAlias}.snapshot_ref_num in
            <foreach collection="${qoTableAlias}.snapshotRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.snapshotRequestId != null">
            AND ${dbTableAlias}.snapshot_request_id = #{${qoTableAlias}.snapshotRequestId}
        </if>
        <if test="${qoTableAlias}.snapshotRequestIdList != null and ${qoTableAlias}.snapshotRequestIdList.size > 0 ">
            AND ${dbTableAlias}.snapshot_request_id in
            <foreach collection="${qoTableAlias}.snapshotRequestIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.snapshotRequestRefNum != null and ${qoTableAlias}.snapshotRequestRefNum != ''">
            AND ${dbTableAlias}.snapshot_request_ref_num = #{${qoTableAlias}.snapshotRequestRefNum}
        </if>
        <if test="${qoTableAlias}.snapshotRequestRefNumList != null and ${qoTableAlias}.snapshotRequestRefNumList.size > 0 ">
            AND ${dbTableAlias}.snapshot_request_ref_num in
            <foreach collection="${qoTableAlias}.snapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.transactionPartnerId != null">
            AND ${dbTableAlias}.transaction_partner_id = #{${qoTableAlias}.transactionPartnerId}
        </if>
        <if test="${qoTableAlias}.transactionPartnerIdList != null and ${qoTableAlias}.transactionPartnerIdList.size > 0 ">
            AND ${dbTableAlias}.transaction_partner_id in
            <foreach collection="${qoTableAlias}.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
        <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
            AND ${dbTableAlias}.warehouse_id in
            <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.fee.page.FeeOriginalDataPageVO">
        SELECT
        <include refid="Base_Column_List">
            <property name="dbTableAlias" value="fod"/>
        </include>
        FROM
        fee_original_data fod
        WHERE
        fod.remove_flag = 0
        <include refid="mapper.valueConditionsPro">
            <property name="dbTableAlias" value="fod" />
            <property name="qoTableAlias" value="qofod" />
        </include>
        <include refid="mapper.valueOrConditionsPro">
            <property name="dbTableAlias" value="fod" />
            <property name="qoTableAlias" value="qofod" />
        </include>
        <include refid="Base_Order_By_List" >
            <property name="dbTableAlias" value="fod"/>
        </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
            SELECT ${InnerTableAlias}.id
            FROM fee_original_data ${InnerTableAlias}
            WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.fee.FeeOriginalDataMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>