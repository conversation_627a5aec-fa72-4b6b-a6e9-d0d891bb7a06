<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.storage.StorageSnapshotRequestMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequest">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="fee_calculation_time" property="feeCalculationTime" />
        <result column="fee_status" property="feeStatus" />
        <result column="note" property="note" />
        <result column="ref_num" property="refNum" />
        <result column="request_status" property="requestStatus" />
        <result column="snapshot_date" property="snapshotDate" />
        <result column="snapshot_time" property="snapshotTime" />
        <result column="total_qty" property="totalQty" />
        <result column="total_volume" property="totalVolume" />
        <result column="total_weight" property="totalWeight" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="time_zone" property="timeZone" />
        <result column="process_start_time" property="processStartTime" />
        <result column="process_end_time" property="processEndTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.fee_calculation_time,
        ${dbTableAlias}.fee_status,
        ${dbTableAlias}.note,
        ${dbTableAlias}.ref_num,
        ${dbTableAlias}.request_status,
        ${dbTableAlias}.snapshot_date,
        ${dbTableAlias}.snapshot_time,
        ${dbTableAlias}.total_qty,
        ${dbTableAlias}.total_volume,
        ${dbTableAlias}.total_weight,
        ${dbTableAlias}.transaction_partner_id,
        ${dbTableAlias}.warehouse_id,
        ${dbTableAlias}.time_zone,
        ${dbTableAlias}.process_start_time,
        ${dbTableAlias}.process_end_time
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestPageVO">
        SELECT
        <include refid="Base_Column_List">
            <property name="dbTableAlias" value="ssr"/>
        </include>
        FROM
        storage_snapshot_request ssr
        WHERE
            ssr.remove_flag = 0
        <include refid="Base_Where_List">
            <property name="dbTableAlias" value="ssr"/>
            <property name="qoTableAlias" value="qossr"/>
        </include>
        <include refid="Base_Order_By_List">
            <property name="dbTableAlias" value="ssr"/>
            <property name="qoTableAlias" value="qossr"/>
        </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qossr.timeZone != null and qossr.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qossr.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                        ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
            storage_snapshot_request ssr
        WHERE
            ssr.remove_flag = 0
        <include refid="Base_Where_List">
            <property name="dbTableAlias" value="ssr"/>
            <property name="qoTableAlias" value="qossr"/>
        </include>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qossr.timeZone != null and qossr.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qossr.timeZone}), "${item.format}")
                </when>
                <otherwise>
                        ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeStart != null">
            AND ${dbTableAlias}.fee_calculation_time &gt;= #{${qoTableAlias}.feeCalculationTimeStart}
        </if>
        <if test="${qoTableAlias}.feeCalculationTimeEnd != null">
            AND ${dbTableAlias}.fee_calculation_time  &lt; #{${qoTableAlias}.feeCalculationTimeEnd}
        </if>
        <if test="${qoTableAlias}.feeStatus != null and ${qoTableAlias}.feeStatus != ''">
            AND ${dbTableAlias}.fee_status = #{${qoTableAlias}.feeStatus}
        </if>
        <if test="${qoTableAlias}.feeStatusList != null and ${qoTableAlias}.feeStatusList.size > 0 ">
        AND ${dbTableAlias}.fee_status in
            <foreach collection="${qoTableAlias}.feeStatusList" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.refNum != null and ${qoTableAlias}.refNum != ''">
            AND ${dbTableAlias}.ref_num = #{${qoTableAlias}.refNum}
        </if>
        <if test="${qoTableAlias}.refNumList != null and ${qoTableAlias}.refNumList.size > 0 ">
        AND ${dbTableAlias}.ref_num in
            <foreach collection="${qoTableAlias}.refNumList" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.requestStatus != null and ${qoTableAlias}.requestStatus != ''">
            AND ${dbTableAlias}.request_status = #{${qoTableAlias}.requestStatus}
        </if>
        <if test="${qoTableAlias}.requestStatusList != null and ${qoTableAlias}.requestStatusList.size > 0 ">
        AND ${dbTableAlias}.request_status in
            <foreach collection="${qoTableAlias}.requestStatusList" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.snapshotDate != null">
            AND ${dbTableAlias}.snapshot_date = #{${qoTableAlias}.snapshotDate}
        </if>
        <if test="${qoTableAlias}.snapshotTimeStart != null">
            AND ${dbTableAlias}.snapshot_time &gt;= #{${qoTableAlias}.snapshotTimeStart}
        </if>
        <if test="${qoTableAlias}.snapshotTimeEnd != null">
            AND ${dbTableAlias}.snapshot_time  &lt; #{${qoTableAlias}.snapshotTimeEnd}
        </if>
        <if test="${qoTableAlias}.totalQty != null">
            AND ${dbTableAlias}.total_qty = #{${qoTableAlias}.totalQty}
        </if>
        <if test="${qoTableAlias}.totalVolume != null">
            AND ${dbTableAlias}.total_volume = #{${qoTableAlias}.totalVolume}
        </if>
        <if test="${qoTableAlias}.totalWeight != null">
            AND ${dbTableAlias}.total_weight = #{${qoTableAlias}.totalWeight}
        </if>
        <if test="${qoTableAlias}.transactionPartnerId != null">
            AND ${dbTableAlias}.transaction_partner_id = #{${qoTableAlias}.transactionPartnerId}
        </if>
        <if test="${qoTableAlias}.transactionPartnerIdList != null and ${qoTableAlias}.transactionPartnerIdList.size > 0 ">
        AND ${dbTableAlias}.transaction_partner_id in
            <foreach collection="${qoTableAlias}.transactionPartnerIdList" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
        <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
            <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
            </foreach>
        </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestPageVO">
        SELECT
        <include refid="Base_Column_List">
            <property name="dbTableAlias" value="ssr"/>
        </include>
        FROM
            storage_snapshot_request ssr
        WHERE
            ssr.remove_flag = 0
        <include refid="mapper.valueConditionsPro">
            <property name="dbTableAlias" value="ssr" />
            <property name="qoTableAlias" value="qossr" />
        </include>
        <include refid="mapper.valueOrConditionsPro">
            <property name="dbTableAlias" value="ssr" />
            <property name="qoTableAlias" value="qossr" />
        </include>
        <include refid="Base_Order_By_List">
            <property name="dbTableAlias" value="ssr"/>
        </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM storage_snapshot_request ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.storage.StorageSnapshotRequestMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>