<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.storage.StorageSnapshotRequestDetailMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.storage.StorageSnapshotRequestDetail">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="bin_location_detail_id" property="binLocationDetailId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="line_num" property="lineNum" />
        <result column="note" property="note" />
        <result column="product_id" property="productId" />
        <result column="product_version_id" property="productVersionId" />
        <result column="qty" property="qty" />
        <result column="storage_snapshot_request_id" property="storageSnapshotRequestId" />
        <result column="volume" property="volume" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="weight" property="weight" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ${dbTableAlias}.id,
        ${dbTableAlias}.create_by,
        ${dbTableAlias}.create_time,
        ${dbTableAlias}.update_by,
        ${dbTableAlias}.update_time,
        ${dbTableAlias}.remove_flag,
        ${dbTableAlias}.version,
        ${dbTableAlias}.tenant_id,
        ${dbTableAlias}.bin_location_detail_id,
        ${dbTableAlias}.bin_location_id,
        ${dbTableAlias}.line_num,
        ${dbTableAlias}.note,
        ${dbTableAlias}.product_id,
        ${dbTableAlias}.product_version_id,
        ${dbTableAlias}.qty,
        ${dbTableAlias}.storage_snapshot_request_id,
        ${dbTableAlias}.volume,
        ${dbTableAlias}.warehouse_id,
        ${dbTableAlias}.weight
    </sql>
    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestDetailPageVO">
        SELECT
            <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ssrd"/>
            </include>
        FROM
        storage_snapshot_request_detail ssrd
        WHERE
            ssrd.remove_flag = 0
            <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ssrd"/>
                    <property name="qoTableAlias" value="qossrd"/>
                </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ssrd"/>
                <property name="qoTableAlias" value="qossrd"/>
            </include>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
            COUNT(*) AS countNum,
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qossrd.timeZone != null and qossrd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qossrd.timeZone}), "${item.format}") AS ${item.name}
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
        FROM
            storage_snapshot_request_detail ssrd
        WHERE
            ssrd.remove_flag = 0
                <include refid="Base_Where_List">
                    <property name="dbTableAlias" value="ssrd"/>
                    <property name="qoTableAlias" value="qossrd"/>
                </include>
        GROUP BY
            <foreach collection="columnList" item="item" separator=",">
                <choose>
                    <when test="qossrd.timeZone != null and qossrd.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                        DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qossrd.timeZone}), "${item.format}")
                    </when>
                    <otherwise>
                        ${item.name}
                    </otherwise>
                </choose>
            </foreach>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <if test="page == null or page.orders == null or page.orders.size() == 0">
            ORDER BY ${dbTableAlias}.create_time DESC
        </if>
    </sql>
    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="${qoTableAlias}.createTimeStart != null">
            AND ${dbTableAlias}.create_time  &gt;= #{${qoTableAlias}.createTimeStart}
        </if>
        <if test="${qoTableAlias}.createTimeEnd != null">
            AND ${dbTableAlias}.create_time  &lt; #{${qoTableAlias}.createTimeEnd}
        </if>
        <if test="${qoTableAlias}.updateTimeStart != null">
            AND ${dbTableAlias}.update_time  &gt;= #{${qoTableAlias}.updateTimeStart}
        </if>
        <if test="${qoTableAlias}.updateTimeEnd != null">
            AND ${dbTableAlias}.update_time  &lt; #{${qoTableAlias}.updateTimeEnd}
        </if>
        <if test="${qoTableAlias}.createBy != null">
            AND ${dbTableAlias}.create_by = #{${qoTableAlias}.createBy}
        </if>
        <if test="${qoTableAlias}.createByList != null and ${qoTableAlias}.createByList.size > 0 ">
            AND ${dbTableAlias}.create_by in
            <foreach collection="${qoTableAlias}.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.updateBy != null">
            AND ${dbTableAlias}.update_by = #{${qoTableAlias}.updateBy}
        </if>
        <if test="${qoTableAlias}.updateByList != null and ${qoTableAlias}.updateByList.size > 0 ">
            AND ${dbTableAlias}.update_by in
            <foreach collection="${qoTableAlias}.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="${qoTableAlias}.binLocationDetailId != null">
            AND ${dbTableAlias}.bin_location_detail_id = #{${qoTableAlias}.binLocationDetailId}
        </if>
    <if test="${qoTableAlias}.binLocationDetailIdList != null and ${qoTableAlias}.binLocationDetailIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_detail_id in
        <foreach collection="${qoTableAlias}.binLocationDetailIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.binLocationId != null">
            AND ${dbTableAlias}.bin_location_id = #{${qoTableAlias}.binLocationId}
        </if>
    <if test="${qoTableAlias}.binLocationIdList != null and ${qoTableAlias}.binLocationIdList.size > 0 ">
        AND ${dbTableAlias}.bin_location_id in
        <foreach collection="${qoTableAlias}.binLocationIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.lineNum != null">
            AND ${dbTableAlias}.line_num = #{${qoTableAlias}.lineNum}
        </if>
        <if test="${qoTableAlias}.note != null and ${qoTableAlias}.note != ''">
            AND ${dbTableAlias}.note = #{${qoTableAlias}.note}
        </if>
        <if test="${qoTableAlias}.productId != null">
            AND ${dbTableAlias}.product_id = #{${qoTableAlias}.productId}
        </if>
    <if test="${qoTableAlias}.productIdList != null and ${qoTableAlias}.productIdList.size > 0 ">
        AND ${dbTableAlias}.product_id in
        <foreach collection="${qoTableAlias}.productIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.productVersionId != null">
            AND ${dbTableAlias}.product_version_id = #{${qoTableAlias}.productVersionId}
        </if>
    <if test="${qoTableAlias}.productVersionIdList != null and ${qoTableAlias}.productVersionIdList.size > 0 ">
        AND ${dbTableAlias}.product_version_id in
        <foreach collection="${qoTableAlias}.productVersionIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.qty != null">
            AND ${dbTableAlias}.qty = #{${qoTableAlias}.qty}
        </if>
        <if test="${qoTableAlias}.storageSnapshotRequestId != null">
            AND ${dbTableAlias}.storage_snapshot_request_id = #{${qoTableAlias}.storageSnapshotRequestId}
        </if>
    <if test="${qoTableAlias}.storageSnapshotRequestIdList != null and ${qoTableAlias}.storageSnapshotRequestIdList.size > 0 ">
        AND ${dbTableAlias}.storage_snapshot_request_id in
        <foreach collection="${qoTableAlias}.storageSnapshotRequestIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.volume != null">
            AND ${dbTableAlias}.volume = #{${qoTableAlias}.volume}
        </if>
        <if test="${qoTableAlias}.warehouseId != null">
            AND ${dbTableAlias}.warehouse_id = #{${qoTableAlias}.warehouseId}
        </if>
    <if test="${qoTableAlias}.warehouseIdList != null and ${qoTableAlias}.warehouseIdList.size > 0 ">
        AND ${dbTableAlias}.warehouse_id in
        <foreach collection="${qoTableAlias}.warehouseIdList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </if>
        <if test="${qoTableAlias}.weight != null">
            AND ${dbTableAlias}.weight = #{${qoTableAlias}.weight}
        </if>
    </sql>

    <select id="listByQueryPro" resultType="cn.need.cloud.biz.model.vo.page.StorageSnapshotRequestDetailPageVO">
        SELECT
           <include refid="Base_Column_List">
                <property name="dbTableAlias" value="ssrd"/>
           </include>
        FROM
            storage_snapshot_request_detail ssrd
        WHERE
            ssrd.remove_flag = 0
            <include refid="mapper.valueConditionsPro">
                <property name="dbTableAlias" value="ssrd" />
                <property name="qoTableAlias" value="qossrd" />
            </include>
            <include refid="mapper.valueOrConditionsPro">
                <property name="dbTableAlias" value="ssrd" />
                <property name="qoTableAlias" value="qossrd" />
            </include>
            <include refid="Base_Order_By_List" >
                <property name="dbTableAlias" value="ssrd"/>
            </include>
    </select>

    <sql id="Exists_InnerTable">
        <if test="${InnerQoTableAlias} != null">
            AND EXISTS (
                SELECT ${InnerTableAlias}.id
                FROM storage_snapshot_request_detail ${InnerTableAlias}
                WHERE ${JoinCondition}
            AND ${InnerTableAlias}.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.storage.StorageSnapshotRequestDetailMapper.Base_Where_List">
                <property name="dbTableAlias" value="${InnerTableAlias}"/>
                <property name="qoTableAlias" value="${InnerQoTableAlias}"/>
            </include>
            )
        </if>
    </sql>
</mapper>