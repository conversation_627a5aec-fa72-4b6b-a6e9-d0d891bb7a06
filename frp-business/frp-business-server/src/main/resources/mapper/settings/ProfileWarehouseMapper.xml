<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.setting.ProfileWarehouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.setting.ProfileWarehouse">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="tenant_id" property="tenantId" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="service_type" property="serviceType" />
        <result column="category_code" property="categoryCode" />
        <result column="category_desc" property="categoryDesc" />
        <result column="value_type" property="valueType" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="note" property="note" />
        <result column="sort" property="sort" />
        <result column="active_flag" property="activeFlag" />
        <result column="code" property="code" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.warehouse_id,
        t.tenant_id,
        t.version,
        t.deleted_note,
        t.service_type,
        t.category_code,
        t.category_desc,
        t.value_type,
        t.name,
        t.value,
        t.note,
        t.sort,
        t.active_flag,
        t.code
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.ProfileWarehousePageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            profile_warehouse t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">
            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.serviceType != null and qo.serviceType != ''">
            AND t.service_type = #{qo.serviceType}
        </if>
        <if test="qo.serviceTypeList != null and qo.serviceTypeList.size > 0 ">
            AND t.service_type in
            <foreach collection="qo.serviceTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.categoryCode != null and qo.categoryCode != ''">
            AND t.category_code = #{qo.categoryCode}
        </if>
        <if test="qo.categoryCodeList != null and qo.categoryCodeList.size > 0 ">
            AND t.category_code in
            <foreach collection="qo.categoryCodeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.categoryDesc != null and qo.categoryDesc != ''">
            AND t.category_desc = #{qo.categoryDesc}
        </if>
        <if test="qo.categoryDescList != null and qo.categoryDescList.size > 0 ">
            AND t.category_desc in
            <foreach collection="qo.categoryDescList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.valueType != null and qo.valueType != ''">
            AND t.value_type = #{qo.valueType}
        </if>
        <if test="qo.valueTypeList != null and qo.valueTypeList.size > 0 ">
            AND t.value_type in
            <foreach collection="qo.valueTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.name != null and qo.name != ''">
            AND t.name = #{qo.name}
        </if>
        <if test="qo.nameList != null and qo.nameList.size > 0 ">
            AND t.name in
            <foreach collection="qo.nameList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.value != null and qo.value != ''">
            AND t.value = #{qo.value}
        </if>
        <if test="qo.valueList != null and qo.valueList.size > 0 ">
            AND t.value in
            <foreach collection="qo.valueList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.sort != null">
            AND t.sort = #{qo.sort}
        </if>
        <if test="qo.activeFlag != null">
            AND t.active_flag = #{qo.activeFlag}
        </if>
        <if test="qo.code != null and qo.code != ''">
            AND t.code = #{qo.code}
        </if>
        <if test="qo.codeList != null and qo.codeList.size > 0 ">
            AND t.code in
            <foreach collection="qo.codeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>