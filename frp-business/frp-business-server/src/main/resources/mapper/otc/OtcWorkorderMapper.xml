<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcWorkorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcWorkorder">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="otc_request_id" property="otcRequestId"/>
        <result column="otc_picking_slip_id" property="otcPickingSlipId"/>
        <result column="request_snapshot_ship_express_flag" property="requestSnapshotShipExpressFlag"/>
        <result column="ship_method" property="shipMethod"/>
        <result column="ship_carrier" property="shipCarrier"/>
        <result column="request_snapshot_last_ship_date" property="requestSnapshotLastShipDate"/>
        <result column="request_snapshot_insurance_amount_amount" property="requestSnapshotInsuranceAmountAmount"/>
        <result column="request_snapshot_signature_type" property="requestSnapshotSignatureType"/>
        <result column="request_snapshot_order_type" property="requestSnapshotOrderType"/>
        <result column="request_snapshot_has_cus_ship_require" property="requestSnapshotHasCusShipRequire"/>
        <result column="request_snapshot_ship_method" property="requestSnapshotShipMethod"/>
        <result column="request_snapshot_provide_shipping_label_flag"
                property="requestSnapshotProvideShippingLabelFlag"/>
        <result column="otc_workorder_status" property="otcWorkorderStatus"/>
        <result column="build_ship_package_type" property="buildShipPackageType"/>
        <result column="picking_slip_build_mode" property="pickingSlipBuildMode"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="ref_num" property="refNum"/>
        <result column="note" property="note"/>
        <result column="request_snapshot_note" property="requestSnapshotNote"/>
        <result column="request_snapshot_ref_num" property="requestSnapshotRefNum"/>
        <result column="request_snapshot_request_ref_num" property="requestSnapshotRequestRefNum"/>
        <result column="request_snapshot_ship_carrier" property="requestSnapshotShipCarrier"/>
        <result column="request_snapshot_transaction_partner_id" property="requestSnapshotTransactionPartnerId"/>
        <result column="request_snapshot_channel" property="requestSnapshotChannel"/>
        <result column="workorder_prep_status" property="workorderPrepStatus"/>
        <result column="request_snapshot_insurance_amount_currency" property="requestSnapshotInsuranceAmountCurrency"/>
        <result column="pick_to_station" property="pickToStation"/>
        <result column="locked_before" property="lockedBefore"/>
        <result column="order_type" property="orderType"/>
        <result column="request_snapshot_ship_api_profile_ref_num" property="requestSnapshotShipApiProfileRefNum"/>
        <result column="workorder_product_type" property="workorderProductType"/>
        <result column="process_type" property="processType" />

    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otc_request_id,
        t.otc_picking_slip_id,
        t.request_snapshot_ship_express_flag,
        t.ship_method,
        t.ship_carrier,
        t.request_snapshot_last_ship_date,
        t.request_snapshot_insurance_amount_amount,
        t.request_snapshot_signature_type,
        t.request_snapshot_order_type,
        t.request_snapshot_has_cus_ship_require,
        t.request_snapshot_ship_method,
        t.request_snapshot_provide_shipping_label_flag,
        t.otc_workorder_status,
        t.build_ship_package_type,
        t.picking_slip_build_mode,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.note,
        t.request_snapshot_note,
        t.request_snapshot_ref_num,
        t.request_snapshot_request_ref_num,
        t.request_snapshot_ship_carrier,
        t.request_snapshot_transaction_partner_id,
        t.request_snapshot_channel,
        t.workorder_prep_status,
        t.request_snapshot_insurance_amount_currency,
        t.pick_to_station,
        t.locked_before,
        t.process_type,
        t.workorder_product_type,
        t.order_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcWorkorderPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        <include refid="List_From_Where_List"/>

    </select>

    <select id="filterBuildPickingSlipCount" resultType="java.lang.Long">
        SELECT
        count(*)
        FROM
        <include refid="List_From_Where_List"/>
    </select>

    <select id="existUnfinishedOrder" resultType="java.lang.Long">
        select count(t.id)
        from otc_workorder_bin_location owbl
        left join otc_workorder t on owbl.otc_workorder_id = t.id
        <where>
            t.remove_flag=0
            and owbl.bin_location_id = #{binLocationId}
            and t.otc_workorder_status not in <foreach collection="workorderStatusList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
        </where>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otc_workorder t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <sql id="List_From_Where_List">
        otc_workorder t
        WHERE
        <if test="bl != null">
            EXISTS (SELECT t2.otc_workorder_id FROM otc_workorder_detail t2
            LEFT JOIN bin_location_detail t3 ON t3.product_id = t2.product_id
            LEFT JOIN bin_location bl ON bl.id = t3.bin_location_id
            WHERE t2.otc_workorder_id = t.id
            <include refid="Bin_Location_Where_List"/>
            GROUP BY t2.otc_workorder_id
            HAVING COUNT(DISTINCT t2.product_id) = COUNT(DISTINCT t3.product_id)
            )
            AND
        </if>
        t.remove_flag = 0
        <if test="qo != null">
            <include refid="Base_Where_List"/>
        </if>
        <choose>
            <when test="bl != null">
                ORDER BY t.request_snapshot_last_ship_date, t.ref_num
            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <sql id="Bin_Location_Where_List">
        <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
            AND bl.warehouse_zone_type in
            <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
            AND bl.bin_type in
            <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.typeList != null and bl.typeList.size > 0 ">
            AND bl.type in
            <foreach collection="bl.typeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lrowList != null and bl.lrowList.size > 0 ">
            AND bl.lrow in
            <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.ldepthList != null and bl.ldepthList.size > 0 ">
            AND bl.ldepth in
            <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.llevelList != null and bl.llevelList.size > 0 ">
            AND bl.llevel in
            <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lsplitsList != null and bl.lsplitsList.size > 0 ">
            AND bl.lsplit in
            <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPickingSlipId != null">
            AND t.otc_picking_slip_id = #{qo.otcPickingSlipId}
        </if>
        <if test="qo.requestSnapshotShipExpressFlag != null">
            AND t.request_snapshot_ship_express_flag = #{qo.requestSnapshotShipExpressFlag}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestSnapshotLastShipDateStart != null">
            AND t.request_snapshot_last_ship_date &gt;= #{qo.requestSnapshotLastShipDateStart}
        </if>
        <if test="qo.requestSnapshotLastShipDateEnd != null">
            AND t.request_snapshot_last_ship_date &lt; #{qo.requestSnapshotLastShipDateEnd}
        </if>
        <if test="qo.requestSnapshotOrderTypeList != null and qo.requestSnapshotOrderTypeList.size > 0 ">
            AND t.request_snapshot_order_type in
            <foreach collection="qo.requestSnapshotOrderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcWorkorderStatusList != null and qo.otcWorkorderStatusList.size > 0 ">
            AND t.otc_workorder_status in
            <foreach collection="qo.otcWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcWorkorderStatus != null">
            AND t.otc_workorder_status = #{qo.otcWorkorderStatus}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.requestSnapshotRequestRefNumList != null and qo.requestSnapshotRequestRefNumList.size > 0 ">
            AND t.request_snapshot_request_ref_num in
            <foreach collection="qo.requestSnapshotRequestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.requestSnapshotChannelList != null and qo.requestSnapshotChannelList.size > 0 ">
            AND t.request_snapshot_channel in
            <foreach collection="qo.requestSnapshotChannelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.workorderPrepStatusList != null and qo.workorderPrepStatusList.size > 0 ">
            AND t.workorder_prep_status in
            <foreach collection="qo.workorderPrepStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.workorderProductTypeList != null and qo.workorderProductTypeList.size > 0 ">
            AND t.workorder_product_type in
            <foreach collection="qo.workorderProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.processTypeList != null and qo.processTypeList.size > 0">
            AND t.process_type IN
            <foreach collection="qo.processTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipBuildMode != null">
            AND t.picking_slip_build_mode = #{qo.pickingSlipBuildMode}
        </if>
        <if test="qo.pickingSlipBuildModeList != null and qo.pickingSlipBuildModeList.size > 0 ">
            AND t.picking_slip_build_mode in
            <foreach collection="qo.pickingSlipBuildModeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>
        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otc_workorder t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otc.OtcWorkorderMapper.Base_Where_List">

            </include>
            )
        </if>
    </sql>
</mapper>