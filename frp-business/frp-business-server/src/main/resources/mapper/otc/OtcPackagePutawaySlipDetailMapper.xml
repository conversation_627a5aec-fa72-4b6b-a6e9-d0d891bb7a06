<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPackagePutawaySlipDetailMapper">

    <select id="listAvailableByWorkorderIds"
            resultType="cn.need.cloud.biz.model.entity.otc.OtcPackagePutawaySlipDetail">
        SELECT
        *
        FROM
        otc_package_putaway_slip_detail oppsd
        WHERE
        oppsd.remove_flag = 0
        AND (EXISTS(select 1 from otc_putaway_slip ops where ops.id = oppsd.putaway_slip_id and ops.putaway_slip_status in ('New', 'Processing')))
        AND oppsd.qty > oppsd.putaway_qty
        AND oppsd.workorder_id IN
        <foreach collection="workorderIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

</mapper> 