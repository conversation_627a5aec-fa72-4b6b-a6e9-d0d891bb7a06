<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPackageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcPackage">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="line_num" property="lineNum" />
        <result column="tracking_num" property="trackingNum" />
        <result column="insurance_amount_amount" property="insuranceAmountAmount" />
        <result column="signature_type" property="signatureType" />
        <result column="ship_to_address_name" property="shipToAddressName" />
        <result column="ship_to_address_company" property="shipToAddressCompany" />
        <result column="ship_to_address_country" property="shipToAddressCountry" />
        <result column="ship_to_address_state" property="shipToAddressState" />
        <result column="ship_to_address_city" property="shipToAddressCity" />
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode" />
        <result column="ship_to_address_addr1" property="shipToAddressAddr1" />
        <result column="ship_to_address_addr2" property="shipToAddressAddr2" />
        <result column="ship_to_address_addr3" property="shipToAddressAddr3" />
        <result column="ship_to_address_email" property="shipToAddressEmail" />
        <result column="ship_to_address_phone" property="shipToAddressPhone" />
        <result column="ship_to_address_note" property="shipToAddressNote" />
        <result column="ship_from_address_name" property="shipFromAddressName" />
        <result column="ship_from_address_company" property="shipFromAddressCompany" />
        <result column="ship_from_address_country" property="shipFromAddressCountry" />
        <result column="ship_from_address_state" property="shipFromAddressState" />
        <result column="ship_from_address_city" property="shipFromAddressCity" />
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode" />
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1" />
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2" />
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3" />
        <result column="ship_from_address_email" property="shipFromAddressEmail" />
        <result column="ship_from_address_phone" property="shipFromAddressPhone" />
        <result column="ship_from_address_note" property="shipFromAddressNote" />
        <result column="otc_workorder_id" property="otcWorkorderId" />
        <result column="ship_express_flag" property="shipExpressFlag" />
        <result column="ship_method" property="shipMethod" />
        <result column="ship_carrier" property="shipCarrier" />
        <result column="ship_size_length" property="shipSizeLength" />
        <result column="ship_size_width" property="shipSizeWidth" />
        <result column="ship_size_height" property="shipSizeHeight" />
        <result column="ship_size_weight" property="shipSizeWeight" />
        <result column="note" property="note" />
        <result column="build_ship_strategy" property="buildShipStrategy" />
        <result column="package_status" property="packageStatus" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="otc_picking_slip_id" property="otcPickingSlipId" />
        <result column="ready_to_ship_time" property="readyToShipTime" />
        <result column="shipped_time" property="shippedTime" />
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential" />
        <result column="ship_size_dimension_unit" property="shipSizeDimensionUnit" />
        <result column="ship_size_weight_unit" property="shipSizeWeightUnit" />
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential" />
        <result column="insurance_amount_currency" property="insuranceAmountCurrency" />
        <result column="ship_api_profile_ref_num" property="shipApiProfileRefNum" />
        <result column="package_multibox_upc" property="packageMultiboxUpc" />
        <result column="package_type" property="packageType" />
        <result column="package_multibox_line_num" property="packageMultiboxLineNum" />
        <result column="package_multibox_product_id" property="packageMultiboxProductId" />
        <result column="package_multibox_version_int" property="packageMultiboxVersionInt" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.line_num,
        t.tracking_num,
        t.insurance_amount_amount,
        t.signature_type,
        t.ship_to_address_name,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_state,
        t.ship_to_address_city,
        t.ship_to_address_zip_code,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_email,
        t.ship_to_address_phone,
        t.ship_to_address_note,
        t.ship_from_address_name,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_state,
        t.ship_from_address_city,
        t.ship_from_address_zip_code,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_email,
        t.ship_from_address_phone,
        t.ship_from_address_note,
        t.otc_workorder_id,
        t.ship_express_flag,
        t.ship_method,
        t.ship_carrier,
        t.ship_size_length,
        t.ship_size_width,
        t.ship_size_height,
        t.ship_size_weight,
        t.note,
        t.build_ship_strategy,
        t.package_status,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.otc_picking_slip_id,
        t.ready_to_ship_time,
        t.shipped_time,
        t.ship_from_address_is_residential,
        t.ship_size_dimension_unit,
        t.ship_size_weight_unit,
        t.ship_to_address_is_residential,
        t.insurance_amount_currency,
        t.ship_api_profile_ref_num,
        t.package_multibox_upc,
        t.package_type,
        t.package_multibox_line_num,
        t.package_multibox_product_id,
        t.process_type,
        t.package_multibox_version_int
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcPackagePageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        otc_package t LEFT JOIN otc_workorder t2 ON t.otc_workorder_id = t2.id
        WHERE
        t.remove_flag = 0
        <if test="wk != null">
            <if test="wk.requestRequestRefNumList != null and wk.requestRequestRefNumList.size > 0 ">
                AND t2.request_snapshot_request_ref_num in
                <foreach collection="wk.requestRequestRefNumList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="wk.requestChannelList != null and wk.requestChannelList.size > 0 ">
                AND t2.request_snapshot_channel in
                <foreach collection="wk.requestChannelList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="wk.requestRefNumList != null and wk.requestRefNumList.size > 0 ">
                AND t2.request_snapshot_ref_num in
                <foreach collection="wk.requestRefNumList" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="qo != null">
            <include refid="List_Where_List"/>
        </if>
        <include refid="Base_Order_By_List"/>
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <sql id="List_Where_List">
        <if test="qo.otcWorkorderId != null">
            AND t.otc_workorder_id = #{qo.otcWorkorderId}
        </if>
        <if test="qo.packageStatus != null and qo.packageStatus != ''">
            AND t.package_status = #{qo.packageStatus}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0">
            AND t.ref_num IN
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPickingSlipId != null">
            AND t.otc_picking_slip_id = #{qo.otcPickingSlipId}
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.tracking_num = #{qo.trackingNum}
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0">
            AND t.tracking_num IN
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0">
            AND t.ship_carrier IN
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0">
            AND t.ship_method IN
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.processTypeList != null and qo.processTypeList.size > 0">
            AND t.process_type IN
            <foreach collection="qo.processTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.tracking_num = #{qo.trackingNum}
        </if>
        <if test="qo.insuranceAmountAmount != null">
            AND t.insurance_amount_amount = #{qo.insuranceAmountAmount}
        </if>
        <if test="qo.signatureType != null and qo.signatureType != ''">
            AND t.signature_type = #{qo.signatureType}
        </if>
        <if test="qo.signatureTypeList != null and qo.signatureTypeList.size > 0 ">
            AND t.signature_type in
            <foreach collection="qo.signatureTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipToAddressName != null and qo.shipToAddressName != ''">
            AND t.ship_to_address_name = #{qo.shipToAddressName}
        </if>
        <if test="qo.shipToAddressCompany != null and qo.shipToAddressCompany != ''">
            AND t.ship_to_address_company = #{qo.shipToAddressCompany}
        </if>
        <if test="qo.shipToAddressCountry != null and qo.shipToAddressCountry != ''">
            AND t.ship_to_address_country = #{qo.shipToAddressCountry}
        </if>
        <if test="qo.shipToAddressState != null and qo.shipToAddressState != ''">
            AND t.ship_to_address_state = #{qo.shipToAddressState}
        </if>
        <if test="qo.shipToAddressCity != null and qo.shipToAddressCity != ''">
            AND t.ship_to_address_city = #{qo.shipToAddressCity}
        </if>
        <if test="qo.shipToAddressZipCode != null and qo.shipToAddressZipCode != ''">
            AND t.ship_to_address_zip_code = #{qo.shipToAddressZipCode}
        </if>
        <if test="qo.shipToAddressAddr1 != null and qo.shipToAddressAddr1 != ''">
            AND t.ship_to_address_addr1 = #{qo.shipToAddressAddr1}
        </if>
        <if test="qo.shipToAddressAddr2 != null and qo.shipToAddressAddr2 != ''">
            AND t.ship_to_address_addr2 = #{qo.shipToAddressAddr2}
        </if>
        <if test="qo.shipToAddressAddr3 != null and qo.shipToAddressAddr3 != ''">
            AND t.ship_to_address_addr3 = #{qo.shipToAddressAddr3}
        </if>
        <if test="qo.shipToAddressEmail != null and qo.shipToAddressEmail != ''">
            AND t.ship_to_address_email = #{qo.shipToAddressEmail}
        </if>
        <if test="qo.shipToAddressPhone != null and qo.shipToAddressPhone != ''">
            AND t.ship_to_address_phone = #{qo.shipToAddressPhone}
        </if>
        <if test="qo.shipToAddressNote != null and qo.shipToAddressNote != ''">
            AND t.ship_to_address_note = #{qo.shipToAddressNote}
        </if>
        <if test="qo.shipFromAddressName != null and qo.shipFromAddressName != ''">
            AND t.ship_from_address_name = #{qo.shipFromAddressName}
        </if>
        <if test="qo.shipFromAddressCompany != null and qo.shipFromAddressCompany != ''">
            AND t.ship_from_address_company = #{qo.shipFromAddressCompany}
        </if>
        <if test="qo.shipFromAddressCountry != null and qo.shipFromAddressCountry != ''">
            AND t.ship_from_address_country = #{qo.shipFromAddressCountry}
        </if>
        <if test="qo.shipFromAddressState != null and qo.shipFromAddressState != ''">
            AND t.ship_from_address_state = #{qo.shipFromAddressState}
        </if>
        <if test="qo.shipFromAddressCity != null and qo.shipFromAddressCity != ''">
            AND t.ship_from_address_city = #{qo.shipFromAddressCity}
        </if>
        <if test="qo.shipFromAddressZipCode != null and qo.shipFromAddressZipCode != ''">
            AND t.ship_from_address_zip_code = #{qo.shipFromAddressZipCode}
        </if>
        <if test="qo.shipFromAddressAddr1 != null and qo.shipFromAddressAddr1 != ''">
            AND t.ship_from_address_addr1 = #{qo.shipFromAddressAddr1}
        </if>
        <if test="qo.shipFromAddressAddr2 != null and qo.shipFromAddressAddr2 != ''">
            AND t.ship_from_address_addr2 = #{qo.shipFromAddressAddr2}
        </if>
        <if test="qo.shipFromAddressAddr3 != null and qo.shipFromAddressAddr3 != ''">
            AND t.ship_from_address_addr3 = #{qo.shipFromAddressAddr3}
        </if>
        <if test="qo.shipFromAddressEmail != null and qo.shipFromAddressEmail != ''">
            AND t.ship_from_address_email = #{qo.shipFromAddressEmail}
        </if>
        <if test="qo.shipFromAddressPhone != null and qo.shipFromAddressPhone != ''">
            AND t.ship_from_address_phone = #{qo.shipFromAddressPhone}
        </if>
        <if test="qo.shipFromAddressNote != null and qo.shipFromAddressNote != ''">
            AND t.ship_from_address_note = #{qo.shipFromAddressNote}
        </if>
        <if test="qo.otcWorkorderId != null">
            AND t.otc_workorder_id = #{qo.otcWorkorderId}
        </if>
        <if test="qo.shipExpressFlag != null">
            AND t.ship_express_flag = #{qo.shipExpressFlag}
        </if>
        <if test="qo.shipMethod != null and qo.shipMethod != ''">
            AND t.ship_method = #{qo.shipMethod}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipSizeLength != null">
            AND t.ship_size_length = #{qo.shipSizeLength}
        </if>
        <if test="qo.shipSizeWidth != null">
            AND t.ship_size_width = #{qo.shipSizeWidth}
        </if>
        <if test="qo.shipSizeHeight != null">
            AND t.ship_size_height = #{qo.shipSizeHeight}
        </if>
        <if test="qo.shipSizeWeight != null">
            AND t.ship_size_weight = #{qo.shipSizeWeight}
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.buildShipStrategy != null and qo.buildShipStrategy != ''">
            AND t.build_ship_strategy = #{qo.buildShipStrategy}
        </if>
        <if test="qo.packageStatus != null and qo.packageStatus != ''">
            AND t.package_status = #{qo.packageStatus}
        </if>
        <if test="qo.packageStatusList != null and qo.packageStatusList.size > 0 ">
            AND t.package_status in
            <foreach collection="qo.packageStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPickingSlipId != null">
            AND t.otc_picking_slip_id = #{qo.otcPickingSlipId}
        </if>
        <if test="qo.readyToShipTimeStart != null">
            AND t.ready_to_ship_time  &gt;= #{qo.readyToShipTimeStart}
        </if>
        <if test="qo.readyToShipTimeEnd != null">
            AND t.ready_to_ship_time  &lt; #{qo.readyToShipTimeEnd}
        </if>
        <if test="qo.shippedTimeStart != null">
            AND t.shipped_time  &gt;= #{qo.shippedTimeStart}
        </if>
        <if test="qo.shippedTimeEnd != null">
            AND t.shipped_time  &lt; #{qo.shippedTimeEnd}
        </if>
        <if test="qo.shipFromAddressIsResidential != null">
            AND t.ship_from_address_is_residential = #{qo.shipFromAddressIsResidential}
        </if>
        <if test="qo.shipSizeDimensionUnit != null and qo.shipSizeDimensionUnit != ''">
            AND t.ship_size_dimension_unit = #{qo.shipSizeDimensionUnit}
        </if>
        <if test="qo.shipSizeWeightUnit != null and qo.shipSizeWeightUnit != ''">
            AND t.ship_size_weight_unit = #{qo.shipSizeWeightUnit}
        </if>
        <if test="qo.shipToAddressIsResidential != null">
            AND t.ship_to_address_is_residential = #{qo.shipToAddressIsResidential}
        </if>
        <if test="qo.insuranceAmountCurrency != null and qo.insuranceAmountCurrency != ''">
            AND t.insurance_amount_currency = #{qo.insuranceAmountCurrency}
        </if>
        <if test="qo.shipApiProfileRefNum != null and qo.shipApiProfileRefNum != ''">
            AND t.ship_api_profile_ref_num = #{qo.shipApiProfileRefNum}
        </if>
        <if test="qo.shipApiProfileRefNumList != null and qo.shipApiProfileRefNumList.size > 0 ">
            AND t.ship_api_profile_ref_num in
            <foreach collection="qo.shipApiProfileRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.packageMultiboxUpc != null and qo.packageMultiboxUpc != ''">
            AND t.package_multibox_upc = #{qo.packageMultiboxUpc}
        </if>
        <if test="qo.packageMultiboxUpcList != null and qo.packageMultiboxUpcList.size > 0 ">
            AND t.package_multibox_upc in
            <foreach collection="qo.packageMultiboxUpcList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.packageType != null and qo.packageType != ''">
            AND t.package_type = #{qo.packageType}
        </if>
        <if test="qo.packageTypeList != null and qo.packageTypeList.size > 0 ">
            AND t.package_type in
            <foreach collection="qo.packageTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.packageMultiboxLineNum != null">
            AND t.package_multibox_line_num = #{qo.packageMultiboxLineNum}
        </if>
        <if test="qo.packageMultiboxProductId != null">
            AND t.package_multibox_product_id = #{qo.packageMultiboxProductId}
        </if>
        <if test="qo.packageMultiboxVersionInt != null">
            AND t.package_multibox_version_int = #{qo.packageMultiboxVersionInt}
        </if>
    </sql>

</mapper>