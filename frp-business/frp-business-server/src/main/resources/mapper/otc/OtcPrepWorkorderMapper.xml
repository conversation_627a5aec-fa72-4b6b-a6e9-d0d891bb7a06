<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPrepWorkorderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcPrepWorkorder">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="otc_workorder_id" property="otcWorkorderId" />
        <result column="otc_workorder_detail_id" property="otcWorkorderDetailId" />
        <result column="qty" property="qty" />
        <result column="prep_workorder_status" property="prepWorkorderStatus" />
        <result column="prep_workorder_type" property="prepWorkorderType" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="otc_prep_picking_slip_id" property="otcPrepPickingSlipId" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="inventory_reserve_id" property="inventoryReserveId" />
        <result column="order_type" property="orderType" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="has_cus_ship_require" property="hasCusShipRequire" />
        <result column="on_site_pack_flag" property="onSitePackFlag" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="locked_before" property="lockedBefore" />
        <result column="prep_workorder_version_int" property="prepWorkorderVersionInt" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="prep_workorder_product_type" property="prepWorkorderProductType" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.otc_workorder_id,
        t.otc_workorder_detail_id,
        t.qty,
        t.prep_workorder_status,
        t.prep_workorder_type,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.product_id,
        t.hazmat_version_ref_num,
        t.otc_prep_picking_slip_id,
        t.putaway_qty,
        t.inventory_reserve_id,
        t.order_type,
        t.pick_to_station,
        t.has_cus_ship_require,
        t.on_site_pack_flag,
        t.bin_location_id,
        t.process_type,
        t.locked_before,
        t.prep_workorder_version_int,
        t.prep_workorder_product_type,
        t.transaction_partner_id
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcPrepWorkorderPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            <include refid="List_From_Where_List" />
    </select>

    <select id="filterBuildPickingSlipCount" resultType="java.lang.Long">
        SELECT
            count(*)
        FROM
            <include refid="List_From_Where_List" />
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        <include refid="List_From_Where_No_Sort_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <sql id="List_From_Where_List" >
        <include refid="List_From_Where_No_Sort_List"/>
        <choose>
            <when test="bl != null">
                ORDER BY t2.request_snapshot_last_ship_date, t.ref_num
            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <sql id="List_From_Where_No_Sort_List">
            otc_prep_workorder t
        WHERE
        <if test="bl != null">
            EXISTS (SELECT t3.otc_prep_workorder_id FROM otc_prep_workorder_detail t3
            LEFT JOIN bin_location_detail t4 ON t4.product_id = t3.product_id
            LEFT JOIN bin_location bl ON bl.id = t4.bin_location_id
            WHERE t3.otc_prep_workorder_id = t.id
                AND prep_workorder_detail_type = 'None'
            <include refid="Bin_Location_Where_List" />
            GROUP BY t3.otc_prep_workorder_id
            HAVING COUNT(DISTINCT t3.product_id) = COUNT(DISTINCT t4.product_id)
            )
            AND
        </if>
        t.remove_flag = 0
        <if test="qo != null">
            AND EXISTS (
                SELECT t2.id FROM otc_workorder t2 WHERE t2.id = t.otc_workorder_id
                <include refid="Work_Order_Where_List" />
            )
            <include refid="List_Query_Where_List" />
        </if>
    </sql>

    <!-- 库位查询条件 -->
    <sql id="Bin_Location_Where_List">
        <if test="bl.warehouseZoneTypeList != null and bl.warehouseZoneTypeList.size > 0 ">
            AND bl.warehouse_zone_type in
            <foreach collection="bl.warehouseZoneTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.binTypeList != null and bl.binTypeList.size > 0 ">
            AND bl.bin_type in
            <foreach collection="bl.binTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lrowList != null and bl.lrowList.size > 0 ">
            AND bl.lrow in
            <foreach collection="bl.lrowList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.ldepthList != null and bl.ldepthList.size > 0 ">
            AND bl.ldepth in
            <foreach collection="bl.ldepthList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.llevelList != null and bl.llevelList.size > 0 ">
            AND bl.llevel in
            <foreach collection="bl.llevelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bl.lsplitsList != null and bl.lsplitsList.size > 0 ">
            AND bl.lsplit in
            <foreach collection="bl.lsplitsList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 工单查询条件 -->
    <sql id="Work_Order_Where_List">
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND t2.request_snapshot_request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.channelList != null and qo.channelList.size > 0 ">
            AND t2.request_snapshot_channel in
            <foreach collection="qo.channelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t2.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t2.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lastShipDateStart != null">
            AND t2.request_snapshot_last_ship_date  &gt;= #{qo.lastShipDateStart}
        </if>
        <if test="qo.lastShipDateEnd != null">
            AND t2.request_snapshot_last_ship_date  &lt; #{qo.lastShipDateEnd}
        </if>
    </sql>

    <!-- 列表查询条件 -->
    <sql id="List_Query_Where_List">
        <if test="qo.prepWorkorderStatus != null and qo.prepWorkorderStatus != ''">
            AND t.prep_workorder_status = #{qo.prepWorkorderStatus}
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.prepWorkorderStatusList != null and qo.prepWorkorderStatusList.size > 0 ">
            AND t.prep_workorder_status in
            <foreach collection="qo.prepWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepWorkorderTypeList != null and qo.prepWorkorderTypeList.size > 0 ">
            AND t.prep_workorder_type in
            <foreach collection="qo.prepWorkorderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.prepWorkorderTypeList != null and qo.prepWorkorderTypeList.size > 0 ">
            AND t.prep_workorder_type in
            <foreach collection="qo.prepWorkorderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPrepWorkorderId != null">
            AND t.otc_prep_workorder_id = #{qo.otcPrepWorkorderId}
        </if>
        <if test="qo.otcPrepWorkorderIdList != null and qo.otcPrepWorkorderIdList.size > 0 ">
            AND t.otc_prep_workorder_id in
            <foreach collection="qo.otcPrepWorkorderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPrepPickingSlipId != null">
            AND t.otc_prep_picking_slip_id = #{qo.otcPrepPickingSlipId}
        </if>
        <if test="qo.prepWorkorderProductTypeList != null and qo.prepWorkorderProductTypeList.size > 0 ">
            AND t.prep_workorder_product_type in
            <foreach collection="qo.prepWorkorderProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcWorkorderId != null">
            AND t.otc_workorder_id = #{qo.otcWorkorderId}
        </if>
        <if test="qo.otcWorkorderIdList != null and qo.otcWorkorderIdList.size > 0 ">
            AND t.otc_workorder_id in
            <foreach collection="qo.otcWorkorderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcWorkorderDetailId != null">
            AND t.otc_workorder_detail_id = #{qo.otcWorkorderDetailId}
        </if>
        <if test="qo.otcWorkorderDetailIdList != null and qo.otcWorkorderDetailIdList.size > 0 ">
            AND t.otc_workorder_detail_id in
            <foreach collection="qo.otcWorkorderDetailIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPrepWorkorderId != null">
            AND t.otc_prep_workorder_id = #{qo.otcPrepWorkorderId}
        </if>
        <if test="qo.otcPrepWorkorderIdList != null and qo.otcPrepWorkorderIdList.size > 0 ">
            AND t.otc_prep_workorder_id in
            <foreach collection="qo.otcPrepWorkorderIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPrepWorkorderDetailId != null">
            AND t.otc_prep_workorder_detail_id = #{qo.otcPrepWorkorderDetailId}
        </if>
        <if test="qo.qty != null">
            AND t.qty = #{qo.qty}
        </if>
        <if test="qo.prepWorkorderStatus != null and qo.prepWorkorderStatus != ''">
            AND t.prep_workorder_status = #{qo.prepWorkorderStatus}
        </if>
        <if test="qo.prepWorkorderStatusList != null and qo.prepWorkorderStatusList.size > 0 ">
            AND t.prep_workorder_status in
            <foreach collection="qo.prepWorkorderStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepWorkorderType != null and qo.prepWorkorderType != ''">
            AND t.prep_workorder_type = #{qo.prepWorkorderType}
        </if>
        <if test="qo.prepWorkorderTypeList != null and qo.prepWorkorderTypeList.size > 0 ">
            AND t.prep_workorder_type in
            <foreach collection="qo.prepWorkorderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.otcPrepPickingSlipId != null">
            AND t.otc_prep_picking_slip_id = #{qo.otcPrepPickingSlipId}
        </if>
        <if test="qo.putawayQty != null">
            AND t.putaway_qty = #{qo.putawayQty}
        </if>
        <if test="qo.inventoryReserveId != null">
            AND t.inventory_reserve_id = #{qo.inventoryReserveId}
        </if>
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickToStation != null and qo.pickToStation != ''">
            AND t.pick_to_station = #{qo.pickToStation}
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.hasCusShipRequire != null">
            AND t.has_cus_ship_require = #{qo.hasCusShipRequire}
        </if>
        <if test="qo.onSitePackFlag != null">
            AND t.on_site_pack_flag = #{qo.onSitePackFlag}
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
        <if test="qo.lockedBefore != null and qo.lockedBefore != ''">
            AND t.locked_before = #{qo.lockedBefore}
        </if>
        <if test="qo.prepWorkorderVersionInt != null">
            AND t.prep_workorder_version_int = #{qo.prepWorkorderVersionInt}
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>
        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otc_prep_workorder t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otc.OtcPrepWorkorderMapper.List_Query_Where_List">

            </include>
            )
        </if>
    </sql>
</mapper>