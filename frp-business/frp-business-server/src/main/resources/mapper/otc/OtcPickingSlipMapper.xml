<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPickingSlipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcPickingSlip">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="picking_slip_status" property="pickingSlipStatus" />
        <result column="note" property="note" />
        <result column="print_status" property="printStatus" />
        <result column="order_type" property="orderType" />
        <result column="has_cus_ship_require" property="hasCusShipRequire" />
        <result column="on_site_pack_flag" property="onSitePackFlag" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="build_from_type" property="buildFromType" />
        <result column="pick_from_type" property="pickFromType" />
        <result column="locked_before" property="lockedBefore" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="picking_slip_product_type" property="pickingSlipProductType" />
        <result column="process_type" property="processType" />
        <result column="picking_slip_build_mode" property="pickingSlipBuildMode" />
        <result column="picking_slip_putaway_slip_status" property="pickingSlipPutAwaySlipStatus" />
        <result column="put_back_type" property="putBackType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.assigned_user_id,
        t.pick_to_station,
        t.picking_slip_status,
        t.note,
        t.print_status,
        t.order_type,
        t.has_cus_ship_require,
        t.on_site_pack_flag,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.build_from_type,
        t.pick_from_type,
        t.locked_before,
        t.picking_slip_product_type,
        t.process_type,
        t.picking_slip_build_mode,
        t.picking_slip_putaway_slip_status,
        t.transaction_partner_id,
        t.put_back_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otc_picking_slip t
        WHERE
            EXISTS (
            SELECT w.otc_picking_slip_id
            FROM otc_workorder w
            WHERE t.id = w.otc_picking_slip_id
                <include refid="Work_Order_Query_Where_List" />
            )
            AND t.remove_flag = 0
            <include refid="List_Where_List"/>
            <include refid="Base_Order_By_List"/>
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otc_picking_slip t
        WHERE
        t.remove_flag = 0
        <include refid="List_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <!--  列表查询where条件   -->
    <sql id="Work_Order_Query_Where_List">
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND w.request_snapshot_request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestLastShipDateStart != null">
            AND w.request_snapshot_last_ship_date  &gt;= #{qo.requestLastShipDateStart}
        </if>
        <if test="qo.requestLastShipDateEnd != null">
            AND w.request_snapshot_last_ship_date  &lt; #{qo.requestLastShipDateEnd}
        </if>

    </sql>

    <sql id="List_Where_List">
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipProductTypeList != null and qo.pickingSlipProductTypeList.size > 0 ">
            AND t.picking_slip_product_type in
            <foreach collection="qo.pickingSlipProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.buildFromTypeList != null and qo.buildFromTypeList.size > 0 ">
            AND t.build_from_type in
            <foreach collection="qo.buildFromTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickFromTypeList != null and qo.pickFromTypeList.size > 0 ">
            AND t.pick_from_type in
            <foreach collection="qo.pickFromTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipStatus != null and qo.pickingSlipStatus != ''">
            AND t.picking_slip_status = #{qo.pickingSlipStatus}
        </if>
        <if test="qo.pickingSlipStatusList != null and qo.pickingSlipStatusList.size > 0 ">
            AND t.picking_slip_status in
            <foreach collection="qo.pickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.refNum != null">
            AND t.ref_num  = #{qo.refNum}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.processTypeList != null and qo.processTypeList.size > 0">
            AND t.process_type IN
            <foreach collection="qo.processTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipBuildMode != null">
            AND t.picking_slip_build_mode = #{qo.pickingSlipBuildMode}
        </if>
        <if test="qo.pickingSlipBuildModeList != null and qo.pickingSlipBuildModeList.size > 0 ">
            AND t.picking_slip_build_mode in
            <foreach collection="qo.pickingSlipBuildModeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipPutAwaySlipStatus != null and qo.pickingSlipPutAwaySlipStatus != ''">
            AND t.picking_slip_putaway_slip_status = #{qo.pickingSlipPutAwaySlipStatus}
        </if>
        <if test="qo.pickingSlipPutAwaySlipStatusList != null and qo.pickingSlipPutAwaySlipStatusList.size > 0 ">
            AND t.picking_slip_putaway_slip_status in
            <foreach collection="qo.pickingSlipPutAwaySlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.putBackType != null and qo.putBackType != ''">
            AND t.put_back_type = #{qo.putBackType}
        </if>
        <if test="qo.putBackTypeList != null and qo.putBackTypeList.size > 0 ">
            AND t.put_back_type in
            <foreach collection="qo.putBackTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.assignedUserId != null">
            AND t.assigned_user_id = #{qo.assignedUserId}
        </if>
        <if test="qo.pickToStation != null and qo.pickToStation != ''">
            AND t.pick_to_station = #{qo.pickToStation}
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickingSlipStatus != null and qo.pickingSlipStatus != ''">
            AND t.picking_slip_status = #{qo.pickingSlipStatus}
        </if>
        <if test="qo.pickingSlipStatusList != null and qo.pickingSlipStatusList.size > 0 ">
            AND t.picking_slip_status in
            <foreach collection="qo.pickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.hasCusShipRequire != null">
            AND t.has_cus_ship_require = #{qo.hasCusShipRequire}
        </if>
        <if test="qo.onSitePackFlag != null">
            AND t.on_site_pack_flag = #{qo.onSitePackFlag}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.buildFromType != null and qo.buildFromType != ''">
            AND t.build_from_type = #{qo.buildFromType}
        </if>
        <if test="qo.buildFromTypeList != null and qo.buildFromTypeList.size > 0 ">
            AND t.build_from_type in
            <foreach collection="qo.buildFromTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lockedBefore != null and qo.lockedBefore != ''">
            AND t.locked_before = #{qo.lockedBefore}
        </if>
        <if test="qo.pickingSlipPutAwaySlipStatus != null and qo.pickingSlipPutAwaySlipStatus != ''">
            AND t.picking_slip_putaway_slip_status = #{qo.pickingSlipPutAwaySlipStatus}
        </if>
        <if test="qo.pickingSlipPutAwaySlipStatusList != null and qo.pickingSlipPutAwaySlipStatusList.size > 0 ">
            AND t.picking_slip_putaway_slip_status in
            <foreach collection="qo.pickingSlipPutAwaySlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>

        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otc_picking_slip t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otc.OtcPickingSlipMapper.List_Where_List">

            </include>
            )
        </if>
    </sql>
</mapper>