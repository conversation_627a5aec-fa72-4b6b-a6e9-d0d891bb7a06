<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcShipPalletMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcShipPallet">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="ship_carrier" property="shipCarrier" />
        <result column="note" property="note" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="carton_count" property="cartonCount" />
        <result column="label_raw_data" property="labelRawData" />
        <result column="raw_data_type" property="rawDataType" />
        <result column="label_ref_num" property="labelRefNum" />
        <result column="file_id_raw_data_type" property="fileIdRawDataType" />
        <result column="paper_type" property="paperType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.ship_carrier,
        t.note,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.carton_count,
        t.label_raw_data,
        t.raw_data_type,
        t.label_ref_num,
        t.file_id_raw_data_type,
        t.paper_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcShipPalletPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otc_ship_pallet t
        WHERE
            <if test="qo.palletDetailTrackingNumList != null and qo.palletDetailTrackingNumList.size > 0">
                EXISTS (
                    SELECT
                        t2.otc_ship_pallet_id
                    FROM
                        otc_ship_pallet_detail t2
                    WHERE
                        t.id = t2.otc_ship_pallet_id
                        AND t2.tracking_num IN
                        <foreach collection="qo.palletDetailTrackingNumList" item="item" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                )
                AND
            </if>
            t.remove_flag = 0
            <include refid="List_Query_Where_List" />
            <include refid="Base_Order_By_List"/>
    </select>

    <!-- 列表查询条件 -->
    <sql id="List_Query_Where_List">
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.palletLabelTrackingNumList != null and qo.palletLabelTrackingNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.palletLabelTrackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.labelRefNumList != null and qo.labelRefNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.labelRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.cartonCount != null">
            AND t.carton_count = #{qo.cartonCount}
        </if>
        <if test="qo.labelRawData != null and qo.labelRawData != ''">
            AND t.label_raw_data = #{qo.labelRawData}
        </if>
        <if test="qo.rawDataType != null and qo.rawDataType != ''">
            AND t.raw_data_type = #{qo.rawDataType}
        </if>
        <if test="qo.rawDataTypeList != null and qo.rawDataTypeList.size > 0 ">
            AND t.raw_data_type in
            <foreach collection="qo.rawDataTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.trackingNum != null and qo.trackingNum != ''">
            AND t.label_ref_num = #{qo.trackingNum}
        </if>
        <if test="qo.trackingNumList != null and qo.trackingNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.trackingNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fileIdRawDataType != null and qo.fileIdRawDataType != ''">
            AND t.file_id_raw_data_type = #{qo.fileIdRawDataType}
        </if>
        <if test="qo.fileIdRawDataTypeList != null and qo.fileIdRawDataTypeList.size > 0 ">
            AND t.file_id_raw_data_type in
            <foreach collection="qo.fileIdRawDataTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.paperType != null and qo.paperType != ''">
            AND t.paper_type = #{qo.paperType}
        </if>
        <if test="qo.paperTypeList != null and qo.paperTypeList.size > 0 ">
            AND t.paper_type in
            <foreach collection="qo.paperTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>