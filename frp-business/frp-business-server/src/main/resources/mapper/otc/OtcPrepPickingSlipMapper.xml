<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPrepPickingSlipMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcPrepPickingSlip">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="assigned_user_id" property="assignedUserId" />
        <result column="bin_location_id" property="binLocationId" />
        <result column="prep_picking_slip_status" property="prepPickingSlipStatus" />
        <result column="note" property="note" />
        <result column="print_status" property="printStatus" />
        <result column="qty" property="qty" />
        <result column="description" property="description" />
        <result column="version" property="version" />
        <result column="deleted_note" property="deletedNote" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="ref_num" property="refNum" />
        <result column="detail_product_type" property="detailProductType" />
        <result column="product_id" property="productId" />
        <result column="hazmat_version_ref_num" property="hazmatVersionRefNum" />
        <result column="allocate_putaway_qty" property="allocatePutawayQty" />
        <result column="putaway_qty" property="putawayQty" />
        <result column="order_type" property="orderType" />
        <result column="otc_picking_slip_id" property="otcPickingSlipId" />
        <result column="pick_to_station" property="pickToStation" />
        <result column="has_cus_ship_require" property="hasCusShipRequire" />
        <result column="on_site_pack_flag" property="onSitePackFlag" />
        <result column="prep_picking_slip_type" property="prepPickingSlipType" />
        <result column="prep_picking_slip_version_int" property="prepPickingSlipVersionInt" />
        <result column="transaction_partner_id" property="transactionPartnerId" />
        <result column="prep_picking_slip_product_type" property="prepPickingSlipProductType" />
        <result column="locked_before" property="lockedBefore" />
        <result column="process_type" property="processType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.assigned_user_id,
        t.bin_location_id,
        t.prep_picking_slip_status,
        t.note,
        t.print_status,
        t.qty,
        t.description,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.detail_product_type,
        t.product_id,
        t.allocate_putaway_qty,
        t.putaway_qty,
        t.order_type,
        t.otc_picking_slip_id,
        t.pick_to_station,
        t.has_cus_ship_require,
        t.on_site_pack_flag,
        t.prep_picking_slip_type,
        t.prep_picking_slip_version_int,
        t.prep_picking_slip_product_type,
        t.transaction_partner_id,
        t.process_type,
        t.locked_before
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcPrepPickingSlipPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otc_prep_picking_slip t
        WHERE
            1=1
            <if test="wk != null">
            AND EXISTS (
                SELECT t2.otc_prep_picking_slip_id
                FROM otc_prep_workorder t2 LEFT JOIN otc_workorder t3 ON t2.otc_workorder_id = t3.id
                WHERE t2.otc_prep_picking_slip_id = t.id
                <include refid="Work_Order_Query_Where_List"/>
                )
            </if>

            AND t.remove_flag = 0
            <if test="qo != null">
                <include refid="List_Where_List" />
            </if>
            <include refid="Base_Order_By_List" />
    </select>

    <select id="dropProList" resultType="hashMap">
        SELECT
        COUNT(*) AS countNum,
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}") AS ${item.name}
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
        FROM
        otc_prep_picking_slip t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        GROUP BY
        <foreach collection="columnList" item="item" separator=",">
            <choose>
                <when test="qo.timeZone != null and qo.timeZone != '' and item.type != null and item.type == 'LocalDateTime'">
                    DATE_FORMAT(CONVERT_TZ(t.${item.name}, '+00:00', #{qo.timeZone}), "${item.format}")
                </when>
                <otherwise>
                    ${item.name}
                </otherwise>
            </choose>
        </foreach>
    </select>

    <sql id="Work_Order_Query_Where_List">
        <if test="wk.requestSnapshotLastShipDateStart != null">
            AND t3.request_snapshot_last_ship_date  &gt;= #{wk.requestSnapshotLastShipDateStart}
        </if>
        <if test="wk.requestSnapshotLastShipDateEnd != null">
            AND t3.request_snapshot_last_ship_date  &lt; #{wk.requestSnapshotLastShipDateEnd}
        </if>
        <if test="wk.requestSnapshotRequestRefNumList != null and wk.requestSnapshotRequestRefNumList.size > 0 ">
            AND t3.request_snapshot_request_ref_num IN
            <foreach collection="wk.requestSnapshotRequestRefNumList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </sql>

    <sql id="List_Where_List">
        <if test="qo.prepPickingSlipStatus != null and qo.prepPickingSlipStatus != ''">
            AND t.prep_picking_slip_status = #{qo.prepPickingSlipStatus}
        </if>
        <if test="qo.processType != null and qo.processType != ''">
            AND t.process_type = #{qo.processType}
        </if>
        <if test="qo.prepPickingSlipStatusList != null and qo.prepPickingSlipStatusList.size > 0 ">
            AND t.prep_picking_slip_status in
            <foreach collection="qo.prepPickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPickingSlipId != null">
            AND t.otc_picking_slip_id = #{qo.otcPickingSlipId}
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.prepPickingSlipType != null and qo.prepPickingSlipType != ''">
            AND t.prep_picking_slip_type = #{qo.prepPickingSlipType}
        </if>
        <if test="qo.prepPickingSlipTypeList != null and qo.prepPickingSlipTypeList.size > 0 ">
            AND t.prep_picking_slip_type in
            <foreach collection="qo.prepPickingSlipTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.pickToStation != null and qo.pickToStation != ''">
            AND t.pick_to_station = #{qo.pickToStation}
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.prepPickingSlipProductTypeList != null and qo.prepPickingSlipProductTypeList.size > 0 ">
            AND t.prep_picking_slip_product_type in
            <foreach collection="qo.prepPickingSlipProductTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.id DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.assignedUserId != null">
            AND t.assigned_user_id = #{qo.assignedUserId}
        </if>
        <if test="qo.binLocationId != null">
            AND t.bin_location_id = #{qo.binLocationId}
        </if>
        <if test="qo.prepPickingSlipStatus != null and qo.prepPickingSlipStatus != ''">
            AND t.prep_picking_slip_status = #{qo.prepPickingSlipStatus}
        </if>
        <if test="qo.prepPickingSlipStatusList != null and qo.prepPickingSlipStatusList.size > 0 ">
            AND t.prep_picking_slip_status in
            <foreach collection="qo.prepPickingSlipStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.qty != null">
            AND t.qty = #{qo.qty}
        </if>
        <if test="qo.description != null and qo.description != ''">
            AND t.description = #{qo.description}
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.productId != null">
            AND t.product_id = #{qo.productId}
        </if>
        <if test="qo.allocatePutawayQty != null">
            AND t.allocate_putaway_qty = #{qo.allocatePutawayQty}
        </if>
        <if test="qo.putawayQty != null">
            AND t.putaway_qty = #{qo.putawayQty}
        </if>
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcPickingSlipId != null">
            AND t.otc_picking_slip_id = #{qo.otcPickingSlipId}
        </if>
        <if test="qo.pickToStation != null and qo.pickToStation != ''">
            AND t.pick_to_station = #{qo.pickToStation}
        </if>
        <if test="qo.pickToStationList != null and qo.pickToStationList.size > 0 ">
            AND t.pick_to_station in
            <foreach collection="qo.pickToStationList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.hasCusShipRequire != null">
            AND t.has_cus_ship_require = #{qo.hasCusShipRequire}
        </if>
        <if test="qo.onSitePackFlag != null">
            AND t.on_site_pack_flag = #{qo.onSitePackFlag}
        </if>
        <if test="qo.prepPickingSlipType != null and qo.prepPickingSlipType != ''">
            AND t.prep_picking_slip_type = #{qo.prepPickingSlipType}
        </if>
        <if test="qo.prepPickingSlipTypeList != null and qo.prepPickingSlipTypeList.size > 0 ">
            AND t.prep_picking_slip_type in
            <foreach collection="qo.prepPickingSlipTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lockedBefore != null and qo.lockedBefore != ''">
            AND t.locked_before = #{qo.lockedBefore}
        </if>
    </sql>

    <sql id="Exists_InnerTable">
        <bind name="qo" value="${InnerQoTableAlias}"/>

        <if test="${InnerQoTableAlias}!=null">
            AND EXISTS(SELECT t.id
            FROM otc_prep_picking_slip t
            WHERE  ${JoinCondition}
            AND t.remove_flag = 0
            <include refid="cn.need.cloud.biz.mapper.otc.OtcPrepPickingSlipMapper.List_Where_List">

            </include>
            )
        </if>
    </sql>
</mapper>