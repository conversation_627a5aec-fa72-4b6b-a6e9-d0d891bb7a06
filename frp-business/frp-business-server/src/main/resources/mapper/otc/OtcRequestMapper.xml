<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcRequestMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcRequest">
        <result column="id" property="id"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remove_flag" property="removeFlag"/>
        <result column="request_ref_num" property="requestRefNum"/>
        <result column="ship_express_flag" property="shipExpressFlag"/>
        <result column="ship_method" property="shipMethod"/>
        <result column="ship_carrier" property="shipCarrier"/>
        <result column="ship_api_profile_ref_num" property="shipApiProfileRefNum"/>
        <result column="last_ship_date" property="lastShipDate"/>
        <result column="insurance_amount_amount" property="insuranceAmountAmount"/>
        <result column="signature_type" property="signatureType"/>
        <result column="ship_to_address_name" property="shipToAddressName"/>
        <result column="ship_to_address_company" property="shipToAddressCompany"/>
        <result column="ship_to_address_country" property="shipToAddressCountry"/>
        <result column="ship_to_address_state" property="shipToAddressState"/>
        <result column="ship_to_address_city" property="shipToAddressCity"/>
        <result column="ship_to_address_zip_code" property="shipToAddressZipCode"/>
        <result column="ship_to_address_addr1" property="shipToAddressAddr1"/>
        <result column="ship_to_address_addr2" property="shipToAddressAddr2"/>
        <result column="ship_to_address_addr3" property="shipToAddressAddr3"/>
        <result column="ship_to_address_email" property="shipToAddressEmail"/>
        <result column="ship_to_address_phone" property="shipToAddressPhone"/>
        <result column="ship_to_address_note" property="shipToAddressNote"/>
        <result column="order_type" property="orderType"/>
        <result column="has_cus_ship_require" property="hasCusShipRequire"/>
        <result column="provide_shipping_label_flag" property="provideShippingLabelFlag"/>
        <result column="version" property="version"/>
        <result column="deleted_note" property="deletedNote"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="warehouse_id" property="warehouseId"/>
        <result column="ref_num" property="refNum"/>
        <result column="transaction_partner_id" property="transactionPartnerId"/>
        <result column="otc_request_status" property="otcRequestStatus"/>
        <result column="note" property="note"/>
        <result column="channel" property="channel"/>
        <result column="ship_to_address_is_residential" property="shipToAddressIsResidential"/>
        <result column="insurance_amount_currency" property="insuranceAmountCurrency"/>
        <result column="ship_from_address_addr1" property="shipFromAddressAddr1"/>
        <result column="ship_from_address_addr2" property="shipFromAddressAddr2"/>
        <result column="ship_from_address_addr3" property="shipFromAddressAddr3"/>
        <result column="ship_from_address_city" property="shipFromAddressCity"/>
        <result column="ship_from_address_company" property="shipFromAddressCompany"/>
        <result column="ship_from_address_country" property="shipFromAddressCountry"/>
        <result column="ship_from_address_email" property="shipFromAddressEmail"/>
        <result column="ship_from_address_is_residential" property="shipFromAddressIsResidential"/>
        <result column="ship_from_address_name" property="shipFromAddressName"/>
        <result column="ship_from_address_note" property="shipFromAddressNote"/>
        <result column="ship_from_address_phone" property="shipFromAddressPhone"/>
        <result column="ship_from_address_state" property="shipFromAddressState"/>
        <result column="ship_from_address_zip_code" property="shipFromAddressZipCode"/>
        <result column="locked_before" property="lockedBefore"/>
        <result column="process_start_time" property="processStartTime" />
        <result column="process_end_time" property="processEndTime" />
        <result column="fee_status" property="feeStatus" />
        <result column="fee_calculation_time" property="feeCalculationTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.request_ref_num,
        t.ship_express_flag,
        t.ship_method,
        t.ship_carrier,
        t.ship_api_profile_ref_num,
        t.last_ship_date,
        t.insurance_amount_amount,
        t.signature_type,
        t.ship_to_address_name,
        t.ship_to_address_company,
        t.ship_to_address_country,
        t.ship_to_address_state,
        t.ship_to_address_city,
        t.ship_to_address_zip_code,
        t.ship_to_address_addr1,
        t.ship_to_address_addr2,
        t.ship_to_address_addr3,
        t.ship_to_address_email,
        t.ship_to_address_phone,
        t.ship_to_address_note,
        t.order_type,
        t.has_cus_ship_require,
        t.provide_shipping_label_flag,
        t.version,
        t.deleted_note,
        t.tenant_id,
        t.warehouse_id,
        t.ref_num,
        t.transaction_partner_id,
        t.otc_request_status,
        t.note,
        t.channel,
        t.ship_to_address_is_residential,
        t.insurance_amount_currency,
        t.ship_from_address_addr1,
        t.ship_from_address_addr2,
        t.ship_from_address_addr3,
        t.ship_from_address_city,
        t.ship_from_address_company,
        t.ship_from_address_country,
        t.ship_from_address_email,
        t.ship_from_address_is_residential,
        t.ship_from_address_name,
        t.ship_from_address_note,
        t.ship_from_address_phone,
        t.ship_from_address_state,
        t.ship_from_address_zip_code,
        t.process_start_time,
        t.process_end_time,
        t.fee_status,
        t.fee_calculation_time,
        t.locked_before
    </sql>
    <update id="updateInsuranceAmount" parameterType="cn.need.cloud.biz.model.entity.otc.OtcRequest">
        UPDATE otc_request
        SET insurance_amount_amount = #{entity.insuranceAmountAmount},
            version                 = #{entity.version} + 1
        WHERE id = #{entity.id}
          AND version = #{entity.version}
          AND remove_flag = 0
    </update>


    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.otc.page.OtcRequestPageVO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        otc_request t
        WHERE
        t.remove_flag = 0
        <include refid="Base_Where_List"/>
        <include refid="Base_Order_By_List"></include>
    </select>

    <select id="detailById" resultMap="GetList"
            parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>,
        d.line_num as dLineNum,
        d.qty as dQty,
        d.finish_qty as finishQty,
        d.product_id as dProductId,
        p.id as pId,
        p.line_num as pLineNum,
        p.tracking_num as pTrackingNum,
        p.ship_express_flag as pShipExpressFlag,
        p.ship_method as pShipMethod,
        p.ship_carrier as pShipCarrier,
        p.ship_size_length,
        p.ship_size_width,
        p.ship_size_height,
        p.ship_size_weight,
        p.note as pNote,
        p.package_multibox_version_int,
        p.package_multibox_upc,
        p.package_multibox_line_num,
        p.package_multibox_product_id,
        p.ship_size_dimension_unit,
        p.ship_size_weight_unit,
        pd.line_num as pdLineNum,
        pd.qty as pdQty,
        pd.product_id as pdProductId,
        pl.line_num as plLineNum,
        pl.label_type,
        pl.paper_type,
        pl.raw_data_type,
        pl.label_ref_num,
        pl.label_raw_data,
        pl.file_id_raw_data_type
        from
        otc_request t
        left join otc_request_detail d on t.id = d.otc_request_id
        left join otc_request_package p on t.id = p.otc_request_id
        left join otc_request_package_detail pd on p.id=pd.otc_request_package_id
        left join otc_request_package_label pl on p.id=pl.otc_request_package_id
        where t.id = #{id}
        and t.remove_flag = 0
        and COALESCE(d.remove_flag, 0) = 0
        and COALESCE(p.remove_flag, 0) = 0
        and COALESCE(pd.remove_flag, 0) = 0
        and COALESCE(pl.remove_flag, 0) = 0
        ORDER BY p.line_num ASC;
    </select>
    <resultMap id="GetList" type="cn.need.cloud.biz.model.vo.otc.request.OtcRequestVO" autoMapping="true">
        <id property="id" column="id"/>
        <collection property="detailList" ofType="cn.need.cloud.biz.model.vo.otc.request.OtcRequestDetailFullVO"
                    autoMapping="true">
            <id property="lineNum" column="dLineNum"/>
            <result property="productId" column="dProductId"/>
            <result property="qty" column="dQty"/>
        </collection>
        <collection property="packageList" ofType="cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageFullVO"
                    autoMapping="true">
            <id property="id" column="pId"/>
            <result property="lineNum" column="pLineNum"/>
            <result property="trackingNum" column="pTrackingNum"/>
            <result property="shipExpressFlag" column="pShipExpressFlag"/>
            <result property="shipMethod" column="pShipMethod"/>
            <result property="shipCarrier" column="pShipCarrier"/>
            <result property="note" column="pNote"/>
            <collection property="detailList" ofType="cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageDetailFullVO"
                        autoMapping="true">
                <id property="lineNum" column="pdLineNum"/>
                <result property="productId" column="pdProductId"/>
                <result property="qty" column="pdQty"/>
                <result property="finishQty" column="finishQty"/>
            </collection>
            <collection property="labelList" ofType="cn.need.cloud.biz.model.vo.otc.request.OtcRequestPackageLabelFullVO"
                        autoMapping="true">
                <id property="lineNum" column="plLineNum"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
    
        <if test="qo.id != null">
            AND t.id = #{qo.id}
        </if>
        <if test="qo.idList != null and qo.idList.size > 0 ">
            AND t.id in
            <foreach collection="qo.idList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.idNiList != null and qo.idNiList.size > 0 ">
            AND t.id not in
            <foreach collection="qo.idNiList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="qo.createTimeStart != null">
            AND t.create_time &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.requestRefNum != null and qo.requestRefNum != ''">
            AND t.request_ref_num = #{qo.requestRefNum}
        </if>
        <if test="qo.requestRefNumList != null and qo.requestRefNumList.size > 0 ">
            AND t.request_ref_num in
            <foreach collection="qo.requestRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipExpressFlag != null">
            AND t.ship_express_flag = #{qo.shipExpressFlag}
        </if>
        <if test="qo.shipMethod != null and qo.shipMethod != ''">
            AND t.ship_method = #{qo.shipMethod}
        </if>
        <if test="qo.shipMethodList != null and qo.shipMethodList.size > 0 ">
            AND t.ship_method in
            <foreach collection="qo.shipMethodList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipCarrier != null and qo.shipCarrier != ''">
            AND t.ship_carrier = #{qo.shipCarrier}
        </if>
        <if test="qo.shipCarrierList != null and qo.shipCarrierList.size > 0 ">
            AND t.ship_carrier in
            <foreach collection="qo.shipCarrierList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipApiProfileRefNum != null and qo.shipApiProfileRefNum != ''">
            AND t.ship_api_profile_ref_num = #{qo.shipApiProfileRefNum}
        </if>
        <if test="qo.shipApiProfileRefNumList != null and qo.shipApiProfileRefNumList.size > 0 ">
            AND t.ship_api_profile_ref_num in
            <foreach collection="qo.shipApiProfileRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lastShipDateStart != null">
            AND t.last_ship_date &gt;= #{qo.lastShipDateStart}
        </if>
        <if test="qo.lastShipDateEnd != null">
            AND t.last_ship_date &lt; #{qo.lastShipDateEnd}
        </if>
        <if test="qo.insuranceAmountAmount != null">
            AND t.insurance_amount_amount = #{qo.insuranceAmountAmount}
        </if>
        <if test="qo.signatureType != null and qo.signatureType != ''">
            AND t.signature_type = #{qo.signatureType}
        </if>
        <if test="qo.signatureTypeList != null and qo.signatureTypeList.size > 0 ">
            AND t.signature_type in
            <foreach collection="qo.signatureTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipToAddressName != null and qo.shipToAddressName != ''">
            AND t.ship_to_address_name = #{qo.shipToAddressName}
        </if>
        <if test="qo.shipToAddressCompany != null and qo.shipToAddressCompany != ''">
            AND t.ship_to_address_company = #{qo.shipToAddressCompany}
        </if>
        <if test="qo.shipToAddressCountry != null and qo.shipToAddressCountry != ''">
            AND t.ship_to_address_country = #{qo.shipToAddressCountry}
        </if>
        <if test="qo.shipToAddressState != null and qo.shipToAddressState != ''">
            AND t.ship_to_address_state = #{qo.shipToAddressState}
        </if>
        <if test="qo.shipToAddressCity != null and qo.shipToAddressCity != ''">
            AND t.ship_to_address_city = #{qo.shipToAddressCity}
        </if>
        <if test="qo.shipToAddressZipCode != null and qo.shipToAddressZipCode != ''">
            AND t.ship_to_address_zip_code = #{qo.shipToAddressZipCode}
        </if>
        <if test="qo.shipToAddressAddr1 != null and qo.shipToAddressAddr1 != ''">
            AND t.ship_to_address_addr1 = #{qo.shipToAddressAddr1}
        </if>
        <if test="qo.shipToAddressAddr2 != null and qo.shipToAddressAddr2 != ''">
            AND t.ship_to_address_addr2 = #{qo.shipToAddressAddr2}
        </if>
        <if test="qo.shipToAddressAddr3 != null and qo.shipToAddressAddr3 != ''">
            AND t.ship_to_address_addr3 = #{qo.shipToAddressAddr3}
        </if>
        <if test="qo.shipToAddressEmail != null and qo.shipToAddressEmail != ''">
            AND t.ship_to_address_email = #{qo.shipToAddressEmail}
        </if>
        <if test="qo.shipToAddressPhone != null and qo.shipToAddressPhone != ''">
            AND t.ship_to_address_phone = #{qo.shipToAddressPhone}
        </if>
        <if test="qo.shipToAddressNote != null and qo.shipToAddressNote != ''">
            AND t.ship_to_address_note = #{qo.shipToAddressNote}
        </if>
        <if test="qo.orderType != null and qo.orderType != ''">
            AND t.order_type = #{qo.orderType}
        </if>
        <if test="qo.orderTypeList != null and qo.orderTypeList.size > 0 ">
            AND t.order_type in
            <foreach collection="qo.orderTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.hasCusShipRequire != null">
            AND t.has_cus_ship_require = #{qo.hasCusShipRequire}
        </if>
        <if test="qo.provideShippingLabelFlag != null">
            AND t.provide_shipping_label_flag = #{qo.provideShippingLabelFlag}
        </if>
        <if test="qo.version != null">
            AND t.version = #{qo.version}
        </if>
        <if test="qo.deletedNote != null and qo.deletedNote != ''">
            AND t.deleted_note = #{qo.deletedNote}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.refNum != null and qo.refNum != ''">
            AND t.ref_num = #{qo.refNum}
        </if>
        <if test="qo.refNumList != null and qo.refNumList.size > 0 ">
            AND t.ref_num in
            <foreach collection="qo.refNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.transactionPartnerId != null">
            AND t.transaction_partner_id = #{qo.transactionPartnerId}
        </if>
        <if test="qo.transactionPartnerIdList != null and qo.transactionPartnerIdList.size > 0 ">
            AND t.transaction_partner_id in
            <foreach collection="qo.transactionPartnerIdList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.otcRequestStatus != null and qo.otcRequestStatus != ''">
            AND t.otc_request_status = #{qo.otcRequestStatus}
        </if>
        <if test="qo.otcRequestStatusList != null and qo.otcRequestStatusList.size > 0 ">
            AND t.otc_request_status in
            <foreach collection="qo.otcRequestStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.note != null and qo.note != ''">
            AND t.note = #{qo.note}
        </if>
        <if test="qo.channel != null and qo.channel != ''">
            AND t.channel = #{qo.channel}
        </if>
        <if test="qo.channelList != null and qo.channelList.size > 0 ">
            AND t.channel in
            <foreach collection="qo.channelList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.shipToAddressIsResidential != null">
            AND t.ship_to_address_is_residential = #{qo.shipToAddressIsResidential}
        </if>
        <if test="qo.insuranceAmountCurrency != null and qo.insuranceAmountCurrency != ''">
            AND t.insurance_amount_currency = #{qo.insuranceAmountCurrency}
        </if>
        <if test="qo.shipFromAddressAddr1 != null and qo.shipFromAddressAddr1 != ''">
            AND t.ship_from_address_addr1 = #{qo.shipFromAddressAddr1}
        </if>
        <if test="qo.shipFromAddressAddr2 != null and qo.shipFromAddressAddr2 != ''">
            AND t.ship_from_address_addr2 = #{qo.shipFromAddressAddr2}
        </if>
        <if test="qo.shipFromAddressAddr3 != null and qo.shipFromAddressAddr3 != ''">
            AND t.ship_from_address_addr3 = #{qo.shipFromAddressAddr3}
        </if>
        <if test="qo.shipFromAddressCity != null and qo.shipFromAddressCity != ''">
            AND t.ship_from_address_city = #{qo.shipFromAddressCity}
        </if>
        <if test="qo.shipFromAddressCompany != null and qo.shipFromAddressCompany != ''">
            AND t.ship_from_address_company = #{qo.shipFromAddressCompany}
        </if>
        <if test="qo.shipFromAddressCountry != null and qo.shipFromAddressCountry != ''">
            AND t.ship_from_address_country = #{qo.shipFromAddressCountry}
        </if>
        <if test="qo.shipFromAddressEmail != null and qo.shipFromAddressEmail != ''">
            AND t.ship_from_address_email = #{qo.shipFromAddressEmail}
        </if>
        <if test="qo.shipFromAddressIsResidential != null">
            AND t.ship_from_address_is_residential = #{qo.shipFromAddressIsResidential}
        </if>
        <if test="qo.shipFromAddressName != null and qo.shipFromAddressName != ''">
            AND t.ship_from_address_name = #{qo.shipFromAddressName}
        </if>
        <if test="qo.shipFromAddressNote != null and qo.shipFromAddressNote != ''">
            AND t.ship_from_address_note = #{qo.shipFromAddressNote}
        </if>
        <if test="qo.shipFromAddressPhone != null and qo.shipFromAddressPhone != ''">
            AND t.ship_from_address_phone = #{qo.shipFromAddressPhone}
        </if>
        <if test="qo.shipFromAddressState != null and qo.shipFromAddressState != ''">
            AND t.ship_from_address_state = #{qo.shipFromAddressState}
        </if>
        <if test="qo.shipFromAddressZipCode != null and qo.shipFromAddressZipCode != ''">
            AND t.ship_from_address_zip_code = #{qo.shipFromAddressZipCode}
        </if>
        <if test="qo.lockedBefore != null and qo.lockedBefore != ''">
            AND t.locked_before = #{qo.lockedBefore}
        </if>
        <if test="qo.feeStatus != null and qo.feeStatus != ''">
            AND t.fee_status = #{qo.feeStatus}
        </if>
        <if test="qo.feeStatusList != null and qo.feeStatusList.size > 0 ">
            AND t.fee_status in
            <foreach collection="qo.feeStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

    </sql>

</mapper>