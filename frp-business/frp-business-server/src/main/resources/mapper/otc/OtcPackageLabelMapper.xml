<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.need.cloud.biz.mapper.otc.OtcPackageLabelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.need.cloud.biz.model.entity.otc.OtcPackageLabel">
        <result column="id" property="id" />
        <result column="create_by" property="createBy" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_time" property="updateTime" />
        <result column="remove_flag" property="removeFlag" />
        <result column="line_num" property="lineNum" />
        <result column="label_type" property="labelType" />
        <result column="label_ref_num" property="labelRefNum" />
        <result column="paper_type" property="paperType" />
        <result column="label_raw_data" property="labelRawData" />
        <result column="otc_package_id" property="otcPackageId" />
        <result column="print_status" property="printStatus" />
        <result column="version" property="version" />
        <result column="tenant_id" property="tenantId" />
        <result column="warehouse_id" property="warehouseId" />
        <result column="raw_data_type" property="rawDataType" />
        <result column="file_id_raw_data_type" property="fileIdRawDataType" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        t.id,
        t.create_by,
        t.create_time,
        t.update_by,
        t.update_time,
        t.remove_flag,
        t.line_num,
        t.label_type,
        t.label_ref_num,
        t.paper_type,
        t.label_raw_data,
        t.otc_package_id,
        t.print_status,
        t.version,
        t.tenant_id,
        t.warehouse_id,
        t.raw_data_type,
        t.file_id_raw_data_type
    </sql>

    <!-- 根据筛选条件查询列表 -->
    <select id="listByQuery" resultType="cn.need.cloud.biz.model.vo.page.OtcPackageLabelPageVO">
        SELECT
            <include refid="Base_Column_List" />
        FROM
            otc_package_label t
        WHERE
            t.remove_flag = 0
            <include refid="Base_Where_List" />
            <include refid="Base_Order_By_List" />
    </select>

    <!-- 通用排序条件 -->
    <sql id="Base_Order_By_List">
        <choose>
            <when test="page != null and page.orders != null and page.orders.size() > 0">

            </when>
            <otherwise>
                ORDER BY t.create_time DESC
            </otherwise>
        </choose>
    </sql>

    <!-- 通用查询条件 -->
    <sql id="Base_Where_List">
        <if test="qo.createTimeStart != null">
            AND t.create_time  &gt;= #{qo.createTimeStart}
        </if>
        <if test="qo.createTimeEnd != null">
            AND t.create_time  &lt; #{qo.createTimeEnd}
        </if>
        <if test="qo.updateTimeStart != null">
            AND t.update_time  &gt;= #{qo.updateTimeStart}
        </if>
        <if test="qo.updateTimeEnd != null">
            AND t.update_time  &lt; #{qo.updateTimeEnd}
        </if>
        <if test="qo.createBy != null">
            AND t.create_by = #{qo.createBy}
        </if>
        <if test="qo.createByList != null and qo.createByList.size > 0 ">
            AND t.create_by in
            <foreach collection="qo.createByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.updateBy != null">
            AND t.update_by = #{qo.updateBy}
        </if>
        <if test="qo.updateByList != null and qo.updateByList.size > 0 ">
            AND t.update_by in
            <foreach collection="qo.updateByList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.lineNum != null">
            AND t.line_num = #{qo.lineNum}
        </if>
        <if test="qo.labelType != null and qo.labelType != ''">
            AND t.label_type = #{qo.labelType}
        </if>
        <if test="qo.labelTypeList != null and qo.labelTypeList.size > 0 ">
            AND t.label_type in
            <foreach collection="qo.labelTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.labelRefNum != null and qo.labelRefNum != ''">
            AND t.label_ref_num = #{qo.labelRefNum}
        </if>
        <if test="qo.labelRefNumList != null and qo.labelRefNumList.size > 0 ">
            AND t.label_ref_num in
            <foreach collection="qo.labelRefNumList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.paperType != null and qo.paperType != ''">
            AND t.paper_type = #{qo.paperType}
        </if>
        <if test="qo.paperTypeList != null and qo.paperTypeList.size > 0 ">
            AND t.paper_type in
            <foreach collection="qo.paperTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.labelRawData != null and qo.labelRawData != ''">
            AND t.label_raw_data = #{qo.labelRawData}
        </if>
        <if test="qo.otcPackageId != null">
            AND t.otc_package_id = #{qo.otcPackageId}
        </if>
        <if test="qo.printStatus != null and qo.printStatus != ''">
            AND t.print_status = #{qo.printStatus}
        </if>
        <if test="qo.printStatusList != null and qo.printStatusList.size > 0 ">
            AND t.print_status in
            <foreach collection="qo.printStatusList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.version != null">
            AND t.version  = #{qo.version}
        </if>
        <if test="qo.tenantId != null">
            AND t.tenant_id = #{qo.tenantId}
        </if>
        <if test="qo.warehouseId != null">
            AND t.warehouse_id = #{qo.warehouseId}
        </if>
        <if test="qo.rawDataType != null and qo.rawDataType != ''">
            AND t.raw_data_type = #{qo.rawDataType}
        </if>
        <if test="qo.rawDataTypeList != null and qo.rawDataTypeList.size > 0 ">
            AND t.raw_data_type in
            <foreach collection="qo.rawDataTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="qo.fileIdRawDataType != null and qo.fileIdRawDataType != ''">
            AND t.file_id_raw_data_type = #{qo.fileIdRawDataType}
        </if>
        <if test="qo.fileIdRawDataTypeList != null and qo.fileIdRawDataTypeList.size > 0 ">
            AND t.file_id_raw_data_type in
            <foreach collection="qo.fileIdRawDataTypeList" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </sql>

</mapper>