package cn.need.cloud.biz.util;

import cn.need.framework.common.core.exception.unchecked.ImpossibleException;
import cn.need.framework.common.mybatis.model.IdExtractable;
import lombok.Data;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 字段验证功能测试
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
class PartialUpdateFieldValidationTest {

    @BeforeEach
    void setUp() {
        // 清除缓存，确保测试独立性
        PartialUpdateUtil.clearEntityFieldsCache();
    }

    @AfterEach
    void tearDown() {
        // 清除缓存
        PartialUpdateUtil.clearEntityFieldsCache();
    }

    @Test
    void testValidFieldsPass() {
        // 准备测试数据
        TestUpdateParam updateParam = new TestUpdateParam();
        updateParam.setId(123L);
        updateParam.setName("Test Name");
        updateParam.setAmount(new BigDecimal("100.50"));

        // 执行字段验证
        assertDoesNotThrow(() -> {
            Map<String, Object> fields = PartialUpdateUtil.getPartialUpdateFields(updateParam, TestEntity.class);
            assertEquals(3, fields.size());
            assertEquals(123L, fields.get("id"));
            assertEquals("Test Name", fields.get("name"));
            assertEquals(new BigDecimal("100.50"), fields.get("amount"));
        });
    }

    @Test
    void testInvalidFieldsThrowException() {
        // 准备包含无效字段的测试数据
        TestUpdateParamWithInvalidField updateParam = new TestUpdateParamWithInvalidField();
        updateParam.setId(123L);
        updateParam.setName("Test Name");
        updateParam.setInvalidField("This field does not exist in entity");

        // 验证抛出异常
        ImpossibleException exception = assertThrows(ImpossibleException.class, () -> {
            PartialUpdateUtil.getPartialUpdateFields(updateParam, TestEntity.class);
        });

        // 验证异常信息
        assertTrue(exception.getMessage().contains("Invalid fields found in update parameter"));
        assertTrue(exception.getMessage().contains("invalidField"));
        assertTrue(exception.getMessage().contains("TestEntity"));
        assertTrue(exception.getMessage().contains("Available fields"));
    }

    @Test
    void testFieldsCaching() {
        // 第一次调用，会进行反射提取
        TestUpdateParam updateParam1 = new TestUpdateParam();
        updateParam1.setId(123L);
        updateParam1.setName("Test 1");

        Map<String, Object> fields1 = PartialUpdateUtil.getPartialUpdateFields(updateParam1, TestEntity.class);
        assertNotNull(fields1);

        // 验证缓存已生成
        String cacheStats = PartialUpdateUtil.getEntityFieldsCacheStats();
        assertTrue(cacheStats.contains("1"));

        // 第二次调用，使用缓存
        TestUpdateParam updateParam2 = new TestUpdateParam();
        updateParam2.setId(456L);
        updateParam2.setAmount(new BigDecimal("200.00"));

        Map<String, Object> fields2 = PartialUpdateUtil.getPartialUpdateFields(updateParam2, TestEntity.class);
        assertNotNull(fields2);
        assertEquals(2, fields2.size());
    }

    @Test
    void testEmptyFieldsValidation() {
        // 空的更新参数
        TestUpdateParam updateParam = new TestUpdateParam();
        updateParam.setId(123L); // 只设置ID

        // 应该正常通过验证，但返回空的字段映射
        Map<String, Object> fields = PartialUpdateUtil.getPartialUpdateFields(updateParam, TestEntity.class);
        assertEquals(1, fields.size()); // 只有ID字段
        assertEquals(123L, fields.get("id"));
    }

    @Test
    void testBatchUpdateWithValidation() {
        // 准备批量更新数据
        TestUpdateParam updateParam1 = new TestUpdateParam();
        updateParam1.setId(123L);
        updateParam1.setName("Test 1");

        TestUpdateParam updateParam2 = new TestUpdateParam();
        updateParam2.setId(456L);
        updateParam2.setAmount(new BigDecimal("300.00"));

        // 执行批量字段验证
        assertDoesNotThrow(() -> {
            PartialUpdateUtil.PartialUpdateChangeSet<TestUpdateParam> changeSet = 
                    PartialUpdateUtil.createPartialUpdateChangeSet(
                            java.util.List.of(updateParam1, updateParam2),
                            TestUpdateParam::getId,
                            TestEntity.class
                    );

            assertTrue(changeSet.hasUpdates());
            assertEquals(2, changeSet.getUpdateCount());
        });
    }

    // 测试实体类
    @Data
    static class TestEntity {
        private Long id;
        private String name;
        private BigDecimal amount;
        private String description;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
    }

    // 有效的更新参数类
    @Data
    static class TestUpdateParam implements IdExtractable {
        private Long id;
        private String name;
        private BigDecimal amount;
        private String description;

        @Override
        public Long getId() {
            return id;
        }
    }

    // 包含无效字段的更新参数类
    @Data
    static class TestUpdateParamWithInvalidField implements IdExtractable {
        private Long id;
        private String name;
        private String invalidField; // 这个字段在TestEntity中不存在

        @Override
        public Long getId() {
            return id;
        }
    }
}
