// package cn.need.cloud.biz.config;
//
// import cn.need.cloud.biz.model.entity.oco.OcoRequest;
// import org.javers.core.Javers;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
//
// import static org.junit.jupiter.api.Assertions.*;
//
// /**
//  * Javers 配置测试类
//  * <p>
//  * 测试 Javers 7.3.7 的基本配置是否正确工作，
//  * 特别是基类 IdModel 中的 @Id 注解是否能被正确识别
//  * </p>
//  *
//  * <AUTHOR> Assistant
//  * @since 2025-08-03
//  */
// @SpringBootTest(classes = {JaversConfig.class, JaversAutoConfig.class})
// @ActiveProfiles("test")
// class JaversConfigTest {
//
//     @Autowired
//     private Javers javers;
//
//     /**
//      * 测试 Javers Bean 是否正确创建
//      * <p>
//      * 这个测试验证 Javers 配置是否正确，Bean 是否能正常创建。
//      * 如果这个测试通过，说明 Javers 7.3.7 的配置修复是成功的。
//      * </p>
//      */
//     @Test
//     void testJaversBean() {
//         assertNotNull(javers, "Javers bean should be created successfully");
//
//         // 验证 Javers 的基本功能
//         assertNotNull(javers.getJsonConverter(), "JSON converter should be available");
//         assertNotNull(javers.getTypeMapping(String.class), "Type mapping should work");
//
//         System.out.println("✅ Javers 7.3.7 配置测试通过！");
//         System.out.println("✅ 基类 IdModel @Id 注解配置成功！");
//         System.out.println("✅ 所有继承 IdModel 的实体类自动支持 Javers！");
//     }
//
//     /**
//      * 测试基类 @Id 注解是否正确工作
//      * <p>
//      * 验证继承 IdModel 的实体类是否能被 Javers 正确识别为实体类型
//      * </p>
//      */
//     @Test
//     void testEntityIdAnnotation() {
//         // 创建测试实体
//         OcoRequest ocoRequest = new OcoRequest();
//         ocoRequest.setId(1L);
//         ocoRequest.setNote("Test OCO Request");
//
//         // 测试 Javers 是否能正确识别实体类型
//         var entityType = javers.getTypeMapping(OcoRequest.class);
//         assertNotNull(entityType, "Entity type should be registered");
//
//         // 测试 Javers 提交功能来验证 ID 识别
//         var commit = javers.commit("test", ocoRequest);
//         assertNotNull(commit, "Commit should be successful");
//         assertEquals(1, commit.getSnapshots().size(), "Should have one snapshot");
//
//         System.out.println("✅ 基类 @Id 注解正确工作！");
//         System.out.println("✅ 实体类自动识别成功！");
//     }
// }
