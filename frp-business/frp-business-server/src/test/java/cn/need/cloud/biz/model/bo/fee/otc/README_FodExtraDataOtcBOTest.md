# FodExtraDataOtcBO 单元测试说明

## 测试目标

为 `FodExtraDataOtcBO` 类中的 `isExpressRequest()` 方法编写全面的单元测试，重点验证加急请求的业务逻辑。

## 业务规则

根据需求，加急请求需要满足以下条件：
1. **时间条件**：创建时间 > 13:30 且 处理时间 ≤ 23:59:59（仓库时区）
2. **同一天条件**：创建时间和处理时间必须在同一天（仓库时区）

## 测试用例设计

### 1. 基础验证测试
- `testIsExpressRequest_RequestIsNull()` - request为null的情况
- `testIsExpressRequest_CreateTimeIsNull()` - createTime为null的情况
- `testIsExpressRequest_WarehouseIdIsNull()` - 仓库ID为null的情况

### 2. 正常业务逻辑测试
- `testIsExpressRequest_SameDay_ValidExpressRequest()` - 同一天的有效加急请求
- `testIsExpressRequest_SameDay_BeforeCutoffTime()` - 同一天但创建时间在13:30前
- `testIsExpressRequest_SameDay_AfterEndOfDay()` - 同一天但处理时间超过23:59:59

### 3. 边界条件测试
- `testIsExpressRequest_ExactBoundaryTimes()` - 恰好在边界时间
- `testIsExpressRequest_ExactCutoffTime()` - 恰好13:30创建
- `testIsExpressRequest_MidnightProcessTime()` - 午夜处理时间

### 4. 跨天情况测试（重点）
- `testIsExpressRequest_DifferentDay_ShouldConsiderSameDay()` - 不同天的情况
- `testIsExpressRequest_CrossMidnight()` - 跨午夜的情况

### 5. 时区测试
- `testIsExpressRequest_DifferentTimeZone()` - 不同时区的测试

### 6. 异常处理测试
- `testIsExpressRequest_WarehouseCacheNotFound()` - 仓库缓存获取失败
- `testIsExpressRequest_EmptyTimeZone()` - 时区配置为空
- `testIsExpressRequest_InvalidTimeZone()` - 无效时区
- `testIsExpressRequest_ProcessEndTimeIsNull()` - processEndTime为null

### 7. 综合测试
- `testIsExpressRequest_ComprehensiveSameDayLogic()` - 综合验证同一天的业务逻辑

## 问题修复

### 原问题：缺少同一天检查
原来的 `isExpressRequest()` 方法只检查时间（13:30-23:59:59），但没有验证创建时间和处理时间是否在同一天。

**问题场景**：
- 创建时间：2025-07-28 14:00（仓库时区）
- 处理时间：2025-07-29 22:00（仓库时区）
- 原代码结果：`true`（因为14:00 > 13:30 且 22:00 < 23:59:59）
- 业务规则期望：`false`（因为不在同一天）

### 已实施的修复方案

已在 `isExpressRequest()` 方法中添加了同一天检查：

```java
// 检查是否在同一天（仓库时区）
LocalDate createDate = createTimeInWarehouse.toLocalDate();
LocalDate processDate = processTimeInWarehouse.toLocalDate();
boolean isSameDay = createDate.equals(processDate);

// 只有在同一天且满足时间条件才是加急请求
return isSameDay && isAfterCutoff && isBeforeEndOfDay;
```

### 修复后的业务逻辑
现在加急请求需要同时满足以下三个条件：
1. **同一天**：创建时间和处理时间必须在同一天（仓库时区）
2. **创建时间**：> 13:30（仓库时区）
3. **处理时间**：≤ 23:59:59（仓库时区）

## 如何运行测试

### 方法1：使用Maven命令
```bash
# 进入项目目录
cd frp-business/frp-business-server

# 运行特定测试类
mvn test -Dtest=FodExtraDataOtcBOTest

# 运行特定测试方法
mvn test -Dtest=FodExtraDataOtcBOTest#testIsExpressRequest_SameDay_ValidExpressRequest
```

### 方法2：使用IDE
在IDE中右键点击测试类或测试方法，选择"Run Test"

## Mock策略

测试中使用了以下Mock策略：

1. **MockedStatic**：Mock `WarehouseCacheUtil.getById()` 静态方法
2. **Mock对象**：创建 `WarehouseCache` 和 `FodOtcRequestBO` 的Mock对象
3. **反射调用**：通过反射调用私有方法 `isExpressRequest()`

## 测试数据

测试中使用的标准测试数据：
- 仓库ID：1L
- 默认时区：America/New_York
- 测试日期：2025-07-28

## 注意事项

1. 测试类使用了 `@ExtendWith(MockitoExtension.class)` 注解
2. 使用 `MockedStatic` 来Mock静态方法，需要在try-with-resources块中使用
3. 通过反射调用私有方法，需要处理异常
4. 测试中包含了一些输出语句，用于观察当前代码的行为

## 预期测试结果

运行测试后，你会看到一些测试用例的输出，这些输出会显示当前代码的行为是否符合业务规则。特别关注跨天情况的测试结果，这些可能会暴露业务逻辑问题。
