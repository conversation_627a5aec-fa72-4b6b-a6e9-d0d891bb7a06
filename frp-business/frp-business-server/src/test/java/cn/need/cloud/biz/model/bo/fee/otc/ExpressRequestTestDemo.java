// package cn.need.cloud.biz.model.bo.fee.otc;
//
// import cn.need.cloud.biz.cache.bean.WarehouseCache;
// import cn.need.cloud.biz.util.WarehouseCacheUtil;
// import org.mockito.MockedStatic;
//
// import java.lang.reflect.Method;
// import java.time.LocalDateTime;
//
// import static org.mockito.ArgumentMatchers.anyLong;
// import static org.mockito.Mockito.mockStatic;
//
// /**
//  * 加急请求逻辑测试演示
//  * 用于验证修复后的isExpressRequest方法是否正确实现了"同一天"的业务规则
//  *
//  * <AUTHOR>
//  * @since 2025/7/28
//  */
// public class ExpressRequestTestDemo {
//
//     public static void main(String[] args) {
//         System.out.println("=== 加急请求逻辑测试演示 ===\n");
//
//         ExpressRequestTestDemo demo = new ExpressRequestTestDemo();
//
//         // 测试各种场景
//         demo.testScenario1_SameDayValid();
//         demo.testScenario2_SameDayInvalidTime();
//         demo.testScenario3_DifferentDay();
//         demo.testScenario4_CrossMidnight();
//         demo.testScenario5_BoundaryCase();
//
//         System.out.println("=== 测试完成 ===");
//     }
//
//     /**
//      * 场景1：同一天的有效加急请求
//      */
//     private void testScenario1_SameDayValid() {
//         System.out.println("场景1：同一天的有效加急请求");
//         System.out.println("创建时间：2025-07-28 14:00 (仓库时区)");
//         System.out.println("处理时间：2025-07-28 22:00 (仓库时区)");
//
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//         LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 22, 0, 0);
//
//         boolean result = testIsExpressRequest(createTime, processTime);
//         System.out.println("结果：" + result + " (期望：true - 同一天且时间有效)");
//         System.out.println();
//     }
//
//     /**
//      * 场景2：同一天但时间无效
//      */
//     private void testScenario2_SameDayInvalidTime() {
//         System.out.println("场景2：同一天但创建时间太早");
//         System.out.println("创建时间：2025-07-28 12:00 (仓库时区)");
//         System.out.println("处理时间：2025-07-28 22:00 (仓库时区)");
//
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 12, 0, 0);
//         LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 22, 0, 0);
//
//         boolean result = testIsExpressRequest(createTime, processTime);
//         System.out.println("结果：" + result + " (期望：false - 创建时间在13:30前)");
//         System.out.println();
//     }
//
//     /**
//      * 场景3：不同天但时间有效
//      */
//     private void testScenario3_DifferentDay() {
//         System.out.println("场景3：不同天但时间范围有效");
//         System.out.println("创建时间：2025-07-28 14:00 (仓库时区)");
//         System.out.println("处理时间：2025-07-29 22:00 (仓库时区)");
//
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//         LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 22, 0, 0);
//
//         boolean result = testIsExpressRequest(createTime, processTime);
//         System.out.println("结果：" + result + " (期望：false - 不在同一天)");
//         System.out.println();
//     }
//
//     /**
//      * 场景4：跨午夜
//      */
//     private void testScenario4_CrossMidnight() {
//         System.out.println("场景4：跨午夜处理");
//         System.out.println("创建时间：2025-07-28 23:00 (仓库时区)");
//         System.out.println("处理时间：2025-07-29 01:00 (仓库时区)");
//
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 23, 0, 0);
//         LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 1, 0, 0);
//
//         boolean result = testIsExpressRequest(createTime, processTime);
//         System.out.println("结果：" + result + " (期望：false - 跨天了)");
//         System.out.println();
//     }
//
//     /**
//      * 场景5：边界情况
//      */
//     private void testScenario5_BoundaryCase() {
//         System.out.println("场景5：边界情况 - 23:59:59处理");
//         System.out.println("创建时间：2025-07-28 13:30:01 (仓库时区)");
//         System.out.println("处理时间：2025-07-28 23:59:59 (仓库时区)");
//
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 13, 30, 1);
//         LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 23, 59, 59);
//
//         boolean result = testIsExpressRequest(createTime, processTime);
//         System.out.println("结果：" + result + " (期望：true - 边界时间但有效)");
//         System.out.println();
//     }
//
//     /**
//      * 测试isExpressRequest方法的辅助方法
//      */
//     private boolean testIsExpressRequest(LocalDateTime createTime, LocalDateTime processTime) {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // 创建测试对象
//             FodExtraDataOtcBO fodExtraDataOtcBO = new FodExtraDataOtcBO();
//             FodOtcRequestBO mockRequest = new FodOtcRequestBO();
//             WarehouseCache mockWarehouseCache = new WarehouseCache();
//
//             // 设置测试数据
//             fodExtraDataOtcBO.setWarehouseId(1L);
//             mockWarehouseCache.setTimeZone("America/New_York");
//             mockRequest.setCreateTime(createTime);
//
//             fodExtraDataOtcBO.setRequest(mockRequest);
//             fodExtraDataOtcBO.setProcessEndTime(processTime);
//
//             // Mock静态方法
//             mockedUtil.when(() -> WarehouseCacheUtil.getById(anyLong()))
//                     .thenReturn(mockWarehouseCache);
//
//             // 通过反射调用私有方法
//             try {
//                 Method method = FodExtraDataOtcBO.class.getDeclaredMethod("isExpressRequest");
//                 method.setAccessible(true);
//                 return (boolean) method.invoke(fodExtraDataOtcBO);
//             } catch (Exception e) {
//                 System.err.println("调用isExpressRequest方法失败: " + e.getMessage());
//                 return false;
//             }
//         }
//     }
// }
