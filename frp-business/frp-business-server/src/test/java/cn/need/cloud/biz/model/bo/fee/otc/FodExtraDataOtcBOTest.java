// package cn.need.cloud.biz.model.bo.fee.otc;
//
// import cn.need.cloud.biz.cache.bean.WarehouseCache;
// import cn.need.cloud.biz.util.WarehouseCacheUtil;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.MockedStatic;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// import java.time.LocalDateTime;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.anyLong;
// import static org.mockito.Mockito.*;
//
// /**
//  * FodExtraDataOtcBO单元测试类
//  * 重点测试isExpressRequest方法的业务逻辑
//  *
//  * <AUTHOR>
//  * @since 2025/7/28
//  */
// @ExtendWith(MockitoExtension.class)
// @DisplayName("FodExtraDataOtcBO单元测试")
// class FodExtraDataOtcBOTest {
//
//     private FodExtraDataOtcBO fodExtraDataOtcBO;
//     private FodOtcRequestBO mockRequest;
//     private WarehouseCache mockWarehouseCache;
//
//     @BeforeEach
//     void setUp() {
//         fodExtraDataOtcBO = new FodExtraDataOtcBO();
//         mockRequest = new FodOtcRequestBO();
//         mockWarehouseCache = new WarehouseCache();
//
//         // 设置默认的仓库ID和时区
//         fodExtraDataOtcBO.setWarehouseId(1L);
//         mockWarehouseCache.setTimeZone("America/New_York");
//     }
//
//     @Test
//     @DisplayName("测试request为null的情况")
//     void testIsExpressRequest_RequestIsNull() {
//         // Given: request为null
//         fodExtraDataOtcBO.setRequest(null);
//
//         // When & Then: 应该返回false
//         assertFalse(callIsExpressRequest());
//     }
//
//     @Test
//     @DisplayName("测试createTime为null的情况")
//     void testIsExpressRequest_CreateTimeIsNull() {
//         // Given: createTime为null
//         mockRequest.setCreateTime(null);
//         fodExtraDataOtcBO.setRequest(mockRequest);
//
//         // When & Then: 应该返回false
//         assertFalse(callIsExpressRequest());
//     }
//
//     @Test
//     @DisplayName("测试同一天的有效加急请求")
//     void testIsExpressRequest_SameDay_ValidExpressRequest() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 同一天，14:00创建，22:00处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 22, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 应该返回true（13:30后创建，23:59:59前处理）
//             assertTrue(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试同一天但创建时间在截止时间前")
//     void testIsExpressRequest_SameDay_BeforeCutoffTime() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 同一天，12:00创建，22:00处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 12, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 22, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 应该返回false（13:30前创建）
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试同一天但处理时间超过截止时间")
//     void testIsExpressRequest_SameDay_AfterEndOfDay() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 同一天，14:00创建，次日01:00处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 1, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 应该返回false（处理时间超过23:59:59）
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试边界情况：恰好在截止时间")
//     void testIsExpressRequest_ExactBoundaryTimes() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 13:30:01创建，23:59:59处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 13, 30, 1);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 23, 59, 59);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 应该返回true
//             assertTrue(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试不同天的情况 - 应该返回false")
//     void testIsExpressRequest_DifferentDay_ShouldReturnFalse() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 今天14:00创建，明天22:00处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 22, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 不同天应该返回false
//             assertFalse(callIsExpressRequest(), "不同天的情况应该返回false");
//         }
//     }
//
//     @Test
//     @DisplayName("测试跨午夜的情况 - 应该返回false")
//     void testIsExpressRequest_CrossMidnight() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 今天23:00创建，明天01:00处理
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 23, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 1, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 跨天应该返回false
//             assertFalse(callIsExpressRequest(), "跨午夜的情况应该返回false");
//         }
//     }
//
//     @Test
//     @DisplayName("测试不同时区的情况")
//     void testIsExpressRequest_DifferentTimeZone() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 使用太平洋时区
//             mockWarehouseCache.setTimeZone("America/Los_Angeles");
//
//             // UTC时间：2025-07-28 21:00 (太平洋时区14:00)
//             // UTC时间：2025-07-29 05:00 (太平洋时区22:00)
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 21, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 5, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 在太平洋时区是同一天的14:00-22:00，应该返回true
//             assertTrue(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试processEndTime为null的情况")
//     void testIsExpressRequest_ProcessEndTimeIsNull() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: processEndTime为null，会使用当前时间
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//
//             mockRequest.setCreateTime(createTime);
//             fodExtraDataOtcBO.setRequest(mockRequest);
//             fodExtraDataOtcBO.setProcessEndTime(null); // 明确设置为null
//
//             mockedUtil.when(() -> WarehouseCacheUtil.getById(anyLong()))
//                     .thenReturn(mockWarehouseCache);
//
//             // When & Then: 应该使用当前时间进行判断
//             // 结果取决于当前时间，这里主要测试不会抛异常
//             assertDoesNotThrow(() -> callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试仓库缓存获取失败的情况")
//     void testIsExpressRequest_WarehouseCacheNotFound() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 仓库缓存返回null
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             mockRequest.setCreateTime(createTime);
//             fodExtraDataOtcBO.setRequest(mockRequest);
//
//             mockedUtil.when(() -> WarehouseCacheUtil.getById(anyLong()))
//                     .thenReturn(null);
//
//             // When & Then: 应该返回false（异常处理）
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试时区配置为空的情况")
//     void testIsExpressRequest_EmptyTimeZone() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 时区为空
//             mockWarehouseCache.setTimeZone("");
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//
//             setupMockData(mockedUtil, createTime, createTime);
//
//             // When & Then: 应该返回false（异常处理）
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     /**
//      * 设置Mock数据的辅助方法
//      */
//     private void setupMockData(MockedStatic<WarehouseCacheUtil> mockedUtil,
//                               LocalDateTime createTime, LocalDateTime processTime) {
//         mockRequest.setCreateTime(createTime);
//         fodExtraDataOtcBO.setRequest(mockRequest);
//         fodExtraDataOtcBO.setProcessEndTime(processTime);
//
//         mockedUtil.when(() -> WarehouseCacheUtil.getById(anyLong()))
//                 .thenReturn(mockWarehouseCache);
//     }
//
//     @Test
//     @DisplayName("测试仓库ID为null的情况")
//     void testIsExpressRequest_WarehouseIdIsNull() {
//         // Given: 仓库ID为null
//         LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//         mockRequest.setCreateTime(createTime);
//         fodExtraDataOtcBO.setRequest(mockRequest);
//         fodExtraDataOtcBO.setWarehouseId(null);
//
//         // When & Then: 应该返回false（异常处理）
//         assertFalse(callIsExpressRequest());
//     }
//
//     @Test
//     @DisplayName("测试无效时区的情况")
//     void testIsExpressRequest_InvalidTimeZone() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 无效的时区
//             mockWarehouseCache.setTimeZone("Invalid/TimeZone");
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//
//             setupMockData(mockedUtil, createTime, createTime);
//
//             // When & Then: 应该返回false（异常处理）
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试边界情况：13:30整点创建")
//     void testIsExpressRequest_ExactCutoffTime() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 恰好13:30创建
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 13, 30, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 22, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 13:30不算after，应该返回false
//             assertFalse(callIsExpressRequest());
//         }
//     }
//
//     @Test
//     @DisplayName("测试边界情况：24:00处理时间 - 应该返回false")
//     void testIsExpressRequest_MidnightProcessTime() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 14:00创建，24:00处理（实际是次日00:00）
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 0, 0, 0);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 跨天了，根据业务规则应该返回false
//             assertFalse(callIsExpressRequest(), "午夜处理时间（跨天）应该返回false");
//         }
//     }
//
//     @Test
//     @DisplayName("测试同一天但处理时间超过23:59:59的边界情况")
//     void testIsExpressRequest_SameDay_ProcessTimeAfter2359() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 同一天，14:00创建，23:59:59处理（边界值）
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 28, 23, 59, 59);
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 同一天且在时间范围内，应该返回true
//             assertTrue(callIsExpressRequest(), "同一天且在23:59:59处理应该返回true");
//         }
//     }
//
//     @Test
//     @DisplayName("测试同一天但处理时间刚好超过23:59:59")
//     void testIsExpressRequest_SameDay_ProcessTimeJustAfter2359() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             // Given: 同一天，14:00创建，23:59:59.001处理（超过边界）
//             LocalDateTime createTime = LocalDateTime.of(2025, 7, 28, 14, 0, 0);
//             LocalDateTime processTime = LocalDateTime.of(2025, 7, 29, 0, 0, 0); // 次日00:00:00
//
//             setupMockData(mockedUtil, createTime, processTime);
//
//             // When & Then: 跨天了，应该返回false
//             assertFalse(callIsExpressRequest(), "跨天到次日00:00:00应该返回false");
//         }
//     }
//
//     @Test
//     @DisplayName("综合测试：验证同一天的完整业务逻辑")
//     void testIsExpressRequest_ComprehensiveSameDayLogic() {
//         try (MockedStatic<WarehouseCacheUtil> mockedUtil = mockStatic(WarehouseCacheUtil.class)) {
//             mockedUtil.when(() -> WarehouseCacheUtil.getById(anyLong()))
//                     .thenReturn(mockWarehouseCache);
//
//             // 测试用例1：同一天，有效时间范围
//             testSameDayScenario("2025-07-28 14:00", "2025-07-28 22:00", true, "同一天有效时间");
//
//             // 测试用例2：同一天，创建时间太早
//             testSameDayScenario("2025-07-28 10:00", "2025-07-28 22:00", false, "同一天创建时间太早");
//
//             // 测试用例3：同一天，处理时间太晚（但仍在同一天）
//             testSameDayScenario("2025-07-28 14:00", "2025-07-28 23:59:59", true, "同一天边界处理时间");
//
//             // 测试用例4：不同天，时间范围有效但跨天
//             testSameDayScenario("2025-07-28 14:00", "2025-07-29 22:00", false, "不同天但时间有效");
//
//             // 测试用例5：跨天到次日凌晨
//             testSameDayScenario("2025-07-28 14:00", "2025-07-29 01:00", false, "跨天到次日凌晨");
//         }
//     }
//
//     /**
//      * 测试同一天场景的辅助方法
//      */
//     private void testSameDayScenario(String createTimeStr, String processTimeStr,
//                                    boolean expectedResult, String description) {
//         LocalDateTime createTime = LocalDateTime.parse(createTimeStr.replace(" ", "T"));
//         LocalDateTime processTime = LocalDateTime.parse(processTimeStr.replace(" ", "T"));
//
//         mockRequest.setCreateTime(createTime);
//         fodExtraDataOtcBO.setRequest(mockRequest);
//         fodExtraDataOtcBO.setProcessEndTime(processTime);
//
//         boolean actualResult = callIsExpressRequest();
//
//         // 验证结果是否符合预期
//         assertEquals(expectedResult, actualResult,
//                 String.format("%s: 创建时间=%s, 处理时间=%s", description, createTimeStr, processTimeStr));
//     }
//
//     /**
//      * 通过反射调用私有方法isExpressRequest的辅助方法
//      */
//     private boolean callIsExpressRequest() {
//         try {
//             var method = FodExtraDataOtcBO.class.getDeclaredMethod("isExpressRequest");
//             method.setAccessible(true);
//             return (boolean) method.invoke(fodExtraDataOtcBO);
//         } catch (Exception e) {
//             throw new RuntimeException("调用isExpressRequest方法失败", e);
//         }
//     }
// }
