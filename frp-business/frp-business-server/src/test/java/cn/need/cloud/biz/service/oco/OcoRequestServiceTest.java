// package cn.need.cloud.biz.service.oco;
//
// import cn.need.cloud.biz.model.param.oco.update.OcoRequestUpdateParam;
// import cn.need.cloud.biz.model.vo.oco.OcoRequestVO;
// import cn.need.cloud.biz.service.oco.impl.OcoRequestServiceImpl;
// import cn.need.cloud.biz.util.ObjectCompareUtil;
// import cn.need.framework.common.core.exception.unchecked.BusinessException;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.InjectMocks;
// import org.mockito.Mock;
// import org.mockito.junit.jupiter.MockitoExtension;
//
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.Mockito.*;
//
// /**
//  * OCO请求服务测试类
//  * <p>
//  * 测试 OCO 请求更新过程中的 Javers 对象比较功能
//  * </p>
//  *
//  * <AUTHOR>
//  * @since 2025-08-05
//  */
// @ExtendWith(MockitoExtension.class)
// class OcoRequestServiceTest {
//
//     @Mock
//     private ObjectCompareUtil objectCompareUtil;
//
//     @InjectMocks
//     private OcoRequestServiceImpl ocoRequestService;
//
//     /**
//      * 测试 Javers 对象比较时 ID 为空的情况
//      */
//     @Test
//     void testJaversCompareWithNullId() {
//         // 创建测试数据
//         OcoRequestVO oldVo = new OcoRequestVO();
//         oldVo.setId(null); // 模拟 ID 为空的情况
//
//         OcoRequestVO newVo = new OcoRequestVO();
//         newVo.setId(1L);
//         newVo.setNote("Updated note");
//
//         // 模拟 Javers 抛出异常
//         when(objectCompareUtil.compareAndGenerateDescription(any(), any()))
//                 .thenThrow(new RuntimeException("ENTITY_INSTANCE_WITH_NULL_ID: Found Entity instance 'cn.need.cloud.biz.model.vo.oco.OcoRequestVO' with null Id-property 'id'"));
//
//         // 验证异常被正确抛出
//         assertThrows(RuntimeException.class, () -> {
//             objectCompareUtil.compareAndGenerateDescription(oldVo, newVo);
//         });
//     }
//
//     /**
//      * 测试正常的 Javers 对象比较
//      */
//     @Test
//     void testJaversCompareWithValidId() {
//         // 创建测试数据
//         OcoRequestVO oldVo = new OcoRequestVO();
//         oldVo.setId(1L);
//         oldVo.setNote("Original note");
//
//         OcoRequestVO newVo = new OcoRequestVO();
//         newVo.setId(1L);
//         newVo.setNote("Updated note");
//
//         // 模拟正常的比较结果
//         String expectedJson = "{\"changes\":[{\"changeType\":\"ValueChange\",\"property\":\"note\",\"left\":\"Original note\",\"right\":\"Updated note\"}]}";
//         when(objectCompareUtil.compareAndGenerateDescription(oldVo, newVo))
//                 .thenReturn(expectedJson);
//
//         // 执行比较
//         String result = objectCompareUtil.compareAndGenerateDescription(oldVo, newVo);
//
//         // 验证结果
//         assertNotNull(result);
//         assertEquals(expectedJson, result);
//         verify(objectCompareUtil, times(1)).compareAndGenerateDescription(oldVo, newVo);
//     }
//
//     /**
//      * 测试 VO 对象 ID 验证逻辑
//      */
//     @Test
//     void testVoIdValidation() {
//         // 测试 ID 为空的情况
//         OcoRequestVO voWithNullId = new OcoRequestVO();
//         voWithNullId.setId(null);
//
//         assertNull(voWithNullId.getId(), "VO ID should be null");
//
//         // 测试 ID 不为空的情况
//         OcoRequestVO voWithValidId = new OcoRequestVO();
//         voWithValidId.setId(1L);
//
//         assertNotNull(voWithValidId.getId(), "VO ID should not be null");
//         assertEquals(1L, voWithValidId.getId(), "VO ID should be 1");
//     }
//
//     /**
//      * 测试 UpdateParam 对象的 ID 验证
//      */
//     @Test
//     void testUpdateParamIdValidation() {
//         // 测试 ID 不为空的情况（UpdateParam 的 ID 有 @NotNull 验证）
//         OcoRequestUpdateParam updateParam = new OcoRequestUpdateParam();
//         updateParam.setId(1L);
//         updateParam.setNote("Test note");
//
//         assertNotNull(updateParam.getId(), "UpdateParam ID should not be null");
//         assertEquals(1L, updateParam.getId(), "UpdateParam ID should be 1");
//     }
// }
