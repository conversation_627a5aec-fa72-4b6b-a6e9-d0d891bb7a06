package cn.need.cloud.biz.service;

import cn.need.framework.common.mybatis.model.IdExtractable;
import cn.need.framework.common.mybatis.model.SuperModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.junit.jupiter.api.Test;
import org.springframework.dao.OptimisticLockingFailureException;

import java.math.BigDecimal;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 乐观锁部分更新测试
 *
 * <AUTHOR>
 * @since 2025-08-05
 */
class OptimisticLockPartialUpdateTest {

    @Test
    void testOptimisticLockConflictDetection() {
        // 模拟乐观锁冲突场景
        
        // 准备测试数据
        TestEntity entity = new TestEntity();
        entity.setId(123L);
        entity.setVersion(1L);
        entity.setQuantity(new BigDecimal("50"));
        entity.setRemark("原始备注");

        TestUpdateParam updateParam1 = new TestUpdateParam();
        updateParam1.setId(123L);
        updateParam1.setQuantity(new BigDecimal("100"));

        TestUpdateParam updateParam2 = new TestUpdateParam();
        updateParam2.setId(123L);
        updateParam2.setRemark("更新的备注");

        // 模拟并发更新场景
        MockUpdateService service = new MockUpdateService(entity);

        // 第一个更新应该成功
        int updated1 = service.partialUpdateWithOptimisticLock(updateParam1);
        assertEquals(1, updated1);
        assertEquals(2L, service.getCurrentEntity().getVersion()); // 版本号应该递增

        // 第二个更新应该失败（乐观锁冲突）
        assertThrows(OptimisticLockingFailureException.class, () -> {
            service.partialUpdateWithOptimisticLock(updateParam2);
        });
    }

    @Test
    void testConcurrentUpdateWithOptimisticLock() throws Exception {
        // 测试真实的并发场景
        
        TestEntity entity = new TestEntity();
        entity.setId(456L);
        entity.setVersion(1L);
        entity.setQuantity(new BigDecimal("100"));

        MockUpdateService service = new MockUpdateService(entity);
        ExecutorService executor = Executors.newFixedThreadPool(2);

        try {
            // 准备两个并发更新
            TestUpdateParam updateParam1 = new TestUpdateParam();
            updateParam1.setId(456L);
            updateParam1.setQuantity(new BigDecimal("200"));

            TestUpdateParam updateParam2 = new TestUpdateParam();
            updateParam2.setId(456L);
            updateParam2.setQuantity(new BigDecimal("300"));

            // 并发执行更新
            CompletableFuture<Integer> future1 = CompletableFuture.supplyAsync(() -> {
                try {
                    return service.partialUpdateWithOptimisticLock(updateParam1);
                } catch (OptimisticLockingFailureException e) {
                    return -1; // 表示冲突
                }
            }, executor);

            CompletableFuture<Integer> future2 = CompletableFuture.supplyAsync(() -> {
                try {
                    Thread.sleep(10); // 稍微延迟，增加冲突概率
                    return service.partialUpdateWithOptimisticLock(updateParam2);
                } catch (Exception e) {
                    return -1; // 表示冲突或其他异常
                }
            }, executor);

            // 等待结果
            Integer result1 = future1.get();
            Integer result2 = future2.get();

            // 验证结果：应该有一个成功，一个失败
            assertTrue((result1 == 1 && result2 == -1) || (result1 == -1 && result2 == 1),
                    "One update should succeed and one should fail due to optimistic lock conflict");

            // 验证版本号正确递增
            assertEquals(2L, service.getCurrentEntity().getVersion());

        } finally {
            executor.shutdown();
        }
    }

    @Test
    void testOptimisticLockWithFieldValidation() {
        // 测试字段验证 + 乐观锁组合
        
        TestEntity entity = new TestEntity();
        entity.setId(789L);
        entity.setVersion(1L);

        TestUpdateParamWithInvalidField invalidParam = new TestUpdateParamWithInvalidField();
        invalidParam.setId(789L);
        invalidParam.setInvalidField("invalid");

        MockUpdateService service = new MockUpdateService(entity);

        // 应该在字段验证阶段就失败，不会到达乐观锁检查
        assertThrows(Exception.class, () -> {
            service.partialUpdateWithValidationAndOptimisticLock(invalidParam);
        });

        // 版本号不应该改变
        assertEquals(1L, service.getCurrentEntity().getVersion());
    }

    @Test
    void testVersionNullHandling() {
        // 测试版本号为null的情况
        
        TestEntity entity = new TestEntity();
        entity.setId(999L);
        entity.setVersion(null); // 版本号为null

        TestUpdateParam updateParam = new TestUpdateParam();
        updateParam.setId(999L);
        updateParam.setQuantity(new BigDecimal("100"));

        MockUpdateService service = new MockUpdateService(entity);

        // 应该抛出异常，因为版本号为null
        Exception exception = assertThrows(Exception.class, () -> {
            service.partialUpdateWithOptimisticLock(updateParam);
        });

        assertTrue(exception.getMessage().contains("Version field is null"));
    }

    // 测试实体类
    @Data
    @EqualsAndHashCode(callSuper = true)
    static class TestEntity extends SuperModel {
        private BigDecimal quantity;
        private String remark;
    }

    // 测试更新参数类
    @Data
    static class TestUpdateParam implements IdExtractable {
        private Long id;
        private BigDecimal quantity;
        private String remark;

        @Override
        public Long getId() {
            return id;
        }
    }

    // 包含无效字段的测试参数类
    @Data
    static class TestUpdateParamWithInvalidField implements IdExtractable {
        private Long id;
        private String invalidField;

        @Override
        public Long getId() {
            return id;
        }
    }

    // 模拟的更新服务
    static class MockUpdateService {
        private TestEntity currentEntity;

        public MockUpdateService(TestEntity entity) {
            this.currentEntity = entity;
        }

        public TestEntity getCurrentEntity() {
            return currentEntity;
        }

        public <P extends IdExtractable> int partialUpdateWithOptimisticLock(P updateParam) {
            // 模拟乐观锁更新逻辑
            Long id = updateParam.getId();
            if (!id.equals(currentEntity.getId())) {
                throw new RuntimeException("Record not found");
            }

            Long currentVersion = currentEntity.getVersion();
            if (currentVersion == null) {
                throw new RuntimeException("Version field is null, cannot perform optimistic lock update");
            }

            // 模拟并发冲突检测
            synchronized (this) {
                if (!currentVersion.equals(currentEntity.getVersion())) {
                    throw new OptimisticLockingFailureException("Optimistic lock conflict detected");
                }

                // 更新字段
                if (updateParam instanceof TestUpdateParam) {
                    TestUpdateParam param = (TestUpdateParam) updateParam;
                    if (param.getQuantity() != null) {
                        currentEntity.setQuantity(param.getQuantity());
                    }
                    if (param.getRemark() != null) {
                        currentEntity.setRemark(param.getRemark());
                    }
                }

                // 递增版本号
                currentEntity.setVersion(currentVersion + 1);
                return 1;
            }
        }

        public <P extends IdExtractable> int partialUpdateWithValidationAndOptimisticLock(P updateParam) {
            // 模拟字段验证
            if (updateParam instanceof TestUpdateParamWithInvalidField) {
                throw new RuntimeException("Invalid fields found in update parameter");
            }

            return partialUpdateWithOptimisticLock(updateParam);
        }
    }
}
