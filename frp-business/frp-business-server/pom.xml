<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.need.cloud</groupId>
        <artifactId>frp-business</artifactId>
        <version>frp-dev.41-SNAPSHOT</version>
    </parent>
    <artifactId>frp-business-server</artifactId>
    <packaging>jar</packaging>
    <name>frp-business-server</name>
    <description>the business server Center for uneed need-cloud</description>

    <properties>

    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>frp-business-client</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>shipping-unify-client</artifactId>
            <version>frp-dev.41-SNAPSHOT</version>
        </dependency>

        <!-- 缓存模块-->
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>frp-business-cache</artifactId>
            <version>${project.version}</version>
        </dependency>

        <!-- uneed common 依赖-->
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-support</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.common</groupId>
            <artifactId>need-common-swagger</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.whvcse</groupId>
            <artifactId>easy-captcha</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-tenant</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-warehouse</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dict-client</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-upms-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.cloud</groupId>
            <artifactId>need-cloud-dfs-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.need.framework.starter</groupId>
            <artifactId>need-starter-job</artifactId>
        </dependency>

        <!-- Test dependencies -->
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <!-- deploy时，过滤当前模块-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>